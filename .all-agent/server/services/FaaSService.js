const axios = require('axios');
const yaml = require('js-yaml');
const fs = require('fs').promises;
const path = require('path');

/**
 * FaaS (Function as a Service) 管理服务
 * 基于 Knative Serving 提供无服务器函数计算
 */
class FaaSService {
    constructor(options = {}) {
        this.options = {
            knativeUrl: options.knativeUrl || process.env.KNATIVE_URL || 'http://kourier.kourier-system.svc.cluster.local',
            namespace: options.namespace || 'knative-serving',
            domain: options.domain || 'all-agent.com',
            functionsPath: options.functionsPath || './faas/functions',
            templatesPath: options.templatesPath || './faas/templates',
            ...options
        };

        this.functions = new Map();
        this.deployments = new Map();
        this.metrics = {
            totalInvocations: 0,
            totalErrors: 0,
            averageLatency: 0,
            activeInstances: 0
        };

        this.initializeDirectories();
        this.loadFunctions();
    }

    /**
     * 初始化目录结构
     */
    async initializeDirectories() {
        try {
            await fs.mkdir(this.options.functionsPath, { recursive: true });
            await fs.mkdir(this.options.templatesPath, { recursive: true });
        } catch (error) {
            console.error('Failed to initialize FaaS directories:', error);
        }
    }

    /**
     * 加载已部署的函数
     */
    async loadFunctions() {
        try {
            // 从 Kubernetes 获取 Knative 服务列表
            const services = await this.getKnativeServices();
            
            for (const service of services) {
                this.functions.set(service.metadata.name, {
                    name: service.metadata.name,
                    namespace: service.metadata.namespace,
                    url: this.getFunctionUrl(service.metadata.name),
                    status: service.status?.conditions?.[0]?.status || 'Unknown',
                    createdAt: service.metadata.creationTimestamp,
                    spec: service.spec
                });
            }

            console.log(`✅ 加载了 ${this.functions.size} 个函数`);
        } catch (error) {
            console.error('Failed to load functions:', error);
        }
    }

    /**
     * 创建函数
     */
    async createFunction(functionConfig) {
        try {
            const {
                name,
                runtime = 'python3.9',
                code,
                handler = 'main',
                memory = '256Mi',
                cpu = '100m',
                timeout = 300,
                minScale = 0,
                maxScale = 10,
                concurrency = 10,
                env = {}
            } = functionConfig;

            // 生成 Knative Service 清单
            const serviceManifest = this.generateKnativeService({
                name,
                runtime,
                code,
                handler,
                memory,
                cpu,
                timeout,
                minScale,
                maxScale,
                concurrency,
                env
            });

            // 保存清单文件
            const manifestPath = path.join(this.options.functionsPath, `${name}.yaml`);
            await fs.writeFile(manifestPath, yaml.dump(serviceManifest));

            // 部署到 Kubernetes
            await this.deployToKubernetes(manifestPath);

            // 等待部署完成
            await this.waitForDeployment(name);

            const functionInfo = {
                name,
                namespace: this.options.namespace,
                url: this.getFunctionUrl(name),
                runtime,
                handler,
                memory,
                cpu,
                timeout,
                minScale,
                maxScale,
                concurrency,
                env,
                status: 'Ready',
                createdAt: new Date().toISOString()
            };

            this.functions.set(name, functionInfo);

            console.log(`✅ 创建函数: ${name}`);
            return functionInfo;

        } catch (error) {
            console.error('Failed to create function:', error);
            throw error;
        }
    }

    /**
     * 生成 Knative Service 清单
     */
    generateKnativeService(config) {
        const { name, runtime, code, handler, memory, cpu, timeout, minScale, maxScale, concurrency, env } = config;

        return {
            apiVersion: 'serving.knative.dev/v1',
            kind: 'Service',
            metadata: {
                name,
                namespace: this.options.namespace,
                labels: {
                    'app.kubernetes.io/name': name,
                    'app.kubernetes.io/component': 'faas',
                    'app.kubernetes.io/managed-by': 'all-agent'
                },
                annotations: {
                    'autoscaling.knative.dev/minScale': minScale.toString(),
                    'autoscaling.knative.dev/maxScale': maxScale.toString(),
                    'autoscaling.knative.dev/target': concurrency.toString()
                }
            },
            spec: {
                template: {
                    metadata: {
                        labels: {
                            'app.kubernetes.io/name': name,
                            'app.kubernetes.io/component': 'faas'
                        },
                        annotations: {
                            'autoscaling.knative.dev/class': 'kpa.autoscaling.knative.dev',
                            'autoscaling.knative.dev/metric': 'concurrency'
                        }
                    },
                    spec: {
                        containerConcurrency: concurrency,
                        timeoutSeconds: timeout,
                        containers: [{
                            name: 'function',
                            image: this.getRuntimeImage(runtime),
                            ports: [{
                                name: 'http1',
                                containerPort: 8080,
                                protocol: 'TCP'
                            }],
                            env: [
                                { name: 'PORT', value: '8080' },
                                { name: 'FUNCTION_NAME', value: name },
                                { name: 'HANDLER', value: handler },
                                ...Object.entries(env).map(([key, value]) => ({ name: key, value: value.toString() }))
                            ],
                            command: this.getRuntimeCommand(runtime, code, handler),
                            resources: {
                                requests: { cpu, memory },
                                limits: { cpu: this.scaleCpu(cpu, 2), memory: this.scaleMemory(memory, 2) }
                            }
                        }]
                    }
                }
            }
        };
    }

    /**
     * 获取运行时镜像
     */
    getRuntimeImage(runtime) {
        const images = {
            'python3.9': 'python:3.9-slim',
            'python3.8': 'python:3.8-slim',
            'node16': 'node:16-alpine',
            'node18': 'node:18-alpine',
            'go1.19': 'golang:1.19-alpine',
            'java11': 'openjdk:11-jre-slim',
            'dotnet6': 'mcr.microsoft.com/dotnet/aspnet:6.0'
        };
        return images[runtime] || 'python:3.9-slim';
    }

    /**
     * 获取运行时启动命令
     */
    getRuntimeCommand(runtime, code, handler) {
        if (runtime.startsWith('python')) {
            return [
                '/bin/bash',
                '-c',
                `
                pip install flask gunicorn
                cat > app.py << 'EOF'
${code}
EOF
                gunicorn --bind 0.0.0.0:8080 --workers 1 --timeout 300 app:app
                `.trim()
            ];
        } else if (runtime.startsWith('node')) {
            return [
                '/bin/sh',
                '-c',
                `
                npm init -y
                npm install express
                cat > index.js << 'EOF'
${code}
EOF
                node index.js
                `.trim()
            ];
        }
        
        return ['/bin/bash', '-c', 'echo "Unsupported runtime" && exit 1'];
    }

    /**
     * 调用函数
     */
    async invokeFunction(name, payload = {}, options = {}) {
        try {
            const func = this.functions.get(name);
            if (!func) {
                throw new Error(`Function ${name} not found`);
            }

            const startTime = Date.now();
            
            const response = await axios.post(func.url, payload, {
                headers: {
                    'Content-Type': 'application/json',
                    'User-Agent': 'All-Agent-FaaS/1.0',
                    ...options.headers
                },
                timeout: options.timeout || 30000
            });

            const latency = Date.now() - startTime;
            
            // 更新指标
            this.metrics.totalInvocations++;
            this.metrics.averageLatency = (this.metrics.averageLatency + latency) / 2;

            const result = {
                success: true,
                data: response.data,
                latency,
                function: name,
                invokedAt: new Date().toISOString()
            };

            console.log(`📞 调用函数: ${name} (${latency}ms)`);
            return result;

        } catch (error) {
            this.metrics.totalErrors++;
            
            console.error(`Failed to invoke function ${name}:`, error.message);
            
            return {
                success: false,
                error: error.message,
                function: name,
                invokedAt: new Date().toISOString()
            };
        }
    }

    /**
     * 更新函数
     */
    async updateFunction(name, updates) {
        try {
            const func = this.functions.get(name);
            if (!func) {
                throw new Error(`Function ${name} not found`);
            }

            // 更新函数配置
            const updatedConfig = { ...func, ...updates };
            
            // 重新生成清单
            const serviceManifest = this.generateKnativeService(updatedConfig);
            
            // 保存更新的清单
            const manifestPath = path.join(this.options.functionsPath, `${name}.yaml`);
            await fs.writeFile(manifestPath, yaml.dump(serviceManifest));

            // 应用更新
            await this.deployToKubernetes(manifestPath);

            // 等待更新完成
            await this.waitForDeployment(name);

            this.functions.set(name, updatedConfig);

            console.log(`🔄 更新函数: ${name}`);
            return updatedConfig;

        } catch (error) {
            console.error('Failed to update function:', error);
            throw error;
        }
    }

    /**
     * 删除函数
     */
    async deleteFunction(name) {
        try {
            // 从 Kubernetes 删除
            await this.deleteFromKubernetes(name);

            // 删除本地文件
            const manifestPath = path.join(this.options.functionsPath, `${name}.yaml`);
            try {
                await fs.unlink(manifestPath);
            } catch (error) {
                // 文件可能不存在
            }

            this.functions.delete(name);

            console.log(`🗑️ 删除函数: ${name}`);
            return { success: true, message: `Function ${name} deleted` };

        } catch (error) {
            console.error('Failed to delete function:', error);
            throw error;
        }
    }

    /**
     * 获取函数列表
     */
    getFunctions() {
        return Array.from(this.functions.values());
    }

    /**
     * 获取函数详情
     */
    getFunction(name) {
        return this.functions.get(name);
    }

    /**
     * 获取函数指标
     */
    async getFunctionMetrics(name) {
        try {
            // 这里可以集成 Prometheus 获取真实指标
            // 目前返回模拟数据
            return {
                function: name,
                invocations: Math.floor(Math.random() * 1000),
                errors: Math.floor(Math.random() * 10),
                averageLatency: Math.floor(Math.random() * 500) + 50,
                p95Latency: Math.floor(Math.random() * 1000) + 100,
                activeInstances: Math.floor(Math.random() * 5),
                coldStarts: Math.floor(Math.random() * 20),
                timestamp: new Date().toISOString()
            };
        } catch (error) {
            console.error('Failed to get function metrics:', error);
            return null;
        }
    }

    /**
     * 获取函数 URL
     */
    getFunctionUrl(name) {
        return `http://${name}.${this.options.namespace}.${this.options.domain}`;
    }

    /**
     * 获取 Knative 服务列表
     */
    async getKnativeServices() {
        // 这里应该调用 Kubernetes API
        // 目前返回空数组
        return [];
    }

    /**
     * 部署到 Kubernetes
     */
    async deployToKubernetes(manifestPath) {
        // 这里应该调用 kubectl apply
        console.log(`Deploying ${manifestPath} to Kubernetes`);
    }

    /**
     * 从 Kubernetes 删除
     */
    async deleteFromKubernetes(name) {
        // 这里应该调用 kubectl delete
        console.log(`Deleting function ${name} from Kubernetes`);
    }

    /**
     * 等待部署完成
     */
    async waitForDeployment(name, timeout = 300000) {
        const startTime = Date.now();
        
        while (Date.now() - startTime < timeout) {
            try {
                // 检查函数是否就绪
                const response = await axios.get(this.getFunctionUrl(name) + '/health', {
                    timeout: 5000
                });
                
                if (response.status === 200) {
                    return true;
                }
            } catch (error) {
                // 继续等待
            }
            
            await new Promise(resolve => setTimeout(resolve, 5000));
        }
        
        throw new Error(`Deployment timeout for function ${name}`);
    }

    /**
     * 扩展 CPU 资源
     */
    scaleCpu(cpu, factor) {
        const value = parseInt(cpu);
        const unit = cpu.replace(/\d+/, '');
        return `${value * factor}${unit}`;
    }

    /**
     * 扩展内存资源
     */
    scaleMemory(memory, factor) {
        const value = parseInt(memory);
        const unit = memory.replace(/\d+/, '');
        return `${value * factor}${unit}`;
    }

    /**
     * 创建函数模板
     */
    async createTemplate(name, template) {
        try {
            const templatePath = path.join(this.options.templatesPath, `${name}.yaml`);
            await fs.writeFile(templatePath, yaml.dump(template));
            
            console.log(`📝 创建模板: ${name}`);
            return { success: true, path: templatePath };
        } catch (error) {
            console.error('Failed to create template:', error);
            throw error;
        }
    }

    /**
     * 获取服务状态
     */
    getStatus() {
        return {
            functionsCount: this.functions.size,
            metrics: this.metrics,
            namespace: this.options.namespace,
            domain: this.options.domain,
            status: 'healthy'
        };
    }

    /**
     * 批量调用函数
     */
    async batchInvoke(requests) {
        const results = await Promise.allSettled(
            requests.map(req => this.invokeFunction(req.function, req.payload, req.options))
        );

        return results.map((result, index) => ({
            request: requests[index],
            result: result.status === 'fulfilled' ? result.value : { success: false, error: result.reason.message }
        }));
    }

    /**
     * 函数日志
     */
    async getFunctionLogs(name, options = {}) {
        try {
            // 这里应该从 Kubernetes 获取 Pod 日志
            // 目前返回模拟日志
            return {
                function: name,
                logs: [
                    `${new Date().toISOString()} INFO Function ${name} started`,
                    `${new Date().toISOString()} INFO Processing request`,
                    `${new Date().toISOString()} INFO Request completed successfully`
                ],
                timestamp: new Date().toISOString()
            };
        } catch (error) {
            console.error('Failed to get function logs:', error);
            return { function: name, logs: [], error: error.message };
        }
    }
}

module.exports = FaaSService;
