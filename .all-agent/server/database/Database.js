const Database = require('better-sqlite3');
const path = require('path');
const fs = require('fs').promises;

/**
 * 数据库管理类
 * 使用 better-sqlite3 提供数据持久化支持
 */
class AllAgentDatabase {
  constructor(options = {}) {
    this.dbPath = options.dbPath || path.join(process.cwd(), '.all-agent/data/all-agent.db');
    this.db = null;
    this.isConnected = false;

    // 确保数据目录存在
    this.ensureDataDirectory();
  }

  /**
   * 确保数据目录存在
   */
  async ensureDataDirectory() {
    const dataDir = path.dirname(this.dbPath);
    try {
      await fs.mkdir(dataDir, { recursive: true });
    } catch (error) {
      console.error('创建数据目录失败:', error);
    }
  }

  /**
   * 连接数据库
   */
  async connect() {
    return new Promise((resolve, reject) => {
      this.db = new sqlite3.Database(this.dbPath, (err) => {
        if (err) {
          console.error('数据库连接失败:', err);
          reject(err);
        } else {
          console.log('✅ 数据库连接成功');
          this.isConnected = true;
          resolve();
        }
      });
    });
  }

  /**
   * 初始化数据库表
   */
  async initialize() {
    if (!this.isConnected) {
      await this.connect();
    }

    try {
      await this.createTables();
      console.log('✅ 数据库表初始化完成');
    } catch (error) {
      console.error('❌ 数据库初始化失败:', error);
      throw error;
    }
  }

  /**
   * 创建数据库表
   */
  async createTables() {
    const tables = [
      // 用户表
      `CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username VARCHAR(50) UNIQUE NOT NULL,
                email VARCHAR(100) UNIQUE NOT NULL,
                password_hash VARCHAR(255) NOT NULL,
                role VARCHAR(20) DEFAULT 'user',
                avatar_url VARCHAR(255),
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                last_login DATETIME,
                is_active BOOLEAN DEFAULT 1
            )`,

      // 项目表
      `CREATE TABLE IF NOT EXISTS projects (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                name VARCHAR(100) NOT NULL,
                description TEXT,
                project_path VARCHAR(500),
                tech_stack TEXT, -- JSON 格式
                status VARCHAR(20) DEFAULT 'active',
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )`,

      // 任务表
      `CREATE TABLE IF NOT EXISTS tasks (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                task_id VARCHAR(100) UNIQUE NOT NULL,
                user_id INTEGER NOT NULL,
                project_id INTEGER,
                agent_type VARCHAR(50) NOT NULL,
                action VARCHAR(100) NOT NULL,
                input_data TEXT, -- JSON 格式
                result_data TEXT, -- JSON 格式
                status VARCHAR(20) DEFAULT 'pending',
                priority VARCHAR(20) DEFAULT 'normal',
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                started_at DATETIME,
                completed_at DATETIME,
                error_message TEXT,
                retry_count INTEGER DEFAULT 0,
                FOREIGN KEY (user_id) REFERENCES users (id),
                FOREIGN KEY (project_id) REFERENCES projects (id)
            )`,

      // 聊天记录表
      `CREATE TABLE IF NOT EXISTS chat_messages (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                project_id INTEGER,
                agent_type VARCHAR(50) NOT NULL,
                message_type VARCHAR(20) NOT NULL, -- 'user' or 'agent'
                content TEXT NOT NULL,
                context_data TEXT, -- JSON 格式
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id),
                FOREIGN KEY (project_id) REFERENCES projects (id)
            )`,

      // 代码生成记录表
      `CREATE TABLE IF NOT EXISTS code_generations (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                project_id INTEGER,
                template_id VARCHAR(100) NOT NULL,
                parameters TEXT, -- JSON 格式
                generated_files TEXT, -- JSON 格式
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id),
                FOREIGN KEY (project_id) REFERENCES projects (id)
            )`,

      // 系统日志表
      `CREATE TABLE IF NOT EXISTS system_logs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                level VARCHAR(10) NOT NULL,
                message TEXT NOT NULL,
                data TEXT, -- JSON 格式
                source VARCHAR(50),
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )`,

      // 用户会话表
      `CREATE TABLE IF NOT EXISTS user_sessions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                session_token VARCHAR(255) UNIQUE NOT NULL,
                expires_at DATETIME NOT NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                last_accessed DATETIME DEFAULT CURRENT_TIMESTAMP,
                ip_address VARCHAR(45),
                user_agent TEXT,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )`,

      // 项目分析缓存表
      `CREATE TABLE IF NOT EXISTS analysis_cache (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                project_path VARCHAR(500) NOT NULL,
                cache_key VARCHAR(255) UNIQUE NOT NULL,
                analysis_data TEXT NOT NULL, -- JSON 格式
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                expires_at DATETIME NOT NULL
            )`
    ];

    for (const sql of tables) {
      await this.run(sql);
    }

    // 创建索引
    const indexes = [
      'CREATE INDEX IF NOT EXISTS idx_users_email ON users(email)',
      'CREATE INDEX IF NOT EXISTS idx_users_username ON users(username)',
      'CREATE INDEX IF NOT EXISTS idx_projects_user_id ON projects(user_id)',
      'CREATE INDEX IF NOT EXISTS idx_tasks_user_id ON tasks(user_id)',
      'CREATE INDEX IF NOT EXISTS idx_tasks_status ON tasks(status)',
      'CREATE INDEX IF NOT EXISTS idx_tasks_agent_type ON tasks(agent_type)',
      'CREATE INDEX IF NOT EXISTS idx_chat_messages_user_id ON chat_messages(user_id)',
      'CREATE INDEX IF NOT EXISTS idx_chat_messages_created_at ON chat_messages(created_at)',
      'CREATE INDEX IF NOT EXISTS idx_system_logs_level ON system_logs(level)',
      'CREATE INDEX IF NOT EXISTS idx_system_logs_created_at ON system_logs(created_at)',
      'CREATE INDEX IF NOT EXISTS idx_user_sessions_token ON user_sessions(session_token)',
      'CREATE INDEX IF NOT EXISTS idx_analysis_cache_key ON analysis_cache(cache_key)'
    ];

    for (const sql of indexes) {
      await this.run(sql);
    }
  }

  /**
   * 执行 SQL 语句
   */
  run(sql, params = []) {
    return new Promise((resolve, reject) => {
      this.db.run(sql, params, function (err) {
        if (err) {
          reject(err);
        } else {
          resolve({ id: this.lastID, changes: this.changes });
        }
      });
    });
  }

  /**
   * 查询单条记录
   */
  get(sql, params = []) {
    return new Promise((resolve, reject) => {
      this.db.get(sql, params, (err, row) => {
        if (err) {
          reject(err);
        } else {
          resolve(row);
        }
      });
    });
  }

  /**
   * 查询多条记录
   */
  all(sql, params = []) {
    return new Promise((resolve, reject) => {
      this.db.all(sql, params, (err, rows) => {
        if (err) {
          reject(err);
        } else {
          resolve(rows);
        }
      });
    });
  }

  /**
   * 开始事务
   */
  async beginTransaction() {
    await this.run('BEGIN TRANSACTION');
  }

  /**
   * 提交事务
   */
  async commit() {
    await this.run('COMMIT');
  }

  /**
   * 回滚事务
   */
  async rollback() {
    await this.run('ROLLBACK');
  }

  /**
   * 关闭数据库连接
   */
  async close() {
    if (this.db) {
      return new Promise((resolve, reject) => {
        this.db.close((err) => {
          if (err) {
            reject(err);
          } else {
            console.log('✅ 数据库连接已关闭');
            this.isConnected = false;
            resolve();
          }
        });
      });
    }
  }

  /**
   * 获取数据库统计信息
   */
  async getStats() {
    const stats = {};

    const tables = ['users', 'projects', 'tasks', 'chat_messages', 'code_generations', 'system_logs'];

    for (const table of tables) {
      try {
        const result = await this.get(`SELECT COUNT(*) as count FROM ${table}`);
        stats[table] = result.count;
      } catch (error) {
        stats[table] = 0;
      }
    }

    return stats;
  }

  /**
   * 清理过期数据
   */
  async cleanup() {
    try {
      // 清理过期会话
      await this.run('DELETE FROM user_sessions WHERE expires_at < datetime("now")');

      // 清理过期缓存
      await this.run('DELETE FROM analysis_cache WHERE expires_at < datetime("now")');

      // 清理旧日志（保留30天）
      await this.run('DELETE FROM system_logs WHERE created_at < datetime("now", "-30 days")');

      console.log('✅ 数据库清理完成');
    } catch (error) {
      console.error('❌ 数据库清理失败:', error);
    }
  }

  /**
   * 备份数据库
   */
  async backup(backupPath) {
    try {
      const backupDir = path.dirname(backupPath);
      await fs.mkdir(backupDir, { recursive: true });

      await fs.copyFile(this.dbPath, backupPath);
      console.log(`✅ 数据库备份完成: ${backupPath}`);
    } catch (error) {
      console.error('❌ 数据库备份失败:', error);
      throw error;
    }
  }

  /**
   * 检查数据库健康状态
   */
  async healthCheck() {
    try {
      await this.get('SELECT 1');
      return { healthy: true, message: '数据库连接正常' };
    } catch (error) {
      return { healthy: false, message: `数据库连接异常: ${error.message}` };
    }
  }
}

module.exports = Database;
