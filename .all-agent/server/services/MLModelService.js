const axios = require('axios');
const fs = require('fs').promises;
const path = require('path');
const FormData = require('form-data');
const { spawn } = require('child_process');

/**
 * ML 模型管理服务
 * 集成 MLflow 进行模型生命周期管理
 */
class MLModelService {
    constructor(options = {}) {
        this.options = {
            mlflowUrl: options.mlflowUrl || process.env.MLFLOW_TRACKING_URI || 'http://mlflow-server:5000',
            authToken: options.authToken || process.env.MLFLOW_AUTH_TOKEN,
            artifactRoot: options.artifactRoot || process.env.MLFLOW_DEFAULT_ARTIFACT_ROOT || './ml-artifacts',
            modelsPath: options.modelsPath || './models',
            experimentsPath: options.experimentsPath || './experiments',
            ...options
        };

        this.models = new Map();
        this.experiments = new Map();
        this.activeRuns = new Map();
        
        this.initializeDirectories();
    }

    /**
     * 初始化目录结构
     */
    async initializeDirectories() {
        try {
            await fs.mkdir(this.options.modelsPath, { recursive: true });
            await fs.mkdir(this.options.experimentsPath, { recursive: true });
            await fs.mkdir(this.options.artifactRoot, { recursive: true });
        } catch (error) {
            console.error('Failed to initialize ML directories:', error);
        }
    }

    /**
     * 创建实验
     */
    async createExperiment(name, description = '', tags = {}) {
        try {
            const response = await this.makeMLflowRequest('POST', '/api/2.0/mlflow/experiments/create', {
                name,
                artifact_location: `${this.options.artifactRoot}/${name}`,
                tags: Object.entries(tags).map(([key, value]) => ({ key, value }))
            });

            const experiment = {
                id: response.data.experiment_id,
                name,
                description,
                tags,
                createdAt: new Date().toISOString(),
                status: 'active'
            };

            this.experiments.set(experiment.id, experiment);
            
            console.log(`✅ 创建实验: ${name} (ID: ${experiment.id})`);
            return experiment;

        } catch (error) {
            console.error('Failed to create experiment:', error);
            throw error;
        }
    }

    /**
     * 开始运行
     */
    async startRun(experimentId, runName, tags = {}, parameters = {}) {
        try {
            const response = await this.makeMLflowRequest('POST', '/api/2.0/mlflow/runs/create', {
                experiment_id: experimentId,
                start_time: Date.now(),
                tags: Object.entries(tags).map(([key, value]) => ({ key, value })),
                run_name: runName
            });

            const run = {
                id: response.data.run.info.run_id,
                experimentId,
                name: runName,
                status: 'RUNNING',
                startTime: Date.now(),
                parameters,
                metrics: {},
                artifacts: [],
                tags
            };

            this.activeRuns.set(run.id, run);

            // 记录参数
            if (Object.keys(parameters).length > 0) {
                await this.logParameters(run.id, parameters);
            }

            console.log(`🚀 开始运行: ${runName} (ID: ${run.id})`);
            return run;

        } catch (error) {
            console.error('Failed to start run:', error);
            throw error;
        }
    }

    /**
     * 记录参数
     */
    async logParameters(runId, parameters) {
        try {
            const params = Object.entries(parameters).map(([key, value]) => ({
                key,
                value: String(value)
            }));

            await this.makeMLflowRequest('POST', '/api/2.0/mlflow/runs/log-batch', {
                run_id: runId,
                params
            });

            const run = this.activeRuns.get(runId);
            if (run) {
                run.parameters = { ...run.parameters, ...parameters };
            }

            console.log(`📝 记录参数: ${Object.keys(parameters).join(', ')}`);

        } catch (error) {
            console.error('Failed to log parameters:', error);
            throw error;
        }
    }

    /**
     * 记录指标
     */
    async logMetrics(runId, metrics, step = 0) {
        try {
            const metricsArray = Object.entries(metrics).map(([key, value]) => ({
                key,
                value: Number(value),
                timestamp: Date.now(),
                step
            }));

            await this.makeMLflowRequest('POST', '/api/2.0/mlflow/runs/log-batch', {
                run_id: runId,
                metrics: metricsArray
            });

            const run = this.activeRuns.get(runId);
            if (run) {
                run.metrics = { ...run.metrics, ...metrics };
            }

            console.log(`📊 记录指标: ${Object.keys(metrics).join(', ')}`);

        } catch (error) {
            console.error('Failed to log metrics:', error);
            throw error;
        }
    }

    /**
     * 记录工件
     */
    async logArtifact(runId, artifactPath, localPath) {
        try {
            const formData = new FormData();
            formData.append('path', artifactPath);
            formData.append('file', await fs.readFile(localPath), {
                filename: path.basename(localPath)
            });

            await this.makeMLflowRequest('POST', `/api/2.0/mlflow/artifacts`, formData, {
                'Content-Type': `multipart/form-data; boundary=${formData.getBoundary()}`,
                'run_id': runId
            });

            const run = this.activeRuns.get(runId);
            if (run) {
                run.artifacts.push({
                    path: artifactPath,
                    size: (await fs.stat(localPath)).size,
                    uploadedAt: new Date().toISOString()
                });
            }

            console.log(`📁 上传工件: ${artifactPath}`);

        } catch (error) {
            console.error('Failed to log artifact:', error);
            throw error;
        }
    }

    /**
     * 结束运行
     */
    async endRun(runId, status = 'FINISHED') {
        try {
            await this.makeMLflowRequest('POST', '/api/2.0/mlflow/runs/update', {
                run_id: runId,
                status,
                end_time: Date.now()
            });

            const run = this.activeRuns.get(runId);
            if (run) {
                run.status = status;
                run.endTime = Date.now();
                run.duration = run.endTime - run.startTime;
            }

            console.log(`✅ 结束运行: ${runId} (状态: ${status})`);
            return run;

        } catch (error) {
            console.error('Failed to end run:', error);
            throw error;
        }
    }

    /**
     * 注册模型
     */
    async registerModel(name, runId, modelPath, description = '', tags = {}) {
        try {
            // 创建注册模型
            const createResponse = await this.makeMLflowRequest('POST', '/api/2.0/mlflow/registered-models/create', {
                name,
                description,
                tags: Object.entries(tags).map(([key, value]) => ({ key, value }))
            });

            // 创建模型版本
            const versionResponse = await this.makeMLflowRequest('POST', '/api/2.0/mlflow/model-versions/create', {
                name,
                source: `runs:/${runId}/${modelPath}`,
                run_id: runId,
                description: `Model version from run ${runId}`
            });

            const model = {
                name,
                version: versionResponse.data.model_version.version,
                runId,
                modelPath,
                description,
                tags,
                status: 'None',
                createdAt: new Date().toISOString()
            };

            this.models.set(`${name}:${model.version}`, model);

            console.log(`🎯 注册模型: ${name} v${model.version}`);
            return model;

        } catch (error) {
            console.error('Failed to register model:', error);
            throw error;
        }
    }

    /**
     * 部署模型
     */
    async deployModel(modelName, version, deploymentName, targetUri = 'kubernetes') {
        try {
            const deployment = {
                name: deploymentName,
                modelName,
                version,
                targetUri,
                status: 'deploying',
                createdAt: new Date().toISOString()
            };

            // 根据目标环境部署模型
            switch (targetUri) {
                case 'kubernetes':
                    await this.deployToKubernetes(deployment);
                    break;
                case 'docker':
                    await this.deployToDocker(deployment);
                    break;
                default:
                    throw new Error(`Unsupported deployment target: ${targetUri}`);
            }

            deployment.status = 'deployed';
            deployment.deployedAt = new Date().toISOString();

            console.log(`🚀 部署模型: ${modelName} v${version} -> ${deploymentName}`);
            return deployment;

        } catch (error) {
            console.error('Failed to deploy model:', error);
            throw error;
        }
    }

    /**
     * 部署到 Kubernetes
     */
    async deployToKubernetes(deployment) {
        const manifest = this.generateKubernetesManifest(deployment);
        
        // 保存 manifest 文件
        const manifestPath = path.join(this.options.modelsPath, `${deployment.name}-deployment.yaml`);
        await fs.writeFile(manifestPath, manifest);

        // 应用到 Kubernetes
        const kubectl = spawn('kubectl', ['apply', '-f', manifestPath]);
        
        return new Promise((resolve, reject) => {
            kubectl.on('close', (code) => {
                if (code === 0) {
                    resolve();
                } else {
                    reject(new Error(`kubectl apply failed with code ${code}`));
                }
            });
        });
    }

    /**
     * 生成 Kubernetes 部署清单
     */
    generateKubernetesManifest(deployment) {
        return `
apiVersion: apps/v1
kind: Deployment
metadata:
  name: ${deployment.name}
  namespace: ml-platform
  labels:
    app.kubernetes.io/name: ${deployment.name}
    app.kubernetes.io/component: model-serving
    model.name: ${deployment.modelName}
    model.version: "${deployment.version}"
spec:
  replicas: 2
  selector:
    matchLabels:
      app.kubernetes.io/name: ${deployment.name}
  template:
    metadata:
      labels:
        app.kubernetes.io/name: ${deployment.name}
        app.kubernetes.io/component: model-serving
        model.name: ${deployment.modelName}
        model.version: "${deployment.version}"
    spec:
      containers:
      - name: model-server
        image: python:3.9-slim
        ports:
        - name: http
          containerPort: 8080
          protocol: TCP
        env:
        - name: MLFLOW_TRACKING_URI
          value: "${this.options.mlflowUrl}"
        - name: MODEL_NAME
          value: "${deployment.modelName}"
        - name: MODEL_VERSION
          value: "${deployment.version}"
        command:
        - /bin/bash
        - -c
        - |
          pip install mlflow[extras] flask gunicorn
          python -c "
          import mlflow
          import mlflow.pyfunc
          from flask import Flask, request, jsonify
          import os
          
          app = Flask(__name__)
          
          # 加载模型
          model_name = os.environ['MODEL_NAME']
          model_version = os.environ['MODEL_VERSION']
          model_uri = f'models:/{model_name}/{model_version}'
          model = mlflow.pyfunc.load_model(model_uri)
          
          @app.route('/predict', methods=['POST'])
          def predict():
              try:
                  data = request.get_json()
                  prediction = model.predict(data['input'])
                  return jsonify({'prediction': prediction.tolist()})
              except Exception as e:
                  return jsonify({'error': str(e)}), 500
          
          @app.route('/health', methods=['GET'])
          def health():
              return jsonify({'status': 'healthy', 'model': model_name, 'version': model_version})
          
          if __name__ == '__main__':
              app.run(host='0.0.0.0', port=8080)
          "
        resources:
          requests:
            cpu: 200m
            memory: 512Mi
          limits:
            cpu: 1000m
            memory: 1Gi
        livenessProbe:
          httpGet:
            path: /health
            port: http
          initialDelaySeconds: 60
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /health
            port: http
          initialDelaySeconds: 30
          periodSeconds: 10
---
apiVersion: v1
kind: Service
metadata:
  name: ${deployment.name}
  namespace: ml-platform
  labels:
    app.kubernetes.io/name: ${deployment.name}
    app.kubernetes.io/component: model-serving
spec:
  type: ClusterIP
  ports:
  - port: 8080
    targetPort: http
    protocol: TCP
    name: http
  selector:
    app.kubernetes.io/name: ${deployment.name}
        `.trim();
    }

    /**
     * 训练模型
     */
    async trainModel(experimentName, modelConfig) {
        try {
            // 创建或获取实验
            let experiment = Array.from(this.experiments.values()).find(e => e.name === experimentName);
            if (!experiment) {
                experiment = await this.createExperiment(experimentName, `Experiment for ${modelConfig.type} model`);
            }

            // 开始运行
            const run = await this.startRun(
                experiment.id,
                `${modelConfig.type}-${Date.now()}`,
                {
                    model_type: modelConfig.type,
                    framework: modelConfig.framework || 'sklearn',
                    environment: 'all-agent'
                },
                modelConfig.parameters || {}
            );

            // 模拟训练过程
            console.log(`🎓 开始训练 ${modelConfig.type} 模型...`);
            
            const trainingResult = await this.executeTraining(modelConfig, run.id);
            
            // 记录训练指标
            await this.logMetrics(run.id, trainingResult.metrics);
            
            // 保存模型文件
            const modelPath = await this.saveModel(trainingResult.model, run.id);
            
            // 上传模型工件
            await this.logArtifact(run.id, 'model', modelPath);
            
            // 结束运行
            await this.endRun(run.id, 'FINISHED');
            
            // 注册模型
            const registeredModel = await this.registerModel(
                `${experimentName}-${modelConfig.type}`,
                run.id,
                'model',
                `${modelConfig.type} model trained on ${new Date().toISOString()}`
            );

            console.log(`✅ 模型训练完成: ${registeredModel.name} v${registeredModel.version}`);
            return {
                run,
                model: registeredModel,
                metrics: trainingResult.metrics
            };

        } catch (error) {
            console.error('Failed to train model:', error);
            throw error;
        }
    }

    /**
     * 执行训练
     */
    async executeTraining(modelConfig, runId) {
        // 这里是模拟的训练过程
        // 在实际应用中，这里会调用真实的机器学习训练代码
        
        const trainingSteps = 10;
        const metrics = {
            accuracy: 0,
            loss: 1.0,
            f1_score: 0,
            precision: 0,
            recall: 0
        };

        for (let step = 0; step < trainingSteps; step++) {
            // 模拟训练进度
            metrics.accuracy = Math.min(0.95, 0.5 + (step / trainingSteps) * 0.45 + Math.random() * 0.1);
            metrics.loss = Math.max(0.05, 1.0 - (step / trainingSteps) * 0.95 + Math.random() * 0.1);
            metrics.f1_score = Math.min(0.92, 0.4 + (step / trainingSteps) * 0.52 + Math.random() * 0.1);
            metrics.precision = Math.min(0.94, 0.45 + (step / trainingSteps) * 0.49 + Math.random() * 0.1);
            metrics.recall = Math.min(0.91, 0.42 + (step / trainingSteps) * 0.49 + Math.random() * 0.1);

            // 记录中间指标
            await this.logMetrics(runId, {
                accuracy: metrics.accuracy,
                loss: metrics.loss,
                f1_score: metrics.f1_score,
                precision: metrics.precision,
                recall: metrics.recall
            }, step);

            // 模拟训练时间
            await new Promise(resolve => setTimeout(resolve, 1000));
        }

        return {
            model: {
                type: modelConfig.type,
                framework: modelConfig.framework || 'sklearn',
                parameters: modelConfig.parameters,
                trainedAt: new Date().toISOString()
            },
            metrics
        };
    }

    /**
     * 保存模型
     */
    async saveModel(model, runId) {
        const modelPath = path.join(this.options.modelsPath, `model-${runId}.json`);
        await fs.writeFile(modelPath, JSON.stringify(model, null, 2));
        return modelPath;
    }

    /**
     * 获取模型列表
     */
    async getModels() {
        try {
            const response = await this.makeMLflowRequest('GET', '/api/2.0/mlflow/registered-models/list');
            return response.data.registered_models || [];
        } catch (error) {
            console.error('Failed to get models:', error);
            return Array.from(this.models.values());
        }
    }

    /**
     * 获取实验列表
     */
    async getExperiments() {
        try {
            const response = await this.makeMLflowRequest('GET', '/api/2.0/mlflow/experiments/list');
            return response.data.experiments || [];
        } catch (error) {
            console.error('Failed to get experiments:', error);
            return Array.from(this.experiments.values());
        }
    }

    /**
     * 获取运行列表
     */
    async getRuns(experimentId) {
        try {
            const response = await this.makeMLflowRequest('POST', '/api/2.0/mlflow/runs/search', {
                experiment_ids: [experimentId],
                max_results: 100
            });
            return response.data.runs || [];
        } catch (error) {
            console.error('Failed to get runs:', error);
            return Array.from(this.activeRuns.values()).filter(r => r.experimentId === experimentId);
        }
    }

    /**
     * 发起 MLflow API 请求
     */
    async makeMLflowRequest(method, endpoint, data = null, headers = {}) {
        const config = {
            method,
            url: `${this.options.mlflowUrl}${endpoint}`,
            headers: {
                'Content-Type': 'application/json',
                ...headers
            }
        };

        if (this.options.authToken) {
            config.headers.Authorization = `Bearer ${this.options.authToken}`;
        }

        if (data) {
            if (data instanceof FormData) {
                config.data = data;
                delete config.headers['Content-Type'];
            } else {
                config.data = data;
            }
        }

        return await axios(config);
    }

    /**
     * 获取服务状态
     */
    getStatus() {
        return {
            mlflowUrl: this.options.mlflowUrl,
            modelsCount: this.models.size,
            experimentsCount: this.experiments.size,
            activeRunsCount: this.activeRuns.size,
            artifactRoot: this.options.artifactRoot,
            status: 'healthy'
        };
    }
}

module.exports = MLModelService;
