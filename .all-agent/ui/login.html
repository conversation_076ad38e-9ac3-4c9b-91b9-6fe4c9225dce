<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>All-Agent 登录</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .login-container {
            width: 100%;
            max-width: 400px;
            padding: 20px;
        }

        .login-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 40px;
            text-align: center;
        }

        .logo {
            font-size: 48px;
            margin-bottom: 16px;
        }

        .login-header h1 {
            color: #2d3748;
            margin-bottom: 8px;
            font-size: 28px;
        }

        .login-header p {
            color: #718096;
            margin-bottom: 32px;
        }

        .form-group {
            margin-bottom: 20px;
            text-align: left;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #4a5568;
            font-weight: 500;
        }

        .form-group input {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus {
            outline: none;
            border-color: #667eea;
        }

        .form-options {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;
        }

        .checkbox {
            display: flex;
            align-items: center;
            cursor: pointer;
        }

        .checkbox input {
            margin-right: 8px;
            width: auto;
        }

        .forgot-link {
            color: #667eea;
            text-decoration: none;
            font-size: 14px;
        }

        .login-btn {
            width: 100%;
            padding: 12px;
            background: #667eea;
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: background-color 0.3s ease;
        }

        .login-btn:hover {
            background: #5a6fd8;
        }

        .login-btn:disabled {
            background: #a0aec0;
            cursor: not-allowed;
        }

        .login-footer {
            margin-top: 24px;
            color: #718096;
        }

        .login-footer a {
            color: #667eea;
            text-decoration: none;
        }

        .error-message {
            color: #e53e3e;
            background: #fed7d7;
            padding: 12px;
            border-radius: 6px;
            margin-bottom: 16px;
            font-size: 14px;
        }

        .success-message {
            color: #38a169;
            background: #c6f6d5;
            padding: 12px;
            border-radius: 6px;
            margin-bottom: 16px;
            font-size: 14px;
        }

        .demo-info {
            background: #bee3f8;
            color: #2b6cb0;
            padding: 12px;
            border-radius: 6px;
            margin-bottom: 16px;
            font-size: 14px;
        }

        @media (max-width: 480px) {
            .login-card {
                padding: 24px;
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-card">
            <div class="logo">🤖</div>
            <div class="login-header">
                <h1>All-Agent</h1>
                <p>AI 项目构建与执行系统</p>
            </div>
            
            <div class="demo-info">
                <strong>演示账户:</strong><br>
                邮箱: <EMAIL><br>
                密码: admin123
            </div>
            
            <form class="login-form" id="loginForm">
                <div class="form-group">
                    <label for="email">邮箱地址</label>
                    <input type="email" id="email" name="email" required>
                </div>
                
                <div class="form-group">
                    <label for="password">密码</label>
                    <input type="password" id="password" name="password" required>
                </div>
                
                <div class="form-options">
                    <label class="checkbox">
                        <input type="checkbox" id="remember">
                        <span>记住我</span>
                    </label>
                    <a href="#" class="forgot-link">忘记密码？</a>
                </div>
                
                <button type="submit" class="login-btn" id="loginBtn">登录</button>
            </form>
            
            <div class="login-footer">
                <p>还没有账户？ <a href="#" onclick="showRegister()">立即注册</a></p>
            </div>
        </div>
    </div>
    
    <script>
        const API_BASE = 'http://localhost:3000';
        
        document.addEventListener('DOMContentLoaded', function() {
            const loginForm = document.getElementById('loginForm');
            const emailInput = document.getElementById('email');
            const passwordInput = document.getElementById('password');
            const loginBtn = document.getElementById('loginBtn');
            const rememberCheckbox = document.getElementById('remember');

            // 检查是否已登录
            const token = localStorage.getItem('auth_token');
            if (token) {
                verifyToken(token);
            }

            // 表单提交处理
            loginForm.addEventListener('submit', async function(e) {
                e.preventDefault();
                
                const email = emailInput.value.trim();
                const password = passwordInput.value.trim();
                const rememberMe = rememberCheckbox.checked;
                
                if (!email || !password) {
                    showError('请填写邮箱和密码');
                    return;
                }
                
                await login(email, password, rememberMe);
            });

            // 登录函数
            async function login(email, password, rememberMe) {
                setLoading(true);
                clearMessages();
                
                try {
                    const response = await fetch(`${API_BASE}/auth/login`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({ 
                            username: email, 
                            password, 
                            rememberMe 
                        })
                    });
                    
                    const data = await response.json();
                    
                    if (data.success) {
                        // 保存令牌
                        localStorage.setItem('auth_token', data.data.token);
                        localStorage.setItem('user_info', JSON.stringify(data.data.user));
                        
                        showSuccess('登录成功，正在跳转...');
                        
                        // 跳转到主界面
                        setTimeout(() => {
                            window.location.href = '/ui/chat_panel.html';
                        }, 1000);
                        
                    } else {
                        showError(data.error || '登录失败');
                    }
                    
                } catch (error) {
                    console.error('登录错误:', error);
                    showError('网络错误，请稍后重试');
                } finally {
                    setLoading(false);
                }
            }

            // 验证令牌
            async function verifyToken(token) {
                try {
                    const response = await fetch(`${API_BASE}/auth/me`, {
                        headers: {
                            'Authorization': `Bearer ${token}`
                        }
                    });
                    
                    if (response.ok) {
                        // 令牌有效，跳转到主界面
                        window.location.href = '/ui/chat_panel.html';
                    } else {
                        // 令牌无效，清除本地存储
                        localStorage.removeItem('auth_token');
                        localStorage.removeItem('user_info');
                    }
                } catch (error) {
                    console.error('令牌验证失败:', error);
                }
            }

            // 显示错误信息
            function showError(message) {
                clearMessages();
                const errorDiv = document.createElement('div');
                errorDiv.className = 'error-message';
                errorDiv.textContent = message;
                loginForm.insertBefore(errorDiv, loginForm.firstChild);
            }

            // 显示成功信息
            function showSuccess(message) {
                clearMessages();
                const successDiv = document.createElement('div');
                successDiv.className = 'success-message';
                successDiv.textContent = message;
                loginForm.insertBefore(successDiv, loginForm.firstChild);
            }

            // 清除消息
            function clearMessages() {
                const messages = loginForm.querySelectorAll('.error-message, .success-message');
                messages.forEach(msg => msg.remove());
            }

            // 设置加载状态
            function setLoading(loading) {
                loginBtn.disabled = loading;
                loginBtn.textContent = loading ? '登录中...' : '登录';
            }

            // 注册功能（简化版）
            window.showRegister = function() {
                alert('注册功能正在开发中，请使用演示账户登录');
            };
        });
    </script>
</body>
</html>
