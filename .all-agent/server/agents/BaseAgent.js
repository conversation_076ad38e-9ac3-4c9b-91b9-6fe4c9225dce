const EventEmitter = require('events');

/**
 * Agent 基类
 * 定义所有 Agent 的通用接口和行为
 */
class BaseAgent extends EventEmitter {
  constructor(config, llmService = null) {
    super();
    this.config = config;
    this.status = 'initializing';
    this.currentTasks = new Map();
    this.totalProcessed = 0;
    this.startTime = Date.now();
    this.lastActivity = null;
    this.isReady = false;
    this.llmService = llmService;
  }

  /**
   * 初始化 Agent
   */
  async initialize() {
    try {
      console.log(`🤖 初始化 ${this.config.name}...`);

      // 子类可以重写此方法进行特定初始化
      await this.onInitialize();

      this.status = 'ready';
      this.isReady = true;
      this.lastActivity = Date.now();

      this.emit('status_change', {
        status: this.status,
        message: '初始化完成'
      });

      console.log(`✅ ${this.config.name} 初始化完成`);
    } catch (error) {
      this.status = 'error';
      this.isReady = false;

      this.emit('status_change', {
        status: this.status,
        error: error.message
      });

      console.error(`❌ ${this.config.name} 初始化失败:`, error);
      throw error;
    }
  }

  /**
   * 子类重写的初始化方法
   */
  async onInitialize() {
    // 子类实现
  }

  /**
   * 处理消息
   */
  async processMessage(message, context = {}) {
    if (!this.isReady) {
      throw new Error(`${this.config.name} 未就绪`);
    }

    try {
      this.lastActivity = Date.now();

      // 子类实现具体的消息处理逻辑
      const response = await this.onProcessMessage(message, context);

      this.emit('message_processed', {
        message,
        response,
        context,
        timestamp: new Date().toISOString()
      });

      return response;
    } catch (error) {
      this.emit('message_error', {
        message,
        error: error.message,
        context,
        timestamp: new Date().toISOString()
      });

      throw error;
    }
  }

  /**
   * 子类重写的消息处理方法
   */
  async onProcessMessage(message, context) {
    throw new Error('子类必须实现 onProcessMessage 方法');
  }

  /**
   * 执行任务
   */
  async executeTask(action, input, options = {}) {
    if (!this.isReady) {
      throw new Error(`${this.config.name} 未就绪`);
    }

    if (!this.canAcceptTask()) {
      throw new Error(`${this.config.name} 任务队列已满`);
    }

    const taskId = this.generateTaskId();
    const task = {
      id: taskId,
      action,
      input,
      options,
      startTime: Date.now(),
      status: 'running'
    };

    try {
      // 添加到当前任务列表
      this.currentTasks.set(taskId, task);
      this.lastActivity = Date.now();

      this.emit('task_started', {
        taskId,
        action,
        agentName: this.config.name
      });

      // 子类实现具体的任务执行逻辑
      const result = await this.onExecuteTask(action, input, options);

      // 任务完成
      task.status = 'completed';
      task.result = result;
      task.endTime = Date.now();
      task.duration = task.endTime - task.startTime;

      this.currentTasks.delete(taskId);
      this.totalProcessed++;

      this.emit('task_completed', {
        taskId,
        action,
        result,
        duration: task.duration,
        agentName: this.config.name
      });

      return result;
    } catch (error) {
      // 任务失败
      task.status = 'failed';
      task.error = error.message;
      task.endTime = Date.now();
      task.duration = task.endTime - task.startTime;

      this.currentTasks.delete(taskId);

      this.emit('task_failed', {
        taskId,
        action,
        error: error.message,
        duration: task.duration,
        agentName: this.config.name
      });

      throw error;
    }
  }

  /**
   * 子类重写的任务执行方法
   */
  async onExecuteTask(action, input, options) {
    throw new Error('子类必须实现 onExecuteTask 方法');
  }

  /**
   * 检查是否可以接受新任务
   */
  canAcceptTask() {
    return this.isReady && this.currentTasks.size < this.config.max_concurrent_tasks;
  }

  /**
   * 获取当前任务数量
   */
  getCurrentTaskCount() {
    return this.currentTasks.size;
  }

  /**
   * 获取总处理数量
   */
  getTotalProcessed() {
    return this.totalProcessed;
  }

  /**
   * 获取最后活动时间
   */
  getLastActivity() {
    return this.lastActivity;
  }

  /**
   * 获取运行时间
   */
  getUptime() {
    return Date.now() - this.startTime;
  }

  /**
   * 获取 Agent 状态
   */
  getStatus() {
    return {
      name: this.config.name,
      status: this.status,
      isReady: this.isReady,
      currentTasks: this.currentTasks.size,
      maxTasks: this.config.max_concurrent_tasks,
      totalProcessed: this.totalProcessed,
      uptime: this.getUptime(),
      lastActivity: this.lastActivity,
      capabilities: this.config.capabilities
    };
  }

  /**
   * 生成任务 ID
   */
  generateTaskId() {
    return `${this.config.name.toLowerCase()}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 停止 Agent
   */
  async stop() {
    console.log(`🛑 正在停止 ${this.config.name}...`);

    this.status = 'stopping';
    this.isReady = false;

    // 等待当前任务完成
    while (this.currentTasks.size > 0) {
      await new Promise((resolve) => setTimeout(resolve, 100));
    }

    // 子类可以重写此方法进行清理
    await this.onStop();

    this.status = 'stopped';

    this.emit('status_change', {
      status: this.status,
      message: '已停止'
    });

    console.log(`✅ ${this.config.name} 已停止`);
  }

  /**
   * 子类重写的停止方法
   */
  async onStop() {
    // 子类实现
  }

  /**
   * 检查 Agent 是否就绪
   */
  isAgentReady() {
    return this.isReady;
  }

  /**
   * 获取能力列表
   */
  getCapabilities() {
    return this.config.capabilities || [];
  }

  /**
   * 检查是否支持某个能力
   */
  hasCapability(capability) {
    return this.getCapabilities().includes(capability);
  }

  /**
   * 记录日志
   */
  log(level, message, data = {}) {
    const logEntry = {
      timestamp: new Date().toISOString(),
      agent: this.config.name,
      level,
      message,
      data
    };

    this.emit('log', logEntry);

    // 简单的控制台输出
    const prefix = `[${this.config.name}]`;
    switch (level) {
      case 'error':
        console.error(prefix, message, data);
        break;
      case 'warn':
        console.warn(prefix, message, data);
        break;
      case 'info':
        console.log(prefix, message, data);
        break;
      case 'debug':
        console.debug(prefix, message, data);
        break;
    }
  }

  /**
   * 健康检查
   */
  healthCheck() {
    const now = Date.now();
    const timeSinceLastActivity = this.lastActivity ? now - this.lastActivity : 0;

    return {
      healthy: this.isReady && this.status === 'ready',
      status: this.status,
      uptime: this.getUptime(),
      timeSinceLastActivity,
      currentLoad: this.currentTasks.size / this.config.max_concurrent_tasks,
      totalProcessed: this.totalProcessed
    };
  }
}

module.exports = BaseAgent;
