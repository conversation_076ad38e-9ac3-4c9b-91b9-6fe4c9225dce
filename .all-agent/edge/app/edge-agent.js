const express = require('express');
const axios = require('axios');
const fs = require('fs').promises;
const path = require('path');
const crypto = require('crypto');
const EventEmitter = require('events');

/**
 * All-Agent 边缘计算节点
 * 支持离线运行、数据同步、本地缓存等功能
 */
class EdgeAgent extends EventEmitter {
    constructor(options = {}) {
        super();
        
        this.options = {
            port: options.port || process.env.PORT || 3000,
            edgeZone: options.edgeZone || process.env.EDGE_ZONE || 'default',
            centralHubUrl: options.centralHubUrl || process.env.CENTRAL_HUB_URL || 'https://central.all-agent.com',
            syncToken: options.syncToken || process.env.SYNC_TOKEN,
            syncInterval: options.syncInterval || parseInt(process.env.SYNC_INTERVAL) || 30000,
            offlineMode: options.offlineMode || process.env.OFFLINE_MODE === 'true',
            dataPath: options.dataPath || '/app/data',
            cachePath: options.cachePath || '/app/cache',
            maxMemoryUsage: options.maxMemoryUsage || parseInt(process.env.MAX_MEMORY_USAGE) || 512,
            maxCpuUsage: options.maxCpuUsage || parseInt(process.env.MAX_CPU_USAGE) || 80,
            dataRetentionDays: options.dataRetentionDays || parseInt(process.env.DATA_RETENTION_DAYS) || 7,
            ...options
        };

        this.app = express();
        this.isOnline = false;
        this.lastSyncTime = null;
        this.localCache = new Map();
        this.syncQueue = [];
        this.metrics = {
            requestCount: 0,
            errorCount: 0,
            syncCount: 0,
            cacheHits: 0,
            cacheMisses: 0
        };

        this.setupMiddleware();
        this.setupRoutes();
        this.startSyncProcess();
        this.startHealthMonitoring();
    }

    /**
     * 设置中间件
     */
    setupMiddleware() {
        this.app.use(express.json({ limit: '10mb' }));
        this.app.use(express.urlencoded({ extended: true }));

        // 请求计数中间件
        this.app.use((req, res, next) => {
            this.metrics.requestCount++;
            req.startTime = Date.now();
            
            res.on('finish', () => {
                const duration = Date.now() - req.startTime;
                this.emit('request', {
                    method: req.method,
                    path: req.path,
                    statusCode: res.statusCode,
                    duration,
                    userAgent: req.get('user-agent')
                });
            });
            
            next();
        });

        // 错误处理中间件
        this.app.use((error, req, res, next) => {
            this.metrics.errorCount++;
            console.error('Edge Agent Error:', error);
            
            res.status(500).json({
                error: 'Internal Server Error',
                message: this.options.offlineMode ? 'Edge node error' : error.message,
                edgeZone: this.options.edgeZone,
                timestamp: new Date().toISOString()
            });
        });
    }

    /**
     * 设置路由
     */
    setupRoutes() {
        // 健康检查
        this.app.get('/health', (req, res) => {
            res.json({
                status: 'healthy',
                edgeZone: this.options.edgeZone,
                isOnline: this.isOnline,
                lastSyncTime: this.lastSyncTime,
                uptime: process.uptime(),
                memory: process.memoryUsage(),
                metrics: this.metrics,
                timestamp: new Date().toISOString()
            });
        });

        // 就绪检查
        this.app.get('/ready', (req, res) => {
            const memoryUsage = process.memoryUsage().heapUsed / 1024 / 1024;
            const isReady = memoryUsage < this.options.maxMemoryUsage;
            
            res.status(isReady ? 200 : 503).json({
                ready: isReady,
                memoryUsage: `${memoryUsage.toFixed(2)}MB`,
                maxMemory: `${this.options.maxMemoryUsage}MB`,
                edgeZone: this.options.edgeZone
            });
        });

        // 指标端点
        this.app.get('/metrics', (req, res) => {
            const memoryUsage = process.memoryUsage();
            const cpuUsage = process.cpuUsage();
            
            res.set('Content-Type', 'text/plain');
            res.send(`
# HELP edge_requests_total Total number of requests
# TYPE edge_requests_total counter
edge_requests_total{zone="${this.options.edgeZone}"} ${this.metrics.requestCount}

# HELP edge_errors_total Total number of errors
# TYPE edge_errors_total counter
edge_errors_total{zone="${this.options.edgeZone}"} ${this.metrics.errorCount}

# HELP edge_sync_total Total number of syncs
# TYPE edge_sync_total counter
edge_sync_total{zone="${this.options.edgeZone}"} ${this.metrics.syncCount}

# HELP edge_cache_hits_total Total number of cache hits
# TYPE edge_cache_hits_total counter
edge_cache_hits_total{zone="${this.options.edgeZone}"} ${this.metrics.cacheHits}

# HELP edge_cache_misses_total Total number of cache misses
# TYPE edge_cache_misses_total counter
edge_cache_misses_total{zone="${this.options.edgeZone}"} ${this.metrics.cacheMisses}

# HELP edge_memory_usage_bytes Memory usage in bytes
# TYPE edge_memory_usage_bytes gauge
edge_memory_usage_bytes{zone="${this.options.edgeZone}",type="rss"} ${memoryUsage.rss}
edge_memory_usage_bytes{zone="${this.options.edgeZone}",type="heapUsed"} ${memoryUsage.heapUsed}
edge_memory_usage_bytes{zone="${this.options.edgeZone}",type="heapTotal"} ${memoryUsage.heapTotal}

# HELP edge_online_status Edge node online status
# TYPE edge_online_status gauge
edge_online_status{zone="${this.options.edgeZone}"} ${this.isOnline ? 1 : 0}
            `.trim());
        });

        // 本地 AI 处理
        this.app.post('/api/process', async (req, res) => {
            try {
                const { task, data, options = {} } = req.body;
                
                // 检查本地缓存
                const cacheKey = this.generateCacheKey(task, data);
                const cachedResult = await this.getFromCache(cacheKey);
                
                if (cachedResult) {
                    this.metrics.cacheHits++;
                    return res.json({
                        success: true,
                        data: cachedResult,
                        cached: true,
                        edgeZone: this.options.edgeZone,
                        timestamp: new Date().toISOString()
                    });
                }

                this.metrics.cacheMisses++;
                
                // 本地处理
                const result = await this.processLocally(task, data, options);
                
                // 缓存结果
                await this.saveToCache(cacheKey, result);
                
                // 如果在线，添加到同步队列
                if (this.isOnline) {
                    this.syncQueue.push({
                        type: 'process',
                        task,
                        data,
                        result,
                        timestamp: new Date().toISOString()
                    });
                }

                res.json({
                    success: true,
                    data: result,
                    cached: false,
                    edgeZone: this.options.edgeZone,
                    timestamp: new Date().toISOString()
                });

            } catch (error) {
                console.error('Processing error:', error);
                res.status(500).json({
                    success: false,
                    error: error.message,
                    edgeZone: this.options.edgeZone
                });
            }
        });

        // 数据同步端点
        this.app.post('/api/sync', async (req, res) => {
            try {
                await this.performSync();
                res.json({
                    success: true,
                    message: 'Sync completed',
                    lastSyncTime: this.lastSyncTime
                });
            } catch (error) {
                res.status(500).json({
                    success: false,
                    error: error.message
                });
            }
        });

        // 缓存管理
        this.app.get('/api/cache/stats', (req, res) => {
            res.json({
                size: this.localCache.size,
                hits: this.metrics.cacheHits,
                misses: this.metrics.cacheMisses,
                hitRate: this.metrics.cacheHits / (this.metrics.cacheHits + this.metrics.cacheMisses) || 0
            });
        });

        this.app.delete('/api/cache', async (req, res) => {
            try {
                await this.clearCache();
                res.json({
                    success: true,
                    message: 'Cache cleared'
                });
            } catch (error) {
                res.status(500).json({
                    success: false,
                    error: error.message
                });
            }
        });
    }

    /**
     * 本地处理任务
     */
    async processLocally(task, data, options) {
        switch (task) {
            case 'analyze_text':
                return this.analyzeText(data.text, options);
            
            case 'process_data':
                return this.processData(data, options);
            
            case 'generate_summary':
                return this.generateSummary(data.content, options);
            
            default:
                throw new Error(`Unknown task: ${task}`);
        }
    }

    /**
     * 文本分析
     */
    async analyzeText(text, options) {
        // 简单的本地文本分析
        const words = text.split(/\s+/).filter(word => word.length > 0);
        const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0);
        const paragraphs = text.split(/\n\s*\n/).filter(p => p.trim().length > 0);
        
        return {
            wordCount: words.length,
            sentenceCount: sentences.length,
            paragraphCount: paragraphs.length,
            averageWordsPerSentence: words.length / sentences.length || 0,
            readingTime: Math.ceil(words.length / 200), // 假设每分钟200词
            complexity: this.calculateTextComplexity(words),
            keywords: this.extractKeywords(words),
            processedAt: new Date().toISOString(),
            edgeZone: this.options.edgeZone
        };
    }

    /**
     * 数据处理
     */
    async processData(data, options) {
        // 简单的数据处理和统计
        if (Array.isArray(data)) {
            const numbers = data.filter(item => typeof item === 'number');
            
            return {
                count: data.length,
                numberCount: numbers.length,
                sum: numbers.reduce((a, b) => a + b, 0),
                average: numbers.length > 0 ? numbers.reduce((a, b) => a + b, 0) / numbers.length : 0,
                min: numbers.length > 0 ? Math.min(...numbers) : null,
                max: numbers.length > 0 ? Math.max(...numbers) : null,
                processedAt: new Date().toISOString(),
                edgeZone: this.options.edgeZone
            };
        }
        
        return {
            type: typeof data,
            size: JSON.stringify(data).length,
            processedAt: new Date().toISOString(),
            edgeZone: this.options.edgeZone
        };
    }

    /**
     * 生成摘要
     */
    async generateSummary(content, options) {
        // 简单的摘要生成
        const sentences = content.split(/[.!?]+/).filter(s => s.trim().length > 0);
        const maxSentences = options.maxSentences || 3;
        
        // 选择最长的句子作为摘要
        const sortedSentences = sentences
            .map(s => s.trim())
            .sort((a, b) => b.length - a.length)
            .slice(0, maxSentences);
        
        return {
            summary: sortedSentences.join('. ') + '.',
            originalLength: content.length,
            summaryLength: sortedSentences.join('. ').length + 1,
            compressionRatio: (sortedSentences.join('. ').length + 1) / content.length,
            processedAt: new Date().toISOString(),
            edgeZone: this.options.edgeZone
        };
    }

    /**
     * 计算文本复杂度
     */
    calculateTextComplexity(words) {
        const longWords = words.filter(word => word.length > 6).length;
        return longWords / words.length || 0;
    }

    /**
     * 提取关键词
     */
    extractKeywords(words) {
        const stopWords = new Set(['the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by']);
        const wordFreq = {};
        
        words
            .map(word => word.toLowerCase().replace(/[^\w]/g, ''))
            .filter(word => word.length > 3 && !stopWords.has(word))
            .forEach(word => {
                wordFreq[word] = (wordFreq[word] || 0) + 1;
            });
        
        return Object.entries(wordFreq)
            .sort(([,a], [,b]) => b - a)
            .slice(0, 5)
            .map(([word]) => word);
    }

    /**
     * 生成缓存键
     */
    generateCacheKey(task, data) {
        const hash = crypto.createHash('md5');
        hash.update(JSON.stringify({ task, data }));
        return hash.digest('hex');
    }

    /**
     * 从缓存获取数据
     */
    async getFromCache(key) {
        if (this.localCache.has(key)) {
            const cached = this.localCache.get(key);
            if (Date.now() - cached.timestamp < 300000) { // 5分钟缓存
                return cached.data;
            } else {
                this.localCache.delete(key);
            }
        }
        
        // 尝试从文件缓存读取
        try {
            const cachePath = path.join(this.options.cachePath, `${key}.json`);
            const cacheData = await fs.readFile(cachePath, 'utf8');
            const parsed = JSON.parse(cacheData);
            
            if (Date.now() - parsed.timestamp < 300000) {
                this.localCache.set(key, parsed);
                return parsed.data;
            }
        } catch (error) {
            // 缓存文件不存在或损坏
        }
        
        return null;
    }

    /**
     * 保存到缓存
     */
    async saveToCache(key, data) {
        const cacheEntry = {
            data,
            timestamp: Date.now()
        };
        
        // 内存缓存
        this.localCache.set(key, cacheEntry);
        
        // 文件缓存
        try {
            await fs.mkdir(this.options.cachePath, { recursive: true });
            const cachePath = path.join(this.options.cachePath, `${key}.json`);
            await fs.writeFile(cachePath, JSON.stringify(cacheEntry));
        } catch (error) {
            console.error('Failed to save cache to file:', error);
        }
    }

    /**
     * 清理缓存
     */
    async clearCache() {
        this.localCache.clear();
        
        try {
            const files = await fs.readdir(this.options.cachePath);
            await Promise.all(
                files
                    .filter(file => file.endsWith('.json'))
                    .map(file => fs.unlink(path.join(this.options.cachePath, file)))
            );
        } catch (error) {
            console.error('Failed to clear file cache:', error);
        }
    }

    /**
     * 启动同步进程
     */
    startSyncProcess() {
        setInterval(async () => {
            try {
                await this.performSync();
            } catch (error) {
                console.error('Sync error:', error);
            }
        }, this.options.syncInterval);
    }

    /**
     * 执行同步
     */
    async performSync() {
        if (this.options.offlineMode) {
            return;
        }

        try {
            // 检查中央节点连接
            await this.checkCentralHub();
            
            if (this.isOnline) {
                // 上传本地数据
                await this.uploadLocalData();
                
                // 下载中央数据
                await this.downloadCentralData();
                
                this.lastSyncTime = new Date().toISOString();
                this.metrics.syncCount++;
                
                this.emit('sync', {
                    success: true,
                    timestamp: this.lastSyncTime
                });
            }
            
        } catch (error) {
            this.isOnline = false;
            console.error('Sync failed:', error);
            
            this.emit('sync', {
                success: false,
                error: error.message,
                timestamp: new Date().toISOString()
            });
        }
    }

    /**
     * 检查中央节点连接
     */
    async checkCentralHub() {
        try {
            const response = await axios.get(`${this.options.centralHubUrl}/health`, {
                timeout: 5000,
                headers: {
                    'Authorization': `Bearer ${this.options.syncToken}`,
                    'X-Edge-Zone': this.options.edgeZone
                }
            });
            
            this.isOnline = response.status === 200;
            return this.isOnline;
            
        } catch (error) {
            this.isOnline = false;
            throw error;
        }
    }

    /**
     * 上传本地数据
     */
    async uploadLocalData() {
        if (this.syncQueue.length === 0) {
            return;
        }

        const data = {
            edgeZone: this.options.edgeZone,
            timestamp: new Date().toISOString(),
            data: this.syncQueue.splice(0, 100) // 批量上传，最多100条
        };

        await axios.post(`${this.options.centralHubUrl}/api/edge/sync/upload`, data, {
            headers: {
                'Authorization': `Bearer ${this.options.syncToken}`,
                'X-Edge-Zone': this.options.edgeZone,
                'Content-Type': 'application/json'
            },
            timeout: 30000
        });
    }

    /**
     * 下载中央数据
     */
    async downloadCentralData() {
        const response = await axios.get(`${this.options.centralHubUrl}/api/edge/sync/download`, {
            headers: {
                'Authorization': `Bearer ${this.options.syncToken}`,
                'X-Edge-Zone': this.options.edgeZone
            },
            timeout: 30000
        });

        if (response.data && response.data.data) {
            // 处理下载的数据
            await this.processCentralData(response.data.data);
        }
    }

    /**
     * 处理中央数据
     */
    async processCentralData(data) {
        // 保存到本地文件
        try {
            await fs.mkdir(this.options.dataPath, { recursive: true });
            const dataPath = path.join(this.options.dataPath, 'central_data.json');
            await fs.writeFile(dataPath, JSON.stringify(data, null, 2));
        } catch (error) {
            console.error('Failed to save central data:', error);
        }
    }

    /**
     * 启动健康监控
     */
    startHealthMonitoring() {
        setInterval(() => {
            const memoryUsage = process.memoryUsage().heapUsed / 1024 / 1024;
            
            if (memoryUsage > this.options.maxMemoryUsage) {
                console.warn(`High memory usage: ${memoryUsage.toFixed(2)}MB`);
                this.emit('warning', {
                    type: 'high_memory',
                    value: memoryUsage,
                    threshold: this.options.maxMemoryUsage
                });
            }
            
            // 清理过期缓存
            this.cleanupExpiredCache();
            
        }, 60000); // 每分钟检查一次
    }

    /**
     * 清理过期缓存
     */
    cleanupExpiredCache() {
        const now = Date.now();
        for (const [key, value] of this.localCache.entries()) {
            if (now - value.timestamp > 300000) { // 5分钟过期
                this.localCache.delete(key);
            }
        }
    }

    /**
     * 启动边缘节点
     */
    async start() {
        try {
            // 确保数据目录存在
            await fs.mkdir(this.options.dataPath, { recursive: true });
            await fs.mkdir(this.options.cachePath, { recursive: true });
            
            // 启动 HTTP 服务器
            this.server = this.app.listen(this.options.port, () => {
                console.log(`🌐 Edge Agent started on port ${this.options.port}`);
                console.log(`📍 Edge Zone: ${this.options.edgeZone}`);
                console.log(`🔗 Central Hub: ${this.options.centralHubUrl}`);
                console.log(`📴 Offline Mode: ${this.options.offlineMode}`);
            });

            // 初始同步
            if (!this.options.offlineMode) {
                setTimeout(() => this.performSync(), 5000);
            }

        } catch (error) {
            console.error('Failed to start Edge Agent:', error);
            process.exit(1);
        }
    }

    /**
     * 停止边缘节点
     */
    async stop() {
        console.log('🛑 Stopping Edge Agent...');
        
        if (this.server) {
            this.server.close();
        }
        
        // 最后一次同步
        if (!this.options.offlineMode && this.isOnline) {
            try {
                await this.performSync();
            } catch (error) {
                console.error('Final sync failed:', error);
            }
        }
        
        console.log('✅ Edge Agent stopped');
    }
}

// 如果直接运行此文件，启动边缘节点
if (require.main === module) {
    const edgeAgent = new EdgeAgent();
    edgeAgent.start();

    // 优雅关闭
    process.on('SIGTERM', () => edgeAgent.stop());
    process.on('SIGINT', () => edgeAgent.stop());
}

module.exports = EdgeAgent;
