const redis = require('redis');
const crypto = require('crypto');

/**
 * Redis 缓存管理器
 * 提供分布式缓存支持，支持集群和高可用
 */
class RedisCache {
    constructor(options = {}) {
        this.options = {
            host: options.host || process.env.REDIS_HOST || 'localhost',
            port: options.port || process.env.REDIS_PORT || 6379,
            password: options.password || process.env.REDIS_PASSWORD,
            db: options.db || process.env.REDIS_DB || 0,
            keyPrefix: options.keyPrefix || 'all-agent:',
            defaultTTL: options.defaultTTL || 3600,
            maxRetries: options.maxRetries || 3,
            retryDelay: options.retryDelay || 1000,
            ...options
        };

        this.client = null;
        this.isConnected = false;
        this.cacheStats = {
            hits: 0,
            misses: 0,
            sets: 0,
            deletes: 0,
            errors: 0
        };

        // 内存缓存作为 Redis 的备份
        this.memoryCache = new Map();
        this.maxMemoryItems = options.maxMemoryItems || 1000;
    }

    /**
     * 连接到 Redis
     */
    async connect() {
        try {
            // 创建 Redis 客户端
            this.client = redis.createClient({
                socket: {
                    host: this.options.host,
                    port: this.options.port
                },
                password: this.options.password,
                database: this.options.db
            });

            // 错误处理
            this.client.on('error', (error) => {
                console.error('Redis 连接错误:', error);
                this.cacheStats.errors++;
                this.isConnected = false;
            });

            this.client.on('connect', () => {
                console.log('✅ Redis 连接成功');
                this.isConnected = true;
            });

            this.client.on('disconnect', () => {
                console.warn('⚠️ Redis 连接断开');
                this.isConnected = false;
            });

            // 连接到 Redis
            await this.client.connect();
            
            // 测试连接
            await this.client.ping();
            
            console.log(`✅ Redis 缓存初始化完成 (${this.options.host}:${this.options.port})`);
            
        } catch (error) {
            console.warn('⚠️ Redis 连接失败，将使用内存缓存:', error.message);
            this.isConnected = false;
        }
    }

    /**
     * 断开 Redis 连接
     */
    async disconnect() {
        if (this.client && this.isConnected) {
            await this.client.quit();
            this.isConnected = false;
            console.log('✅ Redis 连接已关闭');
        }
    }

    /**
     * 生成缓存键
     */
    generateKey(key) {
        const keyString = typeof key === 'string' ? key : JSON.stringify(key);
        const hash = crypto.createHash('md5').update(keyString).digest('hex');
        return `${this.options.keyPrefix}${hash}`;
    }

    /**
     * 设置缓存
     */
    async set(key, value, ttl = this.options.defaultTTL) {
        const cacheKey = this.generateKey(key);
        const serializedValue = JSON.stringify({
            data: value,
            timestamp: Date.now(),
            ttl: ttl
        });

        try {
            // 尝试使用 Redis
            if (this.isConnected) {
                await this.client.setEx(cacheKey, ttl, serializedValue);
            } else {
                // 回退到内存缓存
                this.setMemoryCache(cacheKey, serializedValue, ttl);
            }

            this.cacheStats.sets++;
            return true;

        } catch (error) {
            console.warn('缓存设置失败:', error.message);
            this.cacheStats.errors++;
            
            // 回退到内存缓存
            this.setMemoryCache(cacheKey, serializedValue, ttl);
            return false;
        }
    }

    /**
     * 获取缓存
     */
    async get(key) {
        const cacheKey = this.generateKey(key);

        try {
            let serializedValue = null;

            // 尝试从 Redis 获取
            if (this.isConnected) {
                serializedValue = await this.client.get(cacheKey);
            }

            // 如果 Redis 没有，尝试内存缓存
            if (!serializedValue) {
                serializedValue = this.getMemoryCache(cacheKey);
            }

            if (serializedValue) {
                const cacheData = JSON.parse(serializedValue);
                
                // 检查是否过期
                const now = Date.now();
                const age = (now - cacheData.timestamp) / 1000;
                
                if (age <= cacheData.ttl) {
                    this.cacheStats.hits++;
                    return cacheData.data;
                } else {
                    // 过期，删除缓存
                    await this.delete(key);
                }
            }

            this.cacheStats.misses++;
            return null;

        } catch (error) {
            console.warn('缓存获取失败:', error.message);
            this.cacheStats.errors++;
            this.cacheStats.misses++;
            return null;
        }
    }

    /**
     * 删除缓存
     */
    async delete(key) {
        const cacheKey = this.generateKey(key);

        try {
            let deleted = false;

            // 从 Redis 删除
            if (this.isConnected) {
                const result = await this.client.del(cacheKey);
                deleted = result > 0;
            }

            // 从内存缓存删除
            const memoryDeleted = this.memoryCache.delete(cacheKey);
            deleted = deleted || memoryDeleted;

            if (deleted) {
                this.cacheStats.deletes++;
            }

            return deleted;

        } catch (error) {
            console.warn('缓存删除失败:', error.message);
            this.cacheStats.errors++;
            return false;
        }
    }

    /**
     * 检查缓存是否存在
     */
    async has(key) {
        const value = await this.get(key);
        return value !== null;
    }

    /**
     * 清空所有缓存
     */
    async clear() {
        try {
            // 清空 Redis 缓存
            if (this.isConnected) {
                const keys = await this.client.keys(`${this.options.keyPrefix}*`);
                if (keys.length > 0) {
                    await this.client.del(keys);
                }
            }

            // 清空内存缓存
            this.memoryCache.clear();

            // 重置统计
            this.cacheStats = {
                hits: 0,
                misses: 0,
                sets: 0,
                deletes: 0,
                errors: 0
            };

            return true;

        } catch (error) {
            console.warn('清空缓存失败:', error.message);
            this.cacheStats.errors++;
            return false;
        }
    }

    /**
     * 获取或设置缓存
     */
    async getOrSet(key, fn, ttl = this.options.defaultTTL) {
        let value = await this.get(key);
        
        if (value === null) {
            value = await fn();
            if (value !== null && value !== undefined) {
                await this.set(key, value, ttl);
            }
        }
        
        return value;
    }

    /**
     * 批量获取
     */
    async mget(keys) {
        const results = {};
        
        // 并行获取所有键
        const promises = keys.map(async (key) => {
            const value = await this.get(key);
            return { key, value };
        });

        const responses = await Promise.all(promises);
        
        for (const { key, value } of responses) {
            results[key] = value;
        }
        
        return results;
    }

    /**
     * 批量设置
     */
    async mset(items, ttl = this.options.defaultTTL) {
        const promises = [];
        
        for (const [key, value] of Object.entries(items)) {
            promises.push(this.set(key, value, ttl));
        }
        
        const results = await Promise.all(promises);
        return results.every(result => result);
    }

    /**
     * 设置内存缓存
     */
    setMemoryCache(key, value, ttl) {
        // 检查内存缓存大小
        if (this.memoryCache.size >= this.maxMemoryItems) {
            this.evictLRU();
        }

        this.memoryCache.set(key, {
            value,
            timestamp: Date.now(),
            ttl: ttl * 1000 // 转换为毫秒
        });
    }

    /**
     * 获取内存缓存
     */
    getMemoryCache(key) {
        const item = this.memoryCache.get(key);
        
        if (item) {
            const now = Date.now();
            if (now - item.timestamp <= item.ttl) {
                return item.value;
            } else {
                this.memoryCache.delete(key);
            }
        }
        
        return null;
    }

    /**
     * LRU 淘汰策略
     */
    evictLRU() {
        let oldestKey = null;
        let oldestTime = Date.now();
        
        for (const [key, item] of this.memoryCache.entries()) {
            if (item.timestamp < oldestTime) {
                oldestTime = item.timestamp;
                oldestKey = key;
            }
        }
        
        if (oldestKey) {
            this.memoryCache.delete(oldestKey);
        }
    }

    /**
     * 获取缓存统计
     */
    getStats() {
        const hitRate = this.cacheStats.hits + this.cacheStats.misses > 0 
            ? (this.cacheStats.hits / (this.cacheStats.hits + this.cacheStats.misses) * 100).toFixed(2)
            : 0;

        return {
            ...this.cacheStats,
            hitRate: `${hitRate}%`,
            isConnected: this.isConnected,
            memoryItems: this.memoryCache.size,
            redisInfo: this.isConnected ? {
                host: this.options.host,
                port: this.options.port,
                db: this.options.db
            } : null
        };
    }

    /**
     * 健康检查
     */
    async healthCheck() {
        try {
            if (this.isConnected) {
                await this.client.ping();
                return {
                    healthy: true,
                    type: 'redis',
                    message: 'Redis 连接正常'
                };
            } else {
                return {
                    healthy: true,
                    type: 'memory',
                    message: '使用内存缓存'
                };
            }
        } catch (error) {
            return {
                healthy: false,
                type: 'error',
                message: `缓存检查失败: ${error.message}`
            };
        }
    }

    /**
     * 获取 Redis 信息
     */
    async getRedisInfo() {
        if (!this.isConnected) {
            return null;
        }

        try {
            const info = await this.client.info();
            return this.parseRedisInfo(info);
        } catch (error) {
            console.warn('获取 Redis 信息失败:', error.message);
            return null;
        }
    }

    /**
     * 解析 Redis 信息
     */
    parseRedisInfo(infoString) {
        const info = {};
        const lines = infoString.split('\r\n');
        
        for (const line of lines) {
            if (line && !line.startsWith('#')) {
                const [key, value] = line.split(':');
                if (key && value) {
                    info[key] = value;
                }
            }
        }
        
        return {
            version: info.redis_version,
            mode: info.redis_mode,
            uptime: parseInt(info.uptime_in_seconds),
            connectedClients: parseInt(info.connected_clients),
            usedMemory: parseInt(info.used_memory),
            usedMemoryHuman: info.used_memory_human,
            totalCommandsProcessed: parseInt(info.total_commands_processed)
        };
    }
}

module.exports = RedisCache;
