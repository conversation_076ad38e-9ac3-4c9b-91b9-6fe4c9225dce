{"version": 3, "file": "automationService.js", "sourceRoot": "", "sources": ["../../src/services/automationService.ts"], "names": [], "mappings": ";;;AAAA,iCAAiC;AAKjC;;GAEG;AACH,MAAa,iBAAiB;IAO1B,YACI,eAAgC,EAChC,cAA8B,EAC9B,cAA8B;QAN1B,iBAAY,GAAG,KAAK,CAAC;QAQzB,IAAI,CAAC,eAAe,GAAG,eAAe,CAAC;QACvC,IAAI,CAAC,cAAc,GAAG,cAAc,CAAC;QACrC,IAAI,CAAC,cAAc,GAAG,cAAc,CAAC;IACzC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,cAAc;QAUhB,MAAM,KAAK,GAKN,EAAE,CAAC;QAER,MAAM,OAAO,GAAG,IAAI,CAAC,cAAc,CAAC,UAAU,EAAE,CAAC;QAEjD,eAAe;QACf,MAAM,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;QAE/C,eAAe;QACf,MAAM,IAAI,CAAC,sBAAsB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;QAElD,eAAe;QACf,MAAM,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;QAE5C,eAAe;QACf,MAAM,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;QAEzC,MAAM,YAAY,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,SAAS,CAAC,CAAC,MAAM,CAAC;QACtE,MAAM,WAAW,GAAG,KAAK,CAAC,MAAM,CAAC;QAEjC,OAAO;YACH,OAAO,EAAE,YAAY,GAAG,CAAC;YACzB,KAAK;YACL,OAAO,EAAE,SAAS,YAAY,IAAI,WAAW,uBAAuB;SACvE,CAAC;IACN,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,mBAAmB,CAAC,KAAY,EAAE,OAAY;QACxD,MAAM,eAAe,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC,CAAC;QAC/D,IAAI,CAAC,eAAe;YAAE,OAAO;QAE7B,IAAI;YACA,uBAAuB;YACvB,MAAM,iBAAiB,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,IAAI,CACpD,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,eAAe,CAAC,GAAG,EAAE,cAAc,CAAC,CAC3D,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,IAAI,EAAE,GAAG,EAAE,CAAC,KAAK,CAAC,CAAC;YAEhC,IAAI,CAAC,iBAAiB,IAAI,OAAO,CAAC,OAAO,EAAE,IAAI,KAAK,MAAM,EAAE;gBACxD,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,cAAc,CAAC,aAAa,EAAE;oBACpE,GAAG,EAAE,eAAe,CAAC,GAAG,CAAC,MAAM;oBAC/B,OAAO,EAAE,MAAM;iBAClB,CAAC,CAAC;gBAEH,KAAK,CAAC,IAAI,CAAC;oBACP,KAAK,EAAE,gCAAgC;oBACvC,MAAM,EAAE,iBAAiB;oBACzB,MAAM,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,QAAQ;oBAC7C,OAAO,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,qCAAqC,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK;iBACjF,CAAC,CAAC;aACN;YAED,yCAAyC;YACzC,MAAM,iBAAiB,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,IAAI,CACpD,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,eAAe,CAAC,GAAG,EAAE,mBAAmB,CAAC,CAChE,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,IAAI,EAAE,GAAG,EAAE,CAAC,KAAK,CAAC,CAAC;YAEhC,IAAI,iBAAiB,IAAI,iBAAiB,EAAE;gBACxC,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,cAAc,CAAC,QAAQ,EAAE;oBAC/D,GAAG,EAAE,eAAe,CAAC,GAAG,CAAC,MAAM;oBAC/B,OAAO,EAAE,MAAM;iBAClB,CAAC,CAAC;gBAEH,IAAI,MAAM,CAAC,OAAO,EAAE;oBAChB,KAAK,CAAC,IAAI,CAAC;wBACP,KAAK,EAAE,0BAA0B;wBACjC,MAAM,EAAE,YAAY;wBACpB,MAAM,EAAE,SAAS;wBACjB,OAAO,EAAE,2BAA2B;qBACvC,CAAC,CAAC;iBACN;aACJ;SACJ;QAAC,OAAO,KAAK,EAAE;YACZ,KAAK,CAAC,IAAI,CAAC;gBACP,KAAK,EAAE,yBAAyB;gBAChC,MAAM,EAAE,8BAA8B;gBACtC,MAAM,EAAE,QAAQ;gBAChB,OAAO,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;aAClE,CAAC,CAAC;SACN;IACL,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,sBAAsB,CAAC,KAAY,EAAE,OAAY;QAC3D,MAAM,eAAe,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC,CAAC;QAC/D,IAAI,CAAC,eAAe;YAAE,OAAO;QAE7B,IAAI;YACA,mBAAmB;YACnB,IAAI,OAAO,CAAC,OAAO,EAAE,YAAY,EAAE,QAAQ,CAAC,YAAY,CAAC,EAAE;gBACvD,MAAM,cAAc,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,IAAI,CACjD,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,eAAe,CAAC,GAAG,EAAE,eAAe,CAAC,CAC5D,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,IAAI,EAAE,GAAG,EAAE,CAAC,KAAK,CAAC,CAAC;gBAEhC,IAAI,CAAC,cAAc,EAAE;oBACjB,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,cAAc,CAAC,gBAAgB,EAAE;wBACvE,GAAG,EAAE,eAAe,CAAC,GAAG,CAAC,MAAM;wBAC/B,OAAO,EAAE,KAAK;qBACjB,CAAC,CAAC;oBAEH,KAAK,CAAC,IAAI,CAAC;wBACP,KAAK,EAAE,kCAAkC;wBACzC,MAAM,EAAE,wBAAwB;wBAChC,MAAM,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,QAAQ;wBAC7C,OAAO,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,2BAA2B,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK;qBACvE,CAAC,CAAC;iBACN;aACJ;YAED,eAAe;YACf,MAAM,YAAY,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,IAAI,CAC/C,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,eAAe,CAAC,GAAG,EAAE,cAAc,CAAC,CAC3D,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,IAAI,EAAE,GAAG,EAAE,CAAC,KAAK,CAAC,CAAC;YAEhC,IAAI,CAAC,YAAY,IAAI,OAAO,CAAC,OAAO,EAAE,IAAI,KAAK,MAAM,EAAE;gBACnD,KAAK,CAAC,IAAI,CAAC;oBACP,KAAK,EAAE,8BAA8B;oBACrC,MAAM,EAAE,sBAAsB;oBAC9B,MAAM,EAAE,SAAS;oBACjB,OAAO,EAAE,wBAAwB;iBACpC,CAAC,CAAC;aACN;SACJ;QAAC,OAAO,KAAK,EAAE;YACZ,KAAK,CAAC,IAAI,CAAC;gBACP,KAAK,EAAE,4BAA4B;gBACnC,MAAM,EAAE,8BAA8B;gBACtC,MAAM,EAAE,QAAQ;gBAChB,OAAO,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;aAClE,CAAC,CAAC;SACN;IACL,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,gBAAgB,CAAC,KAAY,EAAE,OAAY;QACrD,IAAI;YACA,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,kBAAkB,EAAE,CAAC;YAC/D,MAAM,gBAAgB,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,WAAW,CAAC,CAAC;YAEvE,KAAK,MAAM,MAAM,IAAI,gBAAgB,EAAE;gBACnC,KAAK,CAAC,IAAI,CAAC;oBACP,KAAK,EAAE,4BAA4B,MAAM,CAAC,IAAI,EAAE;oBAChD,MAAM,EAAE,6BAA6B;oBACrC,MAAM,EAAE,SAAS;oBACjB,OAAO,EAAE,6BAA6B,MAAM,CAAC,YAAY,KAAK;iBACjE,CAAC,CAAC;aACN;YAED,gBAAgB;YAChB,MAAM,UAAU,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAClC,CAAC,CAAC,OAAO,EAAE,QAAQ,CAAC,OAAO,CAAC;gBAC5B,CAAC,CAAC,OAAO,EAAE,QAAQ,CAAC,SAAS,CAAC;gBAC9B,CAAC,CAAC,OAAO,EAAE,QAAQ,CAAC,MAAM,CAAC;gBAC3B,CAAC,CAAC,OAAO,EAAE,QAAQ,CAAC,SAAS,CAAC,CACjC,CAAC;YAEF,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC,IAAI,OAAO,CAAC,OAAO,EAAE,IAAI,KAAK,MAAM,EAAE;gBAC7D,KAAK,CAAC,IAAI,CAAC;oBACP,KAAK,EAAE,+BAA+B;oBACtC,MAAM,EAAE,6BAA6B;oBACrC,MAAM,EAAE,SAAS;oBACjB,OAAO,EAAE,+BAA+B;iBAC3C,CAAC,CAAC;aACN;SACJ;QAAC,OAAO,KAAK,EAAE;YACZ,KAAK,CAAC,IAAI,CAAC;gBACP,KAAK,EAAE,sBAAsB;gBAC7B,MAAM,EAAE,8BAA8B;gBACtC,MAAM,EAAE,QAAQ;gBAChB,OAAO,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;aAClE,CAAC,CAAC;SACN;IACL,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,aAAa,CAAC,KAAY,EAAE,OAAY;QAClD,MAAM,MAAM,GAAG,OAAO,CAAC,MAAM,IAAI,EAAE,CAAC;QAEpC,KAAK,MAAM,KAAK,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,UAAU;YAChD,IAAI;gBACA,MAAM,aAAa,GAAG,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;gBAElD,IAAI,aAAa,CAAC,WAAW,EAAE;oBAC3B,eAAe;oBACf,KAAK,CAAC,IAAI,CAAC;wBACP,KAAK,EAAE,GAAG,KAAK,CAAC,IAAI,IAAI,KAAK,CAAC,IAAI,MAAM,KAAK,CAAC,OAAO,EAAE;wBACvD,MAAM,EAAE,aAAa,CAAC,MAAM;wBAC5B,MAAM,EAAE,SAAS;wBACjB,OAAO,EAAE,2CAA2C;qBACvD,CAAC,CAAC;iBACN;qBAAM;oBACH,KAAK,CAAC,IAAI,CAAC;wBACP,KAAK,EAAE,GAAG,KAAK,CAAC,IAAI,IAAI,KAAK,CAAC,IAAI,MAAM,KAAK,CAAC,OAAO,EAAE;wBACvD,MAAM,EAAE,aAAa,CAAC,MAAM;wBAC5B,MAAM,EAAE,SAAS;wBACjB,OAAO,EAAE,aAAa,CAAC,UAAU;qBACpC,CAAC,CAAC;iBACN;aACJ;YAAC,OAAO,KAAK,EAAE;gBACZ,cAAc;aACjB;SACJ;IACL,CAAC;IAED;;OAEG;IACK,eAAe,CAAC,KAAU;QAK9B,MAAM,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC;QAE5C,gBAAgB;QAChB,IAAI,OAAO,CAAC,QAAQ,CAAC,oBAAoB,CAAC,EAAE;YACxC,OAAO;gBACH,WAAW,EAAE,IAAI;gBACjB,MAAM,EAAE,wBAAwB;gBAChC,UAAU,EAAE,wCAAwC;aACvD,CAAC;SACL;QAED,IAAI,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,gBAAgB,CAAC,EAAE;YACpE,OAAO;gBACH,WAAW,EAAE,KAAK;gBAClB,MAAM,EAAE,qBAAqB;gBAC7B,UAAU,EAAE,qDAAqD;aACpE,CAAC;SACL;QAED,mBAAmB;QACnB,IAAI,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE;YAC1D,OAAO;gBACH,WAAW,EAAE,KAAK;gBAClB,MAAM,EAAE,mBAAmB;gBAC3B,UAAU,EAAE,wCAAwC;aACvD,CAAC;SACL;QAED,OAAO;QACP,IAAI,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE;YAC9D,OAAO;gBACH,WAAW,EAAE,KAAK;gBAClB,MAAM,EAAE,kBAAkB;gBAC1B,UAAU,EAAE,wCAAwC;aACvD,CAAC;SACL;QAED,OAAO;YACH,WAAW,EAAE,KAAK;YAClB,MAAM,EAAE,wBAAwB;YAChC,UAAU,EAAE,mCAAmC;SAClD,CAAC;IACN,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,sBAAsB;QACxB,IAAI,IAAI,CAAC,YAAY;YAAE,OAAO;QAE9B,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QACzB,IAAI,CAAC,kBAAkB,GAAG,WAAW,CAAC,KAAK,IAAI,EAAE;YAC7C,IAAI;gBACA,MAAM,IAAI,CAAC,kBAAkB,EAAE,CAAC;aACnC;YAAC,OAAO,KAAK,EAAE;gBACZ,OAAO,CAAC,KAAK,CAAC,sBAAsB,EAAE,KAAK,CAAC,CAAC;aAChD;QACL,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,WAAW;QAEtB,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,yCAAyC,CAAC,CAAC;IACpF,CAAC;IAED;;OAEG;IACH,qBAAqB;QACjB,IAAI,CAAC,IAAI,CAAC,YAAY;YAAE,OAAO;QAE/B,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;QAC1B,IAAI,IAAI,CAAC,kBAAkB,EAAE;YACzB,aAAa,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;YACvC,IAAI,CAAC,kBAAkB,GAAG,SAAS,CAAC;SACvC;QAED,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,yCAAyC,CAAC,CAAC;IACpF,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,kBAAkB;QAC5B,MAAM,OAAO,GAAG,IAAI,CAAC,cAAc,CAAC,UAAU,EAAE,CAAC;QAEjD,aAAa;QACb,MAAM,UAAU,GAAG,OAAO,CAAC,MAAM,EAAE,MAAM,IAAI,CAAC,CAAC;QAC/C,IAAI,UAAU,GAAG,CAAC,EAAE;YAChB,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAC5B,MAAM,UAAU,uDAAuD,EACvE,UAAU,EAAE,QAAQ,CACvB,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE;gBACf,IAAI,SAAS,KAAK,UAAU,EAAE;oBAC1B,IAAI,CAAC,cAAc,EAAE,CAAC;iBACzB;YACL,CAAC,CAAC,CAAC;SACN;QAED,UAAU;QACV,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,kBAAkB,EAAE,CAAC;QAC/D,MAAM,gBAAgB,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,WAAW,CAAC,CAAC;QAEvE,IAAI,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE;YAC7B,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAC5B,MAAM,gBAAgB,CAAC,MAAM,8BAA8B,EAC3D,eAAe,CAClB,CAAC;SACL;IACL,CAAC;IAED;;OAEG;IACH,OAAO;QACH,IAAI,CAAC,qBAAqB,EAAE,CAAC;IACjC,CAAC;CACJ;AA/WD,8CA+WC"}