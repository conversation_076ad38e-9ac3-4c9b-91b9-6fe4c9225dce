<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket 连接测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
            font-weight: bold;
        }
        .status.connected {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.disconnected {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.connecting {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            margin: 20px 0;
        }
        .controls {
            margin: 20px 0;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        input[type="text"] {
            width: 300px;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔌 WebSocket 连接测试</h1>
        
        <div id="status" class="status disconnected">
            🔴 未连接
        </div>

        <div class="controls">
            <h3>认证信息</h3>
            <input type="text" id="tokenInput" placeholder="输入 JWT Token" style="width: 500px;">
            <br>
            <button class="btn" onclick="connect()">连接</button>
            <button class="btn" onclick="disconnect()">断开</button>
            <button class="btn" onclick="clearLog()">清空日志</button>
        </div>

        <div class="controls">
            <h3>发送消息</h3>
            <input type="text" id="messageInput" placeholder="输入测试消息">
            <button class="btn" onclick="sendMessage()" id="sendBtn" disabled>发送消息</button>
        </div>

        <h3>连接日志</h3>
        <div id="log" class="log"></div>
    </div>

    <script src="/socket.io/socket.io.js"></script>
    <script>
        let socket = null;
        let isConnected = false;

        const statusEl = document.getElementById('status');
        const logEl = document.getElementById('log');
        const tokenInput = document.getElementById('tokenInput');
        const messageInput = document.getElementById('messageInput');
        const sendBtn = document.getElementById('sendBtn');

        // 从 localStorage 获取 token
        const savedToken = localStorage.getItem('auth_token');
        if (savedToken) {
            tokenInput.value = savedToken;
        }

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.style.color = type === 'error' ? 'red' : type === 'success' ? 'green' : 'black';
            logEntry.textContent = `[${timestamp}] ${message}`;
            logEl.appendChild(logEntry);
            logEl.scrollTop = logEl.scrollHeight;
        }

        function updateStatus(status, message) {
            statusEl.className = `status ${status}`;
            statusEl.textContent = message;
        }

        function connect() {
            if (socket) {
                socket.disconnect();
            }

            const token = tokenInput.value.trim();
            
            log('尝试连接 WebSocket...', 'info');
            updateStatus('connecting', '🟡 连接中...');

            try {
                socket = io('http://localhost:3000', {
                    auth: {
                        token: token || null
                    },
                    transports: ['websocket', 'polling']
                });

                socket.on('connect', () => {
                    isConnected = true;
                    log('✅ WebSocket 连接成功!', 'success');
                    updateStatus('connected', '🟢 已连接');
                    sendBtn.disabled = false;

                    // 加入默认房间
                    socket.emit('join', { room: 'default' });
                    log('发送 join 事件到默认房间', 'info');
                });

                socket.on('disconnect', (reason) => {
                    isConnected = false;
                    log(`❌ WebSocket 连接断开: ${reason}`, 'error');
                    updateStatus('disconnected', '🔴 未连接');
                    sendBtn.disabled = true;
                });

                socket.on('connect_error', (error) => {
                    log(`❌ 连接错误: ${error.message}`, 'error');
                    updateStatus('disconnected', '🔴 连接失败');
                    sendBtn.disabled = true;
                });

                socket.on('joined', (data) => {
                    log(`✅ 成功加入房间: ${data.room}, Socket ID: ${data.socketId}`, 'success');
                });

                socket.on('agent_response', (data) => {
                    log(`🤖 Agent 响应: ${data.message}`, 'success');
                });

                socket.on('error', (data) => {
                    log(`❌ 服务器错误: ${data.message}`, 'error');
                });

                // 监听所有事件
                socket.onAny((eventName, ...args) => {
                    log(`📨 收到事件: ${eventName}, 数据: ${JSON.stringify(args)}`, 'info');
                });

            } catch (error) {
                log(`❌ 连接异常: ${error.message}`, 'error');
                updateStatus('disconnected', '🔴 连接异常');
            }
        }

        function disconnect() {
            if (socket) {
                socket.disconnect();
                socket = null;
            }
            isConnected = false;
            log('手动断开连接', 'info');
            updateStatus('disconnected', '🔴 未连接');
            sendBtn.disabled = true;
        }

        function sendMessage() {
            if (!socket || !isConnected) {
                log('❌ 未连接到服务器', 'error');
                return;
            }

            const message = messageInput.value.trim();
            if (!message) {
                log('❌ 消息不能为空', 'error');
                return;
            }

            log(`📤 发送消息: ${message}`, 'info');
            
            socket.emit('chat_message', {
                agentType: 'analyzer',
                message: message,
                context: {
                    timestamp: new Date().toISOString(),
                    test: true
                }
            });

            messageInput.value = '';
        }

        function clearLog() {
            logEl.innerHTML = '';
        }

        // 回车发送消息
        messageInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendMessage();
            }
        });

        // 页面加载时显示说明
        log('WebSocket 测试工具已加载', 'info');
        log('1. 输入 JWT Token（可选，用于认证）', 'info');
        log('2. 点击"连接"按钮建立 WebSocket 连接', 'info');
        log('3. 连接成功后可以发送测试消息', 'info');
        log('4. 观察连接状态和服务器响应', 'info');
    </script>
</body>
</html>
