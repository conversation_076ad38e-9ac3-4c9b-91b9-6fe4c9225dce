#!/bin/bash

# 定期安全检查脚本
# 检查系统安全状态

LOG_FILE="./logs/security-check.log"

log_security() {
    echo "[$(date)] $1" >> "$LOG_FILE"
}

# 检查文件权限
check_file_permissions() {
    echo "检查文件权限..."
    
    # 检查敏感文件权限
    for file in server/.env* server/ssl/* server/data/*; do
        if [ -f "$file" ]; then
            perms=$(stat -c "%a" "$file" 2>/dev/null || stat -f "%A" "$file" 2>/dev/null)
            if [ "$perms" != "600" ] && [ "$perms" != "644" ]; then
                log_security "WARNING: $file has permissions $perms"
            fi
        fi
    done
}

# 检查进程状态
check_processes() {
    echo "检查进程状态..."
    
    # 检查是否有可疑进程
    ps aux | grep -E "(nc|netcat|telnet)" | grep -v grep && log_security "WARNING: Suspicious network tools detected"
}

# 检查网络连接
check_network() {
    echo "检查网络连接..."
    
    # 检查监听端口
    netstat -tlnp 2>/dev/null | grep LISTEN | while read line; do
        port=$(echo "$line" | awk '{print $4}' | cut -d: -f2)
        if [ "$port" != "22" ] && [ "$port" != "80" ] && [ "$port" != "443" ] && [ "$port" != "3000" ]; then
            log_security "INFO: Unexpected listening port: $port"
        fi
    done
}

# 检查日志异常
check_logs() {
    echo "检查日志异常..."
    
    # 检查失败的登录尝试
    if [ -f "./logs/security-$(date +%Y-%m-%d).log" ]; then
        failed_logins=$(grep "AUTH_FAILURE" "./logs/security-$(date +%Y-%m-%d).log" | wc -l)
        if [ "$failed_logins" -gt 10 ]; then
            log_security "WARNING: High number of failed login attempts: $failed_logins"
        fi
    fi
}

# 主检查函数
main() {
    log_security "Starting security check"
    check_file_permissions
    check_processes
    check_network
    check_logs
    log_security "Security check completed"
}

main
