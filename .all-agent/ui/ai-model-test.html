<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI 模型测试工具</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 6px;
            background: #fafafa;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-weight: bold;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.testing {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            margin: 10px 0;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        .btn.danger {
            background: #dc3545;
        }
        .btn.danger:hover {
            background: #c82333;
        }
        input[type="text"], textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin: 5px 0;
            box-sizing: border-box;
        }
        textarea {
            height: 80px;
            resize: vertical;
        }
        .config-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .response-box {
            background: #e9ecef;
            border: 1px solid #ced4da;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 AI 模型测试工具</h1>
        <p>直接测试您配置的 AI 模型是否能正常响应，帮助诊断问题所在。</p>

        <!-- 配置区域 -->
        <div class="test-section">
            <h3>📋 模型配置</h3>
            <div class="config-grid">
                <div>
                    <label>API 提供商:</label>
                    <select id="providerSelect">
                        <option value="openai">OpenAI</option>
                        <option value="anthropic">Anthropic</option>
                        <option value="azure">Azure OpenAI</option>
                        <option value="custom">自定义</option>
                    </select>
                </div>
                <div>
                    <label>模型名称:</label>
                    <input type="text" id="modelInput" placeholder="gpt-3.5-turbo" value="gpt-3.5-turbo">
                </div>
            </div>
            <div>
                <label>API Key:</label>
                <input type="password" id="apiKeyInput" placeholder="输入您的 API Key">
            </div>
            <div>
                <label>API 基础 URL (可选):</label>
                <input type="text" id="baseUrlInput" placeholder="https://api.openai.com/v1">
            </div>
        </div>

        <!-- 测试区域 -->
        <div class="test-section">
            <h3>🔧 快速测试</h3>
            <div id="quickTestStatus" class="status" style="display: none;"></div>
            
            <button class="btn" onclick="testConnection()">测试连接</button>
            <button class="btn" onclick="testSimplePrompt()">测试简单提示</button>
            <button class="btn" onclick="testComplexPrompt()">测试复杂提示</button>
            <button class="btn danger" onclick="clearResults()">清空结果</button>

            <h4>测试日志:</h4>
            <div id="testLog" class="log"></div>
        </div>

        <!-- 自定义测试 -->
        <div class="test-section">
            <h3>✏️ 自定义测试</h3>
            <div>
                <label>测试提示词:</label>
                <textarea id="customPrompt" placeholder="输入您想测试的提示词...">你好，请简单介绍一下你自己。</textarea>
            </div>
            <button class="btn" onclick="testCustomPrompt()" id="customTestBtn">发送测试</button>
            
            <h4>AI 响应:</h4>
            <div id="aiResponse" class="response-box">等待测试...</div>
        </div>

        <!-- 环境检查 -->
        <div class="test-section">
            <h3>🔍 环境检查</h3>
            <button class="btn" onclick="checkEnvironment()">检查环境</button>
            <div id="envResults" class="log" style="height: 150px;"></div>
        </div>
    </div>

    <script>
        let currentConfig = {};

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEl = document.getElementById('testLog');
            const logEntry = document.createElement('div');
            logEntry.style.color = type === 'error' ? 'red' : type === 'success' ? 'green' : 'black';
            logEntry.textContent = `[${timestamp}] ${message}`;
            logEl.appendChild(logEntry);
            logEl.scrollTop = logEl.scrollHeight;
        }

        function updateStatus(status, message) {
            const statusEl = document.getElementById('quickTestStatus');
            statusEl.className = `status ${status}`;
            statusEl.textContent = message;
            statusEl.style.display = 'block';
        }

        function getConfig() {
            return {
                provider: document.getElementById('providerSelect').value,
                model: document.getElementById('modelInput').value,
                apiKey: document.getElementById('apiKeyInput').value,
                baseUrl: document.getElementById('baseUrlInput').value
            };
        }

        async function testConnection() {
            const config = getConfig();
            
            if (!config.apiKey) {
                log('❌ 请先输入 API Key', 'error');
                updateStatus('error', '❌ 配置不完整');
                return;
            }

            log('🔄 测试连接中...', 'info');
            updateStatus('testing', '🔄 测试连接中...');

            try {
                const response = await fetch('/api/test-ai-model', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        action: 'test_connection',
                        config: config
                    })
                });

                const result = await response.json();
                
                if (result.success) {
                    log('✅ 连接测试成功', 'success');
                    updateStatus('success', '✅ 连接正常');
                } else {
                    log(`❌ 连接测试失败: ${result.error}`, 'error');
                    updateStatus('error', '❌ 连接失败');
                }
            } catch (error) {
                log(`❌ 连接测试异常: ${error.message}`, 'error');
                updateStatus('error', '❌ 连接异常');
            }
        }

        async function testSimplePrompt() {
            await testPrompt('你好', 'simple');
        }

        async function testComplexPrompt() {
            await testPrompt('请分析一下人工智能的发展趋势，并给出你的观点。', 'complex');
        }

        async function testCustomPrompt() {
            const prompt = document.getElementById('customPrompt').value.trim();
            if (!prompt) {
                log('❌ 请输入测试提示词', 'error');
                return;
            }
            await testPrompt(prompt, 'custom');
        }

        async function testPrompt(prompt, type) {
            const config = getConfig();
            
            if (!config.apiKey) {
                log('❌ 请先输入 API Key', 'error');
                return;
            }

            log(`🔄 测试${type}提示: ${prompt.substring(0, 50)}...`, 'info');
            updateStatus('testing', '🔄 AI 处理中...');

            const startTime = Date.now();

            try {
                const response = await fetch('/api/test-ai-model', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        action: 'test_prompt',
                        config: config,
                        prompt: prompt
                    })
                });

                const result = await response.json();
                const duration = Date.now() - startTime;
                
                if (result.success) {
                    log(`✅ AI 响应成功 (${duration}ms)`, 'success');
                    updateStatus('success', '✅ AI 响应正常');
                    
                    // 显示响应内容
                    const responseEl = document.getElementById('aiResponse');
                    responseEl.textContent = result.response;
                    
                    log(`📝 响应内容: ${result.response.substring(0, 100)}...`, 'info');
                } else {
                    log(`❌ AI 响应失败: ${result.error}`, 'error');
                    updateStatus('error', '❌ AI 响应失败');
                    
                    const responseEl = document.getElementById('aiResponse');
                    responseEl.textContent = `错误: ${result.error}`;
                }
            } catch (error) {
                log(`❌ 请求异常: ${error.message}`, 'error');
                updateStatus('error', '❌ 请求异常');
                
                const responseEl = document.getElementById('aiResponse');
                responseEl.textContent = `异常: ${error.message}`;
            }
        }

        async function checkEnvironment() {
            const envEl = document.getElementById('envResults');
            envEl.innerHTML = '';
            
            function envLog(message) {
                const div = document.createElement('div');
                div.textContent = message;
                envEl.appendChild(div);
            }

            envLog('🔍 检查环境配置...');
            
            try {
                const response = await fetch('/api/test-ai-model', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        action: 'check_environment'
                    })
                });

                const result = await response.json();
                
                if (result.success) {
                    envLog('✅ 服务器环境正常');
                    envLog(`📋 可用的 AI 提供商: ${result.providers.join(', ')}`);
                    envLog(`🔧 默认配置: ${JSON.stringify(result.defaultConfig, null, 2)}`);
                } else {
                    envLog(`❌ 环境检查失败: ${result.error}`);
                }
            } catch (error) {
                envLog(`❌ 环境检查异常: ${error.message}`);
            }
        }

        function clearResults() {
            document.getElementById('testLog').innerHTML = '';
            document.getElementById('aiResponse').textContent = '等待测试...';
            document.getElementById('envResults').innerHTML = '';
            document.getElementById('quickTestStatus').style.display = 'none';
            log('🧹 结果已清空', 'info');
        }

        // 页面加载时的初始化
        window.onload = function() {
            log('🚀 AI 模型测试工具已加载', 'info');
            log('1. 配置您的 AI 模型信息', 'info');
            log('2. 点击"测试连接"验证配置', 'info');
            log('3. 使用各种测试功能验证 AI 响应', 'info');
        };
    </script>
</body>
</html>
