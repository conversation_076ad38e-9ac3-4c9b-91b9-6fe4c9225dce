#!/bin/bash

# All-Agent 多云部署脚本
# 支持 AWS、Azure、GCP 和边缘节点部署

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# 配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
CLOUD_PROVIDER=${1:-aws}
DEPLOYMENT_TYPE=${2:-kubernetes}
ENVIRONMENT=${3:-production}

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_cloud() {
    echo -e "${PURPLE}[CLOUD]${NC} $1"
}

# 显示帮助信息
show_help() {
    cat << EOF
All-Agent 多云部署脚本

用法:
    $0 [云提供商] [部署类型] [环境]

云提供商:
    aws         Amazon Web Services
    azure       Microsoft Azure
    gcp         Google Cloud Platform
    edge        边缘计算节点
    multi       多云同时部署

部署类型:
    kubernetes  Kubernetes 集群部署 (默认)
    serverless  无服务器部署
    container   容器实例部署
    vm          虚拟机部署

环境:
    development 开发环境
    staging     测试环境
    production  生产环境 (默认)

示例:
    $0 aws kubernetes production
    $0 azure serverless staging
    $0 gcp container production
    $0 edge kubernetes production
    $0 multi kubernetes production

选项:
    -h, --help      显示此帮助信息
    --region        指定部署区域
    --zones         指定可用区
    --size          指定集群大小
    --istio         启用 Istio 服务网格
    --monitoring    启用监控和日志
    --backup        启用备份
    --dry-run       仅显示部署计划
EOF
}

# 检查依赖
check_dependencies() {
    log_info "检查多云部署依赖..."
    
    # 通用依赖
    if ! command -v kubectl &> /dev/null; then
        log_error "kubectl 未安装"
        exit 1
    fi
    
    if ! command -v terraform &> /dev/null; then
        log_error "Terraform 未安装"
        exit 1
    fi
    
    # 云提供商特定依赖
    case $CLOUD_PROVIDER in
        aws)
            if ! command -v aws &> /dev/null; then
                log_error "AWS CLI 未安装"
                exit 1
            fi
            ;;
        azure)
            if ! command -v az &> /dev/null; then
                log_error "Azure CLI 未安装"
                exit 1
            fi
            ;;
        gcp)
            if ! command -v gcloud &> /dev/null; then
                log_error "Google Cloud SDK 未安装"
                exit 1
            fi
            ;;
        edge)
            if ! command -v k3s &> /dev/null && ! command -v microk8s &> /dev/null; then
                log_warning "建议安装 k3s 或 microk8s 用于边缘部署"
            fi
            ;;
    esac
    
    log_success "依赖检查完成"
}

# AWS 部署
deploy_aws() {
    log_cloud "开始 AWS 部署..."
    
    cd "$PROJECT_ROOT/cloud/aws/terraform"
    
    # 初始化 Terraform
    log_info "初始化 Terraform..."
    terraform init
    
    # 创建部署计划
    log_info "创建部署计划..."
    terraform plan \
        -var="environment=$ENVIRONMENT" \
        -var="aws_region=${AWS_REGION:-us-west-2}" \
        -var="enable_istio=${ENABLE_ISTIO:-true}" \
        -var="enable_monitoring=${ENABLE_MONITORING:-true}" \
        -out=tfplan
    
    if [ "$DRY_RUN" = "true" ]; then
        log_info "干运行模式，跳过实际部署"
        return 0
    fi
    
    # 应用部署
    log_info "应用 Terraform 配置..."
    terraform apply tfplan
    
    # 配置 kubectl
    log_info "配置 kubectl..."
    aws eks update-kubeconfig \
        --region "${AWS_REGION:-us-west-2}" \
        --name "all-agent-cluster"
    
    # 部署应用
    deploy_kubernetes_apps "aws"
    
    log_success "AWS 部署完成"
}

# Azure 部署
deploy_azure() {
    log_cloud "开始 Azure 部署..."
    
    cd "$PROJECT_ROOT/cloud/azure/terraform"
    
    # 初始化 Terraform
    log_info "初始化 Terraform..."
    terraform init
    
    # 创建部署计划
    log_info "创建部署计划..."
    terraform plan \
        -var="environment=$ENVIRONMENT" \
        -var="azure_region=${AZURE_REGION:-West US 2}" \
        -var="enable_istio=${ENABLE_ISTIO:-true}" \
        -var="enable_monitoring=${ENABLE_MONITORING:-true}" \
        -out=tfplan
    
    if [ "$DRY_RUN" = "true" ]; then
        log_info "干运行模式，跳过实际部署"
        return 0
    fi
    
    # 应用部署
    log_info "应用 Terraform 配置..."
    terraform apply tfplan
    
    # 配置 kubectl
    log_info "配置 kubectl..."
    az aks get-credentials \
        --resource-group "all-agent-rg" \
        --name "all-agent-aks"
    
    # 部署应用
    deploy_kubernetes_apps "azure"
    
    log_success "Azure 部署完成"
}

# GCP 部署
deploy_gcp() {
    log_cloud "开始 GCP 部署..."
    
    cd "$PROJECT_ROOT/cloud/gcp/terraform"
    
    # 初始化 Terraform
    log_info "初始化 Terraform..."
    terraform init
    
    # 创建部署计划
    log_info "创建部署计划..."
    terraform plan \
        -var="environment=$ENVIRONMENT" \
        -var="gcp_region=${GCP_REGION:-us-west1}" \
        -var="enable_istio=${ENABLE_ISTIO:-true}" \
        -var="enable_monitoring=${ENABLE_MONITORING:-true}" \
        -out=tfplan
    
    if [ "$DRY_RUN" = "true" ]; then
        log_info "干运行模式，跳过实际部署"
        return 0
    fi
    
    # 应用部署
    log_info "应用 Terraform 配置..."
    terraform apply tfplan
    
    # 配置 kubectl
    log_info "配置 kubectl..."
    gcloud container clusters get-credentials \
        "all-agent-cluster" \
        --region "${GCP_REGION:-us-west1}"
    
    # 部署应用
    deploy_kubernetes_apps "gcp"
    
    log_success "GCP 部署完成"
}

# 边缘部署
deploy_edge() {
    log_cloud "开始边缘节点部署..."
    
    # 检查边缘环境
    if command -v k3s &> /dev/null; then
        EDGE_K8S="k3s"
    elif command -v microk8s &> /dev/null; then
        EDGE_K8S="microk8s"
    else
        log_error "未找到边缘 Kubernetes 环境"
        exit 1
    fi
    
    log_info "使用 $EDGE_K8S 进行边缘部署"
    
    # 部署边缘应用
    cd "$PROJECT_ROOT/edge/k3s"
    
    if [ "$EDGE_K8S" = "k3s" ]; then
        kubectl apply -f deployment.yaml
    else
        microk8s kubectl apply -f deployment.yaml
    fi
    
    # 等待部署完成
    log_info "等待边缘节点启动..."
    sleep 30
    
    # 健康检查
    if curl -f http://localhost:30000/health &> /dev/null; then
        log_success "边缘节点部署完成"
    else
        log_error "边缘节点健康检查失败"
        exit 1
    fi
}

# 部署 Kubernetes 应用
deploy_kubernetes_apps() {
    local cloud_provider=$1
    
    log_info "部署 Kubernetes 应用到 $cloud_provider..."
    
    # 创建命名空间
    kubectl apply -f "$PROJECT_ROOT/k8s/namespace.yaml"
    
    # 部署配置和密钥
    kubectl apply -f "$PROJECT_ROOT/k8s/configmap.yaml"
    kubectl apply -f "$PROJECT_ROOT/k8s/secrets.yaml"
    
    # 部署存储
    kubectl apply -f "$PROJECT_ROOT/k8s/pvc.yaml"
    
    # 部署应用
    kubectl apply -f "$PROJECT_ROOT/k8s/deployment.yaml"
    kubectl apply -f "$PROJECT_ROOT/k8s/service.yaml"
    
    # 部署 Istio 配置
    if [ "$ENABLE_ISTIO" = "true" ]; then
        log_info "部署 Istio 服务网格..."
        kubectl apply -f "$PROJECT_ROOT/istio/gateway.yaml"
    fi
    
    # 部署监控
    if [ "$ENABLE_MONITORING" = "true" ]; then
        log_info "部署监控系统..."
        kubectl apply -f "$PROJECT_ROOT/observability/jaeger.yaml"
    fi
    
    # 等待部署完成
    log_info "等待应用启动..."
    kubectl wait --for=condition=available --timeout=300s deployment/all-agent-app -n all-agent
    
    log_success "Kubernetes 应用部署完成"
}

# 多云部署
deploy_multi_cloud() {
    log_cloud "开始多云部署..."
    
    # 并行部署到多个云
    local pids=()
    
    # AWS 部署
    if [ "${DEPLOY_AWS:-true}" = "true" ]; then
        log_info "启动 AWS 部署..."
        (
            export CLOUD_PROVIDER="aws"
            deploy_aws
        ) &
        pids+=($!)
    fi
    
    # Azure 部署
    if [ "${DEPLOY_AZURE:-true}" = "true" ]; then
        log_info "启动 Azure 部署..."
        (
            export CLOUD_PROVIDER="azure"
            deploy_azure
        ) &
        pids+=($!)
    fi
    
    # GCP 部署
    if [ "${DEPLOY_GCP:-true}" = "true" ]; then
        log_info "启动 GCP 部署..."
        (
            export CLOUD_PROVIDER="gcp"
            deploy_gcp
        ) &
        pids+=($!)
    fi
    
    # 等待所有部署完成
    log_info "等待所有云部署完成..."
    for pid in "${pids[@]}"; do
        wait $pid
    done
    
    # 配置多云负载均衡
    configure_multi_cloud_lb
    
    log_success "多云部署完成"
}

# 配置多云负载均衡
configure_multi_cloud_lb() {
    log_info "配置多云负载均衡..."
    
    # 这里可以配置 DNS 负载均衡或全局负载均衡器
    # 例如使用 Cloudflare、AWS Route 53 等
    
    cat > "$PROJECT_ROOT/multi-cloud-endpoints.json" << EOF
{
  "endpoints": [
    {
      "cloud": "aws",
      "region": "${AWS_REGION:-us-west-2}",
      "url": "https://aws.all-agent.com",
      "weight": 33
    },
    {
      "cloud": "azure",
      "region": "${AZURE_REGION:-West US 2}",
      "url": "https://azure.all-agent.com",
      "weight": 33
    },
    {
      "cloud": "gcp",
      "region": "${GCP_REGION:-us-west1}",
      "url": "https://gcp.all-agent.com",
      "weight": 34
    }
  ],
  "failover": {
    "enabled": true,
    "healthCheck": "/health",
    "interval": 30
  }
}
EOF
    
    log_success "多云负载均衡配置完成"
}

# 部署后验证
post_deployment_verification() {
    log_info "执行部署后验证..."
    
    case $CLOUD_PROVIDER in
        aws)
            verify_aws_deployment
            ;;
        azure)
            verify_azure_deployment
            ;;
        gcp)
            verify_gcp_deployment
            ;;
        edge)
            verify_edge_deployment
            ;;
        multi)
            verify_multi_cloud_deployment
            ;;
    esac
    
    log_success "部署验证完成"
}

# AWS 部署验证
verify_aws_deployment() {
    log_info "验证 AWS 部署..."
    
    # 检查 EKS 集群
    aws eks describe-cluster --name "all-agent-cluster" --region "${AWS_REGION:-us-west-2}"
    
    # 检查应用健康状态
    kubectl get pods -n all-agent
    kubectl get svc -n all-agent
    
    # 健康检查
    local lb_url=$(kubectl get svc all-agent-app -n all-agent -o jsonpath='{.status.loadBalancer.ingress[0].hostname}')
    if [ -n "$lb_url" ]; then
        curl -f "http://$lb_url/health" || log_warning "健康检查失败"
    fi
}

# Azure 部署验证
verify_azure_deployment() {
    log_info "验证 Azure 部署..."
    
    # 检查 AKS 集群
    az aks show --resource-group "all-agent-rg" --name "all-agent-aks"
    
    # 检查应用健康状态
    kubectl get pods -n all-agent
    kubectl get svc -n all-agent
    
    # 健康检查
    local lb_ip=$(kubectl get svc all-agent-app -n all-agent -o jsonpath='{.status.loadBalancer.ingress[0].ip}')
    if [ -n "$lb_ip" ]; then
        curl -f "http://$lb_ip/health" || log_warning "健康检查失败"
    fi
}

# GCP 部署验证
verify_gcp_deployment() {
    log_info "验证 GCP 部署..."
    
    # 检查 GKE 集群
    gcloud container clusters describe "all-agent-cluster" --region "${GCP_REGION:-us-west1}"
    
    # 检查应用健康状态
    kubectl get pods -n all-agent
    kubectl get svc -n all-agent
    
    # 健康检查
    local lb_ip=$(kubectl get svc all-agent-app -n all-agent -o jsonpath='{.status.loadBalancer.ingress[0].ip}')
    if [ -n "$lb_ip" ]; then
        curl -f "http://$lb_ip/health" || log_warning "健康检查失败"
    fi
}

# 边缘部署验证
verify_edge_deployment() {
    log_info "验证边缘部署..."
    
    # 检查边缘节点状态
    kubectl get pods -n all-agent-edge
    kubectl get svc -n all-agent-edge
    
    # 健康检查
    curl -f "http://localhost:30000/health" || log_warning "边缘节点健康检查失败"
}

# 多云部署验证
verify_multi_cloud_deployment() {
    log_info "验证多云部署..."
    
    # 验证各个云的部署
    if [ "${DEPLOY_AWS:-true}" = "true" ]; then
        verify_aws_deployment
    fi
    
    if [ "${DEPLOY_AZURE:-true}" = "true" ]; then
        verify_azure_deployment
    fi
    
    if [ "${DEPLOY_GCP:-true}" = "true" ]; then
        verify_gcp_deployment
    fi
    
    # 验证负载均衡配置
    if [ -f "$PROJECT_ROOT/multi-cloud-endpoints.json" ]; then
        log_info "多云端点配置:"
        cat "$PROJECT_ROOT/multi-cloud-endpoints.json"
    fi
}

# 显示部署信息
show_deployment_info() {
    log_success "部署完成！"
    
    echo ""
    echo "🌐 部署信息:"
    echo "   云提供商: $CLOUD_PROVIDER"
    echo "   部署类型: $DEPLOYMENT_TYPE"
    echo "   环境: $ENVIRONMENT"
    echo ""
    
    case $CLOUD_PROVIDER in
        aws)
            echo "📋 AWS 管理命令:"
            echo "   查看集群: aws eks describe-cluster --name all-agent-cluster"
            echo "   配置 kubectl: aws eks update-kubeconfig --name all-agent-cluster"
            ;;
        azure)
            echo "📋 Azure 管理命令:"
            echo "   查看集群: az aks show --resource-group all-agent-rg --name all-agent-aks"
            echo "   配置 kubectl: az aks get-credentials --resource-group all-agent-rg --name all-agent-aks"
            ;;
        gcp)
            echo "📋 GCP 管理命令:"
            echo "   查看集群: gcloud container clusters describe all-agent-cluster"
            echo "   配置 kubectl: gcloud container clusters get-credentials all-agent-cluster"
            ;;
        edge)
            echo "📋 边缘节点管理:"
            echo "   访问地址: http://localhost:30000"
            echo "   查看状态: kubectl get pods -n all-agent-edge"
            ;;
        multi)
            echo "📋 多云管理:"
            echo "   端点配置: cat multi-cloud-endpoints.json"
            echo "   监控面板: 各云提供商的监控服务"
            ;;
    esac
    
    echo ""
    echo "🔧 通用管理命令:"
    echo "   查看 Pod: kubectl get pods -n all-agent"
    echo "   查看服务: kubectl get svc -n all-agent"
    echo "   查看日志: kubectl logs -l app.kubernetes.io/name=all-agent -n all-agent"
}

# 主函数
main() {
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            --region)
                case $CLOUD_PROVIDER in
                    aws) AWS_REGION="$2" ;;
                    azure) AZURE_REGION="$2" ;;
                    gcp) GCP_REGION="$2" ;;
                esac
                shift 2
                ;;
            --istio)
                ENABLE_ISTIO=true
                shift
                ;;
            --monitoring)
                ENABLE_MONITORING=true
                shift
                ;;
            --dry-run)
                DRY_RUN=true
                shift
                ;;
            *)
                shift
                ;;
        esac
    done
    
    log_info "开始多云部署"
    log_info "云提供商: $CLOUD_PROVIDER"
    log_info "部署类型: $DEPLOYMENT_TYPE"
    log_info "环境: $ENVIRONMENT"
    
    # 检查依赖
    check_dependencies
    
    # 执行部署
    case $CLOUD_PROVIDER in
        aws)
            deploy_aws
            ;;
        azure)
            deploy_azure
            ;;
        gcp)
            deploy_gcp
            ;;
        edge)
            deploy_edge
            ;;
        multi)
            deploy_multi_cloud
            ;;
        *)
            log_error "不支持的云提供商: $CLOUD_PROVIDER"
            show_help
            exit 1
            ;;
    esac
    
    # 部署后验证
    post_deployment_verification
    
    # 显示部署信息
    show_deployment_info
}

# 执行主函数
main "$@"
