# All-Agent 生产环境配置
# 生成时间: 2025年 6月12日 星期四 13时45分13秒 CST

# 服务器配置
NODE_ENV=production
PORT=3000
HOST=0.0.0.0

# 安全配置
JWT_SECRET=522a4a38a0c72a26f92f820c1ac6ef1415d2ab329d8c2a13be1f7ac59044b8370c8cf5617d45007f551f97c2b73d9e16e3b07523e608376aabce2c83f2a02033
API_KEY=0ccfc0b0d0b94d7c4ba41b0235a97728297bb8ca84ce3808d1645fef3c4f322a
ENCRYPTION_KEY=4075b6bd5609ab4fb884db8e6d6b6f22e54aeaaf5f80aed4e1e8e309e0070a73
SESSION_SECRET=8e4474d613065ebf84147aad395a06f31699a3b9a39af718326840223e5bedc0

# 数据库配置
DB_PATH=./data/production.db
DB_BACKUP_PATH=./backups/
DB_ENCRYPTION=true

# 日志配置
LOG_LEVEL=info
LOG_FILE=./logs/production.log
LOG_MAX_SIZE=100MB
LOG_MAX_FILES=10

# 安全头配置
SECURITY_HEADERS=true
CORS_ORIGIN=https://yourdomain.com
RATE_LIMIT_WINDOW=15
RATE_LIMIT_MAX=100

# HTTPS 配置
HTTPS_ENABLED=true
SSL_CERT_PATH=/path/to/ssl/cert.pem
SSL_KEY_PATH=/path/to/ssl/key.pem

# 监控配置
MONITORING_ENABLED=true
METRICS_ENDPOINT=/metrics
HEALTH_CHECK_ENDPOINT=/health

# 缓存配置
CACHE_TYPE=redis
REDIS_URL=redis://localhost:6379
CACHE_TTL=3600

# 邮件配置 (用于告警)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
ALERT_EMAIL=<EMAIL>

# AI 模型配置
OPENAI_API_KEY=your-openai-key
ANTHROPIC_API_KEY=your-anthropic-key
DEEPSEEK_API_KEY=your-deepseek-key

# 备份配置
BACKUP_ENABLED=true
BACKUP_INTERVAL=24
BACKUP_RETENTION=30

# 安全扫描
SECURITY_SCAN_ENABLED=true
VULNERABILITY_CHECK_INTERVAL=24
