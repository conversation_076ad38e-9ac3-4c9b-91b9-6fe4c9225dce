# All-Agent 环境配置示例文件
# 复制此文件为 .env 并根据需要修改配置

# =============================================================================
# 基础配置
# =============================================================================

# 服务器端口
PORT=3000

# 运行环境 (development, staging, production)
NODE_ENV=development

# 项目根目录
PROJECT_ROOT=./

# 日志级别 (error, warn, info, debug)
LOG_LEVEL=info

# =============================================================================
# 数据库配置
# =============================================================================

# SQLite 数据库文件路径
DATABASE_PATH=./data/all-agent.db

# 数据库连接池大小
DATABASE_POOL_SIZE=10

# =============================================================================
# 缓存配置
# =============================================================================

# 是否使用 Redis 缓存
USE_REDIS=false

# Redis 配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# 内存缓存配置
MEMORY_CACHE_MAX_SIZE=1000
MEMORY_CACHE_TTL=3600

# =============================================================================
# 认证配置
# =============================================================================

# JWT 密钥 (生产环境请使用强密钥)
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production

# JWT 过期时间
JWT_EXPIRES_IN=24h

# 刷新令牌过期时间
REFRESH_TOKEN_EXPIRES_IN=7d

# 密码加密轮数
BCRYPT_ROUNDS=12

# =============================================================================
# LLM 服务配置
# =============================================================================

# OpenAI API 配置
OPENAI_API_KEY=your-openai-api-key
OPENAI_API_BASE=https://api.openai.com/v1
OPENAI_MODEL=gpt-4

# Anthropic Claude 配置
ANTHROPIC_API_KEY=your-anthropic-api-key
ANTHROPIC_MODEL=claude-3-sonnet-20240229

# Google Gemini 配置
GOOGLE_API_KEY=your-google-api-key
GOOGLE_MODEL=gemini-pro

# 本地 LLM 配置
LOCAL_LLM_URL=http://localhost:11434
LOCAL_LLM_MODEL=llama2

# =============================================================================
# 监控和追踪配置
# =============================================================================

# Prometheus 配置
PROMETHEUS_URL=http://prometheus:9090
PROMETHEUS_PUSH_GATEWAY=http://pushgateway:9091

# Jaeger 追踪配置
JAEGER_ENDPOINT=http://jaeger:14268/api/traces
JAEGER_AGENT_HOST=jaeger
JAEGER_AGENT_PORT=6832

# Grafana 配置
GRAFANA_URL=http://grafana:3000
GRAFANA_API_KEY=your-grafana-api-key

# =============================================================================
# 邮件配置
# =============================================================================

# SMTP 配置
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your-email-password

# 发件人信息
EMAIL_FROM=All-Agent <<EMAIL>>

# =============================================================================
# 文件上传配置
# =============================================================================

# 上传文件大小限制 (MB)
MAX_FILE_SIZE=10

# 上传目录
UPLOAD_DIR=./uploads

# 允许的文件类型
ALLOWED_FILE_TYPES=.js,.py,.java,.cpp,.c,.h,.json,.yaml,.yml,.md,.txt

# =============================================================================
# 前沿技术配置
# =============================================================================

# AIOps 配置
ENABLE_AIOPS=true
ALERTMANAGER_URL=http://alertmanager:9093

# 边缘 AI 配置
ENABLE_EDGE_AI=true
EDGE_DEVICE_PROFILE=edge-standard
EDGE_MAX_MEMORY_MB=512
EDGE_MAX_LATENCY_MS=100

# Web3 配置
ENABLE_WEB3=false
ETHEREUM_RPC_URL=https://mainnet.infura.io/v3/your-project-id
POLYGON_RPC_URL=https://polygon-rpc.com
WEB3_STORAGE_TOKEN=your-web3-storage-token
PRIVATE_KEY=your-private-key
IPFS_GATEWAY=https://ipfs.io/ipfs/

# 量子计算配置
ENABLE_QUANTUM=false
IBM_QUANTUM_TOKEN=your-ibm-quantum-token
GOOGLE_QUANTUM_CREDENTIALS=path/to/google/credentials.json

# =============================================================================
# ML 平台配置
# =============================================================================

# MLflow 配置
MLFLOW_TRACKING_URI=http://mlflow-server:5000
MLFLOW_AUTH_TOKEN=your-mlflow-token
MLFLOW_DEFAULT_ARTIFACT_ROOT=./ml-artifacts

# =============================================================================
# FaaS 配置
# =============================================================================

# Knative 配置
KNATIVE_SERVING_NAMESPACE=knative-serving
KNATIVE_DOMAIN=all-agent.com

# =============================================================================
# Kubernetes 配置
# =============================================================================

# Kubernetes API 服务器
KUBERNETES_API=https://kubernetes.default.svc
KUBERNETES_NAMESPACE=all-agent

# =============================================================================
# 安全配置
# =============================================================================

# CORS 配置
CORS_ORIGIN=*
CORS_CREDENTIALS=true

# 速率限制配置
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=1000

# 会话配置
SESSION_SECRET=your-session-secret-change-this-in-production
SESSION_MAX_AGE=86400000

# =============================================================================
# 开发配置
# =============================================================================

# 是否启用调试模式
DEBUG=false

# 是否启用热重载
HOT_RELOAD=true

# 是否启用 API 文档
ENABLE_SWAGGER=true

# 是否启用性能分析
ENABLE_PROFILING=false

# =============================================================================
# 生产配置
# =============================================================================

# 是否启用 HTTPS
ENABLE_HTTPS=false

# SSL 证书路径
SSL_CERT_PATH=./certs/cert.pem
SSL_KEY_PATH=./certs/key.pem

# 是否启用集群模式
ENABLE_CLUSTER=false

# 工作进程数量
CLUSTER_WORKERS=4

# =============================================================================
# 备份配置
# =============================================================================

# 自动备份间隔 (小时)
BACKUP_INTERVAL=24

# 备份保留天数
BACKUP_RETENTION_DAYS=30

# 备份存储路径
BACKUP_PATH=./backups

# =============================================================================
# 第三方服务配置
# =============================================================================

# GitHub 集成
GITHUB_TOKEN=your-github-token
GITHUB_WEBHOOK_SECRET=your-webhook-secret

# Slack 集成
SLACK_BOT_TOKEN=your-slack-bot-token
SLACK_WEBHOOK_URL=your-slack-webhook-url

# Discord 集成
DISCORD_BOT_TOKEN=your-discord-bot-token
DISCORD_WEBHOOK_URL=your-discord-webhook-url
