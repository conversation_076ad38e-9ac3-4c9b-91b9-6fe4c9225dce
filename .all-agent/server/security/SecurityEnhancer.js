const rateLimit = require('express-rate-limit');
const helmet = require('helmet');
const validator = require('validator');
const crypto = require('crypto');

/**
 * 安全增强模块
 */
class SecurityEnhancer {
  constructor(options = {}) {
    this.options = {
      enableRateLimit: true,
      enableHelmet: true,
      enableInputValidation: true,
      enableCSRF: true,
      enableEncryption: true,
      ...options
    };

    this.rateLimitStore = new Map();
    this.encryptionKey = this.generateEncryptionKey();
  }

  /**
   * 设置安全中间件
   */
  setupSecurityMiddleware(app) {
    // Helmet 安全头
    if (this.options.enableHelmet) {
      app.use(helmet({
        contentSecurityPolicy: {
          directives: {
            defaultSrc: ["'self'"],
            styleSrc: ["'self'", "'unsafe-inline'"],
            scriptSrc: ["'self'"],
            imgSrc: ["'self'", "data:", "https:"],
            connectSrc: ["'self'"],
            fontSrc: ["'self'"],
            objectSrc: ["'none'"],
            mediaSrc: ["'self'"],
            frameSrc: ["'none'"],
          },
        },
        crossOriginEmbedderPolicy: false
      }));
    }

    // 速率限制
    if (this.options.enableRateLimit) {
      const limiter = rateLimit({
        windowMs: parseInt(process.env.RATE_LIMIT_WINDOW) || 15 * 60 * 1000, // 15分钟
        max: parseInt(process.env.RATE_LIMIT_MAX) || 1000, // 限制每个IP 1000次请求
        message: {
          error: 'Too many requests from this IP, please try again later.',
          retryAfter: '15 minutes'
        },
        standardHeaders: true,
        legacyHeaders: false,
        skip: (req) => {
          // 跳过健康检查和监控端点
          return req.path === '/health' || req.path.startsWith('/monitor');
        }
      });

      app.use(limiter);

      // 更严格的认证端点限制
      const authLimiter = rateLimit({
        windowMs: 15 * 60 * 1000, // 15分钟
        max: 10, // 限制每个IP 10次认证尝试
        message: {
          error: 'Too many authentication attempts, please try again later.',
          retryAfter: '15 minutes'
        }
      });

      app.use('/auth/login', authLimiter);
      app.use('/auth/register', authLimiter);
    }

    // 输入验证中间件
    if (this.options.enableInputValidation) {
      app.use(this.inputValidationMiddleware.bind(this));
    }
  }

  /**
   * 输入验证中间件
   */
  inputValidationMiddleware(req, res, next) {
    // 验证请求体大小
    if (req.body && JSON.stringify(req.body).length > 1024 * 1024) { // 1MB
      return res.status(413).json({
        error: 'Request body too large',
        maxSize: '1MB'
      });
    }

    // 验证常见的恶意模式
    const maliciousPatterns = [
      /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
      /javascript:/gi,
      /on\w+\s*=/gi,
      /eval\s*\(/gi,
      /expression\s*\(/gi
    ];

    const checkForMaliciousContent = (obj) => {
      if (typeof obj === 'string') {
        return maliciousPatterns.some(pattern => pattern.test(obj));
      }
      if (typeof obj === 'object' && obj !== null) {
        return Object.values(obj).some(checkForMaliciousContent);
      }
      return false;
    };

    if (req.body && checkForMaliciousContent(req.body)) {
      return res.status(400).json({
        error: 'Malicious content detected in request'
      });
    }

    next();
  }

  /**
   * 验证用户输入
   */
  validateUserInput(data, rules) {
    const errors = [];

    for (const [field, rule] of Object.entries(rules)) {
      const value = data[field];

      // 必填验证
      if (rule.required && (!value || value.toString().trim() === '')) {
        errors.push(`${field} is required`);
        continue;
      }

      // 如果字段为空且不是必填，跳过其他验证
      if (!value && !rule.required) {
        continue;
      }

      // 类型验证
      if (rule.type) {
        switch (rule.type) {
          case 'email':
            if (!validator.isEmail(value)) {
              errors.push(`${field} must be a valid email`);
            }
            break;
          case 'url':
            if (!validator.isURL(value)) {
              errors.push(`${field} must be a valid URL`);
            }
            break;
          case 'uuid':
            if (!validator.isUUID(value)) {
              errors.push(`${field} must be a valid UUID`);
            }
            break;
          case 'alphanumeric':
            if (!validator.isAlphanumeric(value)) {
              errors.push(`${field} must contain only letters and numbers`);
            }
            break;
        }
      }

      // 长度验证
      if (rule.minLength && value.length < rule.minLength) {
        errors.push(`${field} must be at least ${rule.minLength} characters long`);
      }
      if (rule.maxLength && value.length > rule.maxLength) {
        errors.push(`${field} must be no more than ${rule.maxLength} characters long`);
      }

      // 模式验证
      if (rule.pattern && !rule.pattern.test(value)) {
        errors.push(`${field} format is invalid`);
      }

      // 自定义验证
      if (rule.custom && typeof rule.custom === 'function') {
        const customResult = rule.custom(value);
        if (customResult !== true) {
          errors.push(customResult || `${field} is invalid`);
        }
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * 增强密码验证
   */
  validatePassword(password) {
    const errors = [];

    if (!password || password.length < 8) {
      errors.push('Password must be at least 8 characters long');
    }

    if (!/(?=.*[a-z])/.test(password)) {
      errors.push('Password must contain at least one lowercase letter');
    }

    if (!/(?=.*[A-Z])/.test(password)) {
      errors.push('Password must contain at least one uppercase letter');
    }

    if (!/(?=.*\d)/.test(password)) {
      errors.push('Password must contain at least one number');
    }

    if (!/(?=.*[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?])/.test(password)) {
      errors.push('Password must contain at least one special character');
    }

    // 检查常见弱密码
    const commonPasswords = [
      'password', '123456', '123456789', 'qwerty', 'abc123',
      'password123', 'admin', 'letmein', 'welcome', 'monkey'
    ];

    if (commonPasswords.includes(password.toLowerCase())) {
      errors.push('Password is too common, please choose a stronger password');
    }

    // 检查重复字符
    if (/(.)\1{2,}/.test(password)) {
      errors.push('Password should not contain repeated characters');
    }

    return {
      isValid: errors.length === 0,
      errors,
      strength: this.calculatePasswordStrength(password)
    };
  }

  /**
   * 计算密码强度
   */
  calculatePasswordStrength(password) {
    let score = 0;

    // 长度分数
    if (password.length >= 8) score += 1;
    if (password.length >= 12) score += 1;
    if (password.length >= 16) score += 1;

    // 字符类型分数
    if (/[a-z]/.test(password)) score += 1;
    if (/[A-Z]/.test(password)) score += 1;
    if (/\d/.test(password)) score += 1;
    if (/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) score += 1;

    // 复杂性分数
    if (/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(password)) score += 1;
    if (/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*])/.test(password)) score += 1;

    if (score <= 3) return 'weak';
    if (score <= 6) return 'medium';
    if (score <= 8) return 'strong';
    return 'very-strong';
  }

  /**
   * 生成安全令牌
   */
  generateSecureToken(length = 32) {
    return crypto.randomBytes(length).toString('hex');
  }

  /**
   * 生成加密密钥
   */
  generateEncryptionKey() {
    return process.env.ENCRYPTION_KEY || crypto.randomBytes(32).toString('hex');
  }

  /**
   * 加密敏感数据
   */
  encrypt(text) {
    if (!this.options.enableEncryption) {
      return text;
    }

    const algorithm = 'aes-256-gcm';
    const iv = crypto.randomBytes(16);
    const cipher = crypto.createCipher(algorithm, this.encryptionKey);
    
    let encrypted = cipher.update(text, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    
    const authTag = cipher.getAuthTag();
    
    return {
      encrypted,
      iv: iv.toString('hex'),
      authTag: authTag.toString('hex')
    };
  }

  /**
   * 解密敏感数据
   */
  decrypt(encryptedData) {
    if (!this.options.enableEncryption || typeof encryptedData === 'string') {
      return encryptedData;
    }

    const algorithm = 'aes-256-gcm';
    const decipher = crypto.createDecipher(algorithm, this.encryptionKey);
    
    decipher.setAuthTag(Buffer.from(encryptedData.authTag, 'hex'));
    
    let decrypted = decipher.update(encryptedData.encrypted, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    
    return decrypted;
  }

  /**
   * 清理和转义用户输入
   */
  sanitizeInput(input) {
    if (typeof input !== 'string') {
      return input;
    }

    return validator.escape(input.trim());
  }

  /**
   * 检查IP是否在黑名单中
   */
  isBlacklistedIP(ip) {
    // 这里可以实现IP黑名单检查
    const blacklistedIPs = process.env.BLACKLISTED_IPS ? 
      process.env.BLACKLISTED_IPS.split(',') : [];
    
    return blacklistedIPs.includes(ip);
  }

  /**
   * 记录安全事件
   */
  logSecurityEvent(event, details = {}) {
    const securityEvent = {
      timestamp: new Date().toISOString(),
      event,
      details,
      severity: this.getEventSeverity(event)
    };

    console.log(`🔒 Security Event: ${JSON.stringify(securityEvent)}`);
    
    // 这里可以集成到日志系统或安全监控系统
    return securityEvent;
  }

  /**
   * 获取事件严重程度
   */
  getEventSeverity(event) {
    const severityMap = {
      'malicious_input': 'high',
      'rate_limit_exceeded': 'medium',
      'invalid_token': 'medium',
      'failed_login': 'low',
      'password_change': 'low'
    };

    return severityMap[event] || 'low';
  }
}

module.exports = SecurityEnhancer;
