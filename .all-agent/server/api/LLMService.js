const EventEmitter = require('events');
const axios = require('axios');
const MockLLMService = require('../core/MockLLMService');
require('dotenv').config();

/**
 * LLM API 服务
 * 统一管理各种 LLM 提供商的 API 调用
 */
class LLMService extends EventEmitter {
  constructor(logger) {
    super();
    this.providers = new Map();
    this.defaultProvider = null;
    this.rateLimits = new Map();
    this.requestQueue = [];
    this.isProcessingQueue = false;
    this.logger = logger || console;

    // 初始化模拟服务
    this.mockService = new MockLLMService(this.logger);

    // 配置
    this.config = {
      maxRetries: 3,
      retryDelay: 1000,
      timeout: 30000,
      rateLimit: {
        requestsPerMinute: 60,
        tokensPerHour: 100000
      }
    };

    // HTTP 客户端配置
    this.httpClient = axios.create({
      timeout: this.config.timeout,
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'All-Agent/1.0.0'
      }
    });
  }

  /**
   * 初始化 LLM 服务
   */
  async initialize() {
    try {
      // 注册支持的提供商
      this.registerProviders();

      // 设置默认提供商
      this.setDefaultProvider();

      // 启动请求队列处理
      this.startQueueProcessor();

      console.log('✅ LLM 服务初始化完成');
    } catch (error) {
      console.error('❌ LLM 服务初始化失败:', error);
      throw error;
    }
  }

  /**
   * 注册 LLM 提供商
   */
  registerProviders() {
    // Anthropic Claude
    this.providers.set('anthropic', {
      name: 'Anthropic Claude',
      apiKey: process.env.ANTHROPIC_API_KEY,
      baseUrl: process.env.ANTHROPIC_BASE_URL || 'https://api.anthropic.com/v1',
      models: ['claude-3-sonnet-20240229', 'claude-3-haiku-20240307'],
      maxTokens: 4096,
      available: !!process.env.ANTHROPIC_API_KEY
    });

    // OpenAI GPT
    this.providers.set('openai', {
      name: 'OpenAI GPT',
      apiKey: process.env.OPENAI_API_KEY,
      baseUrl: process.env.OPENAI_BASE_URL || 'https://api.openai.com/v1',
      models: ['gpt-4', 'gpt-3.5-turbo', 'gpt-4-turbo-preview'],
      maxTokens: 4096,
      available: !!process.env.OPENAI_API_KEY
    });

    // Google Gemini
    this.providers.set('google', {
      name: 'Google Gemini',
      apiKey: process.env.GOOGLE_API_KEY,
      baseUrl: process.env.GOOGLE_BASE_URL || 'https://generativelanguage.googleapis.com/v1',
      models: ['gemini-pro', 'gemini-pro-vision'],
      maxTokens: 2048,
      available: !!process.env.GOOGLE_API_KEY
    });

    // Mistral AI
    this.providers.set('mistral', {
      name: 'Mistral AI',
      apiKey: process.env.MISTRAL_API_KEY,
      baseUrl: process.env.MISTRAL_API_BASE || 'https://api.mistral.ai/v1',
      models: [process.env.MISTRAL_MODEL || 'mistral-small-latest', 'mistral-medium-latest'],
      maxTokens: 4096,
      available: !!process.env.MISTRAL_API_KEY
    });

    // DeepSeek
    this.providers.set('deepseek', {
      name: 'DeepSeek',
      apiKey: process.env.DEEPSEEK_API_KEY,
      baseUrl: process.env.DEEPSEEK_API_BASE || 'https://api.deepseek.com/v1',
      models: ['deepseek-chat', 'deepseek-coder'],
      maxTokens: 4096,
      available: !!process.env.DEEPSEEK_API_KEY
    });

    // 本地模型（如 Ollama）
    this.providers.set('local', {
      name: 'Local Model',
      baseUrl: process.env.LOCAL_LLM_URL || 'http://localhost:11434',
      models: ['llama2', 'codellama', 'mistral'],
      maxTokens: 2048,
      available: true // 假设本地服务可用
    });
  }

  /**
   * 设置默认提供商
   */
  setDefaultProvider() {
    // 从环境变量获取默认提供商
    const envDefault = process.env.DEFAULT_LLM_PROVIDER;
    if (envDefault && this.providers.get(envDefault)?.available) {
      this.defaultProvider = envDefault;
      console.log(`🎯 默认 LLM 提供商 (环境变量): ${this.providers.get(envDefault).name}`);
      return;
    }

    // 按优先级选择可用的提供商
    const priorities = ['openai', 'deepseek', 'mistral', 'google', 'anthropic', 'local'];

    for (const provider of priorities) {
      if (this.providers.get(provider)?.available) {
        this.defaultProvider = provider;
        console.log(`🎯 默认 LLM 提供商: ${this.providers.get(provider).name}`);
        break;
      }
    }

    if (!this.defaultProvider) {
      console.warn('⚠️ 没有可用的 LLM 提供商，将使用模拟模式');
      this.defaultProvider = 'mock';
    }
  }

  /**
   * 发送聊天请求
   */
  async chat(messages, options = {}) {
    const provider = options.provider || this.defaultProvider;
    const model = options.model || this.getDefaultModel(provider);

    if (!provider) {
      throw new Error('没有可用的 LLM 提供商');
    }

    const request = {
      id: this.generateRequestId(),
      provider,
      model,
      messages,
      options,
      timestamp: Date.now(),
      retries: 0
    };

    return this.processRequest(request);
  }

  /**
   * 处理请求
   */
  async processRequest(request) {
    try {
      // 检查速率限制
      await this.checkRateLimit(request.provider);

      // 根据提供商调用相应的 API
      const response = await this.callProviderAPI(request);

      // 记录成功请求
      this.recordRequest(request.provider, true);

      return response;
    } catch (error) {
      console.error(`LLM 请求失败 (${request.provider}):`, error.message);

      // 记录失败请求
      this.recordRequest(request.provider, false);

      // 重试逻辑
      if (request.retries < this.config.maxRetries) {
        request.retries++;
        console.log(`重试请求 ${request.id} (${request.retries}/${this.config.maxRetries})`);

        // 延迟重试
        await this.delay(this.config.retryDelay * request.retries);
        return this.processRequest(request);
      }

      throw error;
    }
  }

  /**
   * 调用提供商 API
   */
  async callProviderAPI(request) {
    const { provider, model, messages, options } = request;
    const providerConfig = this.providers.get(provider);

    switch (provider) {
      case 'anthropic':
        return this.callAnthropicAPI(providerConfig, model, messages, options);
      case 'openai':
        return this.callOpenAIAPI(providerConfig, model, messages, options);
      case 'google':
        return this.callGoogleAPI(providerConfig, model, messages, options);
      case 'mistral':
        return this.callMistralAPI(providerConfig, model, messages, options);
      case 'deepseek':
        return this.callDeepSeekAPI(providerConfig, model, messages, options);
      case 'local':
        return this.callLocalAPI(providerConfig, model, messages, options);
      default:
        throw new Error(`不支持的提供商: ${provider}`);
    }
  }

  /**
   * 调用 Anthropic API
   */
  async callAnthropicAPI(config, model, messages, options) {
    if (!config.available) {
      return this.simulateAPICall('Anthropic Claude', messages, options);
    }

    try {
      const response = await this.httpClient.post(
        `${config.baseUrl}/messages`,
        {
          model: model || 'claude-3-sonnet-20240229',
          max_tokens: options.maxTokens || 4096,
          messages: this.formatMessagesForAnthropic(messages)
        },
        {
          headers: {
            'x-api-key': config.apiKey,
            'anthropic-version': '2023-06-01'
          }
        }
      );

      return this.formatAnthropicResponse(response.data, config.name);
    } catch (error) {
      console.error('Anthropic API 调用失败:', error.response?.data || error.message);
      throw new Error(`Anthropic API 错误: ${error.response?.data?.error?.message || error.message}`);
    }
  }

  /**
   * 调用 OpenAI API
   */
  async callOpenAIAPI(config, model, messages, options) {
    if (!config.available) {
      return this.simulateAPICall('OpenAI GPT', messages, options);
    }

    try {
      const response = await this.httpClient.post(
        `${config.baseUrl}/chat/completions`,
        {
          model: model || 'gpt-3.5-turbo',
          messages: this.formatMessagesForOpenAI(messages),
          max_tokens: options.maxTokens || 4096,
          temperature: options.temperature || 0.7
        },
        {
          headers: {
            Authorization: `Bearer ${config.apiKey}`
          }
        }
      );

      return this.formatOpenAIResponse(response.data, config.name);
    } catch (error) {
      console.error('OpenAI API 调用失败:', error.response?.data || error.message);
      throw new Error(`OpenAI API 错误: ${error.response?.data?.error?.message || error.message}`);
    }
  }

  /**
   * 调用 Google API
   */
  async callGoogleAPI(config, model, messages, options) {
    if (!config.available) {
      return this.simulateAPICall('Google Gemini', messages, options);
    }

    try {
      const response = await this.httpClient.post(
        `${config.baseUrl}/models/${model || 'gemini-pro'}:generateContent?key=${config.apiKey}`,
        {
          contents: this.formatMessagesForGoogle(messages),
          generationConfig: {
            maxOutputTokens: options.maxTokens || 2048,
            temperature: options.temperature || 0.7
          }
        }
      );

      return this.formatGoogleResponse(response.data, config.name);
    } catch (error) {
      console.error('Google API 调用失败:', error.response?.data || error.message);
      throw new Error(`Google API 错误: ${error.response?.data?.error?.message || error.message}`);
    }
  }

  /**
   * 调用 Mistral API
   */
  async callMistralAPI(config, model, messages, options) {
    if (!config.available) {
      return this.simulateAPICall('Mistral AI', messages, options);
    }

    try {
      const response = await this.httpClient.post(
        `${config.baseUrl}/chat/completions`,
        {
          model: model || process.env.MISTRAL_MODEL || 'mistral-small-latest',
          messages: this.formatMessagesForOpenAI(messages), // 使用 OpenAI 格式
          max_tokens: options.maxTokens || 4096,
          temperature: options.temperature || 0.7
        },
        {
          headers: {
            Authorization: `Bearer ${config.apiKey}`
          }
        }
      );

      return this.formatOpenAIResponse(response.data, config.name);
    } catch (error) {
      console.error('Mistral API 调用失败:', error.response?.data || error.message);
      throw new Error(`Mistral API 错误: ${error.response?.data?.error?.message || error.message}`);
    }
  }

  /**
   * 调用 DeepSeek API
   */
  async callDeepSeekAPI(config, model, messages, options) {
    if (!config.available) {
      return this.simulateAPICall('DeepSeek', messages, options);
    }

    try {
      const response = await this.httpClient.post(
        `${config.baseUrl}/chat/completions`,
        {
          model: model || 'deepseek-chat',
          messages: this.formatMessagesForOpenAI(messages), // 使用 OpenAI 格式
          max_tokens: options.maxTokens || 4096,
          temperature: options.temperature || 0.7
        },
        {
          headers: {
            Authorization: `Bearer ${config.apiKey}`
          }
        }
      );

      return this.formatOpenAIResponse(response.data, config.name);
    } catch (error) {
      console.error('DeepSeek API 调用失败:', error.response?.data || error.message);
      throw new Error(`DeepSeek API 错误: ${error.response?.data?.error?.message || error.message}`);
    }
  }

  /**
   * 调用本地 API
   */
  async callLocalAPI(config, model, messages, options) {
    try {
      const response = await this.httpClient.post(`${config.baseUrl}/api/chat`, {
        model: model || 'llama2',
        messages: this.formatMessagesForOpenAI(messages),
        stream: false
      });

      return this.formatLocalResponse(response.data, config.name);
    } catch (error) {
      console.warn('本地 LLM 不可用，使用模拟响应:', error.message);
      return this.simulateAPICall('Local Model', messages, options);
    }
  }

  /**
   * 格式化消息 - Anthropic 格式
   */
  formatMessagesForAnthropic(messages) {
    return messages.map((msg) => {
      if (typeof msg === 'string') {
        return { role: 'user', content: msg };
      }
      return {
        role: msg.role === 'assistant' ? 'assistant' : 'user',
        content: msg.content || msg.message || ''
      };
    });
  }

  /**
   * 格式化消息 - OpenAI 格式
   */
  formatMessagesForOpenAI(messages) {
    return messages.map((msg) => {
      if (typeof msg === 'string') {
        return { role: 'user', content: msg };
      }
      return {
        role: msg.role || 'user',
        content: msg.content || msg.message || ''
      };
    });
  }

  /**
   * 格式化消息 - Google 格式
   */
  formatMessagesForGoogle(messages) {
    const lastMessage = messages[messages.length - 1];
    const content = typeof lastMessage === 'string' ? lastMessage : lastMessage.content || lastMessage.message || '';

    return [
      {
        parts: [{ text: content }]
      }
    ];
  }

  /**
   * 格式化 Anthropic 响应
   */
  formatAnthropicResponse(data, providerName) {
    return {
      provider: providerName,
      model: data.model,
      message: data.content[0]?.text || '',
      usage: {
        promptTokens: data.usage?.input_tokens || 0,
        completionTokens: data.usage?.output_tokens || 0,
        totalTokens: (data.usage?.input_tokens || 0) + (data.usage?.output_tokens || 0)
      },
      timestamp: new Date().toISOString()
    };
  }

  /**
   * 格式化 OpenAI 响应
   */
  formatOpenAIResponse(data, providerName) {
    return {
      provider: providerName,
      model: data.model,
      message: data.choices[0]?.message?.content || '',
      usage: {
        promptTokens: data.usage?.prompt_tokens || 0,
        completionTokens: data.usage?.completion_tokens || 0,
        totalTokens: data.usage?.total_tokens || 0
      },
      timestamp: new Date().toISOString()
    };
  }

  /**
   * 格式化 Google 响应
   */
  formatGoogleResponse(data, providerName) {
    return {
      provider: providerName,
      model: 'gemini-pro',
      message: data.candidates[0]?.content?.parts[0]?.text || '',
      usage: {
        promptTokens: data.usageMetadata?.promptTokenCount || 0,
        completionTokens: data.usageMetadata?.candidatesTokenCount || 0,
        totalTokens: data.usageMetadata?.totalTokenCount || 0
      },
      timestamp: new Date().toISOString()
    };
  }

  /**
   * 格式化本地模型响应
   */
  formatLocalResponse(data, providerName) {
    return {
      provider: providerName,
      model: data.model || 'local',
      message: data.message?.content || data.response || '',
      usage: {
        promptTokens: 0,
        completionTokens: 0,
        totalTokens: 0
      },
      timestamp: new Date().toISOString()
    };
  }

  /**
   * 模拟 API 调用（用于演示）
   */
  async simulateAPICall(providerName, messages, options) {
    // 模拟网络延迟
    await this.delay(500 + Math.random() * 1000);

    const lastMessage = messages[messages.length - 1];
    const userMessage = lastMessage.content || lastMessage.message || '';

    // 生成模拟响应
    const responses = this.generateMockResponse(userMessage, providerName);
    const response = responses[Math.floor(Math.random() * responses.length)];

    return {
      provider: providerName,
      model: options.model || 'default',
      message: response,
      usage: {
        promptTokens: Math.floor(userMessage.length / 4),
        completionTokens: Math.floor(response.length / 4),
        totalTokens: Math.floor((userMessage.length + response.length) / 4)
      },
      timestamp: new Date().toISOString()
    };
  }

  /**
   * 生成模拟响应
   */
  generateMockResponse(userMessage, provider) {
    const message = userMessage.toLowerCase();

    if (message.includes('分析') || message.includes('analyze')) {
      return [
        `我正在使用 ${provider} 分析您的项目。根据初步扫描，我发现了以下特点：\n\n1. 项目结构清晰，模块化程度较高\n2. 使用了现代化的技术栈\n3. 代码质量良好，遵循最佳实践\n\n需要我进行更详细的分析吗？`,
        `通过 ${provider} 的分析能力，我识别出这是一个 ${this.getRandomTechStack()} 项目。项目包含 ${Math.floor(
          Math.random() * 50 + 10
        )} 个文件，总代码行数约 ${Math.floor(Math.random() * 5000 + 1000)} 行。建议优化的方面包括性能优化和代码重构。`
      ];
    }

    if (message.includes('生成') || message.includes('generate') || message.includes('创建')) {
      return [
        `我将使用 ${provider} 为您生成代码。基于您的需求，我建议创建以下文件结构：\n\n\`\`\`\nsrc/\n  components/\n  utils/\n  styles/\n\`\`\`\n\n请告诉我具体需要生成什么类型的代码？`,
        `使用 ${provider} 的代码生成能力，我可以为您创建高质量的代码。请提供更多细节，比如编程语言、框架选择和具体功能需求。`
      ];
    }

    if (message.includes('规划') || message.includes('plan')) {
      return [
        `基于 ${provider} 的规划能力，我为您制定了以下开发计划：\n\n**阶段一**: 项目初始化和基础架构\n**阶段二**: 核心功能开发\n**阶段三**: 测试和优化\n**阶段四**: 部署和维护\n\n每个阶段预计需要 1-2 周时间。`,
        `通过 ${provider} 分析，我建议采用敏捷开发方法。将项目分为 ${Math.floor(
          Math.random() * 5 + 3
        )} 个迭代周期，每个周期专注于特定功能模块的开发和测试。`
      ];
    }

    // 默认响应
    return [
      `我是由 ${provider} 驱动的 AI 助手，专门帮助您进行项目开发。我可以协助您进行项目分析、代码生成、架构设计等任务。请告诉我您需要什么帮助？`,
      `感谢您使用 All-Agent 系统！我正在使用 ${provider} 来理解您的需求。请提供更多详细信息，这样我就能为您提供更精准的帮助。`,
      `基于 ${provider} 的强大能力，我可以帮您解决各种开发问题。无论是代码编写、项目规划还是技术选型，我都能提供专业建议。`
    ];
  }

  /**
   * 获取随机技术栈
   */
  getRandomTechStack() {
    const stacks = [
      'React + TypeScript',
      'Vue.js + JavaScript',
      'Angular + TypeScript',
      'Node.js + Express',
      'Python + Django',
      'Java + Spring Boot',
      'Go + Gin',
      'Rust + Actix'
    ];
    return stacks[Math.floor(Math.random() * stacks.length)];
  }

  /**
   * 检查速率限制
   */
  async checkRateLimit(provider) {
    const now = Date.now();
    const limit = this.rateLimits.get(provider) || { requests: [], tokens: 0, lastReset: now };

    // 清理过期的请求记录
    limit.requests = limit.requests.filter((time) => now - time < 60000); // 1分钟

    // 检查请求频率限制
    if (limit.requests.length >= this.config.rateLimit.requestsPerMinute) {
      throw new Error(`${provider} 请求频率超限，请稍后再试`);
    }

    // 记录当前请求
    limit.requests.push(now);
    this.rateLimits.set(provider, limit);
  }

  /**
   * 记录请求统计
   */
  recordRequest(provider, success) {
    this.emit('request_completed', {
      provider,
      success,
      timestamp: Date.now()
    });
  }

  /**
   * 获取默认模型
   */
  getDefaultModel(provider) {
    const config = this.providers.get(provider);
    return config?.models?.[0] || 'default';
  }

  /**
   * 生成请求 ID
   */
  generateRequestId() {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 延迟函数
   */
  delay(ms) {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  /**
   * 启动队列处理器
   */
  startQueueProcessor() {
    setInterval(() => {
      if (!this.isProcessingQueue && this.requestQueue.length > 0) {
        this.processQueue();
      }
    }, 100);
  }

  /**
   * 处理请求队列
   */
  async processQueue() {
    this.isProcessingQueue = true;

    while (this.requestQueue.length > 0) {
      const request = this.requestQueue.shift();
      try {
        await this.processRequest(request);
      } catch (error) {
        console.error('队列请求处理失败:', error);
      }
    }

    this.isProcessingQueue = false;
  }

  /**
   * 获取提供商状态
   */
  getProviderStatus() {
    const status = {};

    for (const [name, config] of this.providers.entries()) {
      const limit = this.rateLimits.get(name) || { requests: [], tokens: 0 };

      status[name] = {
        name: config.name,
        available: config.available,
        models: config.models,
        currentRequests: limit.requests.length,
        maxRequests: this.config.rateLimit.requestsPerMinute,
        isDefault: name === this.defaultProvider
      };
    }

    return status;
  }

  /**
   * 切换默认提供商
   */
  switchProvider(provider) {
    if (!this.providers.has(provider)) {
      throw new Error(`提供商不存在: ${provider}`);
    }

    if (!this.providers.get(provider).available) {
      throw new Error(`提供商不可用: ${provider}`);
    }

    this.defaultProvider = provider;
    console.log(`🔄 切换到提供商: ${this.providers.get(provider).name}`);
  }

  /**
   * 获取统计信息
   */
  getStatistics() {
    const stats = {
      totalProviders: this.providers.size,
      availableProviders: 0,
      defaultProvider: this.defaultProvider,
      queueLength: this.requestQueue.length
    };

    for (const config of this.providers.values()) {
      if (config.available) {
        stats.availableProviders++;
      }
    }

    return stats;
  }

  /**
   * 简化的生成响应方法 - 用于 Agent 集成
   */
  async generateResponse(prompt, options = {}) {
    try {
      // 如果没有可用的提供商，使用模拟服务
      if (!this.defaultProvider || this.defaultProvider === 'mock') {
        this.logger.info('使用模拟 LLM 服务生成响应');
        return await this.mockService.generateResponse(prompt, options);
      }

      // 尝试使用真实的 LLM 服务
      const messages = [{ role: 'user', content: prompt }];
      const response = await this.chat(messages, options);

      return response.message;
    } catch (error) {
      this.logger.warn('LLM 服务调用失败，切换到模拟服务', { error: error.message });

      // 如果真实服务失败，回退到模拟服务
      return await this.mockService.generateResponse(prompt, {
        ...options,
        fallback: true,
        originalError: error.message
      });
    }
  }
}

module.exports = LLMService;
