module.exports = {
  env: {
    browser: false,
    es2021: true,
    node: true,
    jest: true
  },
  extends: [
    'standard'
  ],
  parserOptions: {
    ecmaVersion: 'latest',
    sourceType: 'module'
  },
  rules: {
    // 允许 console 输出
    'no-console': 'off',
    
    // 允许未使用的变量（以下划线开头）
    'no-unused-vars': ['error', { 
      argsIgnorePattern: '^_',
      varsIgnorePattern: '^_'
    }],
    
    // 允许空的 catch 块
    'no-empty': ['error', { allowEmptyCatch: true }],
    
    // 要求使用 === 和 !==
    'eqeqeq': ['error', 'always'],
    
    // 禁止使用 var
    'no-var': 'error',
    
    // 优先使用 const
    'prefer-const': 'error',
    
    // 要求箭头函数的参数使用圆括号
    'arrow-parens': ['error', 'always'],
    
    // 要求箭头函数体使用大括号
    'arrow-body-style': ['error', 'as-needed'],
    
    // 要求对象字面量属性名称使用引号
    'quote-props': ['error', 'as-needed'],
    
    // 要求使用模板字面量而非字符串连接
    'prefer-template': 'error',
    
    // 要求回调函数使用箭头函数
    'prefer-arrow-callback': 'error',
    
    // 禁止不必要的分号
    'no-extra-semi': 'error',
    
    // 要求或禁止末尾逗号
    'comma-dangle': ['error', 'never'],
    
    // 强制使用一致的缩进
    'indent': ['error', 2, { 
      SwitchCase: 1,
      VariableDeclarator: 1,
      outerIIFEBody: 1
    }],
    
    // 强制使用一致的换行符风格
    'linebreak-style': ['error', 'unix'],
    
    // 强制使用一致的引号风格
    'quotes': ['error', 'single', { avoidEscape: true }],
    
    // 要求或禁止使用分号
    'semi': ['error', 'always'],
    
    // 强制在逗号前后使用一致的空格
    'comma-spacing': ['error', { before: false, after: true }],
    
    // 强制在对象字面量的属性中键和值之间使用一致的间距
    'key-spacing': ['error', { beforeColon: false, afterColon: true }],
    
    // 强制在关键字前后使用一致的空格
    'keyword-spacing': ['error', { before: true, after: true }],
    
    // 强制一行的最大长度
    'max-len': ['warn', { 
      code: 120,
      tabWidth: 2,
      ignoreUrls: true,
      ignoreStrings: true,
      ignoreTemplateLiterals: true
    }],
    
    // 强制函数中的变量要么一起声明要么分开声明
    'one-var': ['error', 'never'],
    
    // 要求或禁止在变量声明周围换行
    'one-var-declaration-per-line': ['error', 'always'],
    
    // 强制操作符使用一致的换行符
    'operator-linebreak': ['error', 'before'],
    
    // 要求或禁止块内填充
    'padded-blocks': ['error', 'never'],
    
    // 要求对象字面量属性名称用引号括起来
    'object-curly-spacing': ['error', 'always'],
    
    // 强制数组方括号中使用一致的空格
    'array-bracket-spacing': ['error', 'never'],
    
    // 强制在计算的属性的方括号中使用一致的空格
    'computed-property-spacing': ['error', 'never'],
    
    // 强制在函数标识符和其调用之间有空格
    'func-call-spacing': ['error', 'never'],
    
    // 要求构造函数首字母大写
    'new-cap': ['error', { 
      newIsCap: true,
      capIsNew: false,
      properties: true
    }],
    
    // 强制或禁止调用无参构造函数时有圆括号
    'new-parens': 'error',
    
    // 禁用 alert、confirm 和 prompt
    'no-alert': 'error',
    
    // 禁止使用 Array 构造函数
    'no-array-constructor': 'error',
    
    // 禁止使用 arguments.caller 或 arguments.callee
    'no-caller': 'error',
    
    // 禁止修改类声明的变量
    'no-class-assign': 'error',
    
    // 禁止修改 const 声明的变量
    'no-const-assign': 'error',
    
    // 禁止与 -0 进行比较
    'no-compare-neg-zero': 'error',
    
    // 禁止条件表达式中出现赋值操作符
    'no-cond-assign': ['error', 'always'],
    
    // 禁用 debugger
    'no-debugger': 'error',
    
    // 禁止 function 定义中出现重名参数
    'no-dupe-args': 'error',
    
    // 禁止类成员中出现重复的名称
    'no-dupe-class-members': 'error',
    
    // 禁止对象字面量中出现重复的 key
    'no-dupe-keys': 'error',
    
    // 禁止重复的 case 标签
    'no-duplicate-case': 'error',
    
    // 禁用 eval()
    'no-eval': 'error',
    
    // 禁止扩展原生类型
    'no-extend-native': 'error',
    
    // 禁止不必要的 .bind() 调用
    'no-extra-bind': 'error',
    
    // 禁止不必要的布尔转换
    'no-extra-boolean-cast': 'error',
    
    // 禁止不必要的括号
    'no-extra-parens': ['error', 'functions'],
    
    // 禁止 case 语句落空
    'no-fallthrough': 'error',
    
    // 禁止数字字面量中使用前导和末尾小数点
    'no-floating-decimal': 'error',
    
    // 禁止对全局对象的属性进行赋值
    'no-global-assign': 'error',
    
    // 禁用隐式的eval()
    'no-implied-eval': 'error',
    
    // 禁止在嵌套的块中出现变量声明或 function 声明
    'no-inner-declarations': 'error',
    
    // 禁止 RegExp 构造函数中存在无效的正则表达式字符串
    'no-invalid-regexp': 'error',
    
    // 禁止在字符串和注释之外不规则的空白
    'no-irregular-whitespace': 'error',
    
    // 禁用 __iterator__ 属性
    'no-iterator': 'error',
    
    // 禁用标签语句
    'no-labels': 'error',
    
    // 禁用不必要的嵌套块
    'no-lone-blocks': 'error',
    
    // 禁止在循环语句中出现包含不安全引用的函数声明
    'no-loop-func': 'error',
    
    // 禁止使用多行字符串
    'no-multi-str': 'error',
    
    // 禁止使用 Object 的构造函数
    'no-new-object': 'error',
    
    // 禁止使用 new 以避免产生副作用
    'no-new': 'error',
    
    // 禁止使用 Function 构造函数
    'no-new-func': 'error',
    
    // 禁止对 String，Number 和 Boolean 使用 new 操作符
    'no-new-wrappers': 'error',
    
    // 禁止把全局对象作为函数调用
    'no-obj-calls': 'error',
    
    // 禁用八进制字面量
    'no-octal': 'error',
    
    // 禁止在字符串中使用八进制转义序列
    'no-octal-escape': 'error',
    
    // 禁用 __proto__ 属性
    'no-proto': 'error',
    
    // 禁止多次声明同一变量
    'no-redeclare': 'error',
    
    // 禁止正则表达式字面量中出现多个空格
    'no-regex-spaces': 'error',
    
    // 禁用稀疏数组
    'no-sparse-arrays': 'error',
    
    // 禁止在 return、throw、continue 和 break 语句之后出现不可达代码
    'no-unreachable': 'error',
    
    // 要求使用 isNaN() 检查 NaN
    'use-isnan': 'error',
    
    // 强制 typeof 表达式与有效的字符串进行比较
    'valid-typeof': ['error', { requireStringLiterals: true }]
  },
  
  // 全局变量
  globals: {
    // Node.js 全局变量
    global: 'readonly',
    process: 'readonly',
    Buffer: 'readonly',
    __dirname: 'readonly',
    __filename: 'readonly',
    
    // 测试框架全局变量
    describe: 'readonly',
    it: 'readonly',
    test: 'readonly',
    expect: 'readonly',
    beforeEach: 'readonly',
    afterEach: 'readonly',
    beforeAll: 'readonly',
    afterAll: 'readonly',
    jest: 'readonly'
  },
  
  // 忽略的文件和目录
  ignorePatterns: [
    'node_modules/',
    'coverage/',
    'dist/',
    'build/',
    '*.min.js',
    'logs/',
    'data/',
    'backups/',
    'reports/'
  ]
};
