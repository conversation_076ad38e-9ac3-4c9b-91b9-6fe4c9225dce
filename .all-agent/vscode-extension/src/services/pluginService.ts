import * as vscode from 'vscode';

/**
 * 插件接口定义
 */
export interface IAllAgentPlugin {
    name: string;
    version: string;
    description: string;

    // 生命周期方法
    activate(context: vscode.ExtensionContext): Promise<void>;
    deactivate(): Promise<void>;

    // 功能方法
    handleMessage?(message: string, context: any): Promise<string | null>;
    getCommands?(): Array<{
        id: string;
        title: string;
        handler: (...args: any[]) => Promise<any>;
    }>;
    getContextMenuItems?(): Array<{
        id: string;
        title: string;
        when?: string;
        handler: (...args: any[]) => Promise<any>;
    }>;
}

/**
 * 插件服务 - 提供插件系统和扩展能力
 */
export class PluginService {
    private plugins: Map<string, IAllAgentPlugin> = new Map();
    private pluginConfigs: Map<string, any> = new Map();
    private hooks: Map<string, Array<(...args: any[]) => any>> = new Map();

    constructor(private context: vscode.ExtensionContext) {}

    /**
     * 注册插件
     */
    async registerPlugin(plugin: IAllAgentPlugin): Promise<boolean> {
        try {
            if (this.plugins.has(plugin.name)) {
                console.warn(`Plugin ${plugin.name} is already registered`);
                return false;
            }

            // 激活插件
            await plugin.activate(this.context);

            // 注册插件
            this.plugins.set(plugin.name, plugin);

            // 注册命令
            const commands = plugin.getCommands?.() || [];
            for (const command of commands) {
                const disposable = vscode.commands.registerCommand(
                    `all-agent.plugin.${plugin.name}.${command.id}`,
                    command.handler
                );
                this.context.subscriptions.push(disposable);
            }

            console.log(`Plugin ${plugin.name} v${plugin.version} registered successfully`);
            return true;
        } catch (error) {
            console.error(`Failed to register plugin ${plugin.name}:`, error);
            return false;
        }
    }

    /**
     * 卸载插件
     */
    async unregisterPlugin(pluginName: string): Promise<boolean> {
        try {
            const plugin = this.plugins.get(pluginName);
            if (!plugin) {
                return false;
            }

            // 停用插件
            await plugin.deactivate();

            // 移除插件
            this.plugins.delete(pluginName);
            this.pluginConfigs.delete(pluginName);

            console.log(`Plugin ${pluginName} unregistered successfully`);
            return true;
        } catch (error) {
            console.error(`Failed to unregister plugin ${pluginName}:`, error);
            return false;
        }
    }

    /**
     * 获取已注册的插件列表
     */
    getRegisteredPlugins(): Array<{
        name: string;
        version: string;
        description: string;
        active: boolean;
    }> {
        return Array.from(this.plugins.values()).map(plugin => ({
            name: plugin.name,
            version: plugin.version,
            description: plugin.description,
            active: true
        }));
    }

    /**
     * 处理消息（通过所有插件）
     */
    async handleMessage(message: string, context: any): Promise<string | null> {
        for (const plugin of this.plugins.values()) {
            if (plugin.handleMessage) {
                try {
                    const result = await plugin.handleMessage(message, context);
                    if (result) {
                        return result;
                    }
                } catch (error) {
                    console.error(`Plugin ${plugin.name} message handling error:`, error);
                }
            }
        }
        return null;
    }

    /**
     * 注册钩子
     */
    registerHook(hookName: string, handler: (...args: any[]) => any): void {
        if (!this.hooks.has(hookName)) {
            this.hooks.set(hookName, []);
        }
        this.hooks.get(hookName)!.push(handler);
    }

    /**
     * 触发钩子
     */
    async triggerHook(hookName: string, ...args: any[]): Promise<any[]> {
        const handlers = this.hooks.get(hookName) || [];
        const results: any[] = [];

        for (const handler of handlers) {
            try {
                const result = await handler(...args);
                results.push(result);
            } catch (error) {
                console.error(`Hook ${hookName} handler error:`, error);
                results.push(null);
            }
        }

        return results;
    }

    /**
     * 设置插件配置
     */
    setPluginConfig(pluginName: string, config: any): void {
        this.pluginConfigs.set(pluginName, config);
    }

    /**
     * 获取插件配置
     */
    getPluginConfig(pluginName: string): any {
        return this.pluginConfigs.get(pluginName) || {};
    }

    /**
     * 创建自定义命令
     */
    createCustomCommand(id: string, title: string, handler: (...args: any[]) => any): vscode.Disposable {
        const disposable = vscode.commands.registerCommand(`all-agent.custom.${id}`, handler);
        this.context.subscriptions.push(disposable);
        return disposable;
    }

    /**
     * 创建自定义视图
     */
    createCustomView(viewId: string, title: string, provider: vscode.WebviewViewProvider): vscode.Disposable {
        const disposable = vscode.window.registerWebviewViewProvider(`all-agent.custom.${viewId}`, provider);
        this.context.subscriptions.push(disposable);
        return disposable;
    }

    /**
     * 清理资源
     */
    async dispose(): Promise<void> {
        // 停用所有插件
        for (const plugin of this.plugins.values()) {
            try {
                await plugin.deactivate();
            } catch (error) {
                console.error(`Error deactivating plugin ${plugin.name}:`, error);
            }
        }

        this.plugins.clear();
        this.pluginConfigs.clear();
        this.hooks.clear();
    }
}

/**
 * 内置插件示例：Git 集成
 */
export class GitPlugin implements IAllAgentPlugin {
    name = 'git-integration';
    version = '1.0.0';
    description = 'Git integration for All-Agent';

    async activate(context: vscode.ExtensionContext): Promise<void> {
        console.log('Git plugin activated');
    }

    async deactivate(): Promise<void> {
        console.log('Git plugin deactivated');
    }

    async handleMessage(message: string, context: any): Promise<string | null> {
        const lowerMessage = message.toLowerCase();

        if (lowerMessage.includes('git status')) {
            return await this.getGitStatus();
        }

        if (lowerMessage.includes('git commit')) {
            return await this.handleGitCommit(message);
        }

        return null;
    }

    getCommands() {
        return [
            {
                id: 'status',
                title: 'Git Status',
                handler: () => this.getGitStatus()
            },
            {
                id: 'commit',
                title: 'Quick Commit',
                handler: () => this.quickCommit()
            }
        ];
    }

    private async getGitStatus(): Promise<string> {
        try {
            const gitExtension = vscode.extensions.getExtension('vscode.git');
            if (!gitExtension) {
                return '❌ Git extension not found';
            }

            // 这里可以集成 Git API
            return '📊 **Git Status**\n\nNo changes detected.';
        } catch (error) {
            return `❌ Git status error: ${error}`;
        }
    }

    private async handleGitCommit(message: string): Promise<string> {
        const commitMatch = message.match(/commit\s+"([^"]+)"/i);
        const commitMessage = commitMatch ? commitMatch[1] : 'Auto commit from All-Agent';

        return `✅ **Git Commit**\n\nCommit message: "${commitMessage}"\n\n*Note: This is a simulation. Actual Git integration would require Git API.*`;
    }

    private async quickCommit(): Promise<string> {
        const message = await vscode.window.showInputBox({
            prompt: 'Enter commit message',
            placeHolder: 'feat: add new feature'
        });

        if (!message) {
            return '❌ Commit cancelled';
        }

        return `✅ **Quick Commit**\n\nCommit message: "${message}"\n\n*Note: This is a simulation.*`;
    }
}

/**
 * 内置插件示例：代码片段管理
 */
export class SnippetPlugin implements IAllAgentPlugin {
    name = 'snippet-manager';
    version = '1.0.0';
    description = 'Code snippet management for All-Agent';

    private snippets: Map<string, string> = new Map();

    async activate(context: vscode.ExtensionContext): Promise<void> {
        // 加载默认代码片段
        this.loadDefaultSnippets();
        console.log('Snippet plugin activated');
    }

    async deactivate(): Promise<void> {
        console.log('Snippet plugin deactivated');
    }

    async handleMessage(message: string, context: any): Promise<string | null> {
        const lowerMessage = message.toLowerCase();

        if (lowerMessage.includes('snippet') && lowerMessage.includes('list')) {
            return this.listSnippets();
        }

        if (lowerMessage.includes('snippet') && lowerMessage.includes('create')) {
            return this.createSnippetPrompt();
        }

        return null;
    }

    getCommands() {
        return [
            {
                id: 'list',
                title: 'List Snippets',
                handler: async () => this.listSnippets()
            },
            {
                id: 'create',
                title: 'Create Snippet',
                handler: async () => this.createSnippetPrompt()
            }
        ];
    }

    private loadDefaultSnippets(): void {
        this.snippets.set('react-component', `import React from 'react';

const Component = () => {
  return (
    <div>
      <h1>Hello World</h1>
    </div>
  );
};

export default Component;`);

        this.snippets.set('async-function', `async function fetchData() {
  try {
    const response = await fetch('/api/data');
    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error fetching data:', error);
    throw error;
  }
}`);
    }

    private listSnippets(): string {
        let response = '📝 **Available Snippets**\n\n';

        if (this.snippets.size === 0) {
            response += 'No snippets available.';
        } else {
            Array.from(this.snippets.keys()).forEach((key, index) => {
                response += `${index + 1}. **${key}**\n`;
            });
        }

        return response;
    }

    private createSnippetPrompt(): string {
        return '📝 **Create Snippet**\n\nTo create a snippet, use the format:\n`create snippet "name" with content "your code here"`';
    }
}
