"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TerminalService = void 0;
const vscode = require("vscode");
const child_process_1 = require("child_process");
/**
 * 终端服务 - 提供完整的终端操作能力
 */
class TerminalService {
    constructor() {
        this.terminals = new Map();
        this.processes = new Map();
        this.outputBuffers = new Map();
        // 监听终端关闭事件
        vscode.window.onDidCloseTerminal((terminal) => {
            this.cleanupTerminal(terminal);
        });
    }
    /**
     * 执行命令并返回结果
     */
    async executeCommand(command, options = {}) {
        const { cwd = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath, timeout = 30000, showOutput = false, terminalName = 'All-Agent' } = options;
        return new Promise((resolve) => {
            const processId = `cmd_${Date.now()}`;
            let output = '';
            let errorOutput = '';
            // 创建子进程
            const process = (0, child_process_1.spawn)('sh', ['-c', command], {
                cwd,
                stdio: ['pipe', 'pipe', 'pipe']
            });
            this.processes.set(processId, process);
            // 收集输出
            process.stdout?.on('data', (data) => {
                const text = data.toString();
                output += text;
                if (showOutput) {
                    this.appendToTerminal(terminalName, text);
                }
            });
            process.stderr?.on('data', (data) => {
                const text = data.toString();
                errorOutput += text;
                if (showOutput) {
                    this.appendToTerminal(terminalName, text);
                }
            });
            // 处理进程结束
            process.on('close', (code) => {
                this.processes.delete(processId);
                resolve({
                    success: code === 0,
                    output: output.trim(),
                    error: errorOutput.trim() || undefined,
                    exitCode: code || undefined
                });
            });
            process.on('error', (error) => {
                this.processes.delete(processId);
                resolve({
                    success: false,
                    output: '',
                    error: error.message
                });
            });
            // 设置超时
            setTimeout(() => {
                if (this.processes.has(processId)) {
                    process.kill('SIGTERM');
                    this.processes.delete(processId);
                    resolve({
                        success: false,
                        output: output.trim(),
                        error: 'Command timeout'
                    });
                }
            }, timeout);
        });
    }
    /**
     * 在指定终端中执行命令
     */
    async executeInTerminal(command, terminalName = 'All-Agent') {
        let terminal = this.terminals.get(terminalName);
        if (!terminal) {
            terminal = vscode.window.createTerminal({
                name: terminalName,
                cwd: vscode.workspace.workspaceFolders?.[0]?.uri.fsPath
            });
            this.terminals.set(terminalName, terminal);
        }
        terminal.show();
        terminal.sendText(command);
    }
    /**
     * 获取终端输出
     */
    getTerminalOutput(terminalName) {
        return this.outputBuffers.get(terminalName) || '';
    }
    /**
     * 清空终端输出缓冲区
     */
    clearTerminalOutput(terminalName) {
        this.outputBuffers.delete(terminalName);
    }
    /**
     * 向终端追加文本
     */
    appendToTerminal(terminalName, text) {
        const current = this.outputBuffers.get(terminalName) || '';
        this.outputBuffers.set(terminalName, current + text);
        // 限制缓冲区大小
        const buffer = this.outputBuffers.get(terminalName) || '';
        if (buffer.length > 50000) {
            this.outputBuffers.set(terminalName, buffer.slice(-40000));
        }
    }
    /**
     * 检查命令是否存在
     */
    async checkCommand(command) {
        const result = await this.executeCommand(`which ${command}`, { timeout: 5000 });
        return result.success;
    }
    /**
     * 获取系统信息
     */
    async getSystemInfo() {
        const info = {
            platform: process.platform,
            arch: process.arch
        };
        // 检查 Node.js 版本
        try {
            const nodeResult = await this.executeCommand('node --version', { timeout: 5000 });
            if (nodeResult.success) {
                info.nodeVersion = nodeResult.output;
            }
        }
        catch (error) {
            // 忽略错误
        }
        // 检查 npm 版本
        try {
            const npmResult = await this.executeCommand('npm --version', { timeout: 5000 });
            if (npmResult.success) {
                info.npmVersion = npmResult.output;
            }
        }
        catch (error) {
            // 忽略错误
        }
        // 检查 Git 版本
        try {
            const gitResult = await this.executeCommand('git --version', { timeout: 5000 });
            if (gitResult.success) {
                info.gitVersion = gitResult.output;
            }
        }
        catch (error) {
            // 忽略错误
        }
        return info;
    }
    /**
     * 智能命令解析和执行
     */
    async executeSmartCommand(userInput, options = {}) {
        const { showOutput = true, terminalName = 'All-Agent Smart' } = options;
        // 解析用户意图
        const command = this.parseUserCommand(userInput);
        if (!command) {
            return {
                success: false,
                output: '',
                error: 'Could not understand the command',
                suggestion: 'Try commands like: "install dependencies", "run tests", "start server"'
            };
        }
        return await this.executeCommand(command, {
            showOutput,
            terminalName,
            timeout: 120000
        });
    }
    /**
     * 解析用户命令
     */
    parseUserCommand(userInput) {
        const input = userInput.toLowerCase().trim();
        // 安装依赖
        if (input.includes('install') && (input.includes('dependencies') || input.includes('deps'))) {
            return 'npm install';
        }
        if (input.includes('install') && input.includes('package')) {
            const packageMatch = userInput.match(/install\s+(?:package\s+)?([a-zA-Z0-9@\-_/]+)/i);
            if (packageMatch) {
                return `npm install ${packageMatch[1]}`;
            }
        }
        // 启动服务
        if (input.includes('start') && (input.includes('server') || input.includes('dev') || input.includes('app'))) {
            return 'npm start';
        }
        if (input.includes('dev') && input.includes('server')) {
            return 'npm run dev';
        }
        // 运行测试
        if (input.includes('run') && input.includes('test')) {
            return 'npm test';
        }
        if (input.includes('test')) {
            return 'npm test';
        }
        // 构建项目
        if (input.includes('build') && (input.includes('project') || input.includes('app'))) {
            return 'npm run build';
        }
        // Git 操作
        if (input.includes('git')) {
            if (input.includes('status'))
                return 'git status';
            if (input.includes('add'))
                return 'git add .';
            if (input.includes('commit'))
                return 'git commit -m "Auto commit"';
            if (input.includes('push'))
                return 'git push';
            if (input.includes('pull'))
                return 'git pull';
        }
        // 直接命令
        if (input.startsWith('npm ') || input.startsWith('node ') || input.startsWith('git ')) {
            return userInput;
        }
        return null;
    }
    /**
     * 运行项目相关命令
     */
    async runProjectCommand(command, options = {}) {
        const { showOutput = true, terminalName = 'All-Agent Project' } = options;
        // 检查 package.json 是否存在
        const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
        if (!workspaceFolder) {
            return {
                success: false,
                output: '',
                error: 'No workspace folder found'
            };
        }
        const packageJsonPath = vscode.Uri.joinPath(workspaceFolder.uri, 'package.json');
        try {
            await vscode.workspace.fs.stat(packageJsonPath);
        }
        catch (error) {
            return {
                success: false,
                output: '',
                error: 'package.json not found in workspace'
            };
        }
        // 构建命令
        let cmd;
        switch (command) {
            case 'start':
                cmd = 'npm start';
                break;
            case 'test':
                cmd = 'npm test';
                break;
            case 'build':
                cmd = 'npm run build';
                break;
            case 'install':
                cmd = 'npm install';
                break;
            default:
                return {
                    success: false,
                    output: '',
                    error: `Unknown command: ${command}`
                };
        }
        return await this.executeCommand(cmd, {
            cwd: workspaceFolder.uri.fsPath,
            timeout: 120000,
            showOutput,
            terminalName
        });
    }
    /**
     * 获取进程列表
     */
    async getProcessList() {
        const result = await this.executeCommand('ps aux', { timeout: 10000 });
        if (!result.success) {
            return [];
        }
        const lines = result.output.split('\n').slice(1); // 跳过标题行
        const processes = [];
        for (const line of lines) {
            const parts = line.trim().split(/\s+/);
            if (parts.length >= 11) {
                processes.push({
                    pid: parseInt(parts[1]),
                    name: parts.slice(10).join(' '),
                    cpu: parts[2],
                    memory: parts[3]
                });
            }
        }
        return processes;
    }
    /**
     * 杀死进程
     */
    async killProcess(pid) {
        const result = await this.executeCommand(`kill ${pid}`, { timeout: 5000 });
        return result.success;
    }
    /**
     * 清理终端
     */
    cleanupTerminal(terminal) {
        for (const [name, term] of this.terminals.entries()) {
            if (term === terminal) {
                this.terminals.delete(name);
                this.outputBuffers.delete(name);
                break;
            }
        }
    }
    /**
     * 清理所有资源
     */
    dispose() {
        // 终止所有进程
        for (const process of this.processes.values()) {
            process.kill('SIGTERM');
        }
        this.processes.clear();
        // 清理终端
        for (const terminal of this.terminals.values()) {
            terminal.dispose();
        }
        this.terminals.clear();
        this.outputBuffers.clear();
    }
}
exports.TerminalService = TerminalService;
//# sourceMappingURL=terminalService.js.map