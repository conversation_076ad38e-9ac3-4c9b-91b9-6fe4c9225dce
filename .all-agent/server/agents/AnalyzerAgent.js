const BaseAgent = require('./BaseAgent');
const ProjectAnalyzer = require('../core/ProjectAnalyzer');

/**
 * 分析 Agent
 * 专门负责项目分析、代码理解和结构识别
 */
class AnalyzerAgent extends BaseAgent {
    constructor(config) {
        super(config);
        this.projectAnalyzer = new ProjectAnalyzer();
        this.analysisCache = new Map();
        this.cacheTimeout = 3600000; // 1小时缓存
    }

    /**
     * 初始化分析 Agent
     */
    async onInitialize() {
        this.log('info', '分析 Agent 初始化中...');
        
        // 初始化项目分析器
        // this.projectAnalyzer 已经在构造函数中创建
        
        this.log('info', '分析 Agent 初始化完成');
    }

    /**
     * 处理消息
     */
    async onProcessMessage(message, context) {
        this.log('info', '处理分析请求', { message: message.substring(0, 100) });
        
        const lowerMessage = message.toLowerCase();
        
        // 根据消息内容判断分析类型
        if (lowerMessage.includes('分析项目') || lowerMessage.includes('analyze project')) {
            return this.handleProjectAnalysisRequest(message, context);
        } else if (lowerMessage.includes('技术栈') || lowerMessage.includes('tech stack')) {
            return this.handleTechStackRequest(message, context);
        } else if (lowerMessage.includes('依赖') || lowerMessage.includes('dependency')) {
            return this.handleDependencyRequest(message, context);
        } else if (lowerMessage.includes('结构') || lowerMessage.includes('structure')) {
            return this.handleStructureRequest(message, context);
        } else {
            return this.handleGeneralAnalysisRequest(message, context);
        }
    }

    /**
     * 处理项目分析请求
     */
    async handleProjectAnalysisRequest(message, context) {
        const projectPath = context.projectPath || process.cwd();
        
        try {
            // 检查缓存
            const cacheKey = `project_analysis_${projectPath}`;
            const cached = this.getFromCache(cacheKey);
            if (cached) {
                return `基于缓存的分析结果：\n\n${this.formatAnalysisResult(cached)}`;
            }
            
            // 执行项目分析
            const analysis = await this.projectAnalyzer.analyzeProject(projectPath);
            
            // 缓存结果
            this.setCache(cacheKey, analysis);
            
            return `项目分析完成！\n\n${this.formatAnalysisResult(analysis)}`;
            
        } catch (error) {
            this.log('error', '项目分析失败', { error: error.message });
            return `项目分析失败：${error.message}`;
        }
    }

    /**
     * 处理技术栈请求
     */
    async handleTechStackRequest(message, context) {
        const projectPath = context.projectPath || process.cwd();
        
        try {
            const analysis = await this.getOrCreateAnalysis(projectPath);
            const techStack = analysis.techStack;
            
            let response = "🔍 技术栈分析结果：\n\n";
            
            // 编程语言
            if (techStack.languages && Object.keys(techStack.languages).length > 0) {
                response += "**编程语言：**\n";
                for (const [lang, info] of Object.entries(techStack.languages)) {
                    response += `- ${lang}: ${info.files} 个文件 (${this.formatFileSize(info.size)})\n`;
                }
                response += "\n";
            }
            
            // 框架
            if (techStack.frameworks && techStack.frameworks.length > 0) {
                response += "**框架：**\n";
                techStack.frameworks.forEach(framework => {
                    response += `- ${framework.name}${framework.version ? ` (${framework.version})` : ''}\n`;
                });
                response += "\n";
            }
            
            // 包管理器
            if (techStack.packageManagers && techStack.packageManagers.length > 0) {
                response += "**包管理器：**\n";
                techStack.packageManagers.forEach(pm => {
                    response += `- ${pm}\n`;
                });
                response += "\n";
            }
            
            // 工具
            if (techStack.tools && techStack.tools.length > 0) {
                response += "**开发工具：**\n";
                techStack.tools.forEach(tool => {
                    response += `- ${tool}\n`;
                });
            }
            
            return response;
            
        } catch (error) {
            this.log('error', '技术栈分析失败', { error: error.message });
            return `技术栈分析失败：${error.message}`;
        }
    }

    /**
     * 处理依赖请求
     */
    async handleDependencyRequest(message, context) {
        const projectPath = context.projectPath || process.cwd();
        
        try {
            const analysis = await this.getOrCreateAnalysis(projectPath);
            const dependencies = analysis.dependencies;
            
            let response = "🔗 依赖关系分析：\n\n";
            
            response += `- 内部依赖：${dependencies.internal.length} 个\n`;
            response += `- 外部依赖：${dependencies.external.length} 个\n`;
            response += `- 循环依赖：${dependencies.circular.length} 个\n\n`;
            
            if (dependencies.circular.length > 0) {
                response += "⚠️ 发现循环依赖，建议进行重构优化。\n";
            }
            
            return response;
            
        } catch (error) {
            this.log('error', '依赖分析失败', { error: error.message });
            return `依赖分析失败：${error.message}`;
        }
    }

    /**
     * 处理结构请求
     */
    async handleStructureRequest(message, context) {
        const projectPath = context.projectPath || process.cwd();
        
        try {
            const analysis = await this.getOrCreateAnalysis(projectPath);
            const structure = analysis.structure;
            const stats = analysis.statistics;
            
            let response = "📁 项目结构分析：\n\n";
            
            response += `**项目名称：** ${structure.name}\n`;
            response += `**总文件数：** ${stats.totalFiles}\n`;
            response += `**目录数：** ${stats.totalDirectories}\n`;
            response += `**项目大小：** ${this.formatFileSize(stats.totalSize)}\n`;
            response += `**最深路径：** ${stats.deepestPath} 层\n\n`;
            
            // 语言分布
            if (stats.languageDistribution && Object.keys(stats.languageDistribution).length > 0) {
                response += "**语言分布：**\n";
                const sortedLangs = Object.entries(stats.languageDistribution)
                    .sort(([,a], [,b]) => b.files - a.files);
                
                sortedLangs.forEach(([lang, info]) => {
                    const percentage = ((info.files / stats.totalFiles) * 100).toFixed(1);
                    response += `- ${lang}: ${info.files} 文件 (${percentage}%)\n`;
                });
                response += "\n";
            }
            
            // 最大文件
            if (stats.largestFiles && stats.largestFiles.length > 0) {
                response += "**最大文件：**\n";
                stats.largestFiles.slice(0, 5).forEach(file => {
                    response += `- ${file.name}: ${this.formatFileSize(file.size)}\n`;
                });
            }
            
            return response;
            
        } catch (error) {
            this.log('error', '结构分析失败', { error: error.message });
            return `结构分析失败：${error.message}`;
        }
    }

    /**
     * 处理一般分析请求
     */
    async handleGeneralAnalysisRequest(message, context) {
        return `我是分析 Agent，专门负责项目分析和代码理解。我可以帮您：

🔍 **项目分析** - 分析项目整体结构和特征
📊 **技术栈检测** - 识别使用的编程语言和框架
🔗 **依赖关系** - 分析模块间的依赖关系
📁 **结构分析** - 详细的目录和文件结构分析

请告诉我您想要分析什么？例如：
- "分析项目结构"
- "检测技术栈"
- "分析依赖关系"`;
    }

    /**
     * 执行任务
     */
    async onExecuteTask(action, input, options) {
        this.log('info', '执行分析任务', { action, input: typeof input });
        
        switch (action) {
            case 'analyze_project_structure':
                return this.analyzeProjectStructure(input, options);
            case 'detect_tech_stack':
                return this.detectTechStack(input, options);
            case 'analyze_dependencies':
                return this.analyzeDependencies(input, options);
            case 'generate_analysis_report':
                return this.generateAnalysisReport(input, options);
            default:
                throw new Error(`不支持的分析任务: ${action}`);
        }
    }

    /**
     * 分析项目结构
     */
    async analyzeProjectStructure(input, options) {
        const projectPath = input.projectPath || process.cwd();
        const analysis = await this.projectAnalyzer.analyzeProject(projectPath, options);
        return {
            structure: analysis.structure,
            statistics: analysis.statistics
        };
    }

    /**
     * 检测技术栈
     */
    async detectTechStack(input, options) {
        const projectPath = input.projectPath || process.cwd();
        const analysis = await this.getOrCreateAnalysis(projectPath);
        return analysis.techStack;
    }

    /**
     * 分析依赖关系
     */
    async analyzeDependencies(input, options) {
        const projectPath = input.projectPath || process.cwd();
        const analysis = await this.getOrCreateAnalysis(projectPath);
        return analysis.dependencies;
    }

    /**
     * 生成分析报告
     */
    async generateAnalysisReport(input, options) {
        const projectPath = input.projectPath || process.cwd();
        const analysis = await this.getOrCreateAnalysis(projectPath);
        
        return {
            summary: this.formatAnalysisResult(analysis),
            details: analysis,
            recommendations: analysis.recommendations
        };
    }

    /**
     * 获取或创建分析结果
     */
    async getOrCreateAnalysis(projectPath) {
        const cacheKey = `project_analysis_${projectPath}`;
        let analysis = this.getFromCache(cacheKey);
        
        if (!analysis) {
            analysis = await this.projectAnalyzer.analyzeProject(projectPath);
            this.setCache(cacheKey, analysis);
        }
        
        return analysis;
    }

    /**
     * 格式化分析结果
     */
    formatAnalysisResult(analysis) {
        const stats = analysis.statistics;
        const techStack = analysis.techStack;
        
        let result = `📊 **项目分析报告**\n\n`;
        result += `**基本信息：**\n`;
        result += `- 项目路径：${analysis.projectPath}\n`;
        result += `- 分析时间：${analysis.timestamp}\n`;
        result += `- 总文件数：${stats.totalFiles}\n`;
        result += `- 总目录数：${stats.totalDirectories}\n`;
        result += `- 项目大小：${this.formatFileSize(stats.totalSize)}\n\n`;
        
        // 主要编程语言
        if (techStack.languages && Object.keys(techStack.languages).length > 0) {
            const mainLang = Object.entries(techStack.languages)
                .sort(([,a], [,b]) => b.files - a.files)[0];
            result += `**主要语言：** ${mainLang[0]} (${mainLang[1].files} 文件)\n\n`;
        }
        
        // 建议
        if (analysis.recommendations && analysis.recommendations.length > 0) {
            result += `**建议：**\n`;
            analysis.recommendations.forEach(rec => {
                result += `- ${rec.message}\n`;
            });
        }
        
        return result;
    }

    /**
     * 格式化文件大小
     */
    formatFileSize(bytes) {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    /**
     * 缓存操作
     */
    setCache(key, value) {
        this.analysisCache.set(key, {
            value,
            timestamp: Date.now()
        });
    }

    getFromCache(key) {
        const cached = this.analysisCache.get(key);
        if (cached && (Date.now() - cached.timestamp) < this.cacheTimeout) {
            return cached.value;
        }
        this.analysisCache.delete(key);
        return null;
    }

    /**
     * 清理缓存
     */
    clearCache() {
        this.analysisCache.clear();
        this.log('info', '分析缓存已清理');
    }
}

module.exports = AnalyzerAgent;
