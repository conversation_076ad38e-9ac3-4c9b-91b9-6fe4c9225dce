{"version": 3, "file": "extension.js", "sourceRoot": "", "sources": ["../src/extension.ts"], "names": [], "mappings": ";;;AAAA,iCAAiC;AACjC,qCAA0C;AAC1C,2DAAwD;AACxD,6DAA0D;AAC1D,2DAAwD;AACxD,qCAA0C;AAE1C,IAAI,MAAsB,CAAC;AAC3B,IAAI,MAAsB,CAAC;AAE3B,SAAgB,QAAQ,CAAC,OAAgC;IACrD,OAAO,CAAC,GAAG,CAAC,oCAAoC,CAAC,CAAC;IAElD,aAAa;IACb,MAAM,GAAG,IAAI,uBAAc,CAAC,OAAO,CAAC,CAAC;IACrC,MAAM,GAAG,IAAI,uBAAc,EAAE,CAAC;IAE9B,QAAQ;IACR,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,YAAY,EAAE,mBAAmB,EAAE,IAAI,CAAC,CAAC;IAExE,OAAO;IACP,gBAAgB,CAAC,OAAO,CAAC,CAAC;IAE1B,UAAU;IACV,qBAAqB,CAAC,OAAO,CAAC,CAAC;IAE/B,kBAAkB;IAClB,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,WAAW,CAAC,CAAC;IAC9D,IAAI,MAAM,CAAC,GAAG,CAAC,WAAW,EAAE,IAAI,CAAC,EAAE;QAC/B,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,uBAAuB,CAAC,CAAC;KAC3D;IAED,SAAS;IACT,OAAO,CAAC,aAAa,CAAC,IAAI,CACtB,MAAM,CAAC,SAAS,CAAC,wBAAwB,CAAC,CAAC,CAAC,EAAE;QAC1C,IAAI,CAAC,CAAC,oBAAoB,CAAC,WAAW,CAAC,EAAE;YACrC,yBAAyB,EAAE,CAAC;SAC/B;IACL,CAAC,CAAC,CACL,CAAC;AACN,CAAC;AA9BD,4BA8BC;AAED,SAAS,gBAAgB,CAAC,OAAgC;IACtD,QAAQ;IACR,OAAO,CAAC,aAAa,CAAC,IAAI,CACtB,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,uBAAuB,EAAE,KAAK,IAAI,EAAE;QAChE,IAAI;YACA,MAAM,MAAM,CAAC,KAAK,EAAE,CAAC;YACrB,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,wCAAwC,CAAC,CAAC;SAClF;QAAC,OAAO,KAAK,EAAE;YACZ,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,qCAAqC,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;SACjI;IACL,CAAC,CAAC,CACL,CAAC;IAEF,QAAQ;IACR,OAAO,CAAC,aAAa,CAAC,IAAI,CACtB,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,sBAAsB,EAAE,KAAK,IAAI,EAAE;QAC/D,IAAI;YACA,MAAM,MAAM,CAAC,IAAI,EAAE,CAAC;YACpB,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,2BAA2B,CAAC,CAAC;SACrE;QAAC,OAAO,KAAK,EAAE;YACZ,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,oCAAoC,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;SAChI;IACL,CAAC,CAAC,CACL,CAAC;IAEF,SAAS;IACT,OAAO,CAAC,aAAa,CAAC,IAAI,CACtB,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,oBAAoB,EAAE,GAAG,EAAE;QACvD,MAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAC1C,gBAAgB,EAChB,gBAAgB,EAChB,MAAM,CAAC,UAAU,CAAC,MAAM,EACxB;YACI,aAAa,EAAE,IAAI;YACnB,uBAAuB,EAAE,IAAI;SAChC,CACJ,CAAC;QAEF,KAAK,CAAC,OAAO,CAAC,IAAI,GAAG,qBAAqB,EAAE,CAAC;QAE7C,mBAAmB;QACnB,KAAK,CAAC,OAAO,CAAC,mBAAmB,CAC7B,KAAK,EAAC,OAAO,EAAC,EAAE;YACZ,QAAQ,OAAO,CAAC,OAAO,EAAE;gBACrB,KAAK,aAAa;oBACd,IAAI;wBACA,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;wBACxD,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC;4BACtB,OAAO,EAAE,iBAAiB;4BAC1B,QAAQ,EAAE,QAAQ;yBACrB,CAAC,CAAC;qBACN;oBAAC,OAAO,KAAK,EAAE;wBACZ,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC;4BACtB,OAAO,EAAE,OAAO;4BAChB,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;yBAChE,CAAC,CAAC;qBACN;oBACD,MAAM;aACb;QACL,CAAC,CACJ,CAAC;IACN,CAAC,CAAC,CACL,CAAC;IAEF,OAAO;IACP,OAAO,CAAC,aAAa,CAAC,IAAI,CACtB,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,0BAA0B,EAAE,KAAK,EAAE,GAAgB,EAAE,EAAE;QACnF,MAAM,eAAe,GAAG,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC,CAAC;QAEhH,IAAI,CAAC,eAAe,EAAE;YAClB,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,2BAA2B,CAAC,CAAC;YAC5D,OAAO;SACV;QAED,IAAI;YACA,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC;gBACvB,QAAQ,EAAE,MAAM,CAAC,gBAAgB,CAAC,YAAY;gBAC9C,KAAK,EAAE,sBAAsB;gBAC7B,WAAW,EAAE,KAAK;aACrB,EAAE,KAAK,IAAI,EAAE;gBACV,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,cAAc,CAAC,eAAe,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;gBAEzE,SAAS;gBACT,MAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAC1C,oBAAoB,EACpB,kBAAkB,EAClB,MAAM,CAAC,UAAU,CAAC,MAAM,EACxB,EAAE,aAAa,EAAE,IAAI,EAAE,CAC1B,CAAC;gBAEF,KAAK,CAAC,OAAO,CAAC,IAAI,GAAG,yBAAyB,CAAC,QAAQ,CAAC,CAAC;YAC7D,CAAC,CAAC,CAAC;SACN;QAAC,OAAO,KAAK,EAAE;YACZ,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,oBAAoB,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;SAChH;IACL,CAAC,CAAC,CACL,CAAC;IAEF,OAAO;IACP,OAAO,CAAC,aAAa,CAAC,IAAI,CACtB,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,wBAAwB,EAAE,KAAK,IAAI,EAAE;QACjE,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC;QAC9C,IAAI,CAAC,MAAM,EAAE;YACT,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,kBAAkB,CAAC,CAAC;YACnD,OAAO;SACV;QAED,MAAM,SAAS,GAAG,MAAM,CAAC,SAAS,CAAC;QACnC,MAAM,YAAY,GAAG,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;QAExD,IAAI,CAAC,YAAY,EAAE;YACf,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,kBAAkB,CAAC,CAAC;YACnD,OAAO;SACV;QAED,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC;YAC5C,MAAM,EAAE,mDAAmD;YAC3D,WAAW,EAAE,oEAAoE;SACpF,CAAC,CAAC;QAEH,IAAI,CAAC,MAAM;YAAE,OAAO;QAEpB,IAAI;YACA,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC;gBACvB,QAAQ,EAAE,MAAM,CAAC,gBAAgB,CAAC,YAAY;gBAC9C,KAAK,EAAE,oBAAoB;gBAC3B,WAAW,EAAE,KAAK;aACrB,EAAE,KAAK,IAAI,EAAE;gBACV,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,YAAY,CAAC,YAAY,EAAE,MAAM,CAAC,CAAC;gBAE/D,UAAU;gBACV,MAAM,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE;oBAC5B,WAAW,CAAC,OAAO,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;gBAC3C,CAAC,CAAC,CAAC;YACP,CAAC,CAAC,CAAC;SACN;QAAC,OAAO,KAAK,EAAE;YACZ,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,2BAA2B,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;SACvH;IACL,CAAC,CAAC,CACL,CAAC;IAEF,OAAO;IACP,OAAO,CAAC,aAAa,CAAC,IAAI,CACtB,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,wBAAwB,EAAE,GAAG,EAAE;QAC3D,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,+BAA+B,EAAE,WAAW,CAAC,CAAC;IACjF,CAAC,CAAC,CACL,CAAC;AACN,CAAC;AAED,SAAS,qBAAqB,CAAC,OAAgC;IAC3D,QAAQ;IACR,MAAM,YAAY,GAAG,IAAI,2BAAY,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;IACvD,OAAO,CAAC,aAAa,CAAC,IAAI,CACtB,MAAM,CAAC,MAAM,CAAC,2BAA2B,CAAC,gBAAgB,EAAE,YAAY,CAAC,CAC5E,CAAC;IAEF,YAAY;IACZ,MAAM,aAAa,GAAG,IAAI,6BAAa,CAAC,MAAM,CAAC,CAAC;IAChD,OAAO,CAAC,aAAa,CAAC,IAAI,CACtB,MAAM,CAAC,MAAM,CAAC,wBAAwB,CAAC,kBAAkB,EAAE,aAAa,CAAC,CAC5E,CAAC;IAEF,QAAQ;IACR,MAAM,YAAY,GAAG,IAAI,2BAAY,CAAC,MAAM,CAAC,CAAC;IAC9C,OAAO,CAAC,aAAa,CAAC,IAAI,CACtB,MAAM,CAAC,MAAM,CAAC,wBAAwB,CAAC,iBAAiB,EAAE,YAAY,CAAC,CAC1E,CAAC;AACN,CAAC;AAED,SAAS,yBAAyB;IAC9B,UAAU;IACV,IAAI,MAAM,EAAE;QACR,MAAM,CAAC,mBAAmB,EAAE,CAAC;KAChC;AACL,CAAC;AAED,SAAS,qBAAqB;IAC1B,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAkHC,CAAC;AACb,CAAC;AAED,SAAS,yBAAyB,CAAC,QAAa;IAC5C,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iBAgCM,QAAQ,CAAC,OAAO,IAAI,iCAAiC;;;;mBAInD,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC;;;YAGxC,CAAC;AACb,CAAC;AAED,SAAgB,UAAU;IACtB,IAAI,MAAM,EAAE;QACR,MAAM,CAAC,IAAI,EAAE,CAAC;KACjB;AACL,CAAC;AAJD,gCAIC"}