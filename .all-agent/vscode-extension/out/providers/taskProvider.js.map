{"version": 3, "file": "taskProvider.js", "sourceRoot": "", "sources": ["../../src/providers/taskProvider.ts"], "names": [], "mappings": ";;;AAAA,iCAAiC;AAGjC,MAAa,YAAY;IAIrB,YAAoB,MAAsB;QAAtB,WAAM,GAAN,MAAM,CAAgB;QAHlC,yBAAoB,GAA4D,IAAI,MAAM,CAAC,YAAY,EAAsC,CAAC;QAC7I,wBAAmB,GAAqD,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC;QAG7G,WAAW;QACX,WAAW,CAAC,GAAG,EAAE;YACb,IAAI,CAAC,OAAO,EAAE,CAAC;QACnB,CAAC,EAAE,IAAI,CAAC,CAAC;IACb,CAAC;IAED,OAAO;QACH,IAAI,CAAC,oBAAoB,CAAC,IAAI,EAAE,CAAC;IACrC,CAAC;IAED,WAAW,CAAC,OAAiB;QACzB,OAAO,OAAO,CAAC;IACnB,CAAC;IAED,KAAK,CAAC,WAAW,CAAC,OAAkB;QAChC,IAAI,CAAC,OAAO,EAAE;YACV,eAAe;YACf,IAAI;gBACA,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC;gBAC3C,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;oBACpB,OAAO,CAAC,IAAI,QAAQ,CAChB,iBAAiB,EACjB,MAAM,EACN,MAAM,EACN,MAAM,CAAC,wBAAwB,CAAC,IAAI,CACvC,CAAC,CAAC;iBACN;gBAED,OAAO,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,QAAQ,CACjC,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,EAAE,IAAI,cAAc,EACvC,IAAI,CAAC,MAAM,IAAI,SAAS,EACxB,IAAI,CAAC,IAAI,IAAI,SAAS,EACtB,MAAM,CAAC,wBAAwB,CAAC,SAAS,EACzC,IAAI,CACP,CAAC,CAAC;aACN;YAAC,OAAO,KAAK,EAAE;gBACZ,OAAO,CAAC,IAAI,QAAQ,CAChB,sBAAsB,EACtB,OAAO,EACP,OAAO,EACP,MAAM,CAAC,wBAAwB,CAAC,IAAI,CACvC,CAAC,CAAC;aACN;SACJ;aAAM;YACH,SAAS;YACT,MAAM,OAAO,GAAe,EAAE,CAAC;YAE/B,IAAI,OAAO,CAAC,QAAQ,EAAE;gBAClB,MAAM,IAAI,GAAG,OAAO,CAAC,QAAQ,CAAC;gBAE9B,IAAI,IAAI,CAAC,KAAK,EAAE;oBACZ,OAAO,CAAC,IAAI,CAAC,IAAI,QAAQ,CACrB,UAAU,IAAI,CAAC,KAAK,EAAE,EACtB,MAAM,EACN,OAAO,EACP,MAAM,CAAC,wBAAwB,CAAC,IAAI,CACvC,CAAC,CAAC;iBACN;gBAED,IAAI,IAAI,CAAC,MAAM,EAAE;oBACb,OAAO,CAAC,IAAI,CAAC,IAAI,QAAQ,CACrB,WAAW,IAAI,CAAC,MAAM,EAAE,EACxB,MAAM,EACN,QAAQ,EACR,MAAM,CAAC,wBAAwB,CAAC,IAAI,CACvC,CAAC,CAAC;iBACN;gBAED,IAAI,IAAI,CAAC,QAAQ,KAAK,SAAS,EAAE;oBAC7B,OAAO,CAAC,IAAI,CAAC,IAAI,QAAQ,CACrB,aAAa,IAAI,CAAC,QAAQ,GAAG,EAC7B,MAAM,EACN,UAAU,EACV,MAAM,CAAC,wBAAwB,CAAC,IAAI,CACvC,CAAC,CAAC;iBACN;gBAED,IAAI,IAAI,CAAC,SAAS,EAAE;oBAChB,MAAM,SAAS,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,cAAc,EAAE,CAAC;oBAC5D,OAAO,CAAC,IAAI,CAAC,IAAI,QAAQ,CACrB,YAAY,SAAS,EAAE,EACvB,MAAM,EACN,MAAM,EACN,MAAM,CAAC,wBAAwB,CAAC,IAAI,CACvC,CAAC,CAAC;iBACN;gBAED,IAAI,IAAI,CAAC,WAAW,EAAE;oBAClB,OAAO,CAAC,IAAI,CAAC,IAAI,QAAQ,CACrB,gBAAgB,IAAI,CAAC,WAAW,EAAE,EAClC,MAAM,EACN,aAAa,EACb,MAAM,CAAC,wBAAwB,CAAC,IAAI,CACvC,CAAC,CAAC;iBACN;aACJ;YAED,OAAO,OAAO,CAAC;SAClB;IACL,CAAC;CACJ;AAzGD,oCAyGC;AAED,MAAM,QAAS,SAAQ,MAAM,CAAC,QAAQ;IAClC,YACoB,KAAa,EACb,MAAc,EACd,IAAY,EACZ,gBAAiD,EACjD,QAAc;QAE9B,KAAK,CAAC,KAAK,EAAE,gBAAgB,CAAC,CAAC;QANf,UAAK,GAAL,KAAK,CAAQ;QACb,WAAM,GAAN,MAAM,CAAQ;QACd,SAAI,GAAJ,IAAI,CAAQ;QACZ,qBAAgB,GAAhB,gBAAgB,CAAiC;QACjD,aAAQ,GAAR,QAAQ,CAAM;QAI9B,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;QACjC,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;QACzC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;QAC/B,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;IAC/C,CAAC;IAEO,UAAU;QACd,IAAI,IAAI,CAAC,QAAQ,EAAE;YACf,IAAI,OAAO,GAAG,SAAS,IAAI,CAAC,QAAQ,CAAC,KAAK,IAAI,IAAI,CAAC,QAAQ,CAAC,EAAE,IAAI,CAAC;YACnE,OAAO,IAAI,WAAW,IAAI,CAAC,QAAQ,CAAC,MAAM,IAAI,CAAC;YAC/C,OAAO,IAAI,SAAS,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;YACzC,IAAI,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE;gBACrB,OAAO,IAAI,YAAY,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC;aAChD;YACD,IAAI,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE;gBAC3B,OAAO,IAAI,kBAAkB,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC;aAC5D;YACD,OAAO,OAAO,CAAC;SAClB;QACD,OAAO,IAAI,CAAC,KAAK,CAAC;IACtB,CAAC;IAEO,cAAc;QAClB,IAAI,IAAI,CAAC,QAAQ,EAAE;YACf,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,KAAK,SAAS,EAAE;gBACtC,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,QAAQ,GAAG,CAAC;aACvC;YACD,OAAO,IAAI,CAAC,MAAM,CAAC;SACtB;QACD,OAAO,EAAE,CAAC;IACd,CAAC;IAEO,OAAO;QACX,QAAQ,IAAI,CAAC,IAAI,EAAE;YACf,KAAK,UAAU;gBACX,OAAO,IAAI,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;YAC1C,KAAK,UAAU;gBACX,OAAO,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC;YAChD,KAAK,WAAW;gBACZ,OAAO,IAAI,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;YACxC,KAAK,YAAY;gBACb,OAAO,IAAI,MAAM,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;YAC7C,KAAK,OAAO;gBACR,OAAO,IAAI,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;YAC1C,KAAK,QAAQ;gBACT,OAAO,IAAI,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;YACzC,KAAK,UAAU;gBACX,OAAO,IAAI,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;YACzC,KAAK,MAAM;gBACP,OAAO,IAAI,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;YACzC,KAAK,aAAa;gBACd,OAAO,IAAI,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;YACxC,KAAK,MAAM;gBACP,OAAO,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC;YAChD;gBACI,QAAQ,IAAI,CAAC,MAAM,EAAE;oBACjB,KAAK,SAAS;wBACV,OAAO,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,EAAE,IAAI,MAAM,CAAC,UAAU,CAAC,qBAAqB,CAAC,CAAC,CAAC;oBAC9F,KAAK,WAAW;wBACZ,OAAO,IAAI,MAAM,CAAC,SAAS,CAAC,OAAO,EAAE,IAAI,MAAM,CAAC,UAAU,CAAC,oBAAoB,CAAC,CAAC,CAAC;oBACtF,KAAK,QAAQ;wBACT,OAAO,IAAI,MAAM,CAAC,SAAS,CAAC,OAAO,EAAE,IAAI,MAAM,CAAC,UAAU,CAAC,iBAAiB,CAAC,CAAC,CAAC;oBACnF,KAAK,SAAS;wBACV,OAAO,IAAI,MAAM,CAAC,SAAS,CAAC,OAAO,EAAE,IAAI,MAAM,CAAC,UAAU,CAAC,mBAAmB,CAAC,CAAC,CAAC;oBACrF,KAAK,MAAM;wBACP,OAAO,IAAI,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAC;oBAClD;wBACI,OAAO,IAAI,MAAM,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;iBAC/C;SACR;IACL,CAAC;IAEO,eAAe;QACnB,IAAI,IAAI,CAAC,QAAQ,EAAE;YACf,OAAO,MAAM,CAAC;SACjB;QACD,OAAO,UAAU,CAAC;IACtB,CAAC;CACJ"}