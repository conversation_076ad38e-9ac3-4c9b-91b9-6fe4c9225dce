# All-Agent VSCode Extension

AI-powered multi-agent development assistant for code analysis, planning, and execution directly in VSCode/Cursor.

## Features

- 🤖 **Multi-Agent System**: <PERSON><PERSON><PERSON>, Planner, and Executor agents working together
- 💬 **Integrated Chat**: Chat with AI agents directly in the sidebar
- 🔍 **Project Analysis**: Analyze your project structure and dependencies
- ⚡ **Code Generation**: Generate and improve code with AI assistance
- 📊 **Real-time Monitoring**: Monitor agent status and tasks
- 🔧 **Easy Configuration**: Configure API keys and settings through VSCode settings

## Installation

### Method 1: Install from VSIX (Recommended)

1. Download the latest `.vsix` file from releases
2. Open VSCode/Cursor
3. Press `Ctrl+Shift+P` (or `Cmd+Shift+P` on Mac)
4. Type "Extensions: Install from VSIX"
5. Select the downloaded `.vsix` file

### Method 2: Development Installation

1. Clone the All-Agent repository
2. Navigate to the extension directory:
   ```bash
   cd .all-agent/vscode-extension
   ```
3. Install dependencies:
   ```bash
   npm install
   ```
4. Compile TypeScript:
   ```bash
   npm run compile
   ```
5. Press `F5` in VSCode to launch a new Extension Development Host window

## Configuration

1. Open VSCode Settings (`Ctrl+,` or `Cmd+,`)
2. Search for "All-Agent"
3. Configure the following settings:

### Required Settings

- **DeepSeek API Key**: Your DeepSeek API key
- **Mistral API Key**: Your Mistral AI API key (optional)
- **OpenAI API Key**: Your OpenAI API key (optional)

### Optional Settings

- **Server Port**: Port for All-Agent server (default: 3000)
- **Auto Start**: Automatically start server when VSCode opens (default: true)
- **LLM Provider**: Default AI provider (deepseek, mistral, openai, etc.)

## Usage

### 1. Starting the Server

The extension will automatically start the All-Agent server when VSCode opens (if auto-start is enabled).

You can also manually control the server:
- **Start Server**: `Ctrl+Shift+P` → "All-Agent: Start All-Agent Server"
- **Stop Server**: `Ctrl+Shift+P` → "All-Agent: Stop All-Agent Server"

### 2. Using the Chat Interface

1. Click on the All-Agent icon in the Activity Bar (left sidebar)
2. In the "Chat" panel, click "Connect"
3. Start chatting with AI agents!

### 3. Project Analysis

- Right-click on a folder in the Explorer
- Select "All-Agent: Analyze Current Project"
- View the analysis results in a new panel

### 4. Code Generation

1. Select code in the editor
2. Right-click and choose "All-Agent: Generate Code"
3. Enter your prompt (e.g., "Add error handling", "Optimize performance")
4. The AI will generate improved code

### 5. Monitoring Agents and Tasks

- View active agents in the "Agents" panel
- Monitor running tasks in the "Tasks" panel
- Both panels update in real-time

## Commands

| Command | Description |
|---------|-------------|
| `All-Agent: Start All-Agent Server` | Start the All-Agent server |
| `All-Agent: Stop All-Agent Server` | Stop the All-Agent server |
| `All-Agent: Open Chat Panel` | Open chat in a new panel |
| `All-Agent: Analyze Current Project` | Analyze the current project |
| `All-Agent: Generate Code` | Generate code for selected text |
| `All-Agent: Open Settings` | Open All-Agent settings |

## Troubleshooting

### Server Won't Start

1. Ensure the All-Agent project is in your workspace
2. Check that Node.js is installed
3. Verify API keys are configured correctly
4. Check the Output panel for error messages

### Connection Issues

1. Verify the server is running (check status in sidebar)
2. Check firewall settings
3. Try restarting the server

### API Errors

1. Verify your API keys are valid
2. Check your internet connection
3. Ensure you have sufficient API quota

## Development

### Building the Extension

```bash
npm run compile
```

### Watching for Changes

```bash
npm run watch
```

### Packaging

```bash
npm install -g vsce
vsce package
```

## Requirements

- VSCode 1.74.0 or higher
- Node.js 16.x or higher
- Valid API keys for AI services

## License

MIT License - see LICENSE file for details

## Support

For issues and feature requests, please visit our GitHub repository.
