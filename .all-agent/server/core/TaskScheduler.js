const EventEmitter = require('events');
const crypto = require('crypto');

/**
 * 任务调度器
 * 负责任务的调度、执行、监控和管理
 */
class TaskScheduler extends EventEmitter {
    constructor() {
        super();
        this.tasks = new Map();
        this.taskQueue = [];
        this.runningTasks = new Map();
        this.completedTasks = new Map();
        this.isProcessing = false;
        
        // 配置
        this.config = {
            maxConcurrentTasks: 10,
            taskTimeout: 300000, // 5分钟
            retryAttempts: 3,
            retryDelay: 1000,
            cleanupInterval: 3600000 // 1小时
        };
        
        // 启动任务处理器
        this.startTaskProcessor();
        this.startCleanupTimer();
    }

    /**
     * 调度任务
     */
    async scheduleTask(taskDefinition) {
        const task = this.createTask(taskDefinition);
        
        // 添加到任务映射
        this.tasks.set(task.id, task);
        
        // 添加到队列
        this.taskQueue.push(task);
        
        // 触发任务调度事件
        this.emit('task_scheduled', task);
        
        console.log(`📋 任务已调度: ${task.id} (${task.agentType})`);
        
        return task.id;
    }

    /**
     * 创建任务
     */
    createTask(definition) {
        const taskId = this.generateTaskId();
        
        return {
            id: taskId,
            agentType: definition.agentType,
            action: definition.action,
            input: definition.input,
            options: definition.options || {},
            status: 'pending',
            priority: definition.priority || 'normal',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            startedAt: null,
            completedAt: null,
            result: null,
            error: null,
            retryCount: 0,
            progress: 0,
            logs: []
        };
    }

    /**
     * 生成任务 ID
     */
    generateTaskId() {
        return `task_${Date.now()}_${crypto.randomBytes(4).toString('hex')}`;
    }

    /**
     * 获取任务
     */
    getTask(taskId) {
        return this.tasks.get(taskId);
    }

    /**
     * 更新任务状态
     */
    updateTaskStatus(taskId, status, data = {}) {
        const task = this.tasks.get(taskId);
        if (!task) {
            return false;
        }

        task.status = status;
        task.updatedAt = new Date().toISOString();
        
        // 更新其他字段
        Object.assign(task, data);
        
        // 记录状态变更
        this.logTaskEvent(task, `状态变更: ${status}`, data);
        
        // 触发事件
        this.emit('task_update', task);
        
        return true;
    }

    /**
     * 记录任务事件
     */
    logTaskEvent(task, message, data = {}) {
        task.logs.push({
            timestamp: new Date().toISOString(),
            message,
            data
        });
        
        // 限制日志数量
        if (task.logs.length > 100) {
            task.logs.shift();
        }
    }

    /**
     * 启动任务处理器
     */
    startTaskProcessor() {
        setInterval(() => {
            if (!this.isProcessing && this.taskQueue.length > 0) {
                this.processTaskQueue();
            }
        }, 1000);
    }

    /**
     * 处理任务队列
     */
    async processTaskQueue() {
        if (this.runningTasks.size >= this.config.maxConcurrentTasks) {
            return;
        }

        this.isProcessing = true;

        try {
            // 按优先级排序任务
            this.taskQueue.sort((a, b) => {
                const priorityOrder = { high: 3, normal: 2, low: 1 };
                return priorityOrder[b.priority] - priorityOrder[a.priority];
            });

            // 处理队列中的任务
            while (this.taskQueue.length > 0 && 
                   this.runningTasks.size < this.config.maxConcurrentTasks) {
                
                const task = this.taskQueue.shift();
                await this.executeTask(task);
            }

        } finally {
            this.isProcessing = false;
        }
    }

    /**
     * 执行任务
     */
    async executeTask(task) {
        try {
            // 更新任务状态
            this.updateTaskStatus(task.id, 'running', {
                startedAt: new Date().toISOString()
            });

            // 添加到运行中任务
            this.runningTasks.set(task.id, task);

            console.log(`🚀 开始执行任务: ${task.id}`);

            // 设置超时
            const timeoutPromise = new Promise((_, reject) => {
                setTimeout(() => {
                    reject(new Error('任务执行超时'));
                }, this.config.taskTimeout);
            });

            // 执行任务
            const executionPromise = this.performTask(task);

            // 等待任务完成或超时
            const result = await Promise.race([executionPromise, timeoutPromise]);

            // 任务成功完成
            this.updateTaskStatus(task.id, 'completed', {
                completedAt: new Date().toISOString(),
                result,
                progress: 100
            });

            // 移动到已完成任务
            this.runningTasks.delete(task.id);
            this.completedTasks.set(task.id, task);

            console.log(`✅ 任务完成: ${task.id}`);

        } catch (error) {
            console.error(`❌ 任务执行失败: ${task.id}`, error);

            // 检查是否需要重试
            if (task.retryCount < this.config.retryAttempts) {
                await this.retryTask(task, error);
            } else {
                // 任务最终失败
                this.updateTaskStatus(task.id, 'failed', {
                    completedAt: new Date().toISOString(),
                    error: error.message
                });

                this.runningTasks.delete(task.id);
                this.completedTasks.set(task.id, task);
            }
        }
    }

    /**
     * 执行具体任务
     */
    async performTask(task) {
        // 模拟任务执行
        const { agentType, action, input, options } = task;
        
        // 模拟进度更新
        for (let progress = 0; progress <= 100; progress += 20) {
            await new Promise(resolve => setTimeout(resolve, 200));
            
            this.updateTaskStatus(task.id, 'running', { progress });
            this.logTaskEvent(task, `进度更新: ${progress}%`);
        }

        // 根据任务类型生成不同的结果
        let result;
        
        switch (action) {
            case 'analyze_project':
                result = {
                    type: 'analysis',
                    summary: '项目分析完成',
                    details: {
                        fileCount: Math.floor(Math.random() * 100 + 10),
                        techStack: ['JavaScript', 'React', 'Node.js'],
                        recommendations: ['优化性能', '改进架构']
                    }
                };
                break;
                
            case 'generate_code':
                result = {
                    type: 'code_generation',
                    summary: '代码生成完成',
                    files: [
                        { name: 'component.js', size: 1024 },
                        { name: 'styles.css', size: 512 }
                    ]
                };
                break;
                
            case 'create_plan':
                result = {
                    type: 'planning',
                    summary: '项目计划制定完成',
                    phases: [
                        { name: '需求分析', duration: '1周' },
                        { name: '设计开发', duration: '3周' },
                        { name: '测试部署', duration: '1周' }
                    ]
                };
                break;
                
            default:
                result = {
                    type: 'generic',
                    summary: `${action} 执行完成`,
                    data: input
                };
        }

        return result;
    }

    /**
     * 重试任务
     */
    async retryTask(task, error) {
        task.retryCount++;
        
        this.logTaskEvent(task, `重试任务 (${task.retryCount}/${this.config.retryAttempts})`, {
            error: error.message
        });

        // 延迟重试
        await new Promise(resolve => 
            setTimeout(resolve, this.config.retryDelay * task.retryCount)
        );

        // 重新调度任务
        this.updateTaskStatus(task.id, 'pending');
        this.runningTasks.delete(task.id);
        this.taskQueue.unshift(task); // 添加到队列前面
    }

    /**
     * 取消任务
     */
    cancelTask(taskId) {
        const task = this.tasks.get(taskId);
        if (!task) {
            return false;
        }

        if (task.status === 'pending') {
            // 从队列中移除
            const index = this.taskQueue.findIndex(t => t.id === taskId);
            if (index !== -1) {
                this.taskQueue.splice(index, 1);
            }
        } else if (task.status === 'running') {
            // 从运行中任务移除
            this.runningTasks.delete(taskId);
        }

        this.updateTaskStatus(taskId, 'cancelled', {
            completedAt: new Date().toISOString()
        });

        this.completedTasks.set(taskId, task);
        
        console.log(`🚫 任务已取消: ${taskId}`);
        
        return true;
    }

    /**
     * 获取任务统计
     */
    getStatistics() {
        const stats = {
            total: this.tasks.size,
            pending: this.taskQueue.length,
            running: this.runningTasks.size,
            completed: 0,
            failed: 0,
            cancelled: 0
        };

        for (const task of this.tasks.values()) {
            if (task.status === 'completed') stats.completed++;
            else if (task.status === 'failed') stats.failed++;
            else if (task.status === 'cancelled') stats.cancelled++;
        }

        return stats;
    }

    /**
     * 获取任务列表
     */
    getTasks(filter = {}) {
        let tasks = Array.from(this.tasks.values());

        // 应用过滤器
        if (filter.status) {
            tasks = tasks.filter(task => task.status === filter.status);
        }
        
        if (filter.agentType) {
            tasks = tasks.filter(task => task.agentType === filter.agentType);
        }
        
        if (filter.limit) {
            tasks = tasks.slice(0, filter.limit);
        }

        // 按创建时间排序
        tasks.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

        return tasks;
    }

    /**
     * 获取运行中的任务
     */
    getRunningTasks() {
        return Array.from(this.runningTasks.values());
    }

    /**
     * 获取队列状态
     */
    getQueueStatus() {
        return {
            queueLength: this.taskQueue.length,
            runningTasks: this.runningTasks.size,
            maxConcurrent: this.config.maxConcurrentTasks,
            isProcessing: this.isProcessing
        };
    }

    /**
     * 启动清理定时器
     */
    startCleanupTimer() {
        setInterval(() => {
            this.cleanupOldTasks();
        }, this.config.cleanupInterval);
    }

    /**
     * 清理旧任务
     */
    cleanupOldTasks() {
        const cutoffTime = Date.now() - (24 * 60 * 60 * 1000); // 24小时前
        let cleanedCount = 0;

        for (const [taskId, task] of this.completedTasks.entries()) {
            const taskTime = new Date(task.completedAt).getTime();
            
            if (taskTime < cutoffTime) {
                this.completedTasks.delete(taskId);
                this.tasks.delete(taskId);
                cleanedCount++;
            }
        }

        if (cleanedCount > 0) {
            console.log(`🧹 清理了 ${cleanedCount} 个旧任务`);
        }
    }

    /**
     * 暂停任务调度
     */
    pause() {
        this.isProcessing = true;
        console.log('⏸️ 任务调度已暂停');
    }

    /**
     * 恢复任务调度
     */
    resume() {
        this.isProcessing = false;
        console.log('▶️ 任务调度已恢复');
    }

    /**
     * 清理资源
     */
    async cleanup() {
        console.log('🧹 正在清理任务调度器...');
        
        // 取消所有运行中的任务
        for (const taskId of this.runningTasks.keys()) {
            this.cancelTask(taskId);
        }
        
        // 清空队列
        this.taskQueue.length = 0;
        
        console.log('✅ 任务调度器清理完成');
    }
}

module.exports = TaskScheduler;
