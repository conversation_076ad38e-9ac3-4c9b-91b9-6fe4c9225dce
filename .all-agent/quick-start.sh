#!/bin/bash

# All-Agent 快速启动脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🚀 All-Agent 快速启动脚本${NC}"
echo "=================================="

# 检查 Node.js
if ! command -v node &> /dev/null; then
    echo -e "${RED}❌ Node.js 未安装，请先安装 Node.js${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Node.js 版本: $(node --version)${NC}"

# 进入项目目录
cd "$(dirname "$0")"
echo -e "${BLUE}📁 当前目录: $(pwd)${NC}"

# 检查环境配置
if [ ! -f ".env" ]; then
    echo -e "${YELLOW}⚠️ 未找到 .env 文件，正在创建...${NC}"
    cp .env.example .env
    echo -e "${GREEN}✅ 已创建 .env 文件${NC}"
fi

# 进入 server 目录
cd server

# 安装基础依赖
echo -e "${BLUE}📦 安装基础依赖...${NC}"
npm install --no-optional --ignore-scripts

# 手动安装必要的依赖
echo -e "${BLUE}📦 安装核心依赖...${NC}"
npm install better-sqlite3 express cors helmet morgan bcryptjs jsonwebtoken express-rate-limit multer swagger-jsdoc swagger-ui-express socket.io winston express-validator dotenv

# 初始化数据库
echo -e "${BLUE}🗄️ 初始化数据库...${NC}"
npm run init-db

# 启动服务
echo -e "${GREEN}🎉 准备启动 All-Agent 服务...${NC}"
echo -e "${YELLOW}访问地址:${NC}"
echo -e "  主页: ${BLUE}http://localhost:3000${NC}"
echo -e "  演示: ${BLUE}http://localhost:3000/demo${NC}"
echo -e "  API文档: ${BLUE}http://localhost:3000/api-docs${NC}"
echo ""
echo -e "${YELLOW}按 Ctrl+C 停止服务${NC}"
echo ""

# 启动开发服务器
npm start
