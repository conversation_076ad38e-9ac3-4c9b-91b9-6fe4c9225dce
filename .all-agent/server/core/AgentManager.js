const EventEmitter = require('events');
const path = require('path');
const fs = require('fs').promises;

// 导入具体的 Agent 实现
const AnalyzerAgent = require('../agents/AnalyzerAgent');
const PlannerAgent = require('../agents/PlannerAgent');
const ExecutorAgent = require('../agents/ExecutorAgent');

/**
 * Agent 管理器
 * 负责 Agent 的注册、管理、消息路由和状态监控
 */
class AgentManager extends EventEmitter {
  constructor(llmService = null) {
    super();
    this.agents = new Map();
    this.agentInstances = new Map();
    this.config = null;
    this.isInitialized = false;
    this.llmService = llmService;
  }

  /**
   * 初始化 Agent 管理器
   */
  async initialize() {
    try {
      // 加载配置
      await this.loadConfig();

      // 注册内置 Agent
      await this.registerBuiltinAgents();

      // 启动 Agent 实例
      await this.startAgents();

      this.isInitialized = true;
      this.emit('initialized');

      console.log('✅ Agent 管理器初始化完成');
    } catch (error) {
      console.error('❌ Agent 管理器初始化失败:', error);
      throw error;
    }
  }

  /**
   * 加载 Agent 配置
   */
  async loadConfig() {
    try {
      const configPath = path.join(process.cwd(), '../agents.json');
      const configData = await fs.readFile(configPath, 'utf8');
      this.config = JSON.parse(configData);

      console.log('📋 Agent 配置加载成功');
    } catch (error) {
      console.error('❌ Agent 配置加载失败:', error);
      throw new Error('无法加载 Agent 配置文件');
    }
  }

  /**
   * 注册内置 Agent
   */
  async registerBuiltinAgents() {
    const agentClasses = {
      analyzer: AnalyzerAgent,
      planner: PlannerAgent,
      executor: ExecutorAgent
    };

    for (const [agentType, AgentClass] of Object.entries(agentClasses)) {
      const agentConfig = this.config.agents[agentType];
      if (agentConfig) {
        this.registerAgent(agentType, AgentClass, agentConfig);
      }
    }
  }

  /**
   * 注册 Agent
   */
  registerAgent(agentType, AgentClass, config) {
    this.agents.set(agentType, {
      class: AgentClass,
      config: config,
      status: 'registered'
    });

    console.log(`🤖 注册 Agent: ${config.name} (${agentType})`);
  }

  /**
   * 启动所有 Agent
   */
  async startAgents() {
    for (const [agentType, agentInfo] of this.agents.entries()) {
      try {
        const instance = new agentInfo.class(agentInfo.config, this.llmService);
        await instance.initialize();

        this.agentInstances.set(agentType, instance);
        agentInfo.status = 'running';

        // 监听 Agent 状态变化
        instance.on('status_change', (status) => {
          this.handleAgentStatusChange(agentType, status);
        });

        console.log(`🚀 启动 Agent: ${agentInfo.config.name}`);
      } catch (error) {
        console.error(`❌ 启动 Agent 失败 (${agentType}):`, error);
        agentInfo.status = 'error';
      }
    }
  }

  /**
   * 发送消息给指定 Agent
   */
  async sendMessage(agentType, message, context = {}) {
    const agent = this.agentInstances.get(agentType);
    if (!agent) {
      throw new Error(`Agent 不存在: ${agentType}`);
    }

    if (!agent.isReady()) {
      throw new Error(`Agent 未就绪: ${agentType}`);
    }

    try {
      const response = await agent.processMessage(message, context);

      // 记录消息处理
      this.emit('message_processed', {
        agentType,
        message,
        response,
        timestamp: new Date().toISOString()
      });

      return response;
    } catch (error) {
      console.error(`Agent 消息处理失败 (${agentType}):`, error);
      throw error;
    }
  }

  /**
   * 执行 Agent 任务
   */
  async executeTask(agentType, action, input, options = {}) {
    const agent = this.agentInstances.get(agentType);
    if (!agent) {
      throw new Error(`Agent 不存在: ${agentType}`);
    }

    try {
      const result = await agent.executeTask(action, input, options);

      // 记录任务执行
      this.emit('task_executed', {
        agentType,
        action,
        input,
        result,
        timestamp: new Date().toISOString()
      });

      return result;
    } catch (error) {
      console.error(`Agent 任务执行失败 (${agentType}):`, error);
      throw error;
    }
  }

  /**
   * 获取 Agent 状态
   */
  getAgentStatus() {
    const status = {};

    for (const [agentType, agentInfo] of this.agents.entries()) {
      const instance = this.agentInstances.get(agentType);

      status[agentType] = {
        name: agentInfo.config.name,
        status: agentInfo.status,
        capabilities: agentInfo.config.capabilities,
        currentTasks: instance ? instance.getCurrentTaskCount() : 0,
        maxTasks: agentInfo.config.max_concurrent_tasks,
        lastActivity: instance ? instance.getLastActivity() : null
      };
    }

    return status;
  }

  /**
   * 获取所有 Agent 信息
   */
  getAllAgents() {
    const agents = {};

    for (const [agentType, agentInfo] of this.agents.entries()) {
      const instance = this.agentInstances.get(agentType);

      agents[agentType] = {
        ...agentInfo.config,
        status: agentInfo.status,
        instance: instance
          ? {
              isReady: instance.isReady(),
              currentTasks: instance.getCurrentTaskCount(),
              totalProcessed: instance.getTotalProcessed(),
              lastActivity: instance.getLastActivity(),
              uptime: instance.getUptime()
            }
          : null
      };
    }

    return agents;
  }

  /**
   * 获取可用的 Agent
   */
  getAvailableAgents(capability = null) {
    const available = [];

    for (const [agentType, agentInfo] of this.agents.entries()) {
      const instance = this.agentInstances.get(agentType);

      if (agentInfo.status === 'running' && instance && instance.isReady() && instance.canAcceptTask()) {
        // 如果指定了能力，检查 Agent 是否支持
        if (capability && !agentInfo.config.capabilities.includes(capability)) {
          continue;
        }

        available.push({
          type: agentType,
          name: agentInfo.config.name,
          capabilities: agentInfo.config.capabilities,
          currentLoad: instance.getCurrentTaskCount() / agentInfo.config.max_concurrent_tasks
        });
      }
    }

    // 按负载排序，优先选择负载较低的 Agent
    return available.sort((a, b) => a.currentLoad - b.currentLoad);
  }

  /**
   * 选择最佳 Agent
   */
  selectBestAgent(capability, options = {}) {
    const available = this.getAvailableAgents(capability);

    if (available.length === 0) {
      return null;
    }

    // 简单的负载均衡策略
    return available[0];
  }

  /**
   * 处理 Agent 状态变化
   */
  handleAgentStatusChange(agentType, status) {
    const agentInfo = this.agents.get(agentType);
    if (agentInfo) {
      agentInfo.status = status.status || agentInfo.status;

      this.emit('agent_status_change', {
        agentType,
        name: agentInfo.config.name,
        status: agentInfo.status,
        details: status,
        timestamp: new Date().toISOString()
      });
    }
  }

  /**
   * 重启 Agent
   */
  async restartAgent(agentType) {
    const agentInfo = this.agents.get(agentType);
    if (!agentInfo) {
      throw new Error(`Agent 不存在: ${agentType}`);
    }

    try {
      // 停止现有实例
      const existingInstance = this.agentInstances.get(agentType);
      if (existingInstance) {
        await existingInstance.stop();
        this.agentInstances.delete(agentType);
      }

      // 创建新实例
      const instance = new agentInfo.class(agentInfo.config);
      await instance.initialize();

      this.agentInstances.set(agentType, instance);
      agentInfo.status = 'running';

      // 重新监听状态变化
      instance.on('status_change', (status) => {
        this.handleAgentStatusChange(agentType, status);
      });

      console.log(`🔄 重启 Agent 成功: ${agentInfo.config.name}`);

      this.emit('agent_restarted', {
        agentType,
        name: agentInfo.config.name,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error(`❌ 重启 Agent 失败 (${agentType}):`, error);
      agentInfo.status = 'error';
      throw error;
    }
  }

  /**
   * 停止所有 Agent
   */
  async cleanup() {
    console.log('🧹 正在清理 Agent 管理器...');

    for (const [agentType, instance] of this.agentInstances.entries()) {
      try {
        await instance.stop();
        console.log(`🛑 停止 Agent: ${agentType}`);
      } catch (error) {
        console.error(`❌ 停止 Agent 失败 (${agentType}):`, error);
      }
    }

    this.agentInstances.clear();
    this.agents.clear();
    this.isInitialized = false;

    console.log('✅ Agent 管理器清理完成');
  }

  /**
   * 检查管理器是否已初始化
   */
  isReady() {
    return this.isInitialized;
  }

  /**
   * 获取统计信息
   */
  getStatistics() {
    const stats = {
      totalAgents: this.agents.size,
      runningAgents: 0,
      totalTasks: 0,
      totalProcessed: 0
    };

    for (const [agentType, agentInfo] of this.agents.entries()) {
      if (agentInfo.status === 'running') {
        stats.runningAgents++;
      }

      const instance = this.agentInstances.get(agentType);
      if (instance) {
        stats.totalTasks += instance.getCurrentTaskCount();
        stats.totalProcessed += instance.getTotalProcessed();
      }
    }

    return stats;
  }
}

module.exports = AgentManager;
