# 🤖 All-Agent

> 为无代码用户打造的完整 AI 项目构建与执行系统

[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Version](https://img.shields.io/badge/version-1.0.0-blue.svg)](https://github.com/your-username/all-agent)
[![Status](https://img.shields.io/badge/status-active-green.svg)](https://github.com/your-username/all-agent)

## 🎯 项目简介

All-Agent 是一个革命性的 AI 项目构建系统，专为没有编程经验的用户设计。通过智能分析、自动规划和可视化管理，让任何人都能轻松构建和管理复杂的软件项目。

### ✨ 核心特性

- 🔍 **智能项目分析** - 自动分析项目结构和技术栈
- 📋 **自动化规划** - AI 驱动的项目规划和任务分解
- ⚡ **代码自动生成** - 基于需求自动生成高质量代码
- 🎨 **可视化界面** - 直观的 Web 界面和实时监控
- 🤖 **多 Agent 协作** - 专业化 AI Agent 协同工作
- 📚 **模板库管理** - 丰富的 Prompt 模板和最佳实践

## 🚀 快速开始

### 1. 项目结构

```
all-agent/
├── .all-agent/                 # 核心配置目录
│   ├── server/                # 后端服务器
│   │   ├── app.js            # 主服务器文件
│   │   ├── core/             # 核心模块
│   │   ├── agents/           # Agent 实现
│   │   ├── api/              # API 服务
│   │   ├── utils/            # 工具类
│   │   └── package.json      # 服务器依赖
│   ├── ui/                   # Web 界面
│   │   ├── chat_panel.html   # 聊天面板
│   │   ├── agent_trace.html  # Agent 追踪
│   │   └── structure_view.html # 结构视图
│   ├── prompts/              # Prompt 模板库
│   ├── modules/              # 模块文档
│   ├── tasks/                # 任务记录
│   ├── logs/                 # 执行日志
│   ├── start.sh              # 启动脚本
│   └── stop.sh               # 停止脚本
└── README.md                 # 项目说明
```

### 2. 环境要求

- **Node.js**: >= 16.0.0
- **npm**: >= 8.0.0
- **操作系统**: macOS, Linux, Windows

### 3. 安装和启动

#### 快速启动
```bash
# 进入项目目录
cd all-agent/.all-agent

# 启动服务器（自动安装依赖）
./start.sh

# 或者以开发模式启动
./start.sh dev
```

#### 手动安装
```bash
# 进入服务器目录
cd .all-agent/server

# 安装依赖
npm install

# 配置环境变量
cp .env.example .env
# 编辑 .env 文件，配置您的 API 密钥

# 启动服务器
npm start
```

### 4. 访问界面

服务器启动后，您可以通过以下地址访问：

- **🎨 聊天面板**: http://localhost:3000/ui/chat_panel.html
- **📊 Agent 追踪**: http://localhost:3000/ui/agent_trace.html
- **🗺️ 结构视图**: http://localhost:3000/ui/structure_view.html
- **🔧 API 文档**: http://localhost:3000/health

### 3. 核心 Agent

| Agent            | 功能               | 专长领域                       |
| ---------------- | ------------------ | ------------------------------ |
| 🔍 **分析 Agent** | 项目分析、代码理解 | 结构分析、技术栈检测、依赖关系 |
| 📋 **规划 Agent** | 项目规划、任务分解 | 需求分析、路线制定、风险评估   |
| ⚡ **执行 Agent** | 代码生成、任务执行 | 文件操作、依赖管理、自动化部署 |

## 📚 使用指南

### 基础使用流程

1. **项目初始化**
   ```
   用户描述需求 → 分析 Agent 分析 → 规划 Agent 制定计划 → 执行 Agent 实施
   ```

2. **功能开发**
   ```
   功能需求 → 需求分析 → 实现规划 → 代码生成 → 测试验证
   ```

3. **问题诊断**
   ```
   错误报告 → 问题诊断 → 修复策略 → 自动修复 → 验证测试
   ```

### Prompt 模板使用

#### 生成 UI 登录页面
```json
{
  "template": "generate_ui_login",
  "parameters": {
    "project_type": "web应用",
    "design_style": "现代简约",
    "tech_stack": "React",
    "special_requirements": "支持社交登录"
  }
}
```

#### 代码重构优化
```json
{
  "template": "refactor_code",
  "parameters": {
    "file_path": "src/components/UserList.js",
    "refactor_goals": ["提高可读性", "优化性能"],
    "performance_requirements": "高"
  }
}
```

## 🏗️ 架构设计

### 系统架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   用户界面层     │    │   Agent 管理层   │    │   核心引擎层     │
│                │    │                │    │                │
│ • 聊天面板      │◄──►│ • Agent 注册    │◄──►│ • 项目分析      │
│ • 追踪界面      │    │ • 任务调度      │    │ • 文档生成      │
│ • 结构视图      │    │ • 状态监控      │    │ • 模板引擎      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 数据流
```
用户输入 → 需求分析 → 任务分解 → Agent 执行 → 结果聚合 → 用户反馈
```

## 🔧 配置说明

### Agent 配置 (agents.json)
```json
{
  "agents": {
    "analyzer": {
      "name": "分析 Agent",
      "capabilities": ["project_analysis", "code_understanding"],
      "max_concurrent_tasks": 3,
      "timeout": 300
    }
  }
}
```

### 项目配置
- **扫描深度**: 默认 5 层目录
- **支持语言**: JavaScript, Python, Java, TypeScript, Go
- **并发限制**: 最多 20 个并发任务
- **超时设置**: 默认 5 分钟

## 📖 文档

### 核心文档
- [📋 项目总览](.all-agent/project_summary.md) - 项目目标和规划
- [🔧 核心引擎](.all-agent/modules/core.md) - 核心功能模块
- [🎨 用户界面](.all-agent/modules/ui.md) - 界面设计和交互
- [🤖 Agent 管理](.all-agent/modules/agents.md) - Agent 配置和管理

### Prompt 模板
- [🎨 UI 生成模板](.all-agent/prompts/generate_ui_login.json)
- [🔧 代码重构模板](.all-agent/prompts/refactor_prompt.json)
- [🐛 调试诊断模板](.all-agent/prompts/debug_prompt.json)

## 🎯 适用场景

### 目标用户
- 📊 **产品经理** - 快速原型验证和需求实现
- 🎨 **设计师** - 将设计稿转换为可交互原型
- 💼 **创业者** - 快速 MVP 开发和迭代
- 🎓 **学生** - 学习项目和课程作业
- 🏢 **小企业** - 内部工具和业务系统开发

### 应用场景
- 🌐 **Web 应用开发** - 从需求到部署的全流程
- 📱 **移动应用原型** - 快速原型和功能验证
- 🔧 **内部工具开发** - 企业内部管理系统
- 📊 **数据分析工具** - 数据处理和可视化
- 🤖 **自动化脚本** - 重复任务的自动化

## 🔮 发展路线

### Phase 1: 基础功能 (已完成)
- [x] 项目结构和核心文档
- [x] 基础 Agent 配置
- [x] Web 界面原型
- [x] Prompt 模板库

### Phase 2: 核心实现 (进行中)
- [ ] Agent 通信机制
- [ ] 项目分析引擎
- [ ] 代码生成功能
- [ ] 实时状态同步

### Phase 3: 功能增强 (规划中)
- [ ] 高级可视化
- [ ] 协作功能
- [ ] 插件系统
- [ ] 云端部署

### Phase 4: 生态建设 (未来)
- [ ] Agent 市场
- [ ] 模板商店
- [ ] 社区功能
- [ ] 企业版本

## 🤝 贡献指南

我们欢迎所有形式的贡献！

### 贡献方式
- 🐛 **报告 Bug** - 提交 Issue 描述问题
- 💡 **功能建议** - 分享你的想法和需求
- 📝 **文档改进** - 完善文档和示例
- 🔧 **代码贡献** - 提交 Pull Request

### 开发环境
```bash
# 克隆项目
git clone https://github.com/your-username/all-agent.git

# 进入目录
cd all-agent

# 查看项目结构
ls -la .all-agent/
```

## 📄 许可证

本项目采用 [MIT 许可证](LICENSE)，你可以自由使用、修改和分发。

## 📞 联系我们

- 📧 **邮箱**: <EMAIL>
- 💬 **讨论**: [GitHub Discussions](https://github.com/your-username/all-agent/discussions)
- 🐛 **问题**: [GitHub Issues](https://github.com/your-username/all-agent/issues)
- 📖 **文档**: [项目文档](.all-agent/project_summary.md)

---

<div align="center">

**🌟 如果这个项目对你有帮助，请给我们一个 Star！**

Made with ❤️ by All-Agent Team

</div>
