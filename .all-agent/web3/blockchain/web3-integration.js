const { ethers } = require('ethers');
const { Web3Storage } = require('web3.storage');
const IPFS = require('ipfs-http-client');
const crypto = require('crypto');

/**
 * Web3 区块链集成服务
 * 支持智能合约、IPFS 存储、去中心化身份和 DeFi 集成
 */
class Web3Integration {
    constructor(options = {}) {
        this.options = {
            ethereumRpcUrl: options.ethereumRpcUrl || process.env.ETHEREUM_RPC_URL || 'https://mainnet.infura.io/v3/your-project-id',
            polygonRpcUrl: options.polygonRpcUrl || process.env.POLYGON_RPC_URL || 'https://polygon-rpc.com',
            ipfsGateway: options.ipfsGateway || process.env.IPFS_GATEWAY || 'https://ipfs.io/ipfs/',
            web3StorageToken: options.web3StorageToken || process.env.WEB3_STORAGE_TOKEN,
            privateKey: options.privateKey || process.env.PRIVATE_KEY,
            contractAddresses: options.contractAddresses || {},
            enableIPFS: options.enableIPFS !== false,
            enableDID: options.enableDID !== false,
            ...options
        };

        this.providers = {};
        this.wallets = {};
        this.contracts = {};
        this.ipfsClient = null;
        this.web3Storage = null;
        this.didDocuments = new Map();
        this.transactions = [];

        this.initializeWeb3();
    }

    /**
     * 初始化 Web3 服务
     */
    async initializeWeb3() {
        try {
            console.log('🌐 初始化 Web3 集成服务...');

            // 初始化区块链提供者
            await this.initializeProviders();

            // 初始化钱包
            await this.initializeWallets();

            // 初始化 IPFS
            if (this.options.enableIPFS) {
                await this.initializeIPFS();
            }

            // 初始化智能合约
            await this.initializeContracts();

            console.log('✅ Web3 集成服务初始化完成');

        } catch (error) {
            console.error('❌ Web3 初始化失败:', error);
            throw error;
        }
    }

    /**
     * 初始化区块链提供者
     */
    async initializeProviders() {
        // Ethereum 主网
        this.providers.ethereum = new ethers.providers.JsonRpcProvider(this.options.ethereumRpcUrl);
        
        // Polygon 网络
        this.providers.polygon = new ethers.providers.JsonRpcProvider(this.options.polygonRpcUrl);

        // 测试连接
        try {
            const ethNetwork = await this.providers.ethereum.getNetwork();
            console.log(`🔗 连接到 Ethereum 网络: ${ethNetwork.name} (${ethNetwork.chainId})`);

            const polygonNetwork = await this.providers.polygon.getNetwork();
            console.log(`🔗 连接到 Polygon 网络: ${polygonNetwork.name} (${polygonNetwork.chainId})`);
        } catch (error) {
            console.warn('⚠️ 区块链网络连接警告:', error.message);
        }
    }

    /**
     * 初始化钱包
     */
    async initializeWallets() {
        if (this.options.privateKey) {
            this.wallets.ethereum = new ethers.Wallet(this.options.privateKey, this.providers.ethereum);
            this.wallets.polygon = new ethers.Wallet(this.options.privateKey, this.providers.polygon);
            
            console.log(`👛 钱包地址: ${this.wallets.ethereum.address}`);
        } else {
            console.warn('⚠️ 未提供私钥，某些功能将不可用');
        }
    }

    /**
     * 初始化 IPFS
     */
    async initializeIPFS() {
        try {
            // 初始化 IPFS HTTP 客户端
            this.ipfsClient = IPFS.create({
                host: 'ipfs.infura.io',
                port: 5001,
                protocol: 'https',
                headers: {
                    authorization: 'Basic ' + Buffer.from(
                        process.env.INFURA_PROJECT_ID + ':' + process.env.INFURA_PROJECT_SECRET
                    ).toString('base64')
                }
            });

            // 初始化 Web3.Storage
            if (this.options.web3StorageToken) {
                this.web3Storage = new Web3Storage({ token: this.options.web3StorageToken });
            }

            console.log('📁 IPFS 客户端初始化完成');
        } catch (error) {
            console.warn('⚠️ IPFS 初始化失败:', error.message);
        }
    }

    /**
     * 初始化智能合约
     */
    async initializeContracts() {
        // All-Agent 数据存储合约 ABI
        const dataStorageABI = [
            "function storeData(string memory dataHash, string memory metadata) public",
            "function getData(string memory dataHash) public view returns (string memory)",
            "function verifyData(string memory dataHash, string memory signature) public view returns (bool)",
            "event DataStored(string indexed dataHash, address indexed owner, uint256 timestamp)"
        ];

        // All-Agent NFT 合约 ABI
        const nftABI = [
            "function mintAIModel(address to, string memory tokenURI, string memory modelHash) public returns (uint256)",
            "function getModelInfo(uint256 tokenId) public view returns (string memory, string memory)",
            "function transferModel(address to, uint256 tokenId) public",
            "event ModelMinted(uint256 indexed tokenId, address indexed owner, string modelHash)"
        ];

        // 初始化合约实例
        if (this.options.contractAddresses.dataStorage && this.wallets.ethereum) {
            this.contracts.dataStorage = new ethers.Contract(
                this.options.contractAddresses.dataStorage,
                dataStorageABI,
                this.wallets.ethereum
            );
        }

        if (this.options.contractAddresses.nft && this.wallets.ethereum) {
            this.contracts.nft = new ethers.Contract(
                this.options.contractAddresses.nft,
                nftABI,
                this.wallets.ethereum
            );
        }
    }

    /**
     * 存储数据到 IPFS
     */
    async storeToIPFS(data, options = {}) {
        try {
            let cid;

            if (typeof data === 'string') {
                // 存储文本数据
                const file = new File([data], options.filename || 'data.txt', {
                    type: options.contentType || 'text/plain'
                });

                if (this.web3Storage) {
                    cid = await this.web3Storage.put([file]);
                } else if (this.ipfsClient) {
                    const result = await this.ipfsClient.add(data);
                    cid = result.cid.toString();
                }
            } else if (Buffer.isBuffer(data)) {
                // 存储二进制数据
                if (this.ipfsClient) {
                    const result = await this.ipfsClient.add(data);
                    cid = result.cid.toString();
                }
            } else {
                // 存储 JSON 数据
                const jsonString = JSON.stringify(data, null, 2);
                const file = new File([jsonString], options.filename || 'data.json', {
                    type: 'application/json'
                });

                if (this.web3Storage) {
                    cid = await this.web3Storage.put([file]);
                } else if (this.ipfsClient) {
                    const result = await this.ipfsClient.add(jsonString);
                    cid = result.cid.toString();
                }
            }

            console.log(`📁 数据已存储到 IPFS: ${cid}`);

            return {
                cid,
                url: `${this.options.ipfsGateway}${cid}`,
                size: data.length || Buffer.byteLength(JSON.stringify(data)),
                timestamp: new Date().toISOString()
            };

        } catch (error) {
            console.error('IPFS 存储失败:', error);
            throw error;
        }
    }

    /**
     * 从 IPFS 获取数据
     */
    async getFromIPFS(cid) {
        try {
            if (this.ipfsClient) {
                const chunks = [];
                for await (const chunk of this.ipfsClient.cat(cid)) {
                    chunks.push(chunk);
                }
                const data = Buffer.concat(chunks);
                return data.toString();
            } else {
                // 使用 HTTP 网关
                const response = await fetch(`${this.options.ipfsGateway}${cid}`);
                return await response.text();
            }
        } catch (error) {
            console.error('IPFS 获取失败:', error);
            throw error;
        }
    }

    /**
     * 存储 AI 模型到区块链
     */
    async storeAIModel(modelData, metadata = {}) {
        try {
            console.log('🤖 存储 AI 模型到区块链...');

            // 1. 计算模型哈希
            const modelHash = crypto.createHash('sha256').update(JSON.stringify(modelData)).digest('hex');

            // 2. 存储模型数据到 IPFS
            const ipfsResult = await this.storeToIPFS(modelData, {
                filename: `ai-model-${modelHash}.json`,
                contentType: 'application/json'
            });

            // 3. 存储元数据到 IPFS
            const fullMetadata = {
                ...metadata,
                modelHash,
                ipfsCid: ipfsResult.cid,
                timestamp: new Date().toISOString(),
                version: '1.0.0'
            };

            const metadataResult = await this.storeToIPFS(fullMetadata, {
                filename: `metadata-${modelHash}.json`
            });

            // 4. 存储到区块链
            let txHash = null;
            if (this.contracts.dataStorage) {
                const tx = await this.contracts.dataStorage.storeData(
                    modelHash,
                    metadataResult.cid
                );
                await tx.wait();
                txHash = tx.hash;
                console.log(`⛓️ 区块链交易: ${txHash}`);
            }

            const result = {
                modelHash,
                ipfsCid: ipfsResult.cid,
                metadataCid: metadataResult.cid,
                txHash,
                ipfsUrl: ipfsResult.url,
                metadata: fullMetadata,
                timestamp: new Date().toISOString()
            };

            this.transactions.push(result);
            console.log('✅ AI 模型已存储到区块链');

            return result;

        } catch (error) {
            console.error('AI 模型存储失败:', error);
            throw error;
        }
    }

    /**
     * 铸造 AI 模型 NFT
     */
    async mintAIModelNFT(modelHash, recipient, metadata = {}) {
        try {
            if (!this.contracts.nft) {
                throw new Error('NFT 合约未初始化');
            }

            console.log('🎨 铸造 AI 模型 NFT...');

            // 创建 NFT 元数据
            const nftMetadata = {
                name: metadata.name || `AI Model ${modelHash.substring(0, 8)}`,
                description: metadata.description || 'AI Model stored on All-Agent platform',
                image: metadata.image || `https://api.dicebear.com/7.x/shapes/svg?seed=${modelHash}`,
                attributes: [
                    { trait_type: 'Model Hash', value: modelHash },
                    { trait_type: 'Created By', value: 'All-Agent' },
                    { trait_type: 'Timestamp', value: new Date().toISOString() },
                    ...(metadata.attributes || [])
                ],
                external_url: metadata.external_url || 'https://all-agent.com',
                modelHash
            };

            // 存储 NFT 元数据到 IPFS
            const metadataResult = await this.storeToIPFS(nftMetadata, {
                filename: `nft-metadata-${modelHash}.json`
            });

            // 铸造 NFT
            const tx = await this.contracts.nft.mintAIModel(
                recipient,
                `${this.options.ipfsGateway}${metadataResult.cid}`,
                modelHash
            );

            const receipt = await tx.wait();
            const tokenId = receipt.events.find(e => e.event === 'ModelMinted').args.tokenId;

            console.log(`🎨 NFT 已铸造: Token ID ${tokenId}`);

            return {
                tokenId: tokenId.toString(),
                txHash: tx.hash,
                metadataCid: metadataResult.cid,
                metadataUrl: `${this.options.ipfsGateway}${metadataResult.cid}`,
                recipient,
                modelHash
            };

        } catch (error) {
            console.error('NFT 铸造失败:', error);
            throw error;
        }
    }

    /**
     * 创建去中心化身份 (DID)
     */
    async createDID(identity = {}) {
        try {
            console.log('🆔 创建去中心化身份...');

            const did = `did:all-agent:${crypto.randomBytes(16).toString('hex')}`;
            
            const didDocument = {
                '@context': [
                    'https://www.w3.org/ns/did/v1',
                    'https://w3id.org/security/suites/ed25519-2020/v1'
                ],
                id: did,
                verificationMethod: [{
                    id: `${did}#key-1`,
                    type: 'Ed25519VerificationKey2020',
                    controller: did,
                    publicKeyMultibase: this.generatePublicKey()
                }],
                authentication: [`${did}#key-1`],
                assertionMethod: [`${did}#key-1`],
                service: [{
                    id: `${did}#all-agent-service`,
                    type: 'AllAgentService',
                    serviceEndpoint: 'https://all-agent.com/api/did'
                }],
                created: new Date().toISOString(),
                updated: new Date().toISOString(),
                ...identity
            };

            // 存储 DID 文档到 IPFS
            const ipfsResult = await this.storeToIPFS(didDocument, {
                filename: `did-document-${did.split(':')[2]}.json`
            });

            this.didDocuments.set(did, {
                document: didDocument,
                ipfsCid: ipfsResult.cid,
                created: new Date().toISOString()
            });

            console.log(`🆔 DID 已创建: ${did}`);

            return {
                did,
                document: didDocument,
                ipfsCid: ipfsResult.cid,
                ipfsUrl: ipfsResult.url
            };

        } catch (error) {
            console.error('DID 创建失败:', error);
            throw error;
        }
    }

    /**
     * 验证 DID 文档
     */
    async verifyDID(did) {
        try {
            const didInfo = this.didDocuments.get(did);
            if (!didInfo) {
                throw new Error('DID 不存在');
            }

            // 从 IPFS 获取最新的 DID 文档
            const latestDocument = await this.getFromIPFS(didInfo.ipfsCid);
            const parsedDocument = JSON.parse(latestDocument);

            // 验证 DID 文档的完整性
            const isValid = this.validateDIDDocument(parsedDocument);

            return {
                did,
                isValid,
                document: parsedDocument,
                lastVerified: new Date().toISOString()
            };

        } catch (error) {
            console.error('DID 验证失败:', error);
            throw error;
        }
    }

    /**
     * 创建可验证凭证
     */
    async createVerifiableCredential(issuer, subject, claims) {
        try {
            const credential = {
                '@context': [
                    'https://www.w3.org/2018/credentials/v1',
                    'https://all-agent.com/contexts/v1'
                ],
                id: `urn:uuid:${crypto.randomUUID()}`,
                type: ['VerifiableCredential', 'AllAgentCredential'],
                issuer,
                issuanceDate: new Date().toISOString(),
                credentialSubject: {
                    id: subject,
                    ...claims
                },
                proof: {
                    type: 'Ed25519Signature2020',
                    created: new Date().toISOString(),
                    verificationMethod: `${issuer}#key-1`,
                    proofPurpose: 'assertionMethod',
                    proofValue: this.generateProof(claims)
                }
            };

            // 存储凭证到 IPFS
            const ipfsResult = await this.storeToIPFS(credential, {
                filename: `credential-${credential.id.split(':')[2]}.json`
            });

            return {
                credential,
                ipfsCid: ipfsResult.cid,
                ipfsUrl: ipfsResult.url
            };

        } catch (error) {
            console.error('可验证凭证创建失败:', error);
            throw error;
        }
    }

    /**
     * DeFi 集成 - 代币交换
     */
    async swapTokens(tokenA, tokenB, amount, slippage = 0.5) {
        try {
            console.log(`💱 交换代币: ${amount} ${tokenA} -> ${tokenB}`);

            // 这里应该集成 Uniswap 或其他 DEX
            // 简化实现
            const mockSwapResult = {
                txHash: '0x' + crypto.randomBytes(32).toString('hex'),
                inputToken: tokenA,
                outputToken: tokenB,
                inputAmount: amount,
                outputAmount: amount * 0.998, // 模拟滑点
                slippage,
                timestamp: new Date().toISOString()
            };

            console.log(`✅ 代币交换完成: ${mockSwapResult.outputAmount} ${tokenB}`);
            return mockSwapResult;

        } catch (error) {
            console.error('代币交换失败:', error);
            throw error;
        }
    }

    /**
     * 获取代币余额
     */
    async getTokenBalance(tokenAddress, walletAddress) {
        try {
            const tokenABI = [
                "function balanceOf(address owner) view returns (uint256)",
                "function decimals() view returns (uint8)",
                "function symbol() view returns (string)"
            ];

            const tokenContract = new ethers.Contract(tokenAddress, tokenABI, this.providers.ethereum);
            
            const balance = await tokenContract.balanceOf(walletAddress);
            const decimals = await tokenContract.decimals();
            const symbol = await tokenContract.symbol();

            const formattedBalance = ethers.utils.formatUnits(balance, decimals);

            return {
                balance: formattedBalance,
                symbol,
                decimals,
                raw: balance.toString()
            };

        } catch (error) {
            console.error('获取代币余额失败:', error);
            throw error;
        }
    }

    /**
     * 辅助方法
     */
    generatePublicKey() {
        return 'z' + crypto.randomBytes(32).toString('hex');
    }

    generateProof(data) {
        return crypto.createHash('sha256').update(JSON.stringify(data)).digest('hex');
    }

    validateDIDDocument(document) {
        return document && document.id && document.verificationMethod && document.authentication;
    }

    /**
     * 获取 Web3 状态
     */
    getStatus() {
        return {
            providers: Object.keys(this.providers),
            wallets: Object.keys(this.wallets),
            contracts: Object.keys(this.contracts),
            ipfsEnabled: !!this.ipfsClient,
            web3StorageEnabled: !!this.web3Storage,
            didDocuments: this.didDocuments.size,
            transactions: this.transactions.length,
            status: 'connected'
        };
    }

    /**
     * 获取交易历史
     */
    getTransactionHistory() {
        return this.transactions.map(tx => ({
            ...tx,
            explorerUrl: `https://etherscan.io/tx/${tx.txHash}`
        }));
    }

    /**
     * 获取 DID 列表
     */
    getDIDList() {
        return Array.from(this.didDocuments.entries()).map(([did, info]) => ({
            did,
            created: info.created,
            ipfsCid: info.ipfsCid
        }));
    }
}

module.exports = Web3Integration;
