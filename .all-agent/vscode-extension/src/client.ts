import * as vscode from 'vscode';
import axios from 'axios';
import * as WebSocket from 'ws';
import { TerminalService } from './services/terminalService';
import { BrowserService } from './services/browserService';
import { ContextService } from './services/contextService';

export class AllAgentClient {
    private baseUrl: string;
    private ws: WebSocket | null = null;
    private authToken: string | null = null;
    private terminalService: TerminalService;
    private browserService: BrowserService;
    private contextService: ContextService;

    constructor() {
        const config = vscode.workspace.getConfiguration('all-agent');
        const port = config.get('serverPort', 3000);
        this.baseUrl = `http://localhost:${port}`;

        // 初始化服务
        this.terminalService = new TerminalService();
        this.browserService = new BrowserService(this.terminalService);
        this.contextService = new ContextService();
    }

    async connect(): Promise<void> {
        try {
            // 首先尝试登录获取认证令牌
            await this.authenticate();

            // 建立 WebSocket 连接
            const wsUrl = this.baseUrl.replace('http', 'ws');
            this.ws = new WebSocket(wsUrl);

            return new Promise((resolve, reject) => {
                if (!this.ws) {
                    reject(new Error('WebSocket not initialized'));
                    return;
                }

                this.ws.on('open', () => {
                    console.log('Connected to All-Agent server');

                    // 发送认证信息
                    if (this.ws && this.authToken) {
                        this.ws.send(JSON.stringify({
                            type: 'auth',
                            token: this.authToken
                        }));
                    }

                    resolve();
                });

                this.ws.on('error', (error: Error) => {
                    console.error('WebSocket error:', error);
                    reject(error);
                });

                this.ws.on('close', () => {
                    console.log('Disconnected from All-Agent server');
                    this.ws = null;
                });
            });
        } catch (error) {
            throw new Error(`Failed to connect to All-Agent server: ${error}`);
        }
    }

    async disconnect(): Promise<void> {
        if (this.ws) {
            this.ws.close();
            this.ws = null;
        }
    }

    async sendMessage(message: string): Promise<string> {
        try {
            const response = await axios.post(`${this.baseUrl}/api/chat`, {
                message: message,
                agentType: 'analyzer' // 默认使用分析 Agent
            }, {
                headers: {
                    'Authorization': `Bearer ${this.authToken}`,
                    'Content-Type': 'application/json'
                }
            });

            return response.data.response || 'No response received';
        } catch (error) {
            throw new Error(`Failed to send message: ${error}`);
        }
    }

    async analyzeProject(projectPath: string): Promise<any> {
        try {
            const response = await axios.post(`${this.baseUrl}/api/analyze`, {
                path: projectPath,
                options: {
                    includeFiles: true,
                    includeDependencies: true,
                    includeStructure: true
                }
            }, {
                headers: {
                    'Authorization': `Bearer ${this.authToken}`,
                    'Content-Type': 'application/json'
                }
            });

            return response.data;
        } catch (error) {
            throw new Error(`Failed to analyze project: ${error}`);
        }
    }

    async generateCode(context: string, prompt: string): Promise<string> {
        try {
            const response = await axios.post(`${this.baseUrl}/api/generate`, {
                context: context,
                prompt: prompt,
                type: 'code'
            }, {
                headers: {
                    'Authorization': `Bearer ${this.authToken}`,
                    'Content-Type': 'application/json'
                }
            });

            return response.data.code || response.data.result || 'No code generated';
        } catch (error) {
            throw new Error(`Failed to generate code: ${error}`);
        }
    }

    async getAgents(): Promise<any[]> {
        try {
            const response = await axios.get(`${this.baseUrl}/api/agents`, {
                headers: {
                    'Authorization': `Bearer ${this.authToken}`
                }
            });

            return response.data.agents || [];
        } catch (error) {
            console.error('Failed to get agents:', error);
            return [];
        }
    }

    async getTasks(): Promise<any[]> {
        try {
            const response = await axios.get(`${this.baseUrl}/api/tasks`, {
                headers: {
                    'Authorization': `Bearer ${this.authToken}`
                }
            });

            return response.data.tasks || [];
        } catch (error) {
            console.error('Failed to get tasks:', error);
            return [];
        }
    }

    private async authenticate(): Promise<void> {
        try {
            // 尝试使用默认管理员账户登录
            const response = await axios.post(`${this.baseUrl}/auth/login`, {
                email: '<EMAIL>',
                password: 'Cjb65691910'
            });

            this.authToken = response.data.token;
        } catch (error) {
            // 如果管理员登录失败，尝试创建临时用户
            try {
                const registerResponse = await axios.post(`${this.baseUrl}/auth/register`, {
                    username: 'vscode-extension',
                    email: '<EMAIL>',
                    password: 'vscode123456'
                });

                if (registerResponse.data.token) {
                    this.authToken = registerResponse.data.token;
                } else {
                    // 注册成功后登录
                    const loginResponse = await axios.post(`${this.baseUrl}/auth/login`, {
                        username: 'vscode-extension',
                        password: 'vscode123456'
                    });
                    this.authToken = loginResponse.data.token;
                }
            } catch (authError) {
                throw new Error(`Authentication failed: ${authError}`);
            }
        }
    }

    isConnected(): boolean {
        return this.ws !== null && this.ws.readyState === WebSocket.OPEN;
    }

    /**
     * 处理终端请求
     */
    async handleTerminalRequest(message: string): Promise<string> {
        try {
            // 分析消息中的命令
            const commandMatch = message.match(/(?:run|execute|command)[\s:]*(.+)/i);
            if (commandMatch) {
                const command = commandMatch[1].trim();
                const result = await this.terminalService.executeCommand(command, {
                    showOutput: true,
                    timeout: 30000
                });

                if (result.success) {
                    return `✅ Command executed successfully:\n\`\`\`\n${result.output}\n\`\`\``;
                } else {
                    return `❌ Command failed:\n\`\`\`\n${result.error || 'Unknown error'}\n\`\`\``;
                }
            }

            // 如果没有明确的命令，发送到 AI 处理
            return await this.sendMessage(`Terminal request: ${message}`);
        } catch (error) {
            return `❌ Terminal error: ${error instanceof Error ? error.message : String(error)}`;
        }
    }

    /**
     * 处理文件请求
     */
    async handleFileRequest(message: string, context: any): Promise<string> {
        try {
            const contextualMessage = `File operation request in ${context.workspace?.name || 'unknown project'}:
Current file: ${context.editor?.fileName || 'none'}
Selected text: ${context.editor?.selection?.text ? 'yes' : 'no'}
Request: ${message}`;

            return await this.sendMessage(contextualMessage);
        } catch (error) {
            return `❌ File operation error: ${error instanceof Error ? error.message : String(error)}`;
        }
    }

    /**
     * 处理分析请求
     */
    async handleAnalysisRequest(message: string, context: any): Promise<string> {
        try {
            const projectInfo = context.project || {};
            const analysisContext = `Project analysis request:
Project: ${context.workspace?.name || 'unknown'}
Type: ${projectInfo.type || 'unknown'}
Technologies: ${projectInfo.technologies?.join(', ') || 'none detected'}
Has tests: ${projectInfo.hasTests ? 'yes' : 'no'}
Recent errors: ${context.errors?.length || 0}
Request: ${message}`;

            return await this.sendMessage(analysisContext);
        } catch (error) {
            return `❌ Analysis error: ${error instanceof Error ? error.message : String(error)}`;
        }
    }

    /**
     * 处理浏览器请求
     */
    async handleBrowserRequest(message: string): Promise<string> {
        try {
            // 检测本地服务器
            const servers = await this.browserService.detectLocalServers();

            let response = `🌐 Browser request: ${message}\n\n`;

            if (servers.length > 0) {
                response += `**Detected local servers:**\n`;
                servers.forEach(server => {
                    response += `• ${server.url} (${server.service || 'Unknown service'})\n`;
                });
                response += '\n';
            } else {
                response += `No local servers detected on common ports.\n\n`;
            }

            // 如果消息包含 URL，检查页面状态
            const urlMatch = message.match(/(https?:\/\/[^\s]+)/);
            if (urlMatch) {
                const url = urlMatch[1];
                const status = await this.browserService.checkPageStatus(url);
                response += `**Page status for ${url}:**\n`;
                response += `• Status: ${status.status}\n`;
                response += `• Accessible: ${status.accessible ? 'Yes' : 'No'}\n`;
                response += `• Response time: ${status.responseTime}ms\n`;
            }

            return response;
        } catch (error) {
            return `❌ Browser error: ${error instanceof Error ? error.message : String(error)}`;
        }
    }

    /**
     * 处理调试请求
     */
    async handleDebugRequest(message: string, context: any): Promise<string> {
        try {
            const errors = context.errors || [];
            let debugContext = `Debug request: ${message}\n\n`;

            if (errors.length > 0) {
                debugContext += `**Current errors (${errors.length}):**\n`;
                errors.slice(0, 5).forEach((error: any, index: number) => {
                    debugContext += `${index + 1}. ${error.file}:${error.line} - ${error.message}\n`;
                });
                debugContext += '\n';
            } else {
                debugContext += `No current errors detected.\n\n`;
            }

            debugContext += `**Project context:**\n`;
            debugContext += `• Type: ${context.project?.type || 'unknown'}\n`;
            debugContext += `• Technologies: ${context.project?.technologies?.join(', ') || 'none'}\n`;
            debugContext += `• Has tests: ${context.project?.hasTests ? 'yes' : 'no'}\n`;

            return await this.sendMessage(debugContext);
        } catch (error) {
            return `❌ Debug error: ${error instanceof Error ? error.message : String(error)}`;
        }
    }

    /**
     * 获取上下文服务
     */
    getContextService(): ContextService {
        return this.contextService;
    }

    /**
     * 获取终端服务
     */
    getTerminalService(): TerminalService {
        return this.terminalService;
    }

    /**
     * 获取浏览器服务
     */
    getBrowserService(): BrowserService {
        return this.browserService;
    }

    updateConfiguration(): void {
        const config = vscode.workspace.getConfiguration('all-agent');
        const port = config.get('serverPort', 3000);
        this.baseUrl = `http://localhost:${port}`;

        // 如果已连接，重新连接以使用新配置
        if (this.isConnected()) {
            this.disconnect().then(() => {
                this.connect().catch(error => {
                    console.error('Failed to reconnect:', error);
                });
            });
        }
    }

    /**
     * 清理资源
     */
    dispose(): void {
        this.disconnect();
        this.terminalService.dispose();
        this.browserService.dispose();
        this.contextService.dispose();
    }
}
