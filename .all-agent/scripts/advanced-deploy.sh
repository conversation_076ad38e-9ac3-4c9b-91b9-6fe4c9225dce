#!/bin/bash

# All-Agent 高级部署脚本
# 支持 GitOps、Chaos Engineering、ML Platform、FaaS

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
FEATURE=${1:-all}
ENVIRONMENT=${2:-production}

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_feature() {
    echo -e "${PURPLE}[FEATURE]${NC} $1"
}

log_deploy() {
    echo -e "${CYAN}[DEPLOY]${NC} $1"
}

# 显示帮助信息
show_help() {
    cat << EOF
All-Agent 高级部署脚本

用法:
    $0 [功能] [环境]

功能:
    all             部署所有高级功能 (默认)
    gitops          部署 GitOps (ArgoCD)
    chaos           部署混沌工程 (Chaos Mesh)
    ml-platform     部署 ML 平台 (MLflow)
    faas            部署 FaaS (Knative)
    monitoring      部署高级监控
    security        部署安全组件

环境:
    development     开发环境
    staging         测试环境
    production      生产环境 (默认)

示例:
    $0 all production
    $0 gitops development
    $0 chaos staging
    $0 ml-platform production
    $0 faas production

选项:
    -h, --help      显示此帮助信息
    --dry-run       仅显示部署计划
    --skip-deps     跳过依赖检查
    --force         强制重新部署
    --wait          等待部署完成
EOF
}

# 检查依赖
check_dependencies() {
    log_info "检查高级部署依赖..."
    
    # 基础依赖
    local deps=("kubectl" "helm" "git")
    for dep in "${deps[@]}"; do
        if ! command -v $dep &> /dev/null; then
            log_error "$dep 未安装"
            exit 1
        fi
    done
    
    # 检查 Kubernetes 连接
    if ! kubectl cluster-info &> /dev/null; then
        log_error "无法连接到 Kubernetes 集群"
        exit 1
    fi
    
    # 检查 Helm 仓库
    helm repo add argo https://argoproj.github.io/argo-helm &> /dev/null || true
    helm repo add chaos-mesh https://charts.chaos-mesh.org &> /dev/null || true
    helm repo add knative https://knative.github.io/serving &> /dev/null || true
    helm repo update &> /dev/null
    
    log_success "依赖检查完成"
}

# 部署 GitOps (ArgoCD)
deploy_gitops() {
    log_feature "部署 GitOps (ArgoCD)..."
    
    # 创建命名空间
    kubectl create namespace argocd --dry-run=client -o yaml | kubectl apply -f -
    
    # 安装 ArgoCD
    log_deploy "安装 ArgoCD..."
    helm upgrade --install argocd argo/argo-cd \
        --namespace argocd \
        --set server.service.type=LoadBalancer \
        --set server.extraArgs[0]="--insecure" \
        --set configs.params."server\.insecure"=true \
        --wait
    
    # 部署 All-Agent 应用
    log_deploy "部署 All-Agent GitOps 配置..."
    kubectl apply -f "$PROJECT_ROOT/gitops/argocd/application.yaml"
    
    # 获取 ArgoCD 密码
    local argocd_password=$(kubectl -n argocd get secret argocd-initial-admin-secret -o jsonpath="{.data.password}" | base64 -d)
    
    log_success "GitOps 部署完成"
    log_info "ArgoCD 访问信息:"
    log_info "  URL: http://$(kubectl get svc argocd-server -n argocd -o jsonpath='{.status.loadBalancer.ingress[0].ip}')"
    log_info "  用户名: admin"
    log_info "  密码: $argocd_password"
}

# 部署混沌工程 (Chaos Mesh)
deploy_chaos() {
    log_feature "部署混沌工程 (Chaos Mesh)..."
    
    # 创建命名空间
    kubectl create namespace chaos-mesh --dry-run=client -o yaml | kubectl apply -f -
    
    # 安装 Chaos Mesh
    log_deploy "安装 Chaos Mesh..."
    helm upgrade --install chaos-mesh chaos-mesh/chaos-mesh \
        --namespace chaos-mesh \
        --set chaosDaemon.runtime=containerd \
        --set chaosDaemon.socketPath=/run/containerd/containerd.sock \
        --set dashboard.create=true \
        --set dashboard.service.type=LoadBalancer \
        --wait
    
    # 部署混沌实验
    log_deploy "部署混沌实验..."
    kubectl apply -f "$PROJECT_ROOT/chaos/chaos-mesh/experiments.yaml"
    
    # 获取 Dashboard 访问地址
    local dashboard_ip=$(kubectl get svc chaos-dashboard -n chaos-mesh -o jsonpath='{.status.loadBalancer.ingress[0].ip}')
    
    log_success "混沌工程部署完成"
    log_info "Chaos Mesh Dashboard: http://$dashboard_ip:2333"
}

# 部署 ML 平台 (MLflow)
deploy_ml_platform() {
    log_feature "部署 ML 平台 (MLflow)..."
    
    # 创建命名空间
    kubectl create namespace ml-platform --dry-run=client -o yaml | kubectl apply -f -
    
    # 部署 MLflow
    log_deploy "部署 MLflow..."
    kubectl apply -f "$PROJECT_ROOT/ml-platform/mlflow/deployment.yaml"
    
    # 等待部署完成
    if [ "$WAIT_DEPLOYMENT" = "true" ]; then
        log_info "等待 MLflow 部署完成..."
        kubectl wait --for=condition=available --timeout=300s deployment/mlflow-server -n ml-platform
        kubectl wait --for=condition=available --timeout=300s deployment/postgres -n ml-platform
        kubectl wait --for=condition=available --timeout=300s deployment/minio -n ml-platform
    fi
    
    # 端口转发用于访问
    log_deploy "配置 MLflow 访问..."
    kubectl port-forward svc/mlflow-server 5000:5000 -n ml-platform &
    local mlflow_pid=$!
    
    log_success "ML 平台部署完成"
    log_info "MLflow 访问信息:"
    log_info "  URL: http://localhost:5000"
    log_info "  停止端口转发: kill $mlflow_pid"
}

# 部署 FaaS (Knative)
deploy_faas() {
    log_feature "部署 FaaS (Knative)..."
    
    # 安装 Knative Serving
    log_deploy "安装 Knative Serving..."
    kubectl apply -f https://github.com/knative/serving/releases/download/knative-v1.12.0/serving-crds.yaml
    kubectl apply -f https://github.com/knative/serving/releases/download/knative-v1.12.0/serving-core.yaml
    
    # 安装网络层 (Kourier)
    log_deploy "安装 Kourier 网络层..."
    kubectl apply -f https://github.com/knative/net-kourier/releases/download/knative-v1.12.0/kourier.yaml
    
    # 配置 Knative
    kubectl patch configmap/config-network \
        --namespace knative-serving \
        --type merge \
        --patch '{"data":{"ingress-class":"kourier.ingress.networking.knative.dev"}}'
    
    # 等待 Knative 就绪
    kubectl wait --for=condition=ready pod -l app=controller -n knative-serving --timeout=300s
    kubectl wait --for=condition=ready pod -l app=webhook -n knative-serving --timeout=300s
    
    # 部署 FaaS 服务
    log_deploy "部署 FaaS 服务..."
    kubectl apply -f "$PROJECT_ROOT/faas/knative/services.yaml"
    
    # 获取 Kourier 外部 IP
    local kourier_ip=$(kubectl get svc kourier -n kourier-system -o jsonpath='{.status.loadBalancer.ingress[0].ip}')
    
    log_success "FaaS 部署完成"
    log_info "Knative 服务访问:"
    log_info "  外部 IP: $kourier_ip"
    log_info "  文本分析: http://text-analyzer.knative-serving.all-agent.com"
    log_info "  代码生成: http://code-generator.knative-serving.all-agent.com"
    log_info "  数据处理: http://data-processor.knative-serving.all-agent.com"
}

# 部署高级监控
deploy_monitoring() {
    log_feature "部署高级监控..."
    
    # 安装 Prometheus Operator
    log_deploy "安装 Prometheus Operator..."
    helm upgrade --install prometheus-operator prometheus-community/kube-prometheus-stack \
        --namespace monitoring \
        --create-namespace \
        --set prometheus.service.type=LoadBalancer \
        --set grafana.service.type=LoadBalancer \
        --set alertmanager.service.type=LoadBalancer \
        --wait
    
    # 部署自定义监控规则
    log_deploy "部署自定义监控规则..."
    kubectl apply -f - <<EOF
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: all-agent-monitor
  namespace: monitoring
spec:
  selector:
    matchLabels:
      app.kubernetes.io/name: all-agent
  endpoints:
  - port: http
    interval: 30s
    path: /metrics
EOF
    
    # 获取访问信息
    local prometheus_ip=$(kubectl get svc prometheus-operator-kube-p-prometheus -n monitoring -o jsonpath='{.status.loadBalancer.ingress[0].ip}')
    local grafana_ip=$(kubectl get svc prometheus-operator-grafana -n monitoring -o jsonpath='{.status.loadBalancer.ingress[0].ip}')
    local grafana_password=$(kubectl get secret prometheus-operator-grafana -n monitoring -o jsonpath="{.data.admin-password}" | base64 -d)
    
    log_success "高级监控部署完成"
    log_info "监控访问信息:"
    log_info "  Prometheus: http://$prometheus_ip:9090"
    log_info "  Grafana: http://$grafana_ip (admin/$grafana_password)"
}

# 部署安全组件
deploy_security() {
    log_feature "部署安全组件..."
    
    # 安装 Falco (运行时安全)
    log_deploy "安装 Falco..."
    helm repo add falcosecurity https://falcosecurity.github.io/charts
    helm repo update
    helm upgrade --install falco falcosecurity/falco \
        --namespace falco \
        --create-namespace \
        --set falco.grpc.enabled=true \
        --set falco.grpcOutput.enabled=true
    
    # 安装 OPA Gatekeeper (策略引擎)
    log_deploy "安装 OPA Gatekeeper..."
    kubectl apply -f https://raw.githubusercontent.com/open-policy-agent/gatekeeper/release-3.14/deploy/gatekeeper.yaml
    
    # 部署安全策略
    log_deploy "部署安全策略..."
    kubectl apply -f - <<EOF
apiVersion: templates.gatekeeper.sh/v1beta1
kind: ConstraintTemplate
metadata:
  name: k8srequiredsecuritycontext
spec:
  crd:
    spec:
      names:
        kind: K8sRequiredSecurityContext
      validation:
        type: object
        properties:
          runAsNonRoot:
            type: boolean
  targets:
    - target: admission.k8s.gatekeeper.sh
      rego: |
        package k8srequiredsecuritycontext
        
        violation[{"msg": msg}] {
          container := input.review.object.spec.containers[_]
          not container.securityContext.runAsNonRoot
          msg := "Container must run as non-root user"
        }
---
apiVersion: constraints.gatekeeper.sh/v1beta1
kind: K8sRequiredSecurityContext
metadata:
  name: must-run-as-nonroot
spec:
  match:
    kinds:
      - apiGroups: ["apps"]
        kinds: ["Deployment"]
    namespaces: ["all-agent", "ml-platform", "knative-serving"]
  parameters:
    runAsNonRoot: true
EOF
    
    log_success "安全组件部署完成"
}

# 验证部署
verify_deployment() {
    log_info "验证部署状态..."
    
    case $FEATURE in
        gitops|all)
            kubectl get applications -n argocd
            ;;
        chaos|all)
            kubectl get pods -n chaos-mesh
            ;;
        ml-platform|all)
            kubectl get pods -n ml-platform
            ;;
        faas|all)
            kubectl get ksvc -n knative-serving
            ;;
        monitoring|all)
            kubectl get pods -n monitoring
            ;;
        security|all)
            kubectl get pods -n falco
            kubectl get pods -n gatekeeper-system
            ;;
    esac
    
    log_success "部署验证完成"
}

# 显示部署信息
show_deployment_info() {
    log_success "高级功能部署完成！"
    
    echo ""
    echo "🚀 部署信息:"
    echo "   功能: $FEATURE"
    echo "   环境: $ENVIRONMENT"
    echo ""
    
    case $FEATURE in
        gitops|all)
            echo "📋 GitOps (ArgoCD):"
            echo "   访问 ArgoCD UI 管理应用部署"
            echo "   支持自动同步和回滚"
            echo ""
            ;;
        chaos|all)
            echo "🔥 混沌工程 (Chaos Mesh):"
            echo "   访问 Chaos Dashboard 管理实验"
            echo "   支持多种故障注入类型"
            echo ""
            ;;
        ml-platform|all)
            echo "🤖 ML 平台 (MLflow):"
            echo "   访问 MLflow UI 管理模型"
            echo "   支持实验跟踪和模型部署"
            echo ""
            ;;
        faas|all)
            echo "⚡ FaaS (Knative):"
            echo "   无服务器函数计算平台"
            echo "   支持自动扩缩容和按需计费"
            echo ""
            ;;
        monitoring|all)
            echo "📊 高级监控:"
            echo "   Prometheus + Grafana 监控栈"
            echo "   支持自定义指标和告警"
            echo ""
            ;;
        security|all)
            echo "🔒 安全组件:"
            echo "   Falco 运行时安全监控"
            echo "   OPA Gatekeeper 策略引擎"
            echo ""
            ;;
    esac
    
    echo "🔧 管理命令:"
    echo "   查看状态: kubectl get pods --all-namespaces"
    echo "   查看服务: kubectl get svc --all-namespaces"
    echo "   查看应用: kubectl get applications -n argocd"
}

# 主函数
main() {
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            --dry-run)
                DRY_RUN=true
                shift
                ;;
            --skip-deps)
                SKIP_DEPS=true
                shift
                ;;
            --force)
                FORCE_DEPLOY=true
                shift
                ;;
            --wait)
                WAIT_DEPLOYMENT=true
                shift
                ;;
            *)
                shift
                ;;
        esac
    done
    
    log_info "开始高级功能部署"
    log_info "功能: $FEATURE"
    log_info "环境: $ENVIRONMENT"
    
    # 检查依赖
    if [ "$SKIP_DEPS" != "true" ]; then
        check_dependencies
    fi
    
    # 执行部署
    case $FEATURE in
        gitops)
            deploy_gitops
            ;;
        chaos)
            deploy_chaos
            ;;
        ml-platform)
            deploy_ml_platform
            ;;
        faas)
            deploy_faas
            ;;
        monitoring)
            deploy_monitoring
            ;;
        security)
            deploy_security
            ;;
        all)
            deploy_gitops
            deploy_chaos
            deploy_ml_platform
            deploy_faas
            deploy_monitoring
            deploy_security
            ;;
        *)
            log_error "不支持的功能: $FEATURE"
            show_help
            exit 1
            ;;
    esac
    
    # 验证部署
    verify_deployment
    
    # 显示部署信息
    show_deployment_info
}

# 执行主函数
main "$@"
