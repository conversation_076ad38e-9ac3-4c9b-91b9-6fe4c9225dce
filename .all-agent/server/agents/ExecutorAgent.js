const BaseAgent = require('./BaseAgent');
const CodeGenerator = require('../core/CodeGenerator');
const fs = require('fs').promises;
const path = require('path');

/**
 * 执行 Agent
 * 负责代码生成、文件操作和具体任务执行
 */
class ExecutorAgent extends BaseAgent {
    constructor(config) {
        super(config);
        this.codeGenerator = new CodeGenerator();
        this.executionHistory = [];
        this.workingDirectory = process.cwd();
    }

    /**
     * 初始化执行 Agent
     */
    async onInitialize() {
        this.log('info', '执行 Agent 初始化中...');
        
        // 初始化代码生成器
        await this.codeGenerator.initialize();
        
        this.log('info', '执行 Agent 初始化完成');
    }

    /**
     * 处理消息
     */
    async onProcessMessage(message, context) {
        this.log('info', '处理执行请求', { message: message.substring(0, 100) });
        
        const lowerMessage = message.toLowerCase();
        
        if (lowerMessage.includes('生成代码') || lowerMessage.includes('generate code')) {
            return this.handleCodeGenerationRequest(message, context);
        } else if (lowerMessage.includes('创建文件') || lowerMessage.includes('create file')) {
            return this.handleFileCreationRequest(message, context);
        } else if (lowerMessage.includes('安装依赖') || lowerMessage.includes('install')) {
            return this.handleDependencyInstallation(message, context);
        } else if (lowerMessage.includes('运行测试') || lowerMessage.includes('run test')) {
            return this.handleTestExecution(message, context);
        } else if (lowerMessage.includes('部署') || lowerMessage.includes('deploy')) {
            return this.handleDeployment(message, context);
        } else {
            return this.handleGeneralExecutionRequest(message, context);
        }
    }

    /**
     * 处理代码生成请求
     */
    async handleCodeGenerationRequest(message, context) {
        try {
            // 从消息中提取生成需求
            const requirement = this.extractCodeRequirement(message);
            const templateId = this.selectTemplate(requirement);
            const parameters = this.extractParameters(message, context);
            
            // 生成代码
            const result = await this.codeGenerator.generate(templateId, parameters, context);
            
            // 记录执行历史
            this.recordExecution('code_generation', { templateId, parameters }, result);
            
            return this.formatCodeGenerationResponse(result);
            
        } catch (error) {
            this.log('error', '代码生成失败', { error: error.message });
            return `代码生成失败：${error.message}`;
        }
    }

    /**
     * 处理文件创建请求
     */
    async handleFileCreationRequest(message, context) {
        try {
            const fileInfo = this.extractFileInfo(message);
            const result = await this.createFiles(fileInfo, context);
            
            this.recordExecution('file_creation', fileInfo, result);
            
            return `✅ 文件创建完成：\n${result.files.map(f => `- ${f.path}`).join('\n')}`;
            
        } catch (error) {
            this.log('error', '文件创建失败', { error: error.message });
            return `文件创建失败：${error.message}`;
        }
    }

    /**
     * 处理依赖安装请求
     */
    async handleDependencyInstallation(message, context) {
        try {
            const dependencies = this.extractDependencies(message);
            const result = await this.installDependencies(dependencies, context);
            
            this.recordExecution('dependency_installation', dependencies, result);
            
            return `📦 依赖安装完成：\n${result.installed.map(d => `- ${d}`).join('\n')}`;
            
        } catch (error) {
            this.log('error', '依赖安装失败', { error: error.message });
            return `依赖安装失败：${error.message}`;
        }
    }

    /**
     * 处理测试执行请求
     */
    async handleTestExecution(message, context) {
        try {
            const testConfig = this.extractTestConfig(message);
            const result = await this.runTests(testConfig, context);
            
            this.recordExecution('test_execution', testConfig, result);
            
            return this.formatTestResults(result);
            
        } catch (error) {
            this.log('error', '测试执行失败', { error: error.message });
            return `测试执行失败：${error.message}`;
        }
    }

    /**
     * 处理部署请求
     */
    async handleDeployment(message, context) {
        try {
            const deployConfig = this.extractDeployConfig(message);
            const result = await this.deploy(deployConfig, context);
            
            this.recordExecution('deployment', deployConfig, result);
            
            return `🚀 部署完成：\n- 环境：${result.environment}\n- 状态：${result.status}\n- URL：${result.url || '待配置'}`;
            
        } catch (error) {
            this.log('error', '部署失败', { error: error.message });
            return `部署失败：${error.message}`;
        }
    }

    /**
     * 处理一般执行请求
     */
    async handleGeneralExecutionRequest(message, context) {
        return `我是执行 Agent，专门负责代码生成和任务执行。我可以帮您：

⚡ **代码生成** - 基于模板生成高质量代码
📁 **文件操作** - 创建、修改、删除项目文件
📦 **依赖管理** - 安装和管理项目依赖
🧪 **测试执行** - 运行各种类型的测试
🚀 **自动部署** - 部署应用到各种环境

请告诉我您需要执行什么任务，例如：
- "生成登录页面代码"
- "创建React组件文件"
- "安装项目依赖"
- "运行单元测试"`;
    }

    /**
     * 执行任务
     */
    async onExecuteTask(action, input, options) {
        this.log('info', '执行具体任务', { action, input: typeof input });
        
        switch (action) {
            case 'generate_code':
                return this.generateCode(input, options);
            case 'create_files':
                return this.createFiles(input, options);
            case 'install_dependencies':
                return this.installDependencies(input, options);
            case 'run_tests':
                return this.runTests(input, options);
            case 'deploy_application':
                return this.deploy(input, options);
            case 'setup_project_structure':
                return this.setupProjectStructure(input, options);
            default:
                throw new Error(`不支持的执行任务: ${action}`);
        }
    }

    /**
     * 生成代码
     */
    async generateCode(input, options) {
        const { templateId, parameters, context } = input;
        return await this.codeGenerator.generate(templateId, parameters, context);
    }

    /**
     * 创建文件
     */
    async createFiles(input, options) {
        const files = Array.isArray(input) ? input : input.files || [input];
        const results = [];
        
        for (const file of files) {
            try {
                const filePath = path.resolve(this.workingDirectory, file.path || file.name);
                const content = file.content || '';
                
                // 确保目录存在
                await fs.mkdir(path.dirname(filePath), { recursive: true });
                
                // 写入文件
                await fs.writeFile(filePath, content, 'utf8');
                
                results.push({
                    path: filePath,
                    name: file.name,
                    size: content.length,
                    status: 'created'
                });
                
                this.log('info', '文件创建成功', { path: filePath });
                
            } catch (error) {
                this.log('error', '文件创建失败', { file: file.name, error: error.message });
                results.push({
                    path: file.path || file.name,
                    name: file.name,
                    status: 'failed',
                    error: error.message
                });
            }
        }
        
        return { files: results };
    }

    /**
     * 安装依赖
     */
    async installDependencies(input, options) {
        const dependencies = Array.isArray(input) ? input : input.dependencies || [];
        const packageManager = options.packageManager || 'npm';
        
        // 模拟依赖安装
        const installed = [];
        const failed = [];
        
        for (const dep of dependencies) {
            try {
                // 这里应该执行实际的包管理器命令
                // 目前模拟安装过程
                await this.simulateInstallation(dep, packageManager);
                installed.push(dep);
                this.log('info', '依赖安装成功', { dependency: dep });
            } catch (error) {
                failed.push({ dependency: dep, error: error.message });
                this.log('error', '依赖安装失败', { dependency: dep, error: error.message });
            }
        }
        
        return { installed, failed, packageManager };
    }

    /**
     * 运行测试
     */
    async runTests(input, options) {
        const testType = input.type || 'unit';
        const testFiles = input.files || [];
        
        // 模拟测试执行
        const results = {
            type: testType,
            total: testFiles.length || 10,
            passed: 0,
            failed: 0,
            skipped: 0,
            duration: 0,
            details: []
        };
        
        // 模拟测试结果
        const startTime = Date.now();
        
        for (let i = 0; i < results.total; i++) {
            const testName = testFiles[i] || `test_${i + 1}`;
            const success = Math.random() > 0.1; // 90% 成功率
            
            if (success) {
                results.passed++;
                results.details.push({
                    name: testName,
                    status: 'passed',
                    duration: Math.floor(Math.random() * 100) + 10
                });
            } else {
                results.failed++;
                results.details.push({
                    name: testName,
                    status: 'failed',
                    duration: Math.floor(Math.random() * 100) + 10,
                    error: 'Assertion failed'
                });
            }
        }
        
        results.duration = Date.now() - startTime;
        
        return results;
    }

    /**
     * 部署应用
     */
    async deploy(input, options) {
        const environment = input.environment || 'development';
        const platform = input.platform || 'local';
        
        // 模拟部署过程
        const deploymentSteps = [
            '构建应用',
            '运行测试',
            '打包资源',
            '上传文件',
            '配置环境',
            '启动服务'
        ];
        
        const result = {
            environment,
            platform,
            status: 'success',
            steps: [],
            url: null,
            startTime: new Date().toISOString()
        };
        
        for (const step of deploymentSteps) {
            try {
                await this.simulateDeploymentStep(step);
                result.steps.push({ name: step, status: 'completed' });
                this.log('info', '部署步骤完成', { step });
            } catch (error) {
                result.steps.push({ name: step, status: 'failed', error: error.message });
                result.status = 'failed';
                break;
            }
        }
        
        if (result.status === 'success') {
            result.url = `https://${environment}.example.com`;
        }
        
        result.endTime = new Date().toISOString();
        
        return result;
    }

    /**
     * 设置项目结构
     */
    async setupProjectStructure(input, options) {
        const projectType = input.projectType || 'web_app';
        const projectName = input.projectName || 'new-project';
        
        const structure = this.getProjectStructure(projectType);
        const results = [];
        
        for (const item of structure) {
            if (item.type === 'directory') {
                try {
                    const dirPath = path.join(this.workingDirectory, projectName, item.path);
                    await fs.mkdir(dirPath, { recursive: true });
                    results.push({ path: item.path, type: 'directory', status: 'created' });
                } catch (error) {
                    results.push({ path: item.path, type: 'directory', status: 'failed', error: error.message });
                }
            } else if (item.type === 'file') {
                try {
                    const filePath = path.join(this.workingDirectory, projectName, item.path);
                    await fs.mkdir(path.dirname(filePath), { recursive: true });
                    await fs.writeFile(filePath, item.content || '', 'utf8');
                    results.push({ path: item.path, type: 'file', status: 'created' });
                } catch (error) {
                    results.push({ path: item.path, type: 'file', status: 'failed', error: error.message });
                }
            }
        }
        
        return { projectName, projectType, structure: results };
    }

    /**
     * 辅助方法
     */
    extractCodeRequirement(message) {
        // 简单的需求提取
        if (message.includes('登录')) return 'login';
        if (message.includes('注册')) return 'register';
        if (message.includes('组件')) return 'component';
        return 'general';
    }

    selectTemplate(requirement) {
        const templateMap = {
            'login': 'generate_ui_login',
            'register': 'generate_ui_register',
            'component': 'generate_component'
        };
        return templateMap[requirement] || 'generate_ui_login';
    }

    extractParameters(message, context) {
        // 从消息和上下文中提取参数
        return {
            project_type: context.projectType || 'web应用',
            design_style: context.designStyle || '现代简约',
            tech_stack: context.techStack || 'HTML/CSS/JavaScript'
        };
    }

    extractFileInfo(message) {
        // 提取文件信息
        return {
            name: 'example.js',
            content: '// 生成的文件内容\nconsole.log("Hello World");',
            path: 'src/example.js'
        };
    }

    extractDependencies(message) {
        // 提取依赖列表
        const deps = [];
        if (message.includes('react')) deps.push('react', 'react-dom');
        if (message.includes('express')) deps.push('express');
        if (message.includes('lodash')) deps.push('lodash');
        return deps.length > 0 ? deps : ['express', 'cors', 'dotenv'];
    }

    extractTestConfig(message) {
        return {
            type: message.includes('unit') ? 'unit' : 'integration',
            files: []
        };
    }

    extractDeployConfig(message) {
        return {
            environment: message.includes('production') ? 'production' : 'development',
            platform: 'local'
        };
    }

    async simulateInstallation(dependency, packageManager) {
        // 模拟安装延迟
        await new Promise(resolve => setTimeout(resolve, 500 + Math.random() * 1000));
        
        // 模拟偶尔的安装失败
        if (Math.random() < 0.05) {
            throw new Error(`Package ${dependency} not found`);
        }
    }

    async simulateDeploymentStep(step) {
        // 模拟部署步骤延迟
        await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));
        
        // 模拟偶尔的步骤失败
        if (Math.random() < 0.02) {
            throw new Error(`Step ${step} failed`);
        }
    }

    getProjectStructure(projectType) {
        const structures = {
            web_app: [
                { type: 'directory', path: 'src' },
                { type: 'directory', path: 'src/components' },
                { type: 'directory', path: 'src/utils' },
                { type: 'directory', path: 'public' },
                { type: 'file', path: 'src/index.js', content: '// Main entry point' },
                { type: 'file', path: 'package.json', content: '{\n  "name": "web-app",\n  "version": "1.0.0"\n}' }
            ],
            api: [
                { type: 'directory', path: 'src' },
                { type: 'directory', path: 'src/routes' },
                { type: 'directory', path: 'src/models' },
                { type: 'file', path: 'src/app.js', content: '// Express app' },
                { type: 'file', path: 'package.json', content: '{\n  "name": "api",\n  "version": "1.0.0"\n}' }
            ]
        };
        
        return structures[projectType] || structures.web_app;
    }

    formatCodeGenerationResponse(result) {
        let response = `✅ **代码生成完成**\n\n`;
        response += `**模板：** ${result.templateName}\n`;
        response += `**生成时间：** ${result.generatedAt}\n\n`;
        
        if (result.files && result.files.length > 0) {
            response += `**生成文件：**\n`;
            result.files.forEach(file => {
                response += `📄 ${file.name} (${file.language})\n`;
                response += `   ${file.description}\n\n`;
            });
        }
        
        response += `**说明：** ${result.summary}\n\n`;
        
        if (result.instructions && result.instructions.length > 0) {
            response += `**使用说明：**\n`;
            result.instructions.forEach((instruction, index) => {
                response += `${index + 1}. ${instruction}\n`;
            });
        }
        
        return response;
    }

    formatTestResults(results) {
        let response = `🧪 **测试执行结果**\n\n`;
        response += `**测试类型：** ${results.type}\n`;
        response += `**总计：** ${results.total} 个测试\n`;
        response += `**通过：** ${results.passed} ✅\n`;
        response += `**失败：** ${results.failed} ❌\n`;
        response += `**跳过：** ${results.skipped} ⏭️\n`;
        response += `**耗时：** ${results.duration}ms\n\n`;
        
        if (results.failed > 0) {
            response += `**失败详情：**\n`;
            results.details.filter(d => d.status === 'failed').forEach(detail => {
                response += `- ${detail.name}: ${detail.error}\n`;
            });
        }
        
        return response;
    }

    recordExecution(type, input, result) {
        this.executionHistory.push({
            type,
            input,
            result,
            timestamp: new Date().toISOString()
        });
        
        // 限制历史记录数量
        if (this.executionHistory.length > 50) {
            this.executionHistory.shift();
        }
    }

    getExecutionHistory(limit = 10) {
        return this.executionHistory.slice(-limit).reverse();
    }
}

module.exports = ExecutorAgent;
