{"version": 3, "file": "pluginService.js", "sourceRoot": "", "sources": ["../../src/services/pluginService.ts"], "names": [], "mappings": ";;;AAAA,iCAAiC;AA6BjC;;GAEG;AACH,MAAa,aAAa;IAKtB,YAAoB,OAAgC;QAAhC,YAAO,GAAP,OAAO,CAAyB;QAJ5C,YAAO,GAAiC,IAAI,GAAG,EAAE,CAAC;QAClD,kBAAa,GAAqB,IAAI,GAAG,EAAE,CAAC;QAC5C,UAAK,GAAgD,IAAI,GAAG,EAAE,CAAC;IAEhB,CAAC;IAExD;;OAEG;IACH,KAAK,CAAC,cAAc,CAAC,MAAuB;QACxC,IAAI;YACA,IAAI,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE;gBAC/B,OAAO,CAAC,IAAI,CAAC,UAAU,MAAM,CAAC,IAAI,wBAAwB,CAAC,CAAC;gBAC5D,OAAO,KAAK,CAAC;aAChB;YAED,OAAO;YACP,MAAM,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAEpC,OAAO;YACP,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;YAEtC,OAAO;YACP,MAAM,QAAQ,GAAG,MAAM,CAAC,WAAW,EAAE,EAAE,IAAI,EAAE,CAAC;YAC9C,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE;gBAC5B,MAAM,UAAU,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAC9C,oBAAoB,MAAM,CAAC,IAAI,IAAI,OAAO,CAAC,EAAE,EAAE,EAC/C,OAAO,CAAC,OAAO,CAClB,CAAC;gBACF,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;aAC/C;YAED,OAAO,CAAC,GAAG,CAAC,UAAU,MAAM,CAAC,IAAI,KAAK,MAAM,CAAC,OAAO,0BAA0B,CAAC,CAAC;YAChF,OAAO,IAAI,CAAC;SACf;QAAC,OAAO,KAAK,EAAE;YACZ,OAAO,CAAC,KAAK,CAAC,6BAA6B,MAAM,CAAC,IAAI,GAAG,EAAE,KAAK,CAAC,CAAC;YAClE,OAAO,KAAK,CAAC;SAChB;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,gBAAgB,CAAC,UAAkB;QACrC,IAAI;YACA,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;YAC5C,IAAI,CAAC,MAAM,EAAE;gBACT,OAAO,KAAK,CAAC;aAChB;YAED,OAAO;YACP,MAAM,MAAM,CAAC,UAAU,EAAE,CAAC;YAE1B,OAAO;YACP,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;YAChC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;YAEtC,OAAO,CAAC,GAAG,CAAC,UAAU,UAAU,4BAA4B,CAAC,CAAC;YAC9D,OAAO,IAAI,CAAC;SACf;QAAC,OAAO,KAAK,EAAE;YACZ,OAAO,CAAC,KAAK,CAAC,+BAA+B,UAAU,GAAG,EAAE,KAAK,CAAC,CAAC;YACnE,OAAO,KAAK,CAAC;SAChB;IACL,CAAC;IAED;;OAEG;IACH,oBAAoB;QAMhB,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;YACpD,IAAI,EAAE,MAAM,CAAC,IAAI;YACjB,OAAO,EAAE,MAAM,CAAC,OAAO;YACvB,WAAW,EAAE,MAAM,CAAC,WAAW;YAC/B,MAAM,EAAE,IAAI;SACf,CAAC,CAAC,CAAC;IACR,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,aAAa,CAAC,OAAe,EAAE,OAAY;QAC7C,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,EAAE;YACxC,IAAI,MAAM,CAAC,aAAa,EAAE;gBACtB,IAAI;oBACA,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,aAAa,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;oBAC5D,IAAI,MAAM,EAAE;wBACR,OAAO,MAAM,CAAC;qBACjB;iBACJ;gBAAC,OAAO,KAAK,EAAE;oBACZ,OAAO,CAAC,KAAK,CAAC,UAAU,MAAM,CAAC,IAAI,0BAA0B,EAAE,KAAK,CAAC,CAAC;iBACzE;aACJ;SACJ;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;OAEG;IACH,YAAY,CAAC,QAAgB,EAAE,OAAgC;QAC3D,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE;YAC3B,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;SAChC;QACD,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAE,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IAC5C,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,WAAW,CAAC,QAAgB,EAAE,GAAG,IAAW;QAC9C,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;QAChD,MAAM,OAAO,GAAU,EAAE,CAAC;QAE1B,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE;YAC5B,IAAI;gBACA,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,GAAG,IAAI,CAAC,CAAC;gBACtC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;aACxB;YAAC,OAAO,KAAK,EAAE;gBACZ,OAAO,CAAC,KAAK,CAAC,QAAQ,QAAQ,iBAAiB,EAAE,KAAK,CAAC,CAAC;gBACxD,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;aACtB;SACJ;QAED,OAAO,OAAO,CAAC;IACnB,CAAC;IAED;;OAEG;IACH,eAAe,CAAC,UAAkB,EAAE,MAAW;QAC3C,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;IAC/C,CAAC;IAED;;OAEG;IACH,eAAe,CAAC,UAAkB;QAC9B,OAAO,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC;IACpD,CAAC;IAED;;OAEG;IACH,mBAAmB,CAAC,EAAU,EAAE,KAAa,EAAE,OAAgC;QAC3E,MAAM,UAAU,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,oBAAoB,EAAE,EAAE,EAAE,OAAO,CAAC,CAAC;QACtF,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAC5C,OAAO,UAAU,CAAC;IACtB,CAAC;IAED;;OAEG;IACH,gBAAgB,CAAC,MAAc,EAAE,KAAa,EAAE,QAAoC;QAChF,MAAM,UAAU,GAAG,MAAM,CAAC,MAAM,CAAC,2BAA2B,CAAC,oBAAoB,MAAM,EAAE,EAAE,QAAQ,CAAC,CAAC;QACrG,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAC5C,OAAO,UAAU,CAAC;IACtB,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,OAAO;QACT,SAAS;QACT,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,EAAE;YACxC,IAAI;gBACA,MAAM,MAAM,CAAC,UAAU,EAAE,CAAC;aAC7B;YAAC,OAAO,KAAK,EAAE;gBACZ,OAAO,CAAC,KAAK,CAAC,6BAA6B,MAAM,CAAC,IAAI,GAAG,EAAE,KAAK,CAAC,CAAC;aACrE;SACJ;QAED,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;QACrB,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;QAC3B,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;IACvB,CAAC;CACJ;AArLD,sCAqLC;AAED;;GAEG;AACH,MAAa,SAAS;IAAtB;QACI,SAAI,GAAG,iBAAiB,CAAC;QACzB,YAAO,GAAG,OAAO,CAAC;QAClB,gBAAW,GAAG,+BAA+B,CAAC;IAwElD,CAAC;IAtEG,KAAK,CAAC,QAAQ,CAAC,OAAgC;QAC3C,OAAO,CAAC,GAAG,CAAC,sBAAsB,CAAC,CAAC;IACxC,CAAC;IAED,KAAK,CAAC,UAAU;QACZ,OAAO,CAAC,GAAG,CAAC,wBAAwB,CAAC,CAAC;IAC1C,CAAC;IAED,KAAK,CAAC,aAAa,CAAC,OAAe,EAAE,OAAY;QAC7C,MAAM,YAAY,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC;QAE3C,IAAI,YAAY,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE;YACrC,OAAO,MAAM,IAAI,CAAC,YAAY,EAAE,CAAC;SACpC;QAED,IAAI,YAAY,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE;YACrC,OAAO,MAAM,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;SAC9C;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,WAAW;QACP,OAAO;YACH;gBACI,EAAE,EAAE,QAAQ;gBACZ,KAAK,EAAE,YAAY;gBACnB,OAAO,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,YAAY,EAAE;aACrC;YACD;gBACI,EAAE,EAAE,QAAQ;gBACZ,KAAK,EAAE,cAAc;gBACrB,OAAO,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,WAAW,EAAE;aACpC;SACJ,CAAC;IACN,CAAC;IAEO,KAAK,CAAC,YAAY;QACtB,IAAI;YACA,MAAM,YAAY,GAAG,MAAM,CAAC,UAAU,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;YAClE,IAAI,CAAC,YAAY,EAAE;gBACf,OAAO,2BAA2B,CAAC;aACtC;YAED,iBAAiB;YACjB,OAAO,2CAA2C,CAAC;SACtD;QAAC,OAAO,KAAK,EAAE;YACZ,OAAO,uBAAuB,KAAK,EAAE,CAAC;SACzC;IACL,CAAC;IAEO,KAAK,CAAC,eAAe,CAAC,OAAe;QACzC,MAAM,WAAW,GAAG,OAAO,CAAC,KAAK,CAAC,qBAAqB,CAAC,CAAC;QACzD,MAAM,aAAa,GAAG,WAAW,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,4BAA4B,CAAC;QAElF,OAAO,wCAAwC,aAAa,kFAAkF,CAAC;IACnJ,CAAC;IAEO,KAAK,CAAC,WAAW;QACrB,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC;YAC7C,MAAM,EAAE,sBAAsB;YAC9B,WAAW,EAAE,uBAAuB;SACvC,CAAC,CAAC;QAEH,IAAI,CAAC,OAAO,EAAE;YACV,OAAO,oBAAoB,CAAC;SAC/B;QAED,OAAO,0CAA0C,OAAO,oCAAoC,CAAC;IACjG,CAAC;CACJ;AA3ED,8BA2EC;AAED;;GAEG;AACH,MAAa,aAAa;IAA1B;QACI,SAAI,GAAG,iBAAiB,CAAC;QACzB,YAAO,GAAG,OAAO,CAAC;QAClB,gBAAW,GAAG,uCAAuC,CAAC;QAE9C,aAAQ,GAAwB,IAAI,GAAG,EAAE,CAAC;IAmFtD,CAAC;IAjFG,KAAK,CAAC,QAAQ,CAAC,OAAgC;QAC3C,WAAW;QACX,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAC3B,OAAO,CAAC,GAAG,CAAC,0BAA0B,CAAC,CAAC;IAC5C,CAAC;IAED,KAAK,CAAC,UAAU;QACZ,OAAO,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAC;IAC9C,CAAC;IAED,KAAK,CAAC,aAAa,CAAC,OAAe,EAAE,OAAY;QAC7C,MAAM,YAAY,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC;QAE3C,IAAI,YAAY,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,YAAY,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE;YACnE,OAAO,IAAI,CAAC,YAAY,EAAE,CAAC;SAC9B;QAED,IAAI,YAAY,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,YAAY,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE;YACrE,OAAO,IAAI,CAAC,mBAAmB,EAAE,CAAC;SACrC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,WAAW;QACP,OAAO;YACH;gBACI,EAAE,EAAE,MAAM;gBACV,KAAK,EAAE,eAAe;gBACtB,OAAO,EAAE,KAAK,IAAI,EAAE,CAAC,IAAI,CAAC,YAAY,EAAE;aAC3C;YACD;gBACI,EAAE,EAAE,QAAQ;gBACZ,KAAK,EAAE,gBAAgB;gBACvB,OAAO,EAAE,KAAK,IAAI,EAAE,CAAC,IAAI,CAAC,mBAAmB,EAAE;aAClD;SACJ,CAAC;IACN,CAAC;IAEO,mBAAmB;QACvB,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,iBAAiB,EAAE;;;;;;;;;;0BAUnB,CAAC,CAAC;QAEpB,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,gBAAgB,EAAE;;;;;;;;;EAS1C,CAAC,CAAC;IACA,CAAC;IAEO,YAAY;QAChB,IAAI,QAAQ,GAAG,+BAA+B,CAAC;QAE/C,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,KAAK,CAAC,EAAE;YAC1B,QAAQ,IAAI,wBAAwB,CAAC;SACxC;aAAM;YACH,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE;gBACpD,QAAQ,IAAI,GAAG,KAAK,GAAG,CAAC,OAAO,GAAG,MAAM,CAAC;YAC7C,CAAC,CAAC,CAAC;SACN;QAED,OAAO,QAAQ,CAAC;IACpB,CAAC;IAEO,mBAAmB;QACvB,OAAO,sHAAsH,CAAC;IAClI,CAAC;CACJ;AAxFD,sCAwFC"}