import * as vscode from 'vscode';
import * as path from 'path';
import * as fs from 'fs';

/**
 * 上下文服务 - 提供深度项目理解和上下文跟踪
 */
export class ContextService {
    private context: any = {};
    private conversationHistory: any[] = [];
    private projectAnalysis: any = null;
    private fileWatchers: vscode.FileSystemWatcher[] = [];
    private changeListeners: Array<(context: any) => void> = [];

    constructor() {
        this.initializeContext();
        this.setupFileWatchers();
        this.setupEventListeners();
    }

    /**
     * 初始化上下文
     */
    private async initializeContext() {
        this.context = {
            timestamp: new Date().toISOString(),
            workspace: await this.getWorkspaceInfo(),
            editor: this.getEditorInfo(),
            git: await this.getGitInfo(),
            project: await this.getProjectInfo(),
            environment: await this.getEnvironmentInfo(),
            errors: [],
            recentFiles: [],
            activeTask: null
        };

        this.notifyContextChange();
    }

    /**
     * 获取工作区信息
     */
    private async getWorkspaceInfo(): Promise<any> {
        const workspaceFolders = vscode.workspace.workspaceFolders;
        if (!workspaceFolders || workspaceFolders.length === 0) {
            return null;
        }

        const folder = workspaceFolders[0];
        return {
            name: folder.name,
            path: folder.uri.fsPath,
            uri: folder.uri.toString(),
            folders: workspaceFolders.map(f => ({
                name: f.name,
                path: f.uri.fsPath
            }))
        };
    }

    /**
     * 获取编辑器信息
     */
    private getEditorInfo(): any {
        const activeEditor = vscode.window.activeTextEditor;
        if (!activeEditor) {
            return null;
        }

        const selection = activeEditor.selection;
        return {
            fileName: activeEditor.document.fileName,
            language: activeEditor.document.languageId,
            lineCount: activeEditor.document.lineCount,
            isDirty: activeEditor.document.isDirty,
            selection: {
                isEmpty: selection.isEmpty,
                text: selection.isEmpty ? null : activeEditor.document.getText(selection),
                start: { line: selection.start.line, character: selection.start.character },
                end: { line: selection.end.line, character: selection.end.character }
            },
            cursorPosition: {
                line: selection.active.line,
                character: selection.active.character
            }
        };
    }

    /**
     * 获取 Git 信息
     */
    private async getGitInfo(): Promise<any> {
        const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
        if (!workspaceFolder) {
            return null;
        }

        try {
            const gitDir = path.join(workspaceFolder.uri.fsPath, '.git');
            const gitExists = fs.existsSync(gitDir);

            if (!gitExists) {
                return { isGitRepo: false };
            }

            // 这里可以添加更多 Git 信息获取逻辑
            return {
                isGitRepo: true,
                rootPath: workspaceFolder.uri.fsPath
            };
        } catch (error) {
            return { isGitRepo: false, error: error instanceof Error ? error.message : String(error) };
        }
    }

    /**
     * 获取项目信息
     */
    private async getProjectInfo(): Promise<any> {
        const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
        if (!workspaceFolder) {
            return null;
        }

        const projectInfo: any = {
            type: 'unknown',
            technologies: [],
            dependencies: {},
            scripts: {},
            hasTests: false,
            hasDocumentation: false,
            structure: {
                totalFiles: 0,
                codeFiles: 0,
                configFiles: 0,
                testFiles: 0
            },
            health: {
                score: 0,
                issues: []
            }
        };

        try {
            // 检查 package.json
            const packageJsonPath = path.join(workspaceFolder.uri.fsPath, 'package.json');
            if (fs.existsSync(packageJsonPath)) {
                const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
                projectInfo.type = 'node';
                projectInfo.name = packageJson.name;
                projectInfo.version = packageJson.version;
                projectInfo.dependencies = packageJson.dependencies || {};
                projectInfo.devDependencies = packageJson.devDependencies || {};
                projectInfo.scripts = packageJson.scripts || {};

                // 检测技术栈
                const deps = { ...packageJson.dependencies, ...packageJson.devDependencies };
                if (deps.react) projectInfo.technologies.push('React');
                if (deps.vue) projectInfo.technologies.push('Vue');
                if (deps.angular) projectInfo.technologies.push('Angular');
                if (deps.express) projectInfo.technologies.push('Express');
                if (deps.next) projectInfo.technologies.push('Next.js');
                if (deps.typescript) projectInfo.technologies.push('TypeScript');
            }

            // 检查其他项目类型
            const pyFiles = await this.findFiles('**/*.py', 5);
            if (pyFiles.length > 0) {
                projectInfo.type = projectInfo.type === 'unknown' ? 'python' : projectInfo.type;
                projectInfo.technologies.push('Python');
            }

            const javaFiles = await this.findFiles('**/*.java', 5);
            if (javaFiles.length > 0) {
                projectInfo.type = projectInfo.type === 'unknown' ? 'java' : projectInfo.type;
                projectInfo.technologies.push('Java');
            }

            // 检查测试文件
            const testFiles = await this.findFiles('**/*{.test,.spec}.{js,ts,py,java}', 10);
            projectInfo.hasTests = testFiles.length > 0;

            // 检查文档
            const docFiles = await this.findFiles('**/README.{md,txt}', 5);
            projectInfo.hasDocumentation = docFiles.length > 0;

            // 分析项目结构
            await this.analyzeProjectStructure(projectInfo, workspaceFolder.uri.fsPath);

            // 计算项目健康度
            this.calculateProjectHealth(projectInfo);

        } catch (error) {
            projectInfo.error = error instanceof Error ? error.message : String(error);
        }

        return projectInfo;
    }

    /**
     * 分析项目结构
     */
    private async analyzeProjectStructure(projectInfo: any, rootPath: string): Promise<void> {
        try {
            const allFiles = await this.findFiles('**/*', 1000);
            projectInfo.structure.totalFiles = allFiles.length;

            // 分析文件类型
            for (const file of allFiles) {
                const fileName = file.fsPath.toLowerCase();
                const ext = path.extname(fileName);

                // 代码文件
                if (['.js', '.ts', '.jsx', '.tsx', '.py', '.java', '.cpp', '.c', '.cs', '.go', '.rs'].includes(ext)) {
                    projectInfo.structure.codeFiles++;
                }

                // 配置文件
                if (['.json', '.yml', '.yaml', '.toml', '.ini', '.conf'].includes(ext) ||
                    fileName.includes('config') || fileName.includes('.env')) {
                    projectInfo.structure.configFiles++;
                }

                // 测试文件
                if (fileName.includes('test') || fileName.includes('spec') || fileName.includes('__tests__')) {
                    projectInfo.structure.testFiles++;
                }
            }
        } catch (error) {
            console.error('Error analyzing project structure:', error);
        }
    }

    /**
     * 计算项目健康度
     */
    private calculateProjectHealth(projectInfo: any): void {
        let score = 0;
        const issues: string[] = [];

        // 基础分数
        score += 20;

        // 有文档 +15
        if (projectInfo.hasDocumentation) {
            score += 15;
        } else {
            issues.push('Missing documentation (README)');
        }

        // 有测试 +20
        if (projectInfo.hasTests) {
            score += 20;
        } else {
            issues.push('No test files detected');
        }

        // 有依赖管理 +15
        if (Object.keys(projectInfo.dependencies).length > 0) {
            score += 15;
        } else {
            issues.push('No dependencies defined');
        }

        // 有构建脚本 +10
        if (Object.keys(projectInfo.scripts).length > 0) {
            score += 10;
        } else {
            issues.push('No build scripts defined');
        }

        // 技术栈现代化 +10
        if (projectInfo.technologies.includes('TypeScript')) {
            score += 5;
        }
        if (projectInfo.technologies.some((tech: string) =>
            ['React', 'Vue', 'Angular', 'Next.js'].includes(tech))) {
            score += 5;
        }

        // 文件结构合理性 +10
        const { structure } = projectInfo;
        if (structure.codeFiles > 0 && structure.configFiles > 0) {
            score += 10;
        }

        // 测试覆盖率估算 +10
        if (structure.testFiles > 0 && structure.codeFiles > 0) {
            const testRatio = structure.testFiles / structure.codeFiles;
            if (testRatio > 0.3) {
                score += 10;
            } else if (testRatio > 0.1) {
                score += 5;
            }
        }

        projectInfo.health.score = Math.min(100, score);
        projectInfo.health.issues = issues;
    }

    /**
     * 获取环境信息
     */
    private async getEnvironmentInfo(): Promise<any> {
        return {
            platform: process.platform,
            arch: process.arch,
            nodeVersion: process.version,
            vscodeVersion: vscode.version,
            extensions: vscode.extensions.all.length
        };
    }

    /**
     * 查找文件
     */
    private async findFiles(pattern: string, limit: number = 100): Promise<vscode.Uri[]> {
        try {
            return await vscode.workspace.findFiles(pattern, '**/node_modules/**', limit);
        } catch (error) {
            return [];
        }
    }

    /**
     * 设置文件监听器
     */
    private setupFileWatchers() {
        const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
        if (!workspaceFolder) {
            return;
        }

        // 监听配置文件变化
        const configWatcher = vscode.workspace.createFileSystemWatcher(
            new vscode.RelativePattern(workspaceFolder, '{package.json,tsconfig.json,*.config.js}')
        );

        configWatcher.onDidChange(() => this.updateProjectInfo());
        configWatcher.onDidCreate(() => this.updateProjectInfo());
        configWatcher.onDidDelete(() => this.updateProjectInfo());

        this.fileWatchers.push(configWatcher);
    }

    /**
     * 设置事件监听器
     */
    private setupEventListeners() {
        // 监听活动编辑器变化
        vscode.window.onDidChangeActiveTextEditor((editor) => {
            this.context.editor = this.getEditorInfo();
            this.addToRecentFiles(editor?.document.fileName);
            this.notifyContextChange();
        });

        // 监听选择变化
        vscode.window.onDidChangeTextEditorSelection((event) => {
            this.context.editor = this.getEditorInfo();
            this.notifyContextChange();
        });

        // 监听诊断变化
        vscode.languages.onDidChangeDiagnostics((event) => {
            this.updateDiagnostics(event);
        });

        // 监听工作区变化
        vscode.workspace.onDidChangeWorkspaceFolders(() => {
            this.initializeContext();
        });
    }

    /**
     * 更新项目信息
     */
    private async updateProjectInfo() {
        this.context.project = await this.getProjectInfo();
        this.notifyContextChange();
    }

    /**
     * 更新诊断信息
     */
    private updateDiagnostics(event: vscode.DiagnosticChangeEvent) {
        const errors: any[] = [];

        event.uris.forEach(uri => {
            const diagnostics = vscode.languages.getDiagnostics(uri);
            diagnostics.forEach(diagnostic => {
                if (diagnostic.severity === vscode.DiagnosticSeverity.Error) {
                    errors.push({
                        file: uri.fsPath,
                        message: diagnostic.message,
                        line: diagnostic.range.start.line + 1,
                        column: diagnostic.range.start.character + 1,
                        source: diagnostic.source
                    });
                }
            });
        });

        this.context.errors = errors.slice(0, 20); // 保留最近 20 个错误
        this.notifyContextChange();
    }

    /**
     * 添加到最近文件
     */
    private addToRecentFiles(fileName?: string) {
        if (!fileName) return;

        this.context.recentFiles = this.context.recentFiles || [];

        // 移除重复项
        this.context.recentFiles = this.context.recentFiles.filter((f: string) => f !== fileName);

        // 添加到开头
        this.context.recentFiles.unshift(fileName);

        // 限制数量
        this.context.recentFiles = this.context.recentFiles.slice(0, 10);
    }

    /**
     * 获取完整上下文
     */
    getContext(): any {
        return {
            ...this.context,
            timestamp: new Date().toISOString()
        };
    }

    /**
     * 获取上下文摘要
     */
    getContextSummary(): string {
        const ctx = this.context;
        const parts: string[] = [];

        if (ctx.workspace) {
            parts.push(`Project: ${ctx.workspace.name}`);
        }

        if (ctx.editor?.fileName) {
            const fileName = path.basename(ctx.editor.fileName);
            parts.push(`File: ${fileName}`);
        }

        if (ctx.project?.type && ctx.project.type !== 'unknown') {
            parts.push(`Type: ${ctx.project.type}`);
        }

        if (ctx.project?.technologies?.length > 0) {
            parts.push(`Tech: ${ctx.project.technologies.join(', ')}`);
        }

        if (ctx.errors?.length > 0) {
            parts.push(`Errors: ${ctx.errors.length}`);
        }

        return parts.join(' | ');
    }

    /**
     * 添加对话历史
     */
    addToConversationHistory(role: 'user' | 'assistant', content: string, metadata?: any) {
        this.conversationHistory.push({
            role,
            content,
            timestamp: new Date().toISOString(),
            context: this.getContextSummary(),
            metadata
        });

        // 限制历史记录数量
        if (this.conversationHistory.length > 100) {
            this.conversationHistory = this.conversationHistory.slice(-80);
        }
    }

    /**
     * 获取对话历史
     */
    getConversationHistory(limit: number = 20): any[] {
        return this.conversationHistory.slice(-limit);
    }

    /**
     * 清空对话历史
     */
    clearConversationHistory() {
        this.conversationHistory = [];
    }

    /**
     * 监听上下文变化
     */
    onContextChange(listener: (context: any) => void) {
        this.changeListeners.push(listener);
    }

    /**
     * 通知上下文变化
     */
    private notifyContextChange() {
        const context = this.getContext();
        this.changeListeners.forEach(listener => {
            try {
                listener(context);
            } catch (error) {
                console.error('Context change listener error:', error);
            }
        });
    }

    /**
     * 设置活动任务
     */
    setActiveTask(task: any) {
        this.context.activeTask = task;
        this.notifyContextChange();
    }

    /**
     * 清理资源
     */
    dispose() {
        this.fileWatchers.forEach(watcher => watcher.dispose());
        this.fileWatchers = [];
        this.changeListeners = [];
    }
}
