#!/bin/bash

# All-Agent 生产环境安全配置脚本
# 配置生产级别的安全设置

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 1. 生成强密钥
generate_secure_keys() {
    log_info "=== 生成安全密钥 ==="
    
    # 生成 JWT 密钥
    local jwt_secret=$(openssl rand -hex 64)
    local api_key=$(openssl rand -hex 32)
    local encryption_key=$(openssl rand -hex 32)
    
    # 创建生产环境配置文件
    cat > server/.env.production << EOF
# All-Agent 生产环境配置
# 生成时间: $(date)

# 服务器配置
NODE_ENV=production
PORT=3000
HOST=0.0.0.0

# 安全配置
JWT_SECRET=${jwt_secret}
API_KEY=${api_key}
ENCRYPTION_KEY=${encryption_key}
SESSION_SECRET=$(openssl rand -hex 32)

# 数据库配置
DB_PATH=./data/production.db
DB_BACKUP_PATH=./backups/
DB_ENCRYPTION=true

# 日志配置
LOG_LEVEL=info
LOG_FILE=./logs/production.log
LOG_MAX_SIZE=100MB
LOG_MAX_FILES=10

# 安全头配置
SECURITY_HEADERS=true
CORS_ORIGIN=https://yourdomain.com
RATE_LIMIT_WINDOW=15
RATE_LIMIT_MAX=100

# HTTPS 配置
HTTPS_ENABLED=true
SSL_CERT_PATH=/path/to/ssl/cert.pem
SSL_KEY_PATH=/path/to/ssl/key.pem

# 监控配置
MONITORING_ENABLED=true
METRICS_ENDPOINT=/metrics
HEALTH_CHECK_ENDPOINT=/health

# 缓存配置
CACHE_TYPE=redis
REDIS_URL=redis://localhost:6379
CACHE_TTL=3600

# 邮件配置 (用于告警)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
ALERT_EMAIL=<EMAIL>

# AI 模型配置
OPENAI_API_KEY=your-openai-key
ANTHROPIC_API_KEY=your-anthropic-key
DEEPSEEK_API_KEY=your-deepseek-key

# 备份配置
BACKUP_ENABLED=true
BACKUP_INTERVAL=24
BACKUP_RETENTION=30

# 安全扫描
SECURITY_SCAN_ENABLED=true
VULNERABILITY_CHECK_INTERVAL=24
EOF

    chmod 600 server/.env.production
    log_success "生产环境配置文件已创建: server/.env.production"
    log_warning "请根据实际环境修改配置文件中的占位符"
}

# 2. 配置安全中间件
setup_security_middleware() {
    log_info "=== 配置安全中间件 ==="
    
    cat > server/middleware/security.js << 'EOF'
/**
 * 生产环境安全中间件
 * 实施多层安全防护
 */

const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
const slowDown = require('express-slow-down');
const cors = require('cors');
const compression = require('compression');

class SecurityMiddleware {
    constructor(app, options = {}) {
        this.app = app;
        this.options = {
            rateLimitWindow: options.rateLimitWindow || 15,
            rateLimitMax: options.rateLimitMax || 100,
            corsOrigin: options.corsOrigin || false,
            ...options
        };
        this.setupMiddleware();
    }

    setupMiddleware() {
        // 1. 安全头配置
        this.app.use(helmet({
            contentSecurityPolicy: {
                directives: {
                    defaultSrc: ["'self'"],
                    styleSrc: ["'self'", "'unsafe-inline'"],
                    scriptSrc: ["'self'"],
                    imgSrc: ["'self'", "data:", "https:"],
                    connectSrc: ["'self'"],
                    fontSrc: ["'self'"],
                    objectSrc: ["'none'"],
                    mediaSrc: ["'self'"],
                    frameSrc: ["'none'"],
                }
            },
            crossOriginEmbedderPolicy: false
        }));

        // 2. CORS 配置
        this.app.use(cors({
            origin: this.options.corsOrigin,
            credentials: true,
            methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
            allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
        }));

        // 3. 压缩
        this.app.use(compression());

        // 4. 速率限制
        const limiter = rateLimit({
            windowMs: this.options.rateLimitWindow * 60 * 1000,
            max: this.options.rateLimitMax,
            message: {
                error: 'Too many requests',
                retryAfter: this.options.rateLimitWindow * 60
            },
            standardHeaders: true,
            legacyHeaders: false,
        });
        this.app.use(limiter);

        // 5. 慢速攻击防护
        const speedLimiter = slowDown({
            windowMs: 15 * 60 * 1000, // 15分钟
            delayAfter: 50, // 50个请求后开始延迟
            delayMs: 500 // 每个请求延迟500ms
        });
        this.app.use(speedLimiter);

        // 6. 请求大小限制
        this.app.use(express.json({ limit: '10mb' }));
        this.app.use(express.urlencoded({ extended: true, limit: '10mb' }));

        // 7. 隐藏技术栈信息
        this.app.disable('x-powered-by');

        // 8. 安全日志
        this.app.use(this.securityLogger);
    }

    securityLogger(req, res, next) {
        const startTime = Date.now();
        
        res.on('finish', () => {
            const duration = Date.now() - startTime;
            const logData = {
                timestamp: new Date().toISOString(),
                method: req.method,
                url: req.url,
                ip: req.ip,
                userAgent: req.get('User-Agent'),
                statusCode: res.statusCode,
                duration,
                contentLength: res.get('Content-Length') || 0
            };

            // 记录可疑活动
            if (res.statusCode >= 400 || duration > 5000) {
                console.warn('[SECURITY] 可疑请求:', logData);
            }
        });

        next();
    }

    // API 密钥验证中间件
    static apiKeyAuth(req, res, next) {
        const apiKey = req.header('X-API-Key');
        const validApiKey = process.env.API_KEY;

        if (!apiKey || apiKey !== validApiKey) {
            return res.status(401).json({
                error: 'Invalid API key',
                code: 'UNAUTHORIZED'
            });
        }

        next();
    }

    // JWT 验证中间件
    static jwtAuth(req, res, next) {
        const token = req.header('Authorization')?.replace('Bearer ', '');
        
        if (!token) {
            return res.status(401).json({
                error: 'Access token required',
                code: 'TOKEN_REQUIRED'
            });
        }

        try {
            const jwt = require('jsonwebtoken');
            const decoded = jwt.verify(token, process.env.JWT_SECRET);
            req.user = decoded;
            next();
        } catch (error) {
            return res.status(401).json({
                error: 'Invalid token',
                code: 'TOKEN_INVALID'
            });
        }
    }

    // 输入验证中间件
    static validateInput(schema) {
        return (req, res, next) => {
            const { error } = schema.validate(req.body);
            if (error) {
                return res.status(400).json({
                    error: 'Validation failed',
                    details: error.details.map(d => d.message)
                });
            }
            next();
        };
    }
}

module.exports = SecurityMiddleware;
EOF

    log_success "安全中间件已创建: server/middleware/security.js"
}

# 3. 配置 HTTPS
setup_https() {
    log_info "=== 配置 HTTPS ==="
    
    # 创建 SSL 证书目录
    mkdir -p server/ssl
    
    # 生成自签名证书 (仅用于开发/测试)
    if [ ! -f "server/ssl/cert.pem" ]; then
        log_info "生成自签名 SSL 证书..."
        openssl req -x509 -newkey rsa:4096 -keyout server/ssl/key.pem -out server/ssl/cert.pem -days 365 -nodes -subj "/C=CN/ST=State/L=City/O=Organization/CN=localhost"
        chmod 600 server/ssl/key.pem
        chmod 644 server/ssl/cert.pem
        log_success "SSL 证书已生成"
        log_warning "生产环境请使用正式的 SSL 证书"
    fi
    
    # 创建 HTTPS 服务器配置
    cat > server/https-server.js << 'EOF'
/**
 * HTTPS 服务器配置
 * 用于生产环境的安全连接
 */

const https = require('https');
const fs = require('fs');
const path = require('path');

class HTTPSServer {
    constructor(app, options = {}) {
        this.app = app;
        this.options = {
            port: options.port || 443,
            certPath: options.certPath || './ssl/cert.pem',
            keyPath: options.keyPath || './ssl/key.pem',
            ...options
        };
    }

    start() {
        try {
            const httpsOptions = {
                cert: fs.readFileSync(this.options.certPath),
                key: fs.readFileSync(this.options.keyPath)
            };

            const server = https.createServer(httpsOptions, this.app);
            
            server.listen(this.options.port, () => {
                console.log(`🔒 HTTPS Server running on port ${this.options.port}`);
            });

            return server;
        } catch (error) {
            console.error('HTTPS 服务器启动失败:', error);
            throw error;
        }
    }
}

module.exports = HTTPSServer;
EOF

    log_success "HTTPS 服务器配置已创建: server/https-server.js"
}

# 4. 配置防火墙规则
setup_firewall() {
    log_info "=== 配置防火墙规则 ==="
    
    cat > firewall-rules.sh << 'EOF'
#!/bin/bash

# All-Agent 防火墙配置
# 仅允许必要的端口访问

# 清除现有规则
iptables -F
iptables -X
iptables -t nat -F
iptables -t nat -X

# 设置默认策略
iptables -P INPUT DROP
iptables -P FORWARD DROP
iptables -P OUTPUT ACCEPT

# 允许本地回环
iptables -A INPUT -i lo -j ACCEPT
iptables -A OUTPUT -o lo -j ACCEPT

# 允许已建立的连接
iptables -A INPUT -m state --state ESTABLISHED,RELATED -j ACCEPT

# 允许 SSH (端口 22)
iptables -A INPUT -p tcp --dport 22 -j ACCEPT

# 允许 HTTP (端口 80)
iptables -A INPUT -p tcp --dport 80 -j ACCEPT

# 允许 HTTPS (端口 443)
iptables -A INPUT -p tcp --dport 443 -j ACCEPT

# 允许 All-Agent 服务端口 (端口 3000)
iptables -A INPUT -p tcp --dport 3000 -j ACCEPT

# 允许 ping
iptables -A INPUT -p icmp --icmp-type echo-request -j ACCEPT

# 记录被拒绝的连接
iptables -A INPUT -j LOG --log-prefix "DROPPED: "
iptables -A INPUT -j DROP

# 保存规则
iptables-save > /etc/iptables/rules.v4

echo "防火墙规则已配置"
EOF

    chmod +x firewall-rules.sh
    log_success "防火墙配置脚本已创建: firewall-rules.sh"
    log_warning "请以 root 权限运行防火墙配置脚本"
}

# 5. 配置日志安全
setup_secure_logging() {
    log_info "=== 配置安全日志 ==="
    
    cat > server/utils/secureLogger.js << 'EOF'
/**
 * 安全日志记录器
 * 记录安全相关事件和告警
 */

const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

class SecureLogger {
    constructor(options = {}) {
        this.logDir = options.logDir || './logs';
        this.maxFileSize = options.maxFileSize || 100 * 1024 * 1024; // 100MB
        this.maxFiles = options.maxFiles || 10;
        this.encryptLogs = options.encryptLogs || false;
        this.encryptionKey = options.encryptionKey || process.env.ENCRYPTION_KEY;
        
        this.ensureLogDir();
    }

    ensureLogDir() {
        if (!fs.existsSync(this.logDir)) {
            fs.mkdirSync(this.logDir, { recursive: true });
        }
    }

    log(level, message, metadata = {}) {
        const logEntry = {
            timestamp: new Date().toISOString(),
            level: level.toUpperCase(),
            message,
            metadata,
            pid: process.pid,
            hostname: require('os').hostname()
        };

        const logLine = JSON.stringify(logEntry) + '\n';
        const fileName = `security-${new Date().toISOString().split('T')[0]}.log`;
        const filePath = path.join(this.logDir, fileName);

        if (this.encryptLogs && this.encryptionKey) {
            this.writeEncrypted(filePath, logLine);
        } else {
            fs.appendFileSync(filePath, logLine);
        }

        // 控制台输出
        console.log(`[${logEntry.level}] ${logEntry.message}`);
    }

    writeEncrypted(filePath, data) {
        const cipher = crypto.createCipher('aes-256-cbc', this.encryptionKey);
        let encrypted = cipher.update(data, 'utf8', 'hex');
        encrypted += cipher.final('hex');
        fs.appendFileSync(filePath, encrypted + '\n');
    }

    // 安全事件记录方法
    logSecurityEvent(event, details = {}) {
        this.log('security', `Security event: ${event}`, details);
    }

    logAuthFailure(username, ip, reason) {
        this.logSecurityEvent('AUTH_FAILURE', {
            username,
            ip,
            reason,
            timestamp: Date.now()
        });
    }

    logSuspiciousActivity(activity, details) {
        this.logSecurityEvent('SUSPICIOUS_ACTIVITY', {
            activity,
            details,
            timestamp: Date.now()
        });
    }

    logDataAccess(user, resource, action) {
        this.logSecurityEvent('DATA_ACCESS', {
            user,
            resource,
            action,
            timestamp: Date.now()
        });
    }

    logConfigChange(user, setting, oldValue, newValue) {
        this.logSecurityEvent('CONFIG_CHANGE', {
            user,
            setting,
            oldValue: oldValue ? '[REDACTED]' : null,
            newValue: newValue ? '[REDACTED]' : null,
            timestamp: Date.now()
        });
    }
}

module.exports = SecureLogger;
EOF

    log_success "安全日志记录器已创建: server/utils/secureLogger.js"
}

# 6. 创建安全检查脚本
create_security_checker() {
    log_info "=== 创建安全检查脚本 ==="
    
    cat > scripts/security-check.sh << 'EOF'
#!/bin/bash

# 定期安全检查脚本
# 检查系统安全状态

LOG_FILE="./logs/security-check.log"

log_security() {
    echo "[$(date)] $1" >> "$LOG_FILE"
}

# 检查文件权限
check_file_permissions() {
    echo "检查文件权限..."
    
    # 检查敏感文件权限
    for file in server/.env* server/ssl/* server/data/*; do
        if [ -f "$file" ]; then
            perms=$(stat -c "%a" "$file" 2>/dev/null || stat -f "%A" "$file" 2>/dev/null)
            if [ "$perms" != "600" ] && [ "$perms" != "644" ]; then
                log_security "WARNING: $file has permissions $perms"
            fi
        fi
    done
}

# 检查进程状态
check_processes() {
    echo "检查进程状态..."
    
    # 检查是否有可疑进程
    ps aux | grep -E "(nc|netcat|telnet)" | grep -v grep && log_security "WARNING: Suspicious network tools detected"
}

# 检查网络连接
check_network() {
    echo "检查网络连接..."
    
    # 检查监听端口
    netstat -tlnp 2>/dev/null | grep LISTEN | while read line; do
        port=$(echo "$line" | awk '{print $4}' | cut -d: -f2)
        if [ "$port" != "22" ] && [ "$port" != "80" ] && [ "$port" != "443" ] && [ "$port" != "3000" ]; then
            log_security "INFO: Unexpected listening port: $port"
        fi
    done
}

# 检查日志异常
check_logs() {
    echo "检查日志异常..."
    
    # 检查失败的登录尝试
    if [ -f "./logs/security-$(date +%Y-%m-%d).log" ]; then
        failed_logins=$(grep "AUTH_FAILURE" "./logs/security-$(date +%Y-%m-%d).log" | wc -l)
        if [ "$failed_logins" -gt 10 ]; then
            log_security "WARNING: High number of failed login attempts: $failed_logins"
        fi
    fi
}

# 主检查函数
main() {
    log_security "Starting security check"
    check_file_permissions
    check_processes
    check_network
    check_logs
    log_security "Security check completed"
}

main
EOF

    chmod +x scripts/security-check.sh
    log_success "安全检查脚本已创建: scripts/security-check.sh"
}

# 7. 生成安全配置报告
generate_security_report() {
    log_info "=== 生成安全配置报告 ==="
    
    cat > production-security-report.md << EOF
# All-Agent 生产环境安全配置报告

## 配置时间
$(date)

## 已实施的安全措施

### 1. 密钥管理
- ✅ 生成强 JWT 密钥 (128位)
- ✅ 生成 API 密钥 (64位)
- ✅ 生成加密密钥 (64位)
- ✅ 配置会话密钥

### 2. 网络安全
- ✅ HTTPS 配置和 SSL 证书
- ✅ CORS 策略配置
- ✅ 速率限制和慢速攻击防护
- ✅ 防火墙规则配置

### 3. 应用安全
- ✅ 安全头配置 (Helmet.js)
- ✅ 输入验证和清理
- ✅ SQL 注入防护
- ✅ XSS 防护

### 4. 认证和授权
- ✅ JWT 令牌验证
- ✅ API 密钥认证
- ✅ 会话管理

### 5. 日志和监控
- ✅ 安全事件日志
- ✅ 访问日志记录
- ✅ 异常活动检测
- ✅ 定期安全检查

### 6. 数据保护
- ✅ 数据库加密选项
- ✅ 敏感数据脱敏
- ✅ 备份加密

## 配置文件

### 生产环境配置
- \`server/.env.production\` - 生产环境变量
- \`server/middleware/security.js\` - 安全中间件
- \`server/https-server.js\` - HTTPS 服务器
- \`server/utils/secureLogger.js\` - 安全日志

### 安全脚本
- \`firewall-rules.sh\` - 防火墙配置
- \`scripts/security-check.sh\` - 定期安全检查

## 部署清单

### 部署前检查
- [ ] 更新所有配置文件中的占位符
- [ ] 获取正式的 SSL 证书
- [ ] 配置生产数据库
- [ ] 设置邮件服务器
- [ ] 配置监控系统

### 部署后验证
- [ ] 验证 HTTPS 连接
- [ ] 测试安全头
- [ ] 验证速率限制
- [ ] 检查日志记录
- [ ] 运行安全扫描

## 维护任务

### 每日
- 检查安全日志
- 监控异常活动
- 验证备份完整性

### 每周
- 运行安全检查脚本
- 更新安全补丁
- 审查访问日志

### 每月
- 更新依赖包
- 安全漏洞扫描
- 密钥轮换评估

## 应急响应

### 安全事件处理
1. 立即隔离受影响系统
2. 收集和保存证据
3. 通知相关人员
4. 修复漏洞
5. 恢复服务
6. 事后分析

### 联系信息
- 安全团队: <EMAIL>
- 系统管理员: <EMAIL>
- 应急热线: +86-xxx-xxxx-xxxx

## 合规性

### 数据保护
- GDPR 合规性配置
- 数据最小化原则
- 用户同意管理
- 数据删除权利

### 审计要求
- 访问日志保留 1 年
- 安全事件记录
- 配置变更追踪
- 定期安全评估
EOF

    log_success "生产环境安全配置报告已生成: production-security-report.md"
}

# 主函数
main() {
    log_info "开始配置 All-Agent 生产环境安全..."
    log_info "配置时间: $(date)"
    echo
    
    # 检查依赖
    if ! command -v openssl >/dev/null 2>&1; then
        log_error "OpenSSL 未安装，请先安装"
        exit 1
    fi
    
    # 执行安全配置
    generate_secure_keys
    setup_security_middleware
    setup_https
    setup_firewall
    setup_secure_logging
    create_security_checker
    generate_security_report
    
    echo
    log_success "🔒 生产环境安全配置完成！"
    log_info "请查看 production-security-report.md 了解详细信息"
    log_warning "请根据实际环境修改配置文件中的占位符"
    log_warning "生产环境请使用正式的 SSL 证书"
}

# 执行主函数
main "$@"
