#!/usr/bin/env node

/**
 * AI 模型直接测试脚本
 * 使用 .env 文件中的配置直接测试各个 AI 提供商
 */

require('dotenv').config({ path: '../.env' });
const fetch = require('node-fetch');

// 测试配置
const TEST_PROMPT = "你好，请简单介绍一下你自己。";
const SIMPLE_PROMPT = "Hello";

// 颜色输出
const colors = {
    reset: '\x1b[0m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
    console.log(`${colors[color]}${message}${colors.reset}`);
}

function logSection(title) {
    console.log('\n' + '='.repeat(60));
    log(title, 'cyan');
    console.log('='.repeat(60));
}

function logTest(provider, status, message) {
    const statusColor = status === 'SUCCESS' ? 'green' : status === 'ERROR' ? 'red' : 'yellow';
    log(`[${provider}] ${status}: ${message}`, statusColor);
}

// 测试 DeepSeek
async function testDeepSeek() {
    const apiKey = process.env.DEEPSEEK_API_KEY;
    const baseUrl = process.env.DEEPSEEK_API_BASE;
    const model = process.env.DEEPSEEK_MODEL;

    if (!apiKey) {
        logTest('DeepSeek', 'SKIP', 'API Key 未配置');
        return false;
    }

    try {
        logTest('DeepSeek', 'TESTING', `使用模型 ${model}`);
        
        const response = await fetch(`${baseUrl}/chat/completions`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${apiKey}`
            },
            body: JSON.stringify({
                model: model,
                messages: [{ role: 'user', content: TEST_PROMPT }],
                max_tokens: 100,
                temperature: 0.7
            })
        });

        if (!response.ok) {
            const error = await response.text();
            logTest('DeepSeek', 'ERROR', `HTTP ${response.status}: ${error}`);
            return false;
        }

        const data = await response.json();
        const aiResponse = data.choices[0].message.content;
        
        logTest('DeepSeek', 'SUCCESS', `响应长度: ${aiResponse.length} 字符`);
        log(`响应内容: ${aiResponse.substring(0, 100)}...`, 'blue');
        return true;

    } catch (error) {
        logTest('DeepSeek', 'ERROR', error.message);
        return false;
    }
}

// 测试 OpenAI
async function testOpenAI() {
    const apiKey = process.env.OPENAI_API_KEY;
    const baseUrl = process.env.OPENAI_API_BASE;
    const model = process.env.OPENAI_MODEL;

    if (!apiKey) {
        logTest('OpenAI', 'SKIP', 'API Key 未配置');
        return false;
    }

    try {
        logTest('OpenAI', 'TESTING', `使用模型 ${model}`);
        
        const response = await fetch(`${baseUrl}/chat/completions`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${apiKey}`
            },
            body: JSON.stringify({
                model: model,
                messages: [{ role: 'user', content: TEST_PROMPT }],
                max_tokens: 100,
                temperature: 0.7
            })
        });

        if (!response.ok) {
            const error = await response.text();
            logTest('OpenAI', 'ERROR', `HTTP ${response.status}: ${error}`);
            return false;
        }

        const data = await response.json();
        const aiResponse = data.choices[0].message.content;
        
        logTest('OpenAI', 'SUCCESS', `响应长度: ${aiResponse.length} 字符`);
        log(`响应内容: ${aiResponse.substring(0, 100)}...`, 'blue');
        return true;

    } catch (error) {
        logTest('OpenAI', 'ERROR', error.message);
        return false;
    }
}

// 测试 Google Gemini
async function testGoogleGemini() {
    const apiKey = process.env.GOOGLE_API_KEY;
    const model = process.env.GOOGLE_MODEL;

    if (!apiKey) {
        logTest('Google Gemini', 'SKIP', 'API Key 未配置');
        return false;
    }

    try {
        logTest('Google Gemini', 'TESTING', `使用模型 ${model}`);
        
        const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/${model}:generateContent?key=${apiKey}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                contents: [{
                    parts: [{ text: TEST_PROMPT }]
                }]
            })
        });

        if (!response.ok) {
            const error = await response.text();
            logTest('Google Gemini', 'ERROR', `HTTP ${response.status}: ${error}`);
            return false;
        }

        const data = await response.json();
        const aiResponse = data.candidates[0].content.parts[0].text;
        
        logTest('Google Gemini', 'SUCCESS', `响应长度: ${aiResponse.length} 字符`);
        log(`响应内容: ${aiResponse.substring(0, 100)}...`, 'blue');
        return true;

    } catch (error) {
        logTest('Google Gemini', 'ERROR', error.message);
        return false;
    }
}

// 测试 Mistral AI
async function testMistral() {
    const apiKey = process.env.MISTRAL_API_KEY;
    const baseUrl = process.env.MISTRAL_API_BASE;
    const model = process.env.MISTRAL_MODEL;

    if (!apiKey) {
        logTest('Mistral AI', 'SKIP', 'API Key 未配置');
        return false;
    }

    try {
        logTest('Mistral AI', 'TESTING', `使用模型 ${model}`);
        
        const response = await fetch(`${baseUrl}/chat/completions`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${apiKey}`
            },
            body: JSON.stringify({
                model: model,
                messages: [{ role: 'user', content: TEST_PROMPT }],
                max_tokens: 100,
                temperature: 0.7
            })
        });

        if (!response.ok) {
            const error = await response.text();
            logTest('Mistral AI', 'ERROR', `HTTP ${response.status}: ${error}`);
            return false;
        }

        const data = await response.json();
        const aiResponse = data.choices[0].message.content;
        
        logTest('Mistral AI', 'SUCCESS', `响应长度: ${aiResponse.length} 字符`);
        log(`响应内容: ${aiResponse.substring(0, 100)}...`, 'blue');
        return true;

    } catch (error) {
        logTest('Mistral AI', 'ERROR', error.message);
        return false;
    }
}

// 主测试函数
async function runTests() {
    logSection('🧪 AI 模型配置测试');
    
    log('从 .env 文件加载配置...', 'yellow');
    log(`测试提示: "${TEST_PROMPT}"`, 'magenta');
    
    const results = [];
    
    // 测试所有配置的 AI 提供商
    logSection('🔍 测试 DeepSeek');
    results.push({ provider: 'DeepSeek', success: await testDeepSeek() });
    
    logSection('🔍 测试 OpenAI');
    results.push({ provider: 'OpenAI', success: await testOpenAI() });
    
    logSection('🔍 测试 Google Gemini');
    results.push({ provider: 'Google Gemini', success: await testGoogleGemini() });
    
    logSection('🔍 测试 Mistral AI');
    results.push({ provider: 'Mistral AI', success: await testMistral() });
    
    // 汇总结果
    logSection('📊 测试结果汇总');
    
    const successful = results.filter(r => r.success);
    const failed = results.filter(r => !r.success);
    
    log(`✅ 成功: ${successful.length} 个提供商`, 'green');
    successful.forEach(r => log(`  - ${r.provider}`, 'green'));
    
    log(`❌ 失败: ${failed.length} 个提供商`, 'red');
    failed.forEach(r => log(`  - ${r.provider}`, 'red'));
    
    if (successful.length > 0) {
        log('\n🎉 至少有一个 AI 提供商工作正常！', 'green');
        log('问题可能在于 All-Agent 的集成逻辑，而不是 AI 模型配置。', 'yellow');
    } else {
        log('\n❌ 所有 AI 提供商都无法正常工作！', 'red');
        log('请检查网络连接、API Key 和配置。', 'yellow');
    }
    
    return successful.length > 0;
}

// 运行测试
if (require.main === module) {
    runTests().catch(error => {
        log(`测试过程中发生错误: ${error.message}`, 'red');
        process.exit(1);
    });
}

module.exports = { runTests };
