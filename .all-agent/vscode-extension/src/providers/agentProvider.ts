import * as vscode from 'vscode';
import { AllAgentClient } from '../client';

export class AgentProvider implements vscode.TreeDataProvider<AgentItem> {
    private _onDidChangeTreeData: vscode.EventEmitter<AgentItem | undefined | null | void> = new vscode.EventEmitter<AgentItem | undefined | null | void>();
    readonly onDidChangeTreeData: vscode.Event<AgentItem | undefined | null | void> = this._onDidChangeTreeData.event;

    constructor(private client: AllAgentClient) {
        // 定期刷新 Agent 状态
        setInterval(() => {
            this.refresh();
        }, 5000);
    }

    refresh(): void {
        this._onDidChangeTreeData.fire();
    }

    getTreeItem(element: AgentItem): vscode.TreeItem {
        return element;
    }

    async getChildren(element?: AgentItem): Promise<AgentItem[]> {
        if (!element) {
            // 根级别 - 返回所有 Agent
            try {
                const agents = await this.client.getAgents();
                return agents.map(agent => new AgentItem(
                    agent.name || agent.type,
                    agent.status || 'unknown',
                    agent.type,
                    vscode.TreeItemCollapsibleState.Collapsed,
                    agent
                ));
            } catch (error) {
                return [new AgentItem(
                    'Failed to load agents',
                    'error',
                    'error',
                    vscode.TreeItemCollapsibleState.None
                )];
            }
        } else {
            // Agent 详细信息
            const details: AgentItem[] = [];
            
            if (element.agentData) {
                const data = element.agentData;
                
                details.push(new AgentItem(
                    `Status: ${data.status || 'Unknown'}`,
                    'info',
                    'status',
                    vscode.TreeItemCollapsibleState.None
                ));
                
                if (data.capabilities && data.capabilities.length > 0) {
                    details.push(new AgentItem(
                        `Capabilities: ${data.capabilities.join(', ')}`,
                        'info',
                        'capabilities',
                        vscode.TreeItemCollapsibleState.None
                    ));
                }
                
                if (data.currentTasks !== undefined) {
                    details.push(new AgentItem(
                        `Current Tasks: ${data.currentTasks}`,
                        'info',
                        'tasks',
                        vscode.TreeItemCollapsibleState.None
                    ));
                }
                
                if (data.totalProcessed !== undefined) {
                    details.push(new AgentItem(
                        `Total Processed: ${data.totalProcessed}`,
                        'info',
                        'processed',
                        vscode.TreeItemCollapsibleState.None
                    ));
                }
            }
            
            return details;
        }
    }
}

class AgentItem extends vscode.TreeItem {
    constructor(
        public readonly label: string,
        public readonly status: string,
        public readonly type: string,
        public readonly collapsibleState: vscode.TreeItemCollapsibleState,
        public readonly agentData?: any
    ) {
        super(label, collapsibleState);

        this.tooltip = this.getTooltip();
        this.description = this.getDescription();
        this.iconPath = this.getIcon();
        this.contextValue = this.getContextValue();
    }

    private getTooltip(): string {
        if (this.agentData) {
            return `${this.agentData.name || this.agentData.type}\nStatus: ${this.agentData.status}\nType: ${this.agentData.type}`;
        }
        return this.label;
    }

    private getDescription(): string {
        if (this.type === 'analyzer') return 'Analysis';
        if (this.type === 'planner') return 'Planning';
        if (this.type === 'executor') return 'Execution';
        if (this.status === 'running') return '🟢';
        if (this.status === 'stopped') return '🔴';
        if (this.status === 'error') return '❌';
        return '';
    }

    private getIcon(): vscode.ThemeIcon {
        switch (this.type) {
            case 'analyzer':
                return new vscode.ThemeIcon('search');
            case 'planner':
                return new vscode.ThemeIcon('list-ordered');
            case 'executor':
                return new vscode.ThemeIcon('play');
            case 'status':
                return new vscode.ThemeIcon('pulse');
            case 'capabilities':
                return new vscode.ThemeIcon('tools');
            case 'tasks':
                return new vscode.ThemeIcon('tasklist');
            case 'processed':
                return new vscode.ThemeIcon('graph');
            default:
                if (this.status === 'running') {
                    return new vscode.ThemeIcon('circle-filled', new vscode.ThemeColor('terminal.ansiGreen'));
                } else if (this.status === 'error') {
                    return new vscode.ThemeIcon('error', new vscode.ThemeColor('errorForeground'));
                } else {
                    return new vscode.ThemeIcon('circle-outline');
                }
        }
    }

    private getContextValue(): string {
        if (this.agentData) {
            return 'agent';
        }
        return 'agentInfo';
    }
}
