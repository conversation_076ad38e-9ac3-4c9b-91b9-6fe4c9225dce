const express = require('express');
const httpProxy = require('http-proxy-middleware');
const rateLimit = require('express-rate-limit');
const helmet = require('helmet');
const cors = require('cors');
const jwt = require('jsonwebtoken');

/**
 * All-Agent API 网关
 * 负责路由、认证、限流、监控等功能
 */
class APIGateway {
    constructor() {
        this.app = express();
        this.port = process.env.GATEWAY_PORT || 8080;
        
        // 服务注册表
        this.services = {
            auth: {
                url: process.env.AUTH_SERVICE_URL || 'http://auth-service:3001',
                healthPath: '/health'
            },
            project: {
                url: process.env.PROJECT_SERVICE_URL || 'http://project-service:3002',
                healthPath: '/health'
            },
            agent: {
                url: process.env.AGENT_SERVICE_URL || 'http://agent-service:3003',
                healthPath: '/health'
            },
            llm: {
                url: process.env.LLM_SERVICE_URL || 'http://llm-service:3004',
                healthPath: '/health'
            },
            monitor: {
                url: process.env.MONITOR_SERVICE_URL || 'http://monitor-service:3005',
                healthPath: '/health'
            }
        };

        this.setupMiddleware();
        this.setupRoutes();
        this.setupProxies();
        this.startHealthChecks();
    }

    /**
     * 设置中间件
     */
    setupMiddleware() {
        // 安全中间件
        this.app.use(helmet());
        
        // CORS
        this.app.use(cors({
            origin: process.env.CORS_ORIGIN || '*',
            credentials: true
        }));

        // 请求解析
        this.app.use(express.json({ limit: '10mb' }));
        this.app.use(express.urlencoded({ extended: true }));

        // 全局限流
        this.app.use(rateLimit({
            windowMs: 15 * 60 * 1000, // 15分钟
            max: 1000, // 每个IP最多1000个请求
            message: {
                error: 'Too many requests',
                retryAfter: '15 minutes'
            }
        }));

        // 请求日志
        this.app.use((req, res, next) => {
            console.log(`${new Date().toISOString()} ${req.method} ${req.path} - ${req.ip}`);
            next();
        });
    }

    /**
     * 设置路由
     */
    setupRoutes() {
        // 网关健康检查
        this.app.get('/health', async (req, res) => {
            const health = await this.getGatewayHealth();
            res.status(health.healthy ? 200 : 503).json(health);
        });

        // 服务发现
        this.app.get('/services', (req, res) => {
            res.json({
                services: Object.keys(this.services),
                gateway: {
                    version: '1.0.0',
                    uptime: process.uptime()
                }
            });
        });

        // 认证中间件
        this.app.use('/api', this.authenticateRequest.bind(this));
    }

    /**
     * 设置代理
     */
    setupProxies() {
        // 认证服务代理
        this.app.use('/auth', httpProxy.createProxyMiddleware({
            target: this.services.auth.url,
            changeOrigin: true,
            pathRewrite: {
                '^/auth': ''
            },
            onError: this.handleProxyError.bind(this),
            onProxyReq: this.addProxyHeaders.bind(this)
        }));

        // 项目服务代理
        this.app.use('/api/projects', httpProxy.createProxyMiddleware({
            target: this.services.project.url,
            changeOrigin: true,
            pathRewrite: {
                '^/api/projects': '/api'
            },
            onError: this.handleProxyError.bind(this),
            onProxyReq: this.addProxyHeaders.bind(this)
        }));

        // Agent 服务代理
        this.app.use('/api/agents', httpProxy.createProxyMiddleware({
            target: this.services.agent.url,
            changeOrigin: true,
            pathRewrite: {
                '^/api/agents': '/api'
            },
            onError: this.handleProxyError.bind(this),
            onProxyReq: this.addProxyHeaders.bind(this)
        }));

        // LLM 服务代理
        this.app.use('/api/llm', httpProxy.createProxyMiddleware({
            target: this.services.llm.url,
            changeOrigin: true,
            pathRewrite: {
                '^/api/llm': '/api'
            },
            onError: this.handleProxyError.bind(this),
            onProxyReq: this.addProxyHeaders.bind(this)
        }));

        // 监控服务代理
        this.app.use('/api/monitor', httpProxy.createProxyMiddleware({
            target: this.services.monitor.url,
            changeOrigin: true,
            pathRewrite: {
                '^/api/monitor': '/api'
            },
            onError: this.handleProxyError.bind(this),
            onProxyReq: this.addProxyHeaders.bind(this)
        }));

        // WebSocket 代理
        this.app.use('/socket.io', httpProxy.createProxyMiddleware({
            target: this.services.agent.url,
            changeOrigin: true,
            ws: true,
            onError: this.handleProxyError.bind(this)
        }));
    }

    /**
     * 认证请求
     */
    async authenticateRequest(req, res, next) {
        // 跳过公开端点
        const publicPaths = ['/api/health', '/api/docs'];
        if (publicPaths.some(path => req.path.startsWith(path))) {
            return next();
        }

        const token = req.headers.authorization?.replace('Bearer ', '');
        
        if (!token) {
            return res.status(401).json({
                error: 'Authentication required',
                message: 'No token provided'
            });
        }

        try {
            // 验证 JWT 令牌
            const decoded = jwt.verify(token, process.env.JWT_SECRET);
            req.user = decoded;
            
            // 添加用户信息到请求头
            req.headers['x-user-id'] = decoded.id;
            req.headers['x-user-role'] = decoded.role;
            
            next();
        } catch (error) {
            return res.status(403).json({
                error: 'Invalid token',
                message: error.message
            });
        }
    }

    /**
     * 处理代理错误
     */
    handleProxyError(err, req, res) {
        console.error('Proxy error:', err.message);
        
        if (!res.headersSent) {
            res.status(503).json({
                error: 'Service unavailable',
                message: 'The requested service is temporarily unavailable',
                service: this.getServiceFromPath(req.path)
            });
        }
    }

    /**
     * 添加代理请求头
     */
    addProxyHeaders(proxyReq, req) {
        // 添加请求ID用于追踪
        const requestId = req.headers['x-request-id'] || this.generateRequestId();
        proxyReq.setHeader('x-request-id', requestId);
        
        // 添加网关信息
        proxyReq.setHeader('x-gateway', 'all-agent-gateway');
        proxyReq.setHeader('x-forwarded-by', 'all-agent-gateway');
        
        // 传递用户信息
        if (req.user) {
            proxyReq.setHeader('x-user-id', req.user.id);
            proxyReq.setHeader('x-user-role', req.user.role);
        }
    }

    /**
     * 获取网关健康状态
     */
    async getGatewayHealth() {
        const health = {
            healthy: true,
            timestamp: new Date().toISOString(),
            gateway: {
                status: 'healthy',
                uptime: process.uptime(),
                memory: process.memoryUsage(),
                version: '1.0.0'
            },
            services: {}
        };

        // 检查所有服务健康状态
        for (const [name, service] of Object.entries(this.services)) {
            try {
                const response = await fetch(`${service.url}${service.healthPath}`, {
                    timeout: 5000
                });
                
                health.services[name] = {
                    status: response.ok ? 'healthy' : 'unhealthy',
                    url: service.url,
                    responseTime: Date.now() - startTime
                };
                
                if (!response.ok) {
                    health.healthy = false;
                }
            } catch (error) {
                health.services[name] = {
                    status: 'unreachable',
                    url: service.url,
                    error: error.message
                };
                health.healthy = false;
            }
        }

        return health;
    }

    /**
     * 启动服务健康检查
     */
    startHealthChecks() {
        setInterval(async () => {
            for (const [name, service] of Object.entries(this.services)) {
                try {
                    const response = await fetch(`${service.url}${service.healthPath}`, {
                        timeout: 5000
                    });
                    
                    if (!response.ok) {
                        console.warn(`Service ${name} is unhealthy`);
                    }
                } catch (error) {
                    console.error(`Service ${name} is unreachable:`, error.message);
                }
            }
        }, 30000); // 每30秒检查一次
    }

    /**
     * 从路径获取服务名
     */
    getServiceFromPath(path) {
        if (path.startsWith('/auth')) return 'auth';
        if (path.startsWith('/api/projects')) return 'project';
        if (path.startsWith('/api/agents')) return 'agent';
        if (path.startsWith('/api/llm')) return 'llm';
        if (path.startsWith('/api/monitor')) return 'monitor';
        return 'unknown';
    }

    /**
     * 生成请求ID
     */
    generateRequestId() {
        return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    /**
     * 启动网关
     */
    start() {
        this.app.listen(this.port, () => {
            console.log(`🌐 API Gateway started on port ${this.port}`);
            console.log(`📋 Registered services:`, Object.keys(this.services));
        });
    }

    /**
     * 停止网关
     */
    stop() {
        console.log('🛑 Stopping API Gateway...');
        process.exit(0);
    }
}

// 启动网关
if (require.main === module) {
    const gateway = new APIGateway();
    gateway.start();

    // 优雅关闭
    process.on('SIGTERM', () => gateway.stop());
    process.on('SIGINT', () => gateway.stop());
}

module.exports = APIGateway;
