{"version": 3, "file": "terminalService.js", "sourceRoot": "", "sources": ["../../src/services/terminalService.ts"], "names": [], "mappings": ";;;AAAA,iCAAiC;AACjC,iDAAoD;AAEpD;;GAEG;AACH,MAAa,eAAe;IAKxB;QAJQ,cAAS,GAAiC,IAAI,GAAG,EAAE,CAAC;QACpD,cAAS,GAA8B,IAAI,GAAG,EAAE,CAAC;QACjD,kBAAa,GAAwB,IAAI,GAAG,EAAE,CAAC;QAGnD,WAAW;QACX,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC,CAAC,QAAQ,EAAE,EAAE;YAC1C,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;QACnC,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,cAAc,CAAC,OAAe,EAAE,UAKlC,EAAE;QAMF,MAAM,EACF,GAAG,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,MAAM,EACxD,OAAO,GAAG,KAAK,EACf,UAAU,GAAG,KAAK,EAClB,YAAY,GAAG,WAAW,EAC7B,GAAG,OAAO,CAAC;QAEZ,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YAC3B,MAAM,SAAS,GAAG,OAAO,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC;YACtC,IAAI,MAAM,GAAG,EAAE,CAAC;YAChB,IAAI,WAAW,GAAG,EAAE,CAAC;YAErB,QAAQ;YACR,MAAM,OAAO,GAAG,IAAA,qBAAK,EAAC,IAAI,EAAE,CAAC,IAAI,EAAE,OAAO,CAAC,EAAE;gBACzC,GAAG;gBACH,KAAK,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;aAClC,CAAC,CAAC;YAEH,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;YAEvC,OAAO;YACP,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,EAAE;gBAChC,MAAM,IAAI,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;gBAC7B,MAAM,IAAI,IAAI,CAAC;gBAEf,IAAI,UAAU,EAAE;oBACZ,IAAI,CAAC,gBAAgB,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC;iBAC7C;YACL,CAAC,CAAC,CAAC;YAEH,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,EAAE;gBAChC,MAAM,IAAI,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;gBAC7B,WAAW,IAAI,IAAI,CAAC;gBAEpB,IAAI,UAAU,EAAE;oBACZ,IAAI,CAAC,gBAAgB,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC;iBAC7C;YACL,CAAC,CAAC,CAAC;YAEH,SAAS;YACT,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,IAAI,EAAE,EAAE;gBACzB,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;gBAEjC,OAAO,CAAC;oBACJ,OAAO,EAAE,IAAI,KAAK,CAAC;oBACnB,MAAM,EAAE,MAAM,CAAC,IAAI,EAAE;oBACrB,KAAK,EAAE,WAAW,CAAC,IAAI,EAAE,IAAI,SAAS;oBACtC,QAAQ,EAAE,IAAI,IAAI,SAAS;iBAC9B,CAAC,CAAC;YACP,CAAC,CAAC,CAAC;YAEH,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,KAAK,EAAE,EAAE;gBAC1B,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;gBAEjC,OAAO,CAAC;oBACJ,OAAO,EAAE,KAAK;oBACd,MAAM,EAAE,EAAE;oBACV,KAAK,EAAE,KAAK,CAAC,OAAO;iBACvB,CAAC,CAAC;YACP,CAAC,CAAC,CAAC;YAEH,OAAO;YACP,UAAU,CAAC,GAAG,EAAE;gBACZ,IAAI,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE;oBAC/B,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;oBACxB,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;oBAEjC,OAAO,CAAC;wBACJ,OAAO,EAAE,KAAK;wBACd,MAAM,EAAE,MAAM,CAAC,IAAI,EAAE;wBACrB,KAAK,EAAE,iBAAiB;qBAC3B,CAAC,CAAC;iBACN;YACL,CAAC,EAAE,OAAO,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,iBAAiB,CAAC,OAAe,EAAE,eAAuB,WAAW;QACvE,IAAI,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;QAEhD,IAAI,CAAC,QAAQ,EAAE;YACX,QAAQ,GAAG,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC;gBACpC,IAAI,EAAE,YAAY;gBAClB,GAAG,EAAE,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,MAAM;aAC1D,CAAC,CAAC;YACH,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC;SAC9C;QAED,QAAQ,CAAC,IAAI,EAAE,CAAC;QAChB,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;IAC/B,CAAC;IAED;;OAEG;IACH,iBAAiB,CAAC,YAAoB;QAClC,OAAO,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC;IACtD,CAAC;IAED;;OAEG;IACH,mBAAmB,CAAC,YAAoB;QACpC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;IAC5C,CAAC;IAED;;OAEG;IACK,gBAAgB,CAAC,YAAoB,EAAE,IAAY;QACvD,MAAM,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC;QAC3D,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,YAAY,EAAE,OAAO,GAAG,IAAI,CAAC,CAAC;QAErD,UAAU;QACV,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC;QAC1D,IAAI,MAAM,CAAC,MAAM,GAAG,KAAK,EAAE;YACvB,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,YAAY,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;SAC9D;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,YAAY,CAAC,OAAe;QAC9B,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,SAAS,OAAO,EAAE,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;QAChF,OAAO,MAAM,CAAC,OAAO,CAAC;IAC1B,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,aAAa;QAOf,MAAM,IAAI,GAAQ;YACd,QAAQ,EAAE,OAAO,CAAC,QAAQ;YAC1B,IAAI,EAAE,OAAO,CAAC,IAAI;SACrB,CAAC;QAEF,gBAAgB;QAChB,IAAI;YACA,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,gBAAgB,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;YAClF,IAAI,UAAU,CAAC,OAAO,EAAE;gBACpB,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC,MAAM,CAAC;aACxC;SACJ;QAAC,OAAO,KAAK,EAAE;YACZ,OAAO;SACV;QAED,YAAY;QACZ,IAAI;YACA,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,eAAe,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;YAChF,IAAI,SAAS,CAAC,OAAO,EAAE;gBACnB,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC,MAAM,CAAC;aACtC;SACJ;QAAC,OAAO,KAAK,EAAE;YACZ,OAAO;SACV;QAED,YAAY;QACZ,IAAI;YACA,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,eAAe,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;YAChF,IAAI,SAAS,CAAC,OAAO,EAAE;gBACnB,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC,MAAM,CAAC;aACtC;SACJ;QAAC,OAAO,KAAK,EAAE;YACZ,OAAO;SACV;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,mBAAmB,CAAC,SAAiB,EAAE,UAGzC,EAAE;QAMF,MAAM,EAAE,UAAU,GAAG,IAAI,EAAE,YAAY,GAAG,iBAAiB,EAAE,GAAG,OAAO,CAAC;QAExE,SAAS;QACT,MAAM,OAAO,GAAG,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC;QAEjD,IAAI,CAAC,OAAO,EAAE;YACV,OAAO;gBACH,OAAO,EAAE,KAAK;gBACd,MAAM,EAAE,EAAE;gBACV,KAAK,EAAE,kCAAkC;gBACzC,UAAU,EAAE,wEAAwE;aACvF,CAAC;SACL;QAED,OAAO,MAAM,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE;YACtC,UAAU;YACV,YAAY;YACZ,OAAO,EAAE,MAAM;SAClB,CAAC,CAAC;IACP,CAAC;IAED;;OAEG;IACK,gBAAgB,CAAC,SAAiB;QACtC,MAAM,KAAK,GAAG,SAAS,CAAC,WAAW,EAAE,CAAC,IAAI,EAAE,CAAC;QAE7C,OAAO;QACP,IAAI,KAAK,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,cAAc,CAAC,IAAI,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,EAAE;YACzF,OAAO,aAAa,CAAC;SACxB;QACD,IAAI,KAAK,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,KAAK,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE;YACxD,MAAM,YAAY,GAAG,SAAS,CAAC,KAAK,CAAC,+CAA+C,CAAC,CAAC;YACtF,IAAI,YAAY,EAAE;gBACd,OAAO,eAAe,YAAY,CAAC,CAAC,CAAC,EAAE,CAAC;aAC3C;SACJ;QAED,OAAO;QACP,IAAI,KAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE;YACzG,OAAO,WAAW,CAAC;SACtB;QACD,IAAI,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE;YACnD,OAAO,aAAa,CAAC;SACxB;QAED,OAAO;QACP,IAAI,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE;YACjD,OAAO,UAAU,CAAC;SACrB;QACD,IAAI,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE;YACxB,OAAO,UAAU,CAAC;SACrB;QAED,OAAO;QACP,IAAI,KAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE;YACjF,OAAO,eAAe,CAAC;SAC1B;QAED,SAAS;QACT,IAAI,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE;YACvB,IAAI,KAAK,CAAC,QAAQ,CAAC,QAAQ,CAAC;gBAAE,OAAO,YAAY,CAAC;YAClD,IAAI,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC;gBAAE,OAAO,WAAW,CAAC;YAC9C,IAAI,KAAK,CAAC,QAAQ,CAAC,QAAQ,CAAC;gBAAE,OAAO,6BAA6B,CAAC;YACnE,IAAI,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC;gBAAE,OAAO,UAAU,CAAC;YAC9C,IAAI,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC;gBAAE,OAAO,UAAU,CAAC;SACjD;QAED,OAAO;QACP,IAAI,KAAK,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,KAAK,CAAC,UAAU,CAAC,MAAM,CAAC,EAAE;YACnF,OAAO,SAAS,CAAC;SACpB;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,iBAAiB,CAAC,OAA+C,EAAE,UAGrE,EAAE;QAKF,MAAM,EAAE,UAAU,GAAG,IAAI,EAAE,YAAY,GAAG,mBAAmB,EAAE,GAAG,OAAO,CAAC;QAE1E,uBAAuB;QACvB,MAAM,eAAe,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC,CAAC;QAC/D,IAAI,CAAC,eAAe,EAAE;YAClB,OAAO;gBACH,OAAO,EAAE,KAAK;gBACd,MAAM,EAAE,EAAE;gBACV,KAAK,EAAE,2BAA2B;aACrC,CAAC;SACL;QAED,MAAM,eAAe,GAAG,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,eAAe,CAAC,GAAG,EAAE,cAAc,CAAC,CAAC;QAEjF,IAAI;YACA,MAAM,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;SACnD;QAAC,OAAO,KAAK,EAAE;YACZ,OAAO;gBACH,OAAO,EAAE,KAAK;gBACd,MAAM,EAAE,EAAE;gBACV,KAAK,EAAE,qCAAqC;aAC/C,CAAC;SACL;QAED,OAAO;QACP,IAAI,GAAW,CAAC;QAChB,QAAQ,OAAO,EAAE;YACb,KAAK,OAAO;gBACR,GAAG,GAAG,WAAW,CAAC;gBAClB,MAAM;YACV,KAAK,MAAM;gBACP,GAAG,GAAG,UAAU,CAAC;gBACjB,MAAM;YACV,KAAK,OAAO;gBACR,GAAG,GAAG,eAAe,CAAC;gBACtB,MAAM;YACV,KAAK,SAAS;gBACV,GAAG,GAAG,aAAa,CAAC;gBACpB,MAAM;YACV;gBACI,OAAO;oBACH,OAAO,EAAE,KAAK;oBACd,MAAM,EAAE,EAAE;oBACV,KAAK,EAAE,oBAAoB,OAAO,EAAE;iBACvC,CAAC;SACT;QAED,OAAO,MAAM,IAAI,CAAC,cAAc,CAAC,GAAG,EAAE;YAClC,GAAG,EAAE,eAAe,CAAC,GAAG,CAAC,MAAM;YAC/B,OAAO,EAAE,MAAM;YACf,UAAU;YACV,YAAY;SACf,CAAC,CAAC;IACP,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,cAAc;QAMhB,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC,CAAC;QAEvE,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE;YACjB,OAAO,EAAE,CAAC;SACb;QAED,MAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ;QAC1D,MAAM,SAAS,GAKV,EAAE,CAAC;QAER,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE;YACtB,MAAM,KAAK,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YACvC,IAAI,KAAK,CAAC,MAAM,IAAI,EAAE,EAAE;gBACpB,SAAS,CAAC,IAAI,CAAC;oBACX,GAAG,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;oBACvB,IAAI,EAAE,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC;oBAC/B,GAAG,EAAE,KAAK,CAAC,CAAC,CAAC;oBACb,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC;iBACnB,CAAC,CAAC;aACN;SACJ;QAED,OAAO,SAAS,CAAC;IACrB,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,WAAW,CAAC,GAAW;QACzB,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,QAAQ,GAAG,EAAE,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;QAC3E,OAAO,MAAM,CAAC,OAAO,CAAC;IAC1B,CAAC;IAED;;OAEG;IACK,eAAe,CAAC,QAAyB;QAC7C,KAAK,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,EAAE;YACjD,IAAI,IAAI,KAAK,QAAQ,EAAE;gBACnB,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;gBAC5B,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;gBAChC,MAAM;aACT;SACJ;IACL,CAAC;IAED;;OAEG;IACH,OAAO;QACH,SAAS;QACT,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,EAAE;YAC3C,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;SAC3B;QACD,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC;QAEvB,OAAO;QACP,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,EAAE;YAC5C,QAAQ,CAAC,OAAO,EAAE,CAAC;SACtB;QACD,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC;QACvB,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;IAC/B,CAAC;CACJ;AAnbD,0CAmbC"}