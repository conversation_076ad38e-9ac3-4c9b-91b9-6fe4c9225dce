const express = require('express');
const bcrypt = require('bcrypt');
const jwt = require('jsonwebtoken');
const rateLimit = require('express-rate-limit');
const helmet = require('helmet');
const Database = require('./database');

/**
 * 认证微服务
 * 专门处理用户认证、授权和会话管理
 */
class AuthService {
    constructor() {
        this.app = express();
        this.port = process.env.AUTH_SERVICE_PORT || 3001;
        this.database = new Database();
        
        this.setupMiddleware();
        this.setupRoutes();
    }

    /**
     * 设置中间件
     */
    setupMiddleware() {
        this.app.use(helmet());
        this.app.use(express.json({ limit: '1mb' }));
        this.app.use(express.urlencoded({ extended: true }));

        // 认证相关的严格限流
        const authLimiter = rateLimit({
            windowMs: 15 * 60 * 1000, // 15分钟
            max: 5, // 每个IP最多5次认证尝试
            message: {
                error: 'Too many authentication attempts',
                retryAfter: '15 minutes'
            },
            standardHeaders: true,
            legacyHeaders: false
        });

        this.app.use('/api/login', authLimiter);
        this.app.use('/api/register', authLimiter);

        // 请求日志
        this.app.use((req, res, next) => {
            console.log(`${new Date().toISOString()} [AUTH] ${req.method} ${req.path} - ${req.ip}`);
            next();
        });
    }

    /**
     * 设置路由
     */
    setupRoutes() {
        // 健康检查
        this.app.get('/health', (req, res) => {
            res.json({
                service: 'auth-service',
                status: 'healthy',
                timestamp: new Date().toISOString(),
                uptime: process.uptime(),
                database: this.database.isConnected()
            });
        });

        // 用户注册
        this.app.post('/api/register', async (req, res) => {
            try {
                const { username, email, password, role = 'user' } = req.body;

                // 输入验证
                if (!username || !email || !password) {
                    return res.status(400).json({
                        error: 'Missing required fields',
                        required: ['username', 'email', 'password']
                    });
                }

                if (password.length < 6) {
                    return res.status(400).json({
                        error: 'Password too weak',
                        message: 'Password must be at least 6 characters long'
                    });
                }

                // 检查用户是否已存在
                const existingUser = await this.database.getUserByEmail(email);
                if (existingUser) {
                    return res.status(409).json({
                        error: 'User already exists',
                        message: 'A user with this email already exists'
                    });
                }

                // 创建用户
                const passwordHash = await bcrypt.hash(password, 12);
                const user = await this.database.createUser({
                    username,
                    email,
                    password_hash: passwordHash,
                    role
                });

                // 生成令牌
                const token = this.generateToken(user);

                res.status(201).json({
                    success: true,
                    data: {
                        user: this.sanitizeUser(user),
                        token,
                        expiresIn: '7d'
                    }
                });

            } catch (error) {
                console.error('Registration error:', error);
                res.status(500).json({
                    error: 'Registration failed',
                    message: 'Internal server error'
                });
            }
        });

        // 用户登录
        this.app.post('/api/login', async (req, res) => {
            try {
                const { username, password, rememberMe = false } = req.body;

                if (!username || !password) {
                    return res.status(400).json({
                        error: 'Missing credentials',
                        required: ['username', 'password']
                    });
                }

                // 查找用户
                const user = await this.database.getUserByEmailOrUsername(username);
                if (!user) {
                    return res.status(401).json({
                        error: 'Invalid credentials',
                        message: 'Username or password is incorrect'
                    });
                }

                // 验证密码
                const isValidPassword = await bcrypt.compare(password, user.password_hash);
                if (!isValidPassword) {
                    return res.status(401).json({
                        error: 'Invalid credentials',
                        message: 'Username or password is incorrect'
                    });
                }

                // 更新最后登录时间
                await this.database.updateLastLogin(user.id);

                // 生成令牌
                const expiresIn = rememberMe ? '30d' : '7d';
                const token = this.generateToken(user, expiresIn);

                res.json({
                    success: true,
                    data: {
                        user: this.sanitizeUser(user),
                        token,
                        expiresIn
                    }
                });

            } catch (error) {
                console.error('Login error:', error);
                res.status(500).json({
                    error: 'Login failed',
                    message: 'Internal server error'
                });
            }
        });

        // 令牌验证
        this.app.post('/api/verify', async (req, res) => {
            try {
                const { token } = req.body;

                if (!token) {
                    return res.status(400).json({
                        error: 'Missing token',
                        message: 'Token is required'
                    });
                }

                const decoded = jwt.verify(token, process.env.JWT_SECRET);
                const user = await this.database.getUserById(decoded.id);

                if (!user) {
                    return res.status(401).json({
                        error: 'Invalid token',
                        message: 'User not found'
                    });
                }

                res.json({
                    success: true,
                    data: {
                        user: this.sanitizeUser(user),
                        valid: true
                    }
                });

            } catch (error) {
                if (error.name === 'JsonWebTokenError' || error.name === 'TokenExpiredError') {
                    return res.status(401).json({
                        error: 'Invalid token',
                        message: error.message
                    });
                }

                console.error('Token verification error:', error);
                res.status(500).json({
                    error: 'Verification failed',
                    message: 'Internal server error'
                });
            }
        });

        // 令牌刷新
        this.app.post('/api/refresh', async (req, res) => {
            try {
                const { token } = req.body;

                if (!token) {
                    return res.status(400).json({
                        error: 'Missing token',
                        message: 'Token is required'
                    });
                }

                const decoded = jwt.verify(token, process.env.JWT_SECRET);
                const user = await this.database.getUserById(decoded.id);

                if (!user) {
                    return res.status(401).json({
                        error: 'Invalid token',
                        message: 'User not found'
                    });
                }

                // 生成新令牌
                const newToken = this.generateToken(user);

                res.json({
                    success: true,
                    data: {
                        user: this.sanitizeUser(user),
                        token: newToken,
                        expiresIn: '7d'
                    }
                });

            } catch (error) {
                if (error.name === 'JsonWebTokenError' || error.name === 'TokenExpiredError') {
                    return res.status(401).json({
                        error: 'Invalid token',
                        message: error.message
                    });
                }

                console.error('Token refresh error:', error);
                res.status(500).json({
                    error: 'Refresh failed',
                    message: 'Internal server error'
                });
            }
        });

        // 修改密码
        this.app.post('/api/change-password', this.authenticateToken.bind(this), async (req, res) => {
            try {
                const { oldPassword, newPassword } = req.body;
                const userId = req.user.id;

                if (!oldPassword || !newPassword) {
                    return res.status(400).json({
                        error: 'Missing passwords',
                        required: ['oldPassword', 'newPassword']
                    });
                }

                if (newPassword.length < 6) {
                    return res.status(400).json({
                        error: 'Password too weak',
                        message: 'New password must be at least 6 characters long'
                    });
                }

                // 获取用户
                const user = await this.database.getUserById(userId);
                if (!user) {
                    return res.status(404).json({
                        error: 'User not found'
                    });
                }

                // 验证旧密码
                const isValidOldPassword = await bcrypt.compare(oldPassword, user.password_hash);
                if (!isValidOldPassword) {
                    return res.status(401).json({
                        error: 'Invalid old password'
                    });
                }

                // 更新密码
                const newPasswordHash = await bcrypt.hash(newPassword, 12);
                await this.database.updatePassword(userId, newPasswordHash);

                res.json({
                    success: true,
                    message: 'Password changed successfully'
                });

            } catch (error) {
                console.error('Change password error:', error);
                res.status(500).json({
                    error: 'Password change failed',
                    message: 'Internal server error'
                });
            }
        });

        // 获取用户信息
        this.app.get('/api/me', this.authenticateToken.bind(this), async (req, res) => {
            try {
                const user = await this.database.getUserById(req.user.id);
                
                if (!user) {
                    return res.status(404).json({
                        error: 'User not found'
                    });
                }

                res.json({
                    success: true,
                    data: this.sanitizeUser(user)
                });

            } catch (error) {
                console.error('Get user error:', error);
                res.status(500).json({
                    error: 'Failed to get user',
                    message: 'Internal server error'
                });
            }
        });

        // 错误处理
        this.app.use((error, req, res, next) => {
            console.error('Auth service error:', error);
            res.status(500).json({
                error: 'Internal server error',
                message: 'Something went wrong'
            });
        });

        // 404 处理
        this.app.use('*', (req, res) => {
            res.status(404).json({
                error: 'Not found',
                message: 'The requested endpoint does not exist'
            });
        });
    }

    /**
     * 认证中间件
     */
    authenticateToken(req, res, next) {
        const authHeader = req.headers['authorization'];
        const token = authHeader && authHeader.split(' ')[1];

        if (!token) {
            return res.status(401).json({
                error: 'Access token required',
                message: 'No token provided'
            });
        }

        jwt.verify(token, process.env.JWT_SECRET, (err, user) => {
            if (err) {
                return res.status(403).json({
                    error: 'Invalid token',
                    message: err.message
                });
            }

            req.user = user;
            next();
        });
    }

    /**
     * 生成 JWT 令牌
     */
    generateToken(user, expiresIn = '7d') {
        return jwt.sign(
            {
                id: user.id,
                username: user.username,
                email: user.email,
                role: user.role
            },
            process.env.JWT_SECRET,
            { expiresIn }
        );
    }

    /**
     * 清理用户数据（移除敏感信息）
     */
    sanitizeUser(user) {
        const { password_hash, ...sanitized } = user;
        return sanitized;
    }

    /**
     * 启动服务
     */
    async start() {
        try {
            await this.database.initialize();
            
            this.app.listen(this.port, () => {
                console.log(`🔐 Auth Service started on port ${this.port}`);
            });
        } catch (error) {
            console.error('Failed to start auth service:', error);
            process.exit(1);
        }
    }

    /**
     * 停止服务
     */
    async stop() {
        console.log('🛑 Stopping Auth Service...');
        await this.database.close();
        process.exit(0);
    }
}

// 启动服务
if (require.main === module) {
    const authService = new AuthService();
    authService.start();

    // 优雅关闭
    process.on('SIGTERM', () => authService.stop());
    process.on('SIGINT', () => authService.stop());
}

module.exports = AuthService;
