const fs = require('fs').promises;
const path = require('path');

/**
 * 日志工具类
 * 提供结构化日志记录功能
 */
class Logger {
    constructor(options = {}) {
        this.logLevel = options.logLevel || 'info';
        this.logDir = options.logDir || path.join(process.cwd(), '.all-agent/logs');
        this.maxFileSize = options.maxFileSize || 10 * 1024 * 1024; // 10MB
        this.maxFiles = options.maxFiles || 5;
        this.enableConsole = options.enableConsole !== false;
        this.enableFile = options.enableFile !== false;
        
        this.levels = {
            error: 0,
            warn: 1,
            info: 2,
            debug: 3
        };
        
        this.colors = {
            error: '\x1b[31m', // 红色
            warn: '\x1b[33m',  // 黄色
            info: '\x1b[36m',  // 青色
            debug: '\x1b[37m', // 白色
            reset: '\x1b[0m'
        };
        
        // 确保日志目录存在
        this.ensureLogDirectory();
    }

    /**
     * 确保日志目录存在
     */
    async ensureLogDirectory() {
        try {
            await fs.mkdir(this.logDir, { recursive: true });
        } catch (error) {
            console.error('创建日志目录失败:', error);
        }
    }

    /**
     * 记录错误日志
     */
    error(message, data = {}) {
        this.log('error', message, data);
    }

    /**
     * 记录警告日志
     */
    warn(message, data = {}) {
        this.log('warn', message, data);
    }

    /**
     * 记录信息日志
     */
    info(message, data = {}) {
        this.log('info', message, data);
    }

    /**
     * 记录调试日志
     */
    debug(message, data = {}) {
        this.log('debug', message, data);
    }

    /**
     * 核心日志方法
     */
    log(level, message, data = {}) {
        // 检查日志级别
        if (this.levels[level] > this.levels[this.logLevel]) {
            return;
        }

        const logEntry = this.createLogEntry(level, message, data);

        // 控制台输出
        if (this.enableConsole) {
            this.logToConsole(logEntry);
        }

        // 文件输出
        if (this.enableFile) {
            this.logToFile(logEntry);
        }
    }

    /**
     * 创建日志条目
     */
    createLogEntry(level, message, data) {
        return {
            timestamp: new Date().toISOString(),
            level: level.toUpperCase(),
            message,
            data: Object.keys(data).length > 0 ? data : undefined,
            pid: process.pid,
            hostname: require('os').hostname()
        };
    }

    /**
     * 控制台输出
     */
    logToConsole(logEntry) {
        const color = this.colors[logEntry.level.toLowerCase()] || this.colors.reset;
        const timestamp = logEntry.timestamp.substring(11, 19); // 只显示时间部分
        
        let output = `${color}[${timestamp}] ${logEntry.level}${this.colors.reset} ${logEntry.message}`;
        
        if (logEntry.data) {
            output += ` ${JSON.stringify(logEntry.data)}`;
        }
        
        console.log(output);
    }

    /**
     * 文件输出
     */
    async logToFile(logEntry) {
        try {
            const logFileName = this.getLogFileName();
            const logFilePath = path.join(this.logDir, logFileName);
            const logLine = JSON.stringify(logEntry) + '\n';
            
            // 检查文件大小，如果超过限制则轮转
            await this.rotateLogIfNeeded(logFilePath);
            
            // 追加日志
            await fs.appendFile(logFilePath, logLine, 'utf8');
            
        } catch (error) {
            console.error('写入日志文件失败:', error);
        }
    }

    /**
     * 获取日志文件名
     */
    getLogFileName() {
        const date = new Date().toISOString().substring(0, 10); // YYYY-MM-DD
        return `all-agent-${date}.log`;
    }

    /**
     * 日志轮转
     */
    async rotateLogIfNeeded(logFilePath) {
        try {
            const stats = await fs.stat(logFilePath);
            
            if (stats.size >= this.maxFileSize) {
                // 轮转日志文件
                const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
                const rotatedPath = logFilePath.replace('.log', `-${timestamp}.log`);
                
                await fs.rename(logFilePath, rotatedPath);
                
                // 清理旧日志文件
                await this.cleanupOldLogs();
            }
        } catch (error) {
            // 文件不存在或其他错误，忽略
        }
    }

    /**
     * 清理旧日志文件
     */
    async cleanupOldLogs() {
        try {
            const files = await fs.readdir(this.logDir);
            const logFiles = files
                .filter(file => file.startsWith('all-agent-') && file.endsWith('.log'))
                .map(file => ({
                    name: file,
                    path: path.join(this.logDir, file),
                    time: fs.stat(path.join(this.logDir, file)).then(stats => stats.mtime)
                }));

            // 等待所有文件状态
            for (const file of logFiles) {
                file.time = await file.time;
            }

            // 按时间排序，保留最新的文件
            logFiles.sort((a, b) => b.time - a.time);

            // 删除超出数量限制的文件
            if (logFiles.length > this.maxFiles) {
                const filesToDelete = logFiles.slice(this.maxFiles);
                
                for (const file of filesToDelete) {
                    await fs.unlink(file.path);
                    console.log(`删除旧日志文件: ${file.name}`);
                }
            }
        } catch (error) {
            console.error('清理旧日志文件失败:', error);
        }
    }

    /**
     * 设置日志级别
     */
    setLevel(level) {
        if (this.levels.hasOwnProperty(level)) {
            this.logLevel = level;
            this.info('日志级别已更改', { newLevel: level });
        } else {
            this.warn('无效的日志级别', { level, validLevels: Object.keys(this.levels) });
        }
    }

    /**
     * 获取当前日志级别
     */
    getLevel() {
        return this.logLevel;
    }

    /**
     * 创建子日志器
     */
    child(context = {}) {
        return new ChildLogger(this, context);
    }

    /**
     * 获取日志统计
     */
    async getLogStats() {
        try {
            const files = await fs.readdir(this.logDir);
            const logFiles = files.filter(file => file.startsWith('all-agent-') && file.endsWith('.log'));
            
            let totalSize = 0;
            const fileStats = [];
            
            for (const file of logFiles) {
                const filePath = path.join(this.logDir, file);
                const stats = await fs.stat(filePath);
                
                totalSize += stats.size;
                fileStats.push({
                    name: file,
                    size: stats.size,
                    modified: stats.mtime
                });
            }
            
            return {
                totalFiles: logFiles.length,
                totalSize,
                files: fileStats.sort((a, b) => b.modified - a.modified)
            };
        } catch (error) {
            this.error('获取日志统计失败', { error: error.message });
            return { totalFiles: 0, totalSize: 0, files: [] };
        }
    }

    /**
     * 读取日志文件
     */
    async readLogs(options = {}) {
        try {
            const { 
                limit = 100, 
                level = null, 
                startTime = null, 
                endTime = null,
                search = null 
            } = options;
            
            const logFileName = this.getLogFileName();
            const logFilePath = path.join(this.logDir, logFileName);
            
            const content = await fs.readFile(logFilePath, 'utf8');
            const lines = content.trim().split('\n').filter(line => line);
            
            let logs = lines.map(line => {
                try {
                    return JSON.parse(line);
                } catch (error) {
                    return null;
                }
            }).filter(log => log !== null);
            
            // 应用过滤器
            if (level) {
                logs = logs.filter(log => log.level.toLowerCase() === level.toLowerCase());
            }
            
            if (startTime) {
                logs = logs.filter(log => new Date(log.timestamp) >= new Date(startTime));
            }
            
            if (endTime) {
                logs = logs.filter(log => new Date(log.timestamp) <= new Date(endTime));
            }
            
            if (search) {
                logs = logs.filter(log => 
                    log.message.toLowerCase().includes(search.toLowerCase()) ||
                    (log.data && JSON.stringify(log.data).toLowerCase().includes(search.toLowerCase()))
                );
            }
            
            // 限制数量并按时间倒序
            return logs.slice(-limit).reverse();
            
        } catch (error) {
            this.error('读取日志失败', { error: error.message });
            return [];
        }
    }

    /**
     * 清空日志
     */
    async clearLogs() {
        try {
            const files = await fs.readdir(this.logDir);
            const logFiles = files.filter(file => file.startsWith('all-agent-') && file.endsWith('.log'));
            
            for (const file of logFiles) {
                await fs.unlink(path.join(this.logDir, file));
            }
            
            this.info('日志已清空', { deletedFiles: logFiles.length });
            return { success: true, deletedFiles: logFiles.length };
            
        } catch (error) {
            this.error('清空日志失败', { error: error.message });
            return { success: false, error: error.message };
        }
    }
}

/**
 * 子日志器类
 * 继承父日志器的配置，并添加上下文信息
 */
class ChildLogger {
    constructor(parent, context) {
        this.parent = parent;
        this.context = context;
    }

    error(message, data = {}) {
        this.parent.error(message, { ...this.context, ...data });
    }

    warn(message, data = {}) {
        this.parent.warn(message, { ...this.context, ...data });
    }

    info(message, data = {}) {
        this.parent.info(message, { ...this.context, ...data });
    }

    debug(message, data = {}) {
        this.parent.debug(message, { ...this.context, ...data });
    }

    child(additionalContext = {}) {
        return new ChildLogger(this.parent, { ...this.context, ...additionalContext });
    }
}

module.exports = Logger;
