const fs = require('fs').promises;
const path = require('path');
const Handlebars = require('handlebars');

/**
 * 代码生成器
 * 基于模板和参数生成代码文件
 */
class CodeGenerator {
    constructor() {
        this.templates = new Map();
        this.helpers = new Map();
        this.partials = new Map();
        this.outputHistory = [];
        
        // 注册内置助手函数
        this.registerBuiltinHelpers();
    }

    /**
     * 初始化代码生成器
     */
    async initialize() {
        try {
            // 加载模板
            await this.loadTemplates();
            
            // 注册 Handlebars 助手
            this.registerHandlebarsHelpers();
            
            console.log('✅ 代码生成器初始化完成');
            
        } catch (error) {
            console.error('❌ 代码生成器初始化失败:', error);
            throw error;
        }
    }

    /**
     * 加载模板
     */
    async loadTemplates() {
        const templatesDir = path.join(process.cwd(), '.all-agent/prompts');
        
        try {
            const files = await fs.readdir(templatesDir);
            
            for (const file of files) {
                if (file.endsWith('.json')) {
                    const templatePath = path.join(templatesDir, file);
                    const templateData = JSON.parse(await fs.readFile(templatePath, 'utf8'));
                    
                    this.templates.set(templateData.template_id, templateData);
                    console.log(`📄 加载模板: ${templateData.name}`);
                }
            }
            
        } catch (error) {
            console.warn('⚠️ 模板加载失败:', error.message);
        }
    }

    /**
     * 注册内置助手函数
     */
    registerBuiltinHelpers() {
        // 字符串处理助手
        this.helpers.set('camelCase', (str) => {
            return str.replace(/-([a-z])/g, (g) => g[1].toUpperCase());
        });
        
        this.helpers.set('pascalCase', (str) => {
            const camel = str.replace(/-([a-z])/g, (g) => g[1].toUpperCase());
            return camel.charAt(0).toUpperCase() + camel.slice(1);
        });
        
        this.helpers.set('kebabCase', (str) => {
            return str.replace(/([A-Z])/g, '-$1').toLowerCase().replace(/^-/, '');
        });
        
        this.helpers.set('snakeCase', (str) => {
            return str.replace(/([A-Z])/g, '_$1').toLowerCase().replace(/^_/, '');
        });
        
        // 日期助手
        this.helpers.set('currentDate', () => {
            return new Date().toISOString().split('T')[0];
        });
        
        this.helpers.set('currentDateTime', () => {
            return new Date().toISOString();
        });
        
        // 条件助手
        this.helpers.set('ifEquals', (a, b, options) => {
            return a === b ? options.fn(this) : options.inverse(this);
        });
        
        // 循环助手
        this.helpers.set('times', (n, options) => {
            let result = '';
            for (let i = 0; i < n; i++) {
                result += options.fn({ index: i, count: i + 1 });
            }
            return result;
        });
    }

    /**
     * 注册 Handlebars 助手
     */
    registerHandlebarsHelpers() {
        for (const [name, helper] of this.helpers.entries()) {
            Handlebars.registerHelper(name, helper);
        }
    }

    /**
     * 生成代码
     */
    async generate(templateId, parameters, context = {}) {
        try {
            // 获取模板
            const template = this.templates.get(templateId);
            if (!template) {
                throw new Error(`模板不存在: ${templateId}`);
            }

            // 验证参数
            this.validateParameters(template, parameters);

            // 准备模板数据
            const templateData = {
                ...parameters,
                ...context,
                _meta: {
                    templateId,
                    generatedAt: new Date().toISOString(),
                    version: template.version || '1.0.0'
                }
            };

            // 编译并执行模板
            const compiledTemplate = Handlebars.compile(template.prompt_template);
            const generatedPrompt = compiledTemplate(templateData);

            // 这里应该调用 LLM 服务生成实际代码
            // 目前返回模拟的代码生成结果
            const result = await this.generateCodeWithLLM(generatedPrompt, template, templateData);

            // 记录生成历史
            this.recordGeneration(templateId, parameters, result);

            return result;

        } catch (error) {
            console.error('代码生成失败:', error);
            throw error;
        }
    }

    /**
     * 验证参数
     */
    validateParameters(template, parameters) {
        if (!template.parameters) {
            return;
        }

        for (const [paramName, paramConfig] of Object.entries(template.parameters)) {
            const value = parameters[paramName];
            
            // 检查必需参数
            if (paramConfig.required && (value === undefined || value === null || value === '')) {
                throw new Error(`缺少必需参数: ${paramName}`);
            }
            
            // 检查参数类型
            if (value !== undefined && paramConfig.type) {
                if (!this.validateParameterType(value, paramConfig.type)) {
                    throw new Error(`参数类型错误: ${paramName} 应该是 ${paramConfig.type}`);
                }
            }
            
            // 检查选项值
            if (value !== undefined && paramConfig.options && !paramConfig.options.includes(value)) {
                throw new Error(`参数值无效: ${paramName} 必须是 ${paramConfig.options.join(', ')} 中的一个`);
            }
        }
    }

    /**
     * 验证参数类型
     */
    validateParameterType(value, expectedType) {
        switch (expectedType) {
            case 'string':
                return typeof value === 'string';
            case 'number':
                return typeof value === 'number';
            case 'boolean':
                return typeof value === 'boolean';
            case 'array':
                return Array.isArray(value);
            case 'object':
                return typeof value === 'object' && !Array.isArray(value);
            default:
                return true;
        }
    }

    /**
     * 使用 LLM 生成代码（模拟实现）
     */
    async generateCodeWithLLM(prompt, template, data) {
        // 模拟 LLM 调用延迟
        await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));

        // 根据模板类型生成不同的代码
        const result = {
            templateId: template.template_id,
            templateName: template.name,
            generatedAt: new Date().toISOString(),
            files: [],
            summary: '',
            instructions: []
        };

        switch (template.template_id) {
            case 'generate_ui_login':
                result.files = this.generateLoginPageFiles(data);
                result.summary = '成功生成现代化登录页面，包含 HTML、CSS 和 JavaScript 文件';
                result.instructions = [
                    '将生成的文件保存到项目目录',
                    '根据需要调整样式和功能',
                    '测试表单验证和响应式设计'
                ];
                break;
                
            case 'refactor_code':
                result.files = this.generateRefactoredCode(data);
                result.summary = '代码重构完成，提高了可读性和性能';
                result.instructions = [
                    '仔细审查重构后的代码',
                    '运行测试确保功能正常',
                    '更新相关文档'
                ];
                break;
                
            case 'debug_code_issues':
                result.files = this.generateDebugSolution(data);
                result.summary = '问题诊断完成，提供了修复方案';
                result.instructions = [
                    '按照修复步骤逐一执行',
                    '验证问题是否解决',
                    '添加预防措施'
                ];
                break;
                
            default:
                result.files = [{
                    name: 'generated_code.txt',
                    content: `// 基于模板 ${template.name} 生成的代码\n// 生成时间: ${new Date().toISOString()}\n\n${prompt}`,
                    language: 'text'
                }];
                result.summary = '代码生成完成';
        }

        return result;
    }

    /**
     * 生成登录页面文件
     */
    generateLoginPageFiles(data) {
        const { project_type = 'web应用', design_style = '现代简约', tech_stack = 'HTML/CSS/JavaScript' } = data;
        
        return [
            {
                name: 'login.html',
                content: this.generateLoginHTML(design_style),
                language: 'html',
                description: '登录页面 HTML 结构'
            },
            {
                name: 'login.css',
                content: this.generateLoginCSS(design_style),
                language: 'css',
                description: '登录页面样式文件'
            },
            {
                name: 'login.js',
                content: this.generateLoginJS(tech_stack),
                language: 'javascript',
                description: '登录页面交互逻辑'
            }
        ];
    }

    /**
     * 生成登录 HTML
     */
    generateLoginHTML(style) {
        return `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户登录</title>
    <link rel="stylesheet" href="login.css">
</head>
<body>
    <div class="login-container">
        <div class="login-card">
            <div class="login-header">
                <h1>欢迎回来</h1>
                <p>请登录您的账户</p>
            </div>
            
            <form class="login-form" id="loginForm">
                <div class="form-group">
                    <label for="email">邮箱地址</label>
                    <input type="email" id="email" name="email" required>
                </div>
                
                <div class="form-group">
                    <label for="password">密码</label>
                    <input type="password" id="password" name="password" required>
                </div>
                
                <div class="form-options">
                    <label class="checkbox">
                        <input type="checkbox" id="remember">
                        <span>记住我</span>
                    </label>
                    <a href="#" class="forgot-link">忘记密码？</a>
                </div>
                
                <button type="submit" class="login-btn">登录</button>
            </form>
            
            <div class="login-footer">
                <p>还没有账户？ <a href="#">立即注册</a></p>
            </div>
        </div>
    </div>
    
    <script src="login.js"></script>
</body>
</html>`;
    }

    /**
     * 生成登录 CSS
     */
    generateLoginCSS(style) {
        return `/* ${style}风格登录页面样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
}

.login-container {
    width: 100%;
    max-width: 400px;
    padding: 20px;
}

.login-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    padding: 40px;
    text-align: center;
}

.login-header h1 {
    color: #2d3748;
    margin-bottom: 8px;
    font-size: 28px;
}

.login-header p {
    color: #718096;
    margin-bottom: 32px;
}

.form-group {
    margin-bottom: 20px;
    text-align: left;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    color: #4a5568;
    font-weight: 500;
}

.form-group input {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 16px;
    transition: border-color 0.3s ease;
}

.form-group input:focus {
    outline: none;
    border-color: #667eea;
}

.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
}

.checkbox {
    display: flex;
    align-items: center;
    cursor: pointer;
}

.checkbox input {
    margin-right: 8px;
}

.forgot-link {
    color: #667eea;
    text-decoration: none;
    font-size: 14px;
}

.login-btn {
    width: 100%;
    padding: 12px;
    background: #667eea;
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.login-btn:hover {
    background: #5a6fd8;
}

.login-footer {
    margin-top: 24px;
    color: #718096;
}

.login-footer a {
    color: #667eea;
    text-decoration: none;
}

@media (max-width: 480px) {
    .login-card {
        padding: 24px;
    }
}`;
    }

    /**
     * 生成登录 JavaScript
     */
    generateLoginJS(techStack) {
        return `// 登录页面交互逻辑
document.addEventListener('DOMContentLoaded', function() {
    const loginForm = document.getElementById('loginForm');
    const emailInput = document.getElementById('email');
    const passwordInput = document.getElementById('password');
    const loginBtn = document.querySelector('.login-btn');

    // 表单验证
    function validateForm() {
        const email = emailInput.value.trim();
        const password = passwordInput.value.trim();
        
        if (!email || !password) {
            return false;
        }
        
        // 邮箱格式验证
        const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;
        if (!emailRegex.test(email)) {
            return false;
        }
        
        return true;
    }

    // 显示错误信息
    function showError(message) {
        // 移除现有错误信息
        const existingError = document.querySelector('.error-message');
        if (existingError) {
            existingError.remove();
        }
        
        // 创建错误信息元素
        const errorDiv = document.createElement('div');
        errorDiv.className = 'error-message';
        errorDiv.style.cssText = \`
            color: #e53e3e;
            background: #fed7d7;
            padding: 12px;
            border-radius: 6px;
            margin-bottom: 16px;
            font-size: 14px;
        \`;
        errorDiv.textContent = message;
        
        // 插入到表单前面
        loginForm.insertBefore(errorDiv, loginForm.firstChild);
    }

    // 显示加载状态
    function setLoading(loading) {
        loginBtn.disabled = loading;
        loginBtn.textContent = loading ? '登录中...' : '登录';
    }

    // 表单提交处理
    loginForm.addEventListener('submit', async function(e) {
        e.preventDefault();
        
        if (!validateForm()) {
            showError('请填写有效的邮箱地址和密码');
            return;
        }
        
        setLoading(true);
        
        try {
            // 模拟登录请求
            const formData = new FormData(loginForm);
            const loginData = {
                email: formData.get('email'),
                password: formData.get('password'),
                remember: formData.get('remember') === 'on'
            };
            
            // 这里应该调用实际的登录 API
            const response = await simulateLogin(loginData);
            
            if (response.success) {
                // 登录成功，重定向到主页
                window.location.href = '/dashboard';
            } else {
                showError(response.message || '登录失败，请检查邮箱和密码');
            }
            
        } catch (error) {
            showError('网络错误，请稍后重试');
        } finally {
            setLoading(false);
        }
    });

    // 模拟登录 API
    async function simulateLogin(data) {
        // 模拟网络延迟
        await new Promise(resolve => setTimeout(resolve, 1500));
        
        // 模拟登录验证
        if (data.email === '<EMAIL>' && data.password === 'password') {
            return { success: true };
        } else {
            return { success: false, message: '邮箱或密码错误' };
        }
    }

    // 实时验证
    emailInput.addEventListener('blur', function() {
        const email = this.value.trim();
        if (email && !/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(email)) {
            this.style.borderColor = '#e53e3e';
        } else {
            this.style.borderColor = '#e2e8f0';
        }
    });

    passwordInput.addEventListener('blur', function() {
        const password = this.value.trim();
        if (password && password.length < 6) {
            this.style.borderColor = '#e53e3e';
        } else {
            this.style.borderColor = '#e2e8f0';
        }
    });
});`;
    }

    /**
     * 生成重构代码
     */
    generateRefactoredCode(data) {
        return [{
            name: 'refactored_code.js',
            content: `// 重构后的代码
// 原始代码已优化，提高了可读性和性能
// 重构时间: ${new Date().toISOString()}

${data.source_code || '// 原始代码'}

// 重构建议已应用：
// 1. 提取公共函数
// 2. 优化变量命名
// 3. 添加错误处理
// 4. 改善代码结构`,
            language: data.programming_language || 'javascript',
            description: '重构后的代码文件'
        }];
    }

    /**
     * 生成调试解决方案
     */
    generateDebugSolution(data) {
        return [{
            name: 'debug_solution.md',
            content: `# 问题诊断和解决方案

## 问题描述
${data.error_message || '未知错误'}

## 根因分析
基于错误信息分析，问题可能由以下原因引起：
1. 变量未定义或为空
2. 类型转换错误
3. 异步操作处理不当

## 修复方案
\`\`\`${data.programming_language || 'javascript'}
// 修复后的代码
${data.problematic_code || '// 原始代码'}
\`\`\`

## 预防措施
1. 添加输入验证
2. 使用 TypeScript 进行类型检查
3. 完善错误处理机制

生成时间: ${new Date().toISOString()}`,
            language: 'markdown',
            description: '问题诊断和解决方案文档'
        }];
    }

    /**
     * 记录生成历史
     */
    recordGeneration(templateId, parameters, result) {
        this.outputHistory.push({
            templateId,
            parameters,
            result,
            timestamp: new Date().toISOString()
        });

        // 限制历史记录数量
        if (this.outputHistory.length > 100) {
            this.outputHistory.shift();
        }
    }

    /**
     * 获取生成历史
     */
    getHistory(limit = 10) {
        return this.outputHistory.slice(-limit).reverse();
    }

    /**
     * 获取可用模板
     */
    getAvailableTemplates() {
        const templates = [];
        
        for (const [id, template] of this.templates.entries()) {
            templates.push({
                id,
                name: template.name,
                description: template.description,
                category: template.category,
                parameters: template.parameters
            });
        }
        
        return templates;
    }

    /**
     * 获取模板详情
     */
    getTemplate(templateId) {
        return this.templates.get(templateId);
    }
}

module.exports = CodeGenerator;
