"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AutomationService = void 0;
const vscode = require("vscode");
/**
 * 自动化服务 - 提供自动修复和闭环操作能力
 */
class AutomationService {
    constructor(terminalService, browserService, contextService) {
        this.isMonitoring = false;
        this.terminalService = terminalService;
        this.browserService = browserService;
        this.contextService = contextService;
    }
    /**
     * 自动诊断和修复项目问题
     */
    async autoFixProject() {
        const fixes = [];
        const context = this.contextService.getContext();
        // 1. 检查并修复依赖问题
        await this.fixDependencyIssues(fixes, context);
        // 2. 检查并修复配置问题
        await this.fixConfigurationIssues(fixes, context);
        // 3. 检查并修复服务问题
        await this.fixServiceIssues(fixes, context);
        // 4. 检查并修复代码错误
        await this.fixCodeErrors(fixes, context);
        const successCount = fixes.filter(f => f.result === 'success').length;
        const totalIssues = fixes.length;
        return {
            success: successCount > 0,
            fixes,
            summary: `Fixed ${successCount}/${totalIssues} issues automatically`
        };
    }
    /**
     * 修复依赖问题
     */
    async fixDependencyIssues(fixes, context) {
        const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
        if (!workspaceFolder)
            return;
        try {
            // 检查 node_modules 是否存在
            const nodeModulesExists = await vscode.workspace.fs.stat(vscode.Uri.joinPath(workspaceFolder.uri, 'node_modules')).then(() => true, () => false);
            if (!nodeModulesExists && context.project?.type === 'node') {
                const result = await this.terminalService.executeCommand('npm install', {
                    cwd: workspaceFolder.uri.fsPath,
                    timeout: 120000
                });
                fixes.push({
                    issue: 'Missing node_modules directory',
                    action: 'Run npm install',
                    result: result.success ? 'success' : 'failed',
                    details: result.success ? 'Dependencies installed successfully' : result.error
                });
            }
            // 检查 package-lock.json 和 node_modules 同步
            const packageLockExists = await vscode.workspace.fs.stat(vscode.Uri.joinPath(workspaceFolder.uri, 'package-lock.json')).then(() => true, () => false);
            if (packageLockExists && nodeModulesExists) {
                const result = await this.terminalService.executeCommand('npm ci', {
                    cwd: workspaceFolder.uri.fsPath,
                    timeout: 120000
                });
                if (result.success) {
                    fixes.push({
                        issue: 'Dependencies out of sync',
                        action: 'Run npm ci',
                        result: 'success',
                        details: 'Dependencies synchronized'
                    });
                }
            }
        }
        catch (error) {
            fixes.push({
                issue: 'Dependency check failed',
                action: 'Manual intervention required',
                result: 'failed',
                details: error instanceof Error ? error.message : String(error)
            });
        }
    }
    /**
     * 修复配置问题
     */
    async fixConfigurationIssues(fixes, context) {
        const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
        if (!workspaceFolder)
            return;
        try {
            // 检查 TypeScript 配置
            if (context.project?.technologies?.includes('TypeScript')) {
                const tsconfigExists = await vscode.workspace.fs.stat(vscode.Uri.joinPath(workspaceFolder.uri, 'tsconfig.json')).then(() => true, () => false);
                if (!tsconfigExists) {
                    const result = await this.terminalService.executeCommand('npx tsc --init', {
                        cwd: workspaceFolder.uri.fsPath,
                        timeout: 30000
                    });
                    fixes.push({
                        issue: 'Missing TypeScript configuration',
                        action: 'Generate tsconfig.json',
                        result: result.success ? 'success' : 'failed',
                        details: result.success ? 'TypeScript config created' : result.error
                    });
                }
            }
            // 检查 ESLint 配置
            const eslintExists = await vscode.workspace.fs.stat(vscode.Uri.joinPath(workspaceFolder.uri, '.eslintrc.js')).then(() => true, () => false);
            if (!eslintExists && context.project?.type === 'node') {
                fixes.push({
                    issue: 'Missing ESLint configuration',
                    action: 'Suggest ESLint setup',
                    result: 'skipped',
                    details: 'Run: npx eslint --init'
                });
            }
        }
        catch (error) {
            fixes.push({
                issue: 'Configuration check failed',
                action: 'Manual intervention required',
                result: 'failed',
                details: error instanceof Error ? error.message : String(error)
            });
        }
    }
    /**
     * 修复服务问题
     */
    async fixServiceIssues(fixes, context) {
        try {
            const servers = await this.browserService.detectLocalServers();
            const unhealthyServers = servers.filter(s => s.health === 'unhealthy');
            for (const server of unhealthyServers) {
                fixes.push({
                    issue: `Unhealthy server on port ${server.port}`,
                    action: 'Monitor and suggest restart',
                    result: 'skipped',
                    details: `Server responding slowly (${server.responseTime}ms)`
                });
            }
            // 检查是否有开发服务器在运行
            const devServers = servers.filter(s => s.service?.includes('React') ||
                s.service?.includes('Next.js') ||
                s.service?.includes('Vite') ||
                s.service?.includes('Angular'));
            if (devServers.length === 0 && context.project?.type === 'node') {
                fixes.push({
                    issue: 'No development server running',
                    action: 'Suggest starting dev server',
                    result: 'skipped',
                    details: 'Run: npm start or npm run dev'
                });
            }
        }
        catch (error) {
            fixes.push({
                issue: 'Service check failed',
                action: 'Manual intervention required',
                result: 'failed',
                details: error instanceof Error ? error.message : String(error)
            });
        }
    }
    /**
     * 修复代码错误
     */
    async fixCodeErrors(fixes, context) {
        const errors = context.errors || [];
        for (const error of errors.slice(0, 5)) { // 处理前5个错误
            try {
                const fixSuggestion = this.suggestErrorFix(error);
                if (fixSuggestion.autoFixable) {
                    // 这里可以实现自动修复逻辑
                    fixes.push({
                        issue: `${error.file}:${error.line} - ${error.message}`,
                        action: fixSuggestion.action,
                        result: 'skipped',
                        details: 'Automatic code fixing not implemented yet'
                    });
                }
                else {
                    fixes.push({
                        issue: `${error.file}:${error.line} - ${error.message}`,
                        action: fixSuggestion.action,
                        result: 'skipped',
                        details: fixSuggestion.suggestion
                    });
                }
            }
            catch (error) {
                // 忽略单个错误的处理失败
            }
        }
    }
    /**
     * 建议错误修复方案
     */
    suggestErrorFix(error) {
        const message = error.message.toLowerCase();
        // TypeScript 错误
        if (message.includes('cannot find module')) {
            return {
                autoFixable: true,
                action: 'Install missing module',
                suggestion: 'Run npm install for the missing module'
            };
        }
        if (message.includes('property') && message.includes('does not exist')) {
            return {
                autoFixable: false,
                action: 'Fix property access',
                suggestion: 'Check property name spelling or add type definition'
            };
        }
        // Import/Export 错误
        if (message.includes('import') || message.includes('export')) {
            return {
                autoFixable: false,
                action: 'Fix import/export',
                suggestion: 'Check import path and exported members'
            };
        }
        // 语法错误
        if (message.includes('syntax') || message.includes('unexpected')) {
            return {
                autoFixable: false,
                action: 'Fix syntax error',
                suggestion: 'Check syntax around the error location'
            };
        }
        return {
            autoFixable: false,
            action: 'Manual review required',
            suggestion: 'Review the error and fix manually'
        };
    }
    /**
     * 启动项目监控
     */
    async startProjectMonitoring() {
        if (this.isMonitoring)
            return;
        this.isMonitoring = true;
        this.monitoringInterval = setInterval(async () => {
            try {
                await this.performHealthCheck();
            }
            catch (error) {
                console.error('Health check failed:', error);
            }
        }, 30000); // 每30秒检查一次
        vscode.window.showInformationMessage('🔍 All-Agent project monitoring started');
    }
    /**
     * 停止项目监控
     */
    stopProjectMonitoring() {
        if (!this.isMonitoring)
            return;
        this.isMonitoring = false;
        if (this.monitoringInterval) {
            clearInterval(this.monitoringInterval);
            this.monitoringInterval = undefined;
        }
        vscode.window.showInformationMessage('⏹️ All-Agent project monitoring stopped');
    }
    /**
     * 执行健康检查
     */
    async performHealthCheck() {
        const context = this.contextService.getContext();
        // 检查错误数量是否增加
        const errorCount = context.errors?.length || 0;
        if (errorCount > 5) {
            vscode.window.showWarningMessage(`⚠️ ${errorCount} errors detected. Would you like me to help fix them?`, 'Auto Fix', 'Ignore').then(selection => {
                if (selection === 'Auto Fix') {
                    this.autoFixProject();
                }
            });
        }
        // 检查服务器状态
        const servers = await this.browserService.detectLocalServers();
        const unhealthyServers = servers.filter(s => s.health === 'unhealthy');
        if (unhealthyServers.length > 0) {
            vscode.window.showWarningMessage(`⚠️ ${unhealthyServers.length} server(s) responding slowly`, 'Check Servers');
        }
    }
    /**
     * 清理资源
     */
    dispose() {
        this.stopProjectMonitoring();
    }
}
exports.AutomationService = AutomationService;
//# sourceMappingURL=automationService.js.map