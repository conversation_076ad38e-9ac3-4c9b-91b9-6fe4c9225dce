<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>All-Agent 聊天调度面板</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .chat-container {
            width: 90%;
            max-width: 1200px;
            height: 80vh;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            display: flex;
            overflow: hidden;
        }

        .sidebar {
            width: 300px;
            background: #f8f9fa;
            border-right: 1px solid #e9ecef;
            display: flex;
            flex-direction: column;
        }

        .sidebar-header {
            padding: 20px;
            border-bottom: 1px solid #e9ecef;
        }

        .sidebar-header h2 {
            color: #495057;
            font-size: 18px;
            margin-bottom: 10px;
        }

        .agent-list {
            flex: 1;
            overflow-y: auto;
            padding: 10px;
        }

        .agent-item {
            padding: 15px;
            margin-bottom: 10px;
            background: white;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .agent-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .agent-item.active {
            border-color: #667eea;
            background: #f0f4ff;
        }

        .agent-name {
            font-weight: 600;
            color: #495057;
            margin-bottom: 5px;
        }

        .agent-status {
            font-size: 12px;
            color: #6c757d;
        }

        .status-online {
            color: #28a745;
        }

        .status-busy {
            color: #ffc107;
        }

        .chat-main {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .chat-header {
            padding: 20px;
            border-bottom: 1px solid #e9ecef;
            background: #f8f9fa;
        }

        .chat-title {
            font-size: 20px;
            color: #495057;
            margin-bottom: 5px;
        }

        .chat-subtitle {
            color: #6c757d;
            font-size: 14px;
        }

        .chat-messages {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
            background: #ffffff;
        }

        .message {
            margin-bottom: 20px;
            display: flex;
            align-items: flex-start;
        }

        .message.user {
            flex-direction: row-reverse;
        }

        .message-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            margin: 0 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: white;
        }

        .user-avatar {
            background: #667eea;
        }

        .agent-avatar {
            background: #28a745;
        }

        .message-content {
            max-width: 70%;
            padding: 15px 20px;
            border-radius: 20px;
            position: relative;
        }

        .user .message-content {
            background: #667eea;
            color: white;
        }

        .agent .message-content {
            background: #f8f9fa;
            color: #495057;
        }

        .message-time {
            font-size: 12px;
            color: #6c757d;
            margin-top: 5px;
        }

        .chat-input {
            padding: 20px;
            border-top: 1px solid #e9ecef;
            background: #f8f9fa;
        }

        .input-container {
            display: flex;
            gap: 10px;
            align-items: flex-end;
        }

        .input-field {
            flex: 1;
            min-height: 50px;
            max-height: 120px;
            padding: 15px;
            border: 2px solid #e9ecef;
            border-radius: 25px;
            resize: none;
            font-family: inherit;
            font-size: 14px;
            outline: none;
            transition: border-color 0.3s ease;
        }

        .input-field:focus {
            border-color: #667eea;
        }

        .send-button {
            width: 50px;
            height: 50px;
            border: none;
            background: #667eea;
            color: white;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }

        .send-button:hover {
            background: #5a6fd8;
            transform: scale(1.05);
        }

        .send-button:disabled {
            background: #6c757d;
            cursor: not-allowed;
            transform: none;
        }

        .typing-indicator {
            display: none;
            padding: 10px 20px;
            color: #6c757d;
            font-style: italic;
        }

        .typing-dots {
            display: inline-block;
            animation: typing 1.5s infinite;
        }

        @keyframes typing {
            0%, 60%, 100% { opacity: 0; }
            30% { opacity: 1; }
        }

        @media (max-width: 768px) {
            .chat-container {
                width: 100%;
                height: 100vh;
                border-radius: 0;
            }
            
            .sidebar {
                width: 250px;
            }
            
            .message-content {
                max-width: 85%;
            }
        }
    </style>
</head>
<body>
    <div class="chat-container">
        <div class="sidebar">
            <div class="sidebar-header">
                <h2>🤖 可用 Agent</h2>
                <p style="font-size: 12px; color: #6c757d;">选择一个 Agent 开始对话</p>
            </div>
            <div class="agent-list">
                <div class="agent-item active" data-agent="analyzer">
                    <div class="agent-name">🔍 分析 Agent</div>
                    <div class="agent-status status-online">● 在线 - 专注项目分析</div>
                </div>
                <div class="agent-item" data-agent="planner">
                    <div class="agent-name">📋 规划 Agent</div>
                    <div class="agent-status status-online">● 在线 - 制定执行计划</div>
                </div>
                <div class="agent-item" data-agent="executor">
                    <div class="agent-name">⚡ 执行 Agent</div>
                    <div class="agent-status status-busy">● 忙碌 - 正在生成代码</div>
                </div>
            </div>
        </div>

        <div class="chat-main">
            <div class="chat-header">
                <div class="chat-title">与分析 Agent 对话</div>
                <div class="chat-subtitle">专业的项目分析和代码理解助手</div>
            </div>

            <div class="chat-messages" id="chatMessages">
                <div class="message agent">
                    <div class="message-avatar agent-avatar">🔍</div>
                    <div class="message-content">
                        <div>你好！我是分析 Agent，专门负责项目分析和代码理解。我可以帮你：</div>
                        <ul style="margin: 10px 0; padding-left: 20px;">
                            <li>分析项目结构和依赖关系</li>
                            <li>识别技术栈和架构模式</li>
                            <li>生成项目文档和模块说明</li>
                            <li>评估代码质量和复杂度</li>
                        </ul>
                        <div>请告诉我你想要分析什么？</div>
                        <div class="message-time">刚刚</div>
                    </div>
                </div>
            </div>

            <div class="typing-indicator" id="typingIndicator">
                Agent 正在思考<span class="typing-dots">...</span>
            </div>

            <div class="chat-input">
                <div class="input-container">
                    <textarea 
                        class="input-field" 
                        id="messageInput" 
                        placeholder="输入你的问题或需求..."
                        rows="1"
                    ></textarea>
                    <button class="send-button" id="sendButton">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"/>
                        </svg>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 基础聊天功能
        const chatMessages = document.getElementById('chatMessages');
        const messageInput = document.getElementById('messageInput');
        const sendButton = document.getElementById('sendButton');
        const typingIndicator = document.getElementById('typingIndicator');
        const agentItems = document.querySelectorAll('.agent-item');

        let currentAgent = 'analyzer';

        // Agent 切换
        agentItems.forEach(item => {
            item.addEventListener('click', () => {
                agentItems.forEach(i => i.classList.remove('active'));
                item.classList.add('active');
                currentAgent = item.dataset.agent;
                updateChatHeader();
            });
        });

        // 更新聊天头部
        function updateChatHeader() {
            const agentNames = {
                analyzer: '分析 Agent',
                planner: '规划 Agent', 
                executor: '执行 Agent'
            };
            const agentDescriptions = {
                analyzer: '专业的项目分析和代码理解助手',
                planner: '智能的项目规划和任务分解专家',
                executor: '高效的代码生成和任务执行助手'
            };
            
            document.querySelector('.chat-title').textContent = `与${agentNames[currentAgent]}对话`;
            document.querySelector('.chat-subtitle').textContent = agentDescriptions[currentAgent];
        }

        // 发送消息
        function sendMessage() {
            const message = messageInput.value.trim();
            if (!message) return;

            addMessage('user', message);
            messageInput.value = '';
            
            // 显示输入指示器
            showTypingIndicator();
            
            // 模拟 Agent 响应
            setTimeout(() => {
                hideTypingIndicator();
                addMessage('agent', generateAgentResponse(message));
            }, 1500 + Math.random() * 2000);
        }

        // 添加消息
        function addMessage(sender, content) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${sender}`;
            
            const avatar = sender === 'user' ? '👤' : getAgentEmoji();
            const avatarClass = sender === 'user' ? 'user-avatar' : 'agent-avatar';
            
            messageDiv.innerHTML = `
                <div class="message-avatar ${avatarClass}">${avatar}</div>
                <div class="message-content">
                    <div>${content}</div>
                    <div class="message-time">${new Date().toLocaleTimeString()}</div>
                </div>
            `;
            
            chatMessages.appendChild(messageDiv);
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        // 获取当前 Agent 表情符号
        function getAgentEmoji() {
            const emojis = {
                analyzer: '🔍',
                planner: '📋',
                executor: '⚡'
            };
            return emojis[currentAgent];
        }

        // 生成 Agent 响应（模拟）
        function generateAgentResponse(userMessage) {
            const responses = {
                analyzer: [
                    '我正在分析你的需求，让我为你生成详细的项目分析报告...',
                    '根据你的描述，我建议从以下几个方面进行分析：1) 技术栈评估 2) 架构设计 3) 依赖关系',
                    '我已经完成了初步分析，发现了一些有趣的模式和潜在的优化点。'
                ],
                planner: [
                    '基于你的需求，我制定了一个详细的执行计划，包含3个主要阶段...',
                    '让我为你分解这个任务：首先我们需要明确目标，然后制定时间线，最后分配资源。',
                    '我建议采用敏捷开发方法，将项目分为多个迭代周期。'
                ],
                executor: [
                    '我正在为你生成代码，请稍等片刻...',
                    '代码生成完成！我已经创建了基础结构，包含了最佳实践和错误处理。',
                    '任务执行中，当前进度：60%。正在处理依赖安装和配置文件生成。'
                ]
            };
            
            const agentResponses = responses[currentAgent];
            return agentResponses[Math.floor(Math.random() * agentResponses.length)];
        }

        // 显示/隐藏输入指示器
        function showTypingIndicator() {
            typingIndicator.style.display = 'block';
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        function hideTypingIndicator() {
            typingIndicator.style.display = 'none';
        }

        // 事件监听
        sendButton.addEventListener('click', sendMessage);
        messageInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendMessage();
            }
        });

        // 自动调整输入框高度
        messageInput.addEventListener('input', function() {
            this.style.height = 'auto';
            this.style.height = Math.min(this.scrollHeight, 120) + 'px';
        });
    </script>
</body>
</html>
