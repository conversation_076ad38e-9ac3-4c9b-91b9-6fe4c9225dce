import * as vscode from 'vscode';
import axios from 'axios';
import * as WebSocket from 'ws';

export class AllAgentClient {
    private baseUrl: string;
    private ws: WebSocket | null = null;
    private authToken: string | null = null;

    constructor() {
        const config = vscode.workspace.getConfiguration('all-agent');
        const port = config.get('serverPort', 3000);
        this.baseUrl = `http://localhost:${port}`;
    }

    async connect(): Promise<void> {
        try {
            // 首先尝试登录获取认证令牌
            await this.authenticate();

            // 建立 WebSocket 连接
            const wsUrl = this.baseUrl.replace('http', 'ws');
            this.ws = new WebSocket(wsUrl);

            return new Promise((resolve, reject) => {
                if (!this.ws) {
                    reject(new Error('WebSocket not initialized'));
                    return;
                }

                this.ws.on('open', () => {
                    console.log('Connected to All-Agent server');

                    // 发送认证信息
                    if (this.ws && this.authToken) {
                        this.ws.send(JSON.stringify({
                            type: 'auth',
                            token: this.authToken
                        }));
                    }

                    resolve();
                });

                this.ws.on('error', (error: Error) => {
                    console.error('WebSocket error:', error);
                    reject(error);
                });

                this.ws.on('close', () => {
                    console.log('Disconnected from All-Agent server');
                    this.ws = null;
                });
            });
        } catch (error) {
            throw new Error(`Failed to connect to All-Agent server: ${error}`);
        }
    }

    async disconnect(): Promise<void> {
        if (this.ws) {
            this.ws.close();
            this.ws = null;
        }
    }

    async sendMessage(message: string): Promise<string> {
        try {
            const response = await axios.post(`${this.baseUrl}/api/chat`, {
                message: message,
                agentType: 'analyzer' // 默认使用分析 Agent
            }, {
                headers: {
                    'Authorization': `Bearer ${this.authToken}`,
                    'Content-Type': 'application/json'
                }
            });

            return response.data.response || 'No response received';
        } catch (error) {
            throw new Error(`Failed to send message: ${error}`);
        }
    }

    async analyzeProject(projectPath: string): Promise<any> {
        try {
            const response = await axios.post(`${this.baseUrl}/api/analyze`, {
                path: projectPath,
                options: {
                    includeFiles: true,
                    includeDependencies: true,
                    includeStructure: true
                }
            }, {
                headers: {
                    'Authorization': `Bearer ${this.authToken}`,
                    'Content-Type': 'application/json'
                }
            });

            return response.data;
        } catch (error) {
            throw new Error(`Failed to analyze project: ${error}`);
        }
    }

    async generateCode(context: string, prompt: string): Promise<string> {
        try {
            const response = await axios.post(`${this.baseUrl}/api/generate`, {
                context: context,
                prompt: prompt,
                type: 'code'
            }, {
                headers: {
                    'Authorization': `Bearer ${this.authToken}`,
                    'Content-Type': 'application/json'
                }
            });

            return response.data.code || response.data.result || 'No code generated';
        } catch (error) {
            throw new Error(`Failed to generate code: ${error}`);
        }
    }

    async getAgents(): Promise<any[]> {
        try {
            const response = await axios.get(`${this.baseUrl}/api/agents`, {
                headers: {
                    'Authorization': `Bearer ${this.authToken}`
                }
            });

            return response.data.agents || [];
        } catch (error) {
            console.error('Failed to get agents:', error);
            return [];
        }
    }

    async getTasks(): Promise<any[]> {
        try {
            const response = await axios.get(`${this.baseUrl}/api/tasks`, {
                headers: {
                    'Authorization': `Bearer ${this.authToken}`
                }
            });

            return response.data.tasks || [];
        } catch (error) {
            console.error('Failed to get tasks:', error);
            return [];
        }
    }

    private async authenticate(): Promise<void> {
        try {
            // 尝试使用默认管理员账户登录
            const response = await axios.post(`${this.baseUrl}/auth/login`, {
                email: '<EMAIL>',
                password: 'Cjb65691910'
            });

            this.authToken = response.data.token;
        } catch (error) {
            // 如果管理员登录失败，尝试创建临时用户
            try {
                const registerResponse = await axios.post(`${this.baseUrl}/auth/register`, {
                    username: 'vscode-extension',
                    email: '<EMAIL>',
                    password: 'vscode123456'
                });

                if (registerResponse.data.token) {
                    this.authToken = registerResponse.data.token;
                } else {
                    // 注册成功后登录
                    const loginResponse = await axios.post(`${this.baseUrl}/auth/login`, {
                        username: 'vscode-extension',
                        password: 'vscode123456'
                    });
                    this.authToken = loginResponse.data.token;
                }
            } catch (authError) {
                throw new Error(`Authentication failed: ${authError}`);
            }
        }
    }

    isConnected(): boolean {
        return this.ws !== null && this.ws.readyState === WebSocket.OPEN;
    }

    updateConfiguration(): void {
        const config = vscode.workspace.getConfiguration('all-agent');
        const port = config.get('serverPort', 3000);
        this.baseUrl = `http://localhost:${port}`;

        // 如果已连接，重新连接以使用新配置
        if (this.isConnected()) {
            this.disconnect().then(() => {
                this.connect().catch(error => {
                    console.error('Failed to reconnect:', error);
                });
            });
        }
    }
}
