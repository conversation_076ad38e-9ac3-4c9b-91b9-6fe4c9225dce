"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AllAgentClient = void 0;
const vscode = require("vscode");
const axios_1 = require("axios");
const WebSocket = require("ws");
class AllAgentClient {
    constructor() {
        this.ws = null;
        this.authToken = null;
        const config = vscode.workspace.getConfiguration('all-agent');
        const port = config.get('serverPort', 3000);
        this.baseUrl = `http://localhost:${port}`;
    }
    async connect() {
        try {
            // 首先尝试登录获取认证令牌
            await this.authenticate();
            // 建立 WebSocket 连接
            const wsUrl = this.baseUrl.replace('http', 'ws');
            this.ws = new WebSocket(wsUrl);
            return new Promise((resolve, reject) => {
                if (!this.ws) {
                    reject(new Error('WebSocket not initialized'));
                    return;
                }
                this.ws.on('open', () => {
                    console.log('Connected to All-Agent server');
                    // 发送认证信息
                    if (this.ws && this.authToken) {
                        this.ws.send(JSON.stringify({
                            type: 'auth',
                            token: this.authToken
                        }));
                    }
                    resolve();
                });
                this.ws.on('error', (error) => {
                    console.error('WebSocket error:', error);
                    reject(error);
                });
                this.ws.on('close', () => {
                    console.log('Disconnected from All-Agent server');
                    this.ws = null;
                });
            });
        }
        catch (error) {
            throw new Error(`Failed to connect to All-Agent server: ${error}`);
        }
    }
    async disconnect() {
        if (this.ws) {
            this.ws.close();
            this.ws = null;
        }
    }
    async sendMessage(message) {
        try {
            const response = await axios_1.default.post(`${this.baseUrl}/api/chat`, {
                message: message,
                agentType: 'analyzer' // 默认使用分析 Agent
            }, {
                headers: {
                    'Authorization': `Bearer ${this.authToken}`,
                    'Content-Type': 'application/json'
                }
            });
            return response.data.response || 'No response received';
        }
        catch (error) {
            throw new Error(`Failed to send message: ${error}`);
        }
    }
    async analyzeProject(projectPath) {
        try {
            const response = await axios_1.default.post(`${this.baseUrl}/api/analyze`, {
                path: projectPath,
                options: {
                    includeFiles: true,
                    includeDependencies: true,
                    includeStructure: true
                }
            }, {
                headers: {
                    'Authorization': `Bearer ${this.authToken}`,
                    'Content-Type': 'application/json'
                }
            });
            return response.data;
        }
        catch (error) {
            throw new Error(`Failed to analyze project: ${error}`);
        }
    }
    async generateCode(context, prompt) {
        try {
            const response = await axios_1.default.post(`${this.baseUrl}/api/generate`, {
                context: context,
                prompt: prompt,
                type: 'code'
            }, {
                headers: {
                    'Authorization': `Bearer ${this.authToken}`,
                    'Content-Type': 'application/json'
                }
            });
            return response.data.code || response.data.result || 'No code generated';
        }
        catch (error) {
            throw new Error(`Failed to generate code: ${error}`);
        }
    }
    async getAgents() {
        try {
            const response = await axios_1.default.get(`${this.baseUrl}/api/agents`, {
                headers: {
                    'Authorization': `Bearer ${this.authToken}`
                }
            });
            return response.data.agents || [];
        }
        catch (error) {
            console.error('Failed to get agents:', error);
            return [];
        }
    }
    async getTasks() {
        try {
            const response = await axios_1.default.get(`${this.baseUrl}/api/tasks`, {
                headers: {
                    'Authorization': `Bearer ${this.authToken}`
                }
            });
            return response.data.tasks || [];
        }
        catch (error) {
            console.error('Failed to get tasks:', error);
            return [];
        }
    }
    async authenticate() {
        try {
            // 尝试使用默认管理员账户登录
            const response = await axios_1.default.post(`${this.baseUrl}/auth/login`, {
                email: '<EMAIL>',
                password: 'Cjb65691910'
            });
            this.authToken = response.data.token;
        }
        catch (error) {
            // 如果管理员登录失败，尝试创建临时用户
            try {
                const registerResponse = await axios_1.default.post(`${this.baseUrl}/auth/register`, {
                    username: 'vscode-extension',
                    email: '<EMAIL>',
                    password: 'vscode123456'
                });
                if (registerResponse.data.token) {
                    this.authToken = registerResponse.data.token;
                }
                else {
                    // 注册成功后登录
                    const loginResponse = await axios_1.default.post(`${this.baseUrl}/auth/login`, {
                        username: 'vscode-extension',
                        password: 'vscode123456'
                    });
                    this.authToken = loginResponse.data.token;
                }
            }
            catch (authError) {
                throw new Error(`Authentication failed: ${authError}`);
            }
        }
    }
    isConnected() {
        return this.ws !== null && this.ws.readyState === WebSocket.OPEN;
    }
    updateConfiguration() {
        const config = vscode.workspace.getConfiguration('all-agent');
        const port = config.get('serverPort', 3000);
        this.baseUrl = `http://localhost:${port}`;
        // 如果已连接，重新连接以使用新配置
        if (this.isConnected()) {
            this.disconnect().then(() => {
                this.connect().catch(error => {
                    console.error('Failed to reconnect:', error);
                });
            });
        }
    }
}
exports.AllAgentClient = AllAgentClient;
//# sourceMappingURL=client.js.map