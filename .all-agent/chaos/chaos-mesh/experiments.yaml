apiVersion: v1
kind: Namespace
metadata:
  name: chaos-mesh
  labels:
    name: chaos-mesh
---
# Pod 故障实验 - 随机杀死 Pod
apiVersion: chaos-mesh.org/v1alpha1
kind: PodChaos
metadata:
  name: all-agent-pod-kill
  namespace: chaos-mesh
  labels:
    app.kubernetes.io/name: all-agent
    app.kubernetes.io/component: chaos-pod
spec:
  action: pod-kill
  mode: one
  duration: "30s"
  selector:
    namespaces:
      - all-agent
    labelSelectors:
      "app.kubernetes.io/name": "all-agent"
  scheduler:
    cron: "@every 10m"
---
# Pod 故障实验 - Pod 失效
apiVersion: chaos-mesh.org/v1alpha1
kind: PodChaos
metadata:
  name: all-agent-pod-failure
  namespace: chaos-mesh
  labels:
    app.kubernetes.io/name: all-agent
    app.kubernetes.io/component: chaos-pod-failure
spec:
  action: pod-failure
  mode: fixed-percent
  value: "20"
  duration: "2m"
  selector:
    namespaces:
      - all-agent
    labelSelectors:
      "app.kubernetes.io/name": "all-agent"
  scheduler:
    cron: "@every 30m"
---
# 网络故障实验 - 网络延迟
apiVersion: chaos-mesh.org/v1alpha1
kind: NetworkChaos
metadata:
  name: all-agent-network-delay
  namespace: chaos-mesh
  labels:
    app.kubernetes.io/name: all-agent
    app.kubernetes.io/component: chaos-network
spec:
  action: delay
  mode: one
  selector:
    namespaces:
      - all-agent
    labelSelectors:
      "app.kubernetes.io/name": "all-agent"
  delay:
    latency: "100ms"
    correlation: "100"
    jitter: "0ms"
  duration: "5m"
  scheduler:
    cron: "@every 1h"
---
# 网络故障实验 - 网络分区
apiVersion: chaos-mesh.org/v1alpha1
kind: NetworkChaos
metadata:
  name: all-agent-network-partition
  namespace: chaos-mesh
  labels:
    app.kubernetes.io/name: all-agent
    app.kubernetes.io/component: chaos-network-partition
spec:
  action: partition
  mode: fixed-percent
  value: "50"
  selector:
    namespaces:
      - all-agent
    labelSelectors:
      "app.kubernetes.io/name": "all-agent"
  direction: both
  duration: "3m"
  scheduler:
    cron: "@every 2h"
---
# 网络故障实验 - 丢包
apiVersion: chaos-mesh.org/v1alpha1
kind: NetworkChaos
metadata:
  name: all-agent-network-loss
  namespace: chaos-mesh
  labels:
    app.kubernetes.io/name: all-agent
    app.kubernetes.io/component: chaos-network-loss
spec:
  action: loss
  mode: one
  selector:
    namespaces:
      - all-agent
    labelSelectors:
      "app.kubernetes.io/name": "all-agent"
  loss:
    loss: "10"
    correlation: "100"
  duration: "2m"
  scheduler:
    cron: "@every 45m"
---
# IO 故障实验 - 磁盘延迟
apiVersion: chaos-mesh.org/v1alpha1
kind: IOChaos
metadata:
  name: all-agent-io-delay
  namespace: chaos-mesh
  labels:
    app.kubernetes.io/name: all-agent
    app.kubernetes.io/component: chaos-io
spec:
  action: latency
  mode: one
  selector:
    namespaces:
      - all-agent
    labelSelectors:
      "app.kubernetes.io/name": "all-agent"
  volumePath: /app/data
  path: "/app/data/**/*"
  delay: "100ms"
  percent: 50
  duration: "3m"
  scheduler:
    cron: "@every 90m"
---
# 压力测试实验 - CPU 压力
apiVersion: chaos-mesh.org/v1alpha1
kind: StressChaos
metadata:
  name: all-agent-cpu-stress
  namespace: chaos-mesh
  labels:
    app.kubernetes.io/name: all-agent
    app.kubernetes.io/component: chaos-stress
spec:
  mode: one
  selector:
    namespaces:
      - all-agent
    labelSelectors:
      "app.kubernetes.io/name": "all-agent"
  stressors:
    cpu:
      workers: 2
      load: 80
  duration: "5m"
  scheduler:
    cron: "@every 3h"
---
# 压力测试实验 - 内存压力
apiVersion: chaos-mesh.org/v1alpha1
kind: StressChaos
metadata:
  name: all-agent-memory-stress
  namespace: chaos-mesh
  labels:
    app.kubernetes.io/name: all-agent
    app.kubernetes.io/component: chaos-memory-stress
spec:
  mode: one
  selector:
    namespaces:
      - all-agent
    labelSelectors:
      "app.kubernetes.io/name": "all-agent"
  stressors:
    memory:
      workers: 1
      size: "512MB"
  duration: "3m"
  scheduler:
    cron: "@every 4h"
---
# HTTP 故障实验 - HTTP 错误注入
apiVersion: chaos-mesh.org/v1alpha1
kind: HTTPChaos
metadata:
  name: all-agent-http-abort
  namespace: chaos-mesh
  labels:
    app.kubernetes.io/name: all-agent
    app.kubernetes.io/component: chaos-http
spec:
  mode: one
  selector:
    namespaces:
      - all-agent
    labelSelectors:
      "app.kubernetes.io/name": "all-agent"
  target: Request
  port: 3000
  method: POST
  path: "/api/*"
  abort: true
  duration: "2m"
  scheduler:
    cron: "@every 6h"
---
# HTTP 故障实验 - HTTP 延迟注入
apiVersion: chaos-mesh.org/v1alpha1
kind: HTTPChaos
metadata:
  name: all-agent-http-delay
  namespace: chaos-mesh
  labels:
    app.kubernetes.io/name: all-agent
    app.kubernetes.io/component: chaos-http-delay
spec:
  mode: fixed-percent
  value: "10"
  selector:
    namespaces:
      - all-agent
    labelSelectors:
      "app.kubernetes.io/name": "all-agent"
  target: Request
  port: 3000
  method: GET
  path: "/api/*"
  delay: "2s"
  duration: "5m"
  scheduler:
    cron: "@every 2h"
---
# 时间故障实验 - 时间偏移
apiVersion: chaos-mesh.org/v1alpha1
kind: TimeChaos
metadata:
  name: all-agent-time-skew
  namespace: chaos-mesh
  labels:
    app.kubernetes.io/name: all-agent
    app.kubernetes.io/component: chaos-time
spec:
  mode: one
  selector:
    namespaces:
      - all-agent
    labelSelectors:
      "app.kubernetes.io/name": "all-agent"
  timeOffset: "-1h"
  clockIds:
    - CLOCK_REALTIME
  duration: "10m"
  scheduler:
    cron: "@every 12h"
---
# 工作流实验 - 复合故障场景
apiVersion: chaos-mesh.org/v1alpha1
kind: Workflow
metadata:
  name: all-agent-disaster-recovery
  namespace: chaos-mesh
  labels:
    app.kubernetes.io/name: all-agent
    app.kubernetes.io/component: chaos-workflow
spec:
  entry: disaster-scenario
  templates:
  - name: disaster-scenario
    templateType: Serial
    deadline: "20m"
    children:
    - network-partition
    - pod-failure
    - recovery-check
  - name: network-partition
    templateType: NetworkChaos
    deadline: "5m"
    networkChaos:
      action: partition
      mode: fixed-percent
      value: "30"
      selector:
        namespaces:
          - all-agent
        labelSelectors:
          "app.kubernetes.io/name": "all-agent"
      direction: both
      duration: "3m"
  - name: pod-failure
    templateType: PodChaos
    deadline: "5m"
    podChaos:
      action: pod-kill
      mode: fixed-percent
      value: "20"
      selector:
        namespaces:
          - all-agent
        labelSelectors:
          "app.kubernetes.io/name": "all-agent"
  - name: recovery-check
    templateType: Task
    deadline: "10m"
    task:
      container:
        name: recovery-checker
        image: curlimages/curl:latest
        command:
        - /bin/sh
        - -c
        - |
          echo "等待系统恢复..."
          sleep 30
          
          # 检查服务健康状态
          for i in {1..10}; do
            if curl -f http://all-agent-app.all-agent.svc.cluster.local:3000/health; then
              echo "系统已恢复正常"
              exit 0
            fi
            echo "等待恢复... ($i/10)"
            sleep 30
          done
          
          echo "系统恢复失败"
          exit 1
---
# 混沌实验调度器
apiVersion: chaos-mesh.org/v1alpha1
kind: Schedule
metadata:
  name: all-agent-chaos-schedule
  namespace: chaos-mesh
  labels:
    app.kubernetes.io/name: all-agent
    app.kubernetes.io/component: chaos-schedule
spec:
  schedule: "@every 24h"
  type: Workflow
  workflowSpec:
    entry: daily-chaos
    templates:
    - name: daily-chaos
      templateType: Parallel
      deadline: "1h"
      children:
      - light-chaos
      - medium-chaos
    - name: light-chaos
      templateType: Serial
      deadline: "30m"
      children:
      - network-delay
      - cpu-stress
    - name: medium-chaos
      templateType: Serial
      deadline: "30m"
      children:
      - pod-kill
      - memory-stress
    - name: network-delay
      templateType: NetworkChaos
      deadline: "10m"
      networkChaos:
        action: delay
        mode: one
        selector:
          namespaces:
            - all-agent
          labelSelectors:
            "app.kubernetes.io/name": "all-agent"
        delay:
          latency: "50ms"
          correlation: "100"
        duration: "5m"
    - name: cpu-stress
      templateType: StressChaos
      deadline: "10m"
      stressChaos:
        mode: one
        selector:
          namespaces:
            - all-agent
          labelSelectors:
            "app.kubernetes.io/name": "all-agent"
        stressors:
          cpu:
            workers: 1
            load: 50
        duration: "5m"
    - name: pod-kill
      templateType: PodChaos
      deadline: "5m"
      podChaos:
        action: pod-kill
        mode: one
        selector:
          namespaces:
            - all-agent
          labelSelectors:
            "app.kubernetes.io/name": "all-agent"
    - name: memory-stress
      templateType: StressChaos
      deadline: "10m"
      stressChaos:
        mode: one
        selector:
          namespaces:
            - all-agent
          labelSelectors:
            "app.kubernetes.io/name": "all-agent"
        stressors:
          memory:
            workers: 1
            size: "256MB"
        duration: "5m"
