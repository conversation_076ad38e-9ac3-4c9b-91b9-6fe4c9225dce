{"version": "1.0.0", "description": "All-Agent 系统 Agent 配置文件", "agents": {"analyzer": {"name": "分析 Agent", "description": "专门负责项目分析、代码理解和结构识别", "capabilities": ["project_structure_analysis", "code_dependency_mapping", "technology_stack_detection", "module_relationship_analysis", "documentation_generation"], "prompt_templates": ["analyze_project_structure", "generate_dependency_graph", "identify_tech_stack"], "priority": "high", "execution_mode": "synchronous", "max_concurrent_tasks": 3, "specializations": {"languages": ["javascript", "python", "java", "typescript", "go"], "frameworks": ["react", "vue", "angular", "django", "spring", "express"], "domains": ["web", "mobile", "api", "database", "devops"]}}, "planner": {"name": "规划 Agent", "description": "负责项目规划、任务分解和执行路线制定", "capabilities": ["project_planning", "task_decomposition", "milestone_definition", "resource_estimation", "risk_assessment", "timeline_creation"], "prompt_templates": ["create_project_plan", "decompose_user_requirements", "generate_development_roadmap"], "priority": "high", "execution_mode": "synchronous", "max_concurrent_tasks": 2, "specializations": {"methodologies": ["agile", "waterfall", "lean", "design_thinking"], "project_types": ["web_app", "mobile_app", "api", "data_pipeline", "automation"], "complexity_levels": ["simple", "medium", "complex", "enterprise"]}}, "executor": {"name": "执行 Agent", "description": "负责代码生成、文件操作和具体任务执行", "capabilities": ["code_generation", "file_manipulation", "dependency_management", "testing_automation", "deployment_assistance", "debugging_support"], "prompt_templates": ["generate_component_code", "create_api_endpoint", "setup_project_structure", "fix_code_issues"], "priority": "medium", "execution_mode": "asynchronous", "max_concurrent_tasks": 5, "specializations": {"code_types": ["frontend", "backend", "database", "config", "test"], "operations": ["create", "update", "delete", "refactor", "optimize"], "tools": ["npm", "pip", "maven", "docker", "git"]}}}, "agent_workflows": {"project_initialization": {"description": "新项目初始化工作流", "steps": [{"agent": "analyzer", "action": "analyze_requirements", "input": "user_requirements"}, {"agent": "planner", "action": "create_project_plan", "input": "analysis_results"}, {"agent": "executor", "action": "setup_project_structure", "input": "project_plan"}]}, "feature_development": {"description": "功能开发工作流", "steps": [{"agent": "analyzer", "action": "analyze_feature_requirements", "input": "feature_description"}, {"agent": "planner", "action": "plan_implementation", "input": "feature_analysis"}, {"agent": "executor", "action": "implement_feature", "input": "implementation_plan"}]}, "debugging_workflow": {"description": "问题诊断和修复工作流", "steps": [{"agent": "analyzer", "action": "diagnose_issue", "input": "error_description"}, {"agent": "planner", "action": "plan_fix_strategy", "input": "diagnosis_results"}, {"agent": "executor", "action": "apply_fixes", "input": "fix_strategy"}]}}, "global_settings": {"default_timeout": 300, "max_retries": 3, "log_level": "info", "enable_parallel_execution": true, "auto_save_progress": true, "backup_before_changes": true}, "integration_settings": {"supported_llm_providers": ["openai", "anthropic", "google", "local"], "default_provider": "anthropic", "fallback_providers": ["openai", "google"], "api_rate_limits": {"requests_per_minute": 60, "tokens_per_hour": 100000}}}