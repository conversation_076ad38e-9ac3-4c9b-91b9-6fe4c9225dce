#!/usr/bin/env node

/**
 * All-Agent 简化数据库初始化脚本
 * 使用 better-sqlite3 创建基础数据库
 */

const Database = require('better-sqlite3');
const path = require('path');
const fs = require('fs');

// 配置
const DB_PATH = process.env.DATABASE_PATH || './data/all-agent.db';
const DATA_DIR = path.dirname(DB_PATH);

console.log('🚀 开始初始化 All-Agent 数据库...');

// 确保数据目录存在
if (!fs.existsSync(DATA_DIR)) {
    fs.mkdirSync(DATA_DIR, { recursive: true });
    console.log(`✅ 创建数据目录: ${DATA_DIR}`);
}

// 连接数据库
let db;
try {
    db = new Database(DB_PATH);
    console.log(`🔗 连接到数据库: ${DB_PATH}`);
} catch (err) {
    console.error('❌ 数据库连接失败:', err.message);
    process.exit(1);
}

// 基础表结构
const tables = [
    // 用户表
    `CREATE TABLE IF NOT EXISTS users (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        username VARCHAR(50) UNIQUE NOT NULL,
        email VARCHAR(100) UNIQUE NOT NULL,
        password_hash VARCHAR(255) NOT NULL,
        role VARCHAR(20) DEFAULT 'user',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        is_active BOOLEAN DEFAULT 1
    )`,

    // 项目表
    `CREATE TABLE IF NOT EXISTS projects (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name VARCHAR(100) NOT NULL,
        description TEXT,
        path VARCHAR(500) NOT NULL,
        user_id INTEGER NOT NULL,
        status VARCHAR(20) DEFAULT 'active',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users (id)
    )`,

    // 任务表
    `CREATE TABLE IF NOT EXISTS tasks (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        task_id VARCHAR(100) UNIQUE NOT NULL,
        user_id INTEGER NOT NULL,
        project_id INTEGER,
        agent_type VARCHAR(50) NOT NULL,
        action VARCHAR(100) NOT NULL,
        input_data TEXT,
        output_data TEXT,
        status VARCHAR(20) DEFAULT 'pending',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users (id),
        FOREIGN KEY (project_id) REFERENCES projects (id)
    )`,

    // 聊天消息表
    `CREATE TABLE IF NOT EXISTS chat_messages (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER NOT NULL,
        agent_type VARCHAR(50) NOT NULL,
        message_type VARCHAR(20) NOT NULL,
        content TEXT NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users (id)
    )`,

    // 系统配置表
    `CREATE TABLE IF NOT EXISTS system_config (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        config_key VARCHAR(100) UNIQUE NOT NULL,
        config_value TEXT,
        description TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )`
];

// 创建表
console.log('📋 开始创建数据库表...');

try {
    for (let i = 0; i < tables.length; i++) {
        db.exec(tables[i]);
        console.log(`✅ 创建表 ${i + 1}/${tables.length}`);
    }

    // 创建索引
    const indexes = [
        'CREATE INDEX IF NOT EXISTS idx_users_username ON users(username)',
        'CREATE INDEX IF NOT EXISTS idx_users_email ON users(email)',
        'CREATE INDEX IF NOT EXISTS idx_projects_user_id ON projects(user_id)',
        'CREATE INDEX IF NOT EXISTS idx_tasks_user_id ON tasks(user_id)',
        'CREATE INDEX IF NOT EXISTS idx_tasks_status ON tasks(status)',
        'CREATE INDEX IF NOT EXISTS idx_chat_messages_user_id ON chat_messages(user_id)'
    ];

    console.log('🔍 创建数据库索引...');
    for (const index of indexes) {
        db.exec(index);
    }
    console.log('✅ 索引创建完成');

    // 插入初始配置
    console.log('📝 插入初始数据...');
    const insertConfig = db.prepare(`
        INSERT OR IGNORE INTO system_config (config_key, config_value, description) 
        VALUES (?, ?, ?)
    `);

    const configs = [
        ['system_version', '1.0.0', '系统版本号'],
        ['max_concurrent_tasks', '10', '最大并发任务数'],
        ['default_agent_timeout', '300000', '默认 Agent 超时时间（毫秒）'],
        ['enable_audit_logging', 'true', '是否启用审计日志']
    ];

    for (const config of configs) {
        insertConfig.run(config);
    }
    console.log('✅ 初始数据插入完成');

    // 验证数据库
    console.log('🔍 验证数据库结构...');
    const tableCount = db.prepare("SELECT COUNT(*) as count FROM sqlite_master WHERE type='table'").get();
    console.log(`📊 数据库统计: ${tableCount.count} 个表`);

    console.log('🎉 数据库初始化完成！');
    console.log(`📍 数据库位置: ${path.resolve(DB_PATH)}`);

} catch (error) {
    console.error('❌ 数据库初始化失败:', error.message);
    process.exit(1);
} finally {
    if (db) {
        db.close();
        console.log('🔒 数据库连接已关闭');
    }
}
