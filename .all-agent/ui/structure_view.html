<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>All-Agent 项目结构可视化</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            min-height: 100vh;
        }

        .container {
            display: flex;
            height: 100vh;
        }

        .sidebar {
            width: 300px;
            background: white;
            border-right: 1px solid #e2e8f0;
            display: flex;
            flex-direction: column;
        }

        .sidebar-header {
            padding: 20px;
            border-bottom: 1px solid #e2e8f0;
        }

        .sidebar-title {
            font-size: 18px;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 10px;
        }

        .project-info {
            font-size: 14px;
            color: #718096;
        }

        .tree-container {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
        }

        .tree-item {
            display: flex;
            align-items: center;
            padding: 8px 0;
            cursor: pointer;
            border-radius: 6px;
            transition: background-color 0.2s;
        }

        .tree-item:hover {
            background: #f7fafc;
        }

        .tree-item.selected {
            background: #ebf8ff;
            color: #3182ce;
        }

        .tree-indent {
            width: 20px;
            display: inline-block;
        }

        .tree-icon {
            width: 20px;
            height: 20px;
            margin-right: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
        }

        .tree-label {
            flex: 1;
            font-size: 14px;
        }

        .tree-meta {
            font-size: 12px;
            color: #a0aec0;
        }

        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .toolbar {
            background: white;
            padding: 15px 20px;
            border-bottom: 1px solid #e2e8f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .toolbar-left {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .toolbar-title {
            font-size: 20px;
            font-weight: 600;
            color: #2d3748;
        }

        .view-controls {
            display: flex;
            gap: 5px;
        }

        .view-btn {
            padding: 8px 12px;
            border: 1px solid #e2e8f0;
            background: white;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s;
        }

        .view-btn:hover {
            background: #f7fafc;
        }

        .view-btn.active {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }

        .visualization-area {
            flex: 1;
            position: relative;
            overflow: hidden;
        }

        .graph-container {
            width: 100%;
            height: 100%;
            background: white;
            position: relative;
        }

        .node {
            position: absolute;
            background: white;
            border: 2px solid #e2e8f0;
            border-radius: 10px;
            padding: 15px;
            min-width: 120px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .node:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 16px rgba(0,0,0,0.15);
        }

        .node.folder {
            border-color: #fbbf24;
            background: #fffbeb;
        }

        .node.file {
            border-color: #60a5fa;
            background: #eff6ff;
        }

        .node.component {
            border-color: #34d399;
            background: #ecfdf5;
        }

        .node.config {
            border-color: #a78bfa;
            background: #f3e8ff;
        }

        .node-icon {
            font-size: 24px;
            margin-bottom: 8px;
        }

        .node-label {
            font-size: 12px;
            font-weight: 500;
            color: #374151;
        }

        .connection {
            position: absolute;
            pointer-events: none;
        }

        .connection-line {
            stroke: #d1d5db;
            stroke-width: 2;
            fill: none;
        }

        .stats-panel {
            position: absolute;
            top: 20px;
            right: 20px;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 4px 16px rgba(0,0,0,0.1);
            min-width: 200px;
        }

        .stats-title {
            font-size: 16px;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 15px;
        }

        .stat-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            font-size: 14px;
        }

        .stat-label {
            color: #718096;
        }

        .stat-value {
            font-weight: 500;
            color: #2d3748;
        }

        .minimap {
            position: absolute;
            bottom: 20px;
            right: 20px;
            width: 200px;
            height: 150px;
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            overflow: hidden;
        }

        .minimap-content {
            width: 100%;
            height: 100%;
            background: #f8fafc;
            position: relative;
        }

        .minimap-viewport {
            position: absolute;
            border: 2px solid #667eea;
            background: rgba(102, 126, 234, 0.1);
            cursor: move;
        }

        @media (max-width: 768px) {
            .container {
                flex-direction: column;
            }
            
            .sidebar {
                width: 100%;
                height: 300px;
            }
            
            .stats-panel {
                position: relative;
                top: 0;
                right: 0;
                margin: 20px;
            }
            
            .minimap {
                display: none;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="sidebar">
            <div class="sidebar-header">
                <div class="sidebar-title">📁 项目结构</div>
                <div class="project-info">All-Agent 项目 • 23 个文件</div>
            </div>
            <div class="tree-container" id="treeContainer">
                <div class="tree-item selected" data-path="/">
                    <span class="tree-icon">📁</span>
                    <span class="tree-label">all-agent</span>
                    <span class="tree-meta">根目录</span>
                </div>
                <div class="tree-item" data-path="/.all-agent">
                    <span class="tree-indent"></span>
                    <span class="tree-icon">📁</span>
                    <span class="tree-label">.all-agent</span>
                    <span class="tree-meta">配置</span>
                </div>
                <div class="tree-item" data-path="/.all-agent/ui">
                    <span class="tree-indent"></span>
                    <span class="tree-indent"></span>
                    <span class="tree-icon">📁</span>
                    <span class="tree-label">ui</span>
                    <span class="tree-meta">界面</span>
                </div>
                <div class="tree-item" data-path="/.all-agent/ui/chat_panel.html">
                    <span class="tree-indent"></span>
                    <span class="tree-indent"></span>
                    <span class="tree-indent"></span>
                    <span class="tree-icon">🌐</span>
                    <span class="tree-label">chat_panel.html</span>
                    <span class="tree-meta">15KB</span>
                </div>
                <div class="tree-item" data-path="/.all-agent/ui/agent_trace.html">
                    <span class="tree-indent"></span>
                    <span class="tree-indent"></span>
                    <span class="tree-indent"></span>
                    <span class="tree-icon">🌐</span>
                    <span class="tree-label">agent_trace.html</span>
                    <span class="tree-meta">12KB</span>
                </div>
                <div class="tree-item" data-path="/.all-agent/prompts">
                    <span class="tree-indent"></span>
                    <span class="tree-indent"></span>
                    <span class="tree-icon">📁</span>
                    <span class="tree-label">prompts</span>
                    <span class="tree-meta">模板</span>
                </div>
                <div class="tree-item" data-path="/.all-agent/prompts/generate_ui_login.json">
                    <span class="tree-indent"></span>
                    <span class="tree-indent"></span>
                    <span class="tree-indent"></span>
                    <span class="tree-icon">⚙️</span>
                    <span class="tree-label">generate_ui_login.json</span>
                    <span class="tree-meta">8KB</span>
                </div>
                <div class="tree-item" data-path="/.all-agent/modules">
                    <span class="tree-indent"></span>
                    <span class="tree-indent"></span>
                    <span class="tree-icon">📁</span>
                    <span class="tree-label">modules</span>
                    <span class="tree-meta">文档</span>
                </div>
                <div class="tree-item" data-path="/.all-agent/agents.json">
                    <span class="tree-indent"></span>
                    <span class="tree-indent"></span>
                    <span class="tree-icon">⚙️</span>
                    <span class="tree-label">agents.json</span>
                    <span class="tree-meta">5KB</span>
                </div>
            </div>
        </div>

        <div class="main-content">
            <div class="toolbar">
                <div class="toolbar-left">
                    <div class="toolbar-title">🗺️ 项目可视化</div>
                </div>
                <div class="view-controls">
                    <button class="view-btn active" data-view="graph">图形视图</button>
                    <button class="view-btn" data-view="tree">树形视图</button>
                    <button class="view-btn" data-view="dependency">依赖关系</button>
                </div>
            </div>

            <div class="visualization-area">
                <div class="graph-container" id="graphContainer">
                    <!-- 节点将通过 JavaScript 动态生成 -->
                </div>

                <div class="stats-panel">
                    <div class="stats-title">📊 项目统计</div>
                    <div class="stat-item">
                        <span class="stat-label">总文件数</span>
                        <span class="stat-value">23</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">目录数</span>
                        <span class="stat-value">8</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">代码文件</span>
                        <span class="stat-value">12</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">配置文件</span>
                        <span class="stat-value">6</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">文档文件</span>
                        <span class="stat-value">5</span>
                    </div>
                </div>

                <div class="minimap">
                    <div class="minimap-content">
                        <div class="minimap-viewport" style="width: 40%; height: 30%; top: 20%; left: 30%;"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 项目结构数据
        const projectData = {
            nodes: [
                { id: 'root', label: 'all-agent', type: 'folder', x: 400, y: 100 },
                { id: 'all-agent', label: '.all-agent', type: 'folder', x: 400, y: 200 },
                { id: 'ui', label: 'ui', type: 'folder', x: 200, y: 300 },
                { id: 'prompts', label: 'prompts', type: 'folder', x: 400, y: 300 },
                { id: 'modules', label: 'modules', type: 'folder', x: 600, y: 300 },
                { id: 'chat', label: 'chat_panel.html', type: 'file', x: 100, y: 400 },
                { id: 'trace', label: 'agent_trace.html', type: 'file', x: 300, y: 400 },
                { id: 'login', label: 'generate_ui_login.json', type: 'config', x: 400, y: 400 },
                { id: 'agents', label: 'agents.json', type: 'config', x: 500, y: 200 }
            ],
            connections: [
                { from: 'root', to: 'all-agent' },
                { from: 'all-agent', to: 'ui' },
                { from: 'all-agent', to: 'prompts' },
                { from: 'all-agent', to: 'modules' },
                { from: 'all-agent', to: 'agents' },
                { from: 'ui', to: 'chat' },
                { from: 'ui', to: 'trace' },
                { from: 'prompts', to: 'login' }
            ]
        };

        // 渲染图形视图
        function renderGraphView() {
            const container = document.getElementById('graphContainer');
            container.innerHTML = '';

            // 创建 SVG 用于连接线
            const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
            svg.style.position = 'absolute';
            svg.style.top = '0';
            svg.style.left = '0';
            svg.style.width = '100%';
            svg.style.height = '100%';
            svg.style.pointerEvents = 'none';
            container.appendChild(svg);

            // 渲染连接线
            projectData.connections.forEach(conn => {
                const fromNode = projectData.nodes.find(n => n.id === conn.from);
                const toNode = projectData.nodes.find(n => n.id === conn.to);
                
                if (fromNode && toNode) {
                    const line = document.createElementNS('http://www.w3.org/2000/svg', 'path');
                    const d = `M ${fromNode.x + 60} ${fromNode.y + 40} Q ${(fromNode.x + toNode.x) / 2} ${(fromNode.y + toNode.y) / 2 - 50} ${toNode.x + 60} ${toNode.y + 40}`;
                    line.setAttribute('d', d);
                    line.setAttribute('class', 'connection-line');
                    line.style.stroke = '#d1d5db';
                    line.style.strokeWidth = '2';
                    line.style.fill = 'none';
                    svg.appendChild(line);
                }
            });

            // 渲染节点
            projectData.nodes.forEach(node => {
                const nodeElement = document.createElement('div');
                nodeElement.className = `node ${node.type}`;
                nodeElement.style.left = node.x + 'px';
                nodeElement.style.top = node.y + 'px';

                const icon = getNodeIcon(node.type);
                nodeElement.innerHTML = `
                    <div class="node-icon">${icon}</div>
                    <div class="node-label">${node.label}</div>
                `;

                nodeElement.addEventListener('click', () => {
                    selectNode(node.id);
                });

                container.appendChild(nodeElement);
            });
        }

        // 获取节点图标
        function getNodeIcon(type) {
            const icons = {
                folder: '📁',
                file: '📄',
                component: '🧩',
                config: '⚙️'
            };
            return icons[type] || '📄';
        }

        // 选择节点
        function selectNode(nodeId) {
            // 更新侧边栏选择
            document.querySelectorAll('.tree-item').forEach(item => {
                item.classList.remove('selected');
            });

            // 高亮对应的树项
            const treeItem = document.querySelector(`[data-path*="${nodeId}"]`);
            if (treeItem) {
                treeItem.classList.add('selected');
            }

            // 高亮图形节点
            document.querySelectorAll('.node').forEach(node => {
                node.style.transform = '';
                node.style.boxShadow = '';
            });

            const selectedNode = document.querySelector(`.node:nth-child(${projectData.nodes.findIndex(n => n.id === nodeId) + 2})`);
            if (selectedNode) {
                selectedNode.style.transform = 'scale(1.1)';
                selectedNode.style.boxShadow = '0 8px 24px rgba(102, 126, 234, 0.3)';
            }
        }

        // 视图切换
        document.querySelectorAll('.view-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                document.querySelectorAll('.view-btn').forEach(b => b.classList.remove('active'));
                btn.classList.add('active');

                const view = btn.dataset.view;
                switch (view) {
                    case 'graph':
                        renderGraphView();
                        break;
                    case 'tree':
                        renderTreeView();
                        break;
                    case 'dependency':
                        renderDependencyView();
                        break;
                }
            });
        });

        // 树形视图（简化版）
        function renderTreeView() {
            const container = document.getElementById('graphContainer');
            container.innerHTML = '<div style="padding: 40px; font-size: 18px; color: #718096; text-align: center;">🌳 树形视图开发中...</div>';
        }

        // 依赖关系视图（简化版）
        function renderDependencyView() {
            const container = document.getElementById('graphContainer');
            container.innerHTML = '<div style="padding: 40px; font-size: 18px; color: #718096; text-align: center;">🔗 依赖关系视图开发中...</div>';
        }

        // 侧边栏交互
        document.querySelectorAll('.tree-item').forEach(item => {
            item.addEventListener('click', () => {
                document.querySelectorAll('.tree-item').forEach(i => i.classList.remove('selected'));
                item.classList.add('selected');
            });
        });

        // 初始化
        renderGraphView();

        // 拖拽功能（简化版）
        let isDragging = false;
        let dragOffset = { x: 0, y: 0 };

        document.addEventListener('mousedown', (e) => {
            if (e.target.classList.contains('node')) {
                isDragging = true;
                const rect = e.target.getBoundingClientRect();
                dragOffset.x = e.clientX - rect.left;
                dragOffset.y = e.clientY - rect.top;
                e.target.style.cursor = 'grabbing';
            }
        });

        document.addEventListener('mousemove', (e) => {
            if (isDragging && e.target.classList.contains('node')) {
                const container = document.getElementById('graphContainer');
                const containerRect = container.getBoundingClientRect();
                const x = e.clientX - containerRect.left - dragOffset.x;
                const y = e.clientY - containerRect.top - dragOffset.y;
                
                e.target.style.left = Math.max(0, Math.min(x, container.clientWidth - e.target.clientWidth)) + 'px';
                e.target.style.top = Math.max(0, Math.min(y, container.clientHeight - e.target.clientHeight)) + 'px';
            }
        });

        document.addEventListener('mouseup', (e) => {
            if (isDragging) {
                isDragging = false;
                if (e.target.classList.contains('node')) {
                    e.target.style.cursor = 'pointer';
                }
            }
        });
    </script>
</body>
</html>
