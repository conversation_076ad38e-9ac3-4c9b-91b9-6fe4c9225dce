/**
 * 增强版告警管理器
 * 实现自动化监控和智能告警功能
 */

const EventEmitter = require('events');
const nodemailer = require('nodemailer');
const fs = require('fs');
const path = require('path');
const os = require('os');

class EnhancedAlertManager extends EventEmitter {
    constructor(options = {}) {
        super();
        
        this.config = {
            // 邮件配置
            smtp: {
                host: options.smtpHost || process.env.SMTP_HOST || 'smtp.gmail.com',
                port: options.smtpPort || process.env.SMTP_PORT || 587,
                secure: false,
                auth: {
                    user: options.smtpUser || process.env.SMTP_USER,
                    pass: options.smtpPass || process.env.SMTP_PASS
                }
            },
            
            // 告警配置
            alertEmail: options.alertEmail || process.env.ALERT_EMAIL,
            alertThresholds: {
                memoryUsage: options.memoryThreshold || 80,
                cpuUsage: options.cpuThreshold || 80,
                diskUsage: options.diskThreshold || 85,
                responseTime: options.responseThreshold || 5000,
                errorRate: options.errorThreshold || 5,
                connectionCount: options.connectionThreshold || 1000
            },
            
            // 告警频率限制
            alertCooldown: options.alertCooldown || 300000, // 5分钟
            maxAlertsPerHour: options.maxAlertsPerHour || 10
        };
        
        this.alerts = new Map();
        this.alertHistory = [];
        this.lastAlertTime = new Map();
        this.alertCount = new Map();
        this.isMonitoring = false;
        this.monitoringIntervals = [];
        
        this.setupEmailTransporter();
    }

    /**
     * 设置邮件发送器
     */
    setupEmailTransporter() {
        if (this.config.smtp.auth.user && this.config.smtp.auth.pass) {
            this.emailTransporter = nodemailer.createTransporter(this.config.smtp);
            console.log('[EnhancedAlertManager] 邮件发送器已配置');
        } else {
            console.warn('[EnhancedAlertManager] 邮件配置不完整，将仅记录告警');
        }
    }

    /**
     * 开始监控
     */
    startMonitoring() {
        if (this.isMonitoring) {
            console.log('[EnhancedAlertManager] 监控已在运行');
            return;
        }

        this.isMonitoring = true;
        
        // 系统资源监控
        const systemMonitor = setInterval(() => {
            this.checkSystemResources();
        }, 30000);
        this.monitoringIntervals.push(systemMonitor);

        // 应用性能监控
        const performanceMonitor = setInterval(() => {
            this.checkApplicationPerformance();
        }, 60000);
        this.monitoringIntervals.push(performanceMonitor);

        // 日志监控
        const logMonitor = setInterval(() => {
            this.checkErrorLogs();
        }, 120000);
        this.monitoringIntervals.push(logMonitor);

        // 清理过期告警
        const cleanupMonitor = setInterval(() => {
            this.cleanupExpiredAlerts();
        }, 300000);
        this.monitoringIntervals.push(cleanupMonitor);

        console.log('[EnhancedAlertManager] 自动化监控已启动');
    }

    /**
     * 停止监控
     */
    stopMonitoring() {
        this.isMonitoring = false;
        
        this.monitoringIntervals.forEach(interval => {
            clearInterval(interval);
        });
        this.monitoringIntervals = [];
        
        console.log('[EnhancedAlertManager] 监控已停止');
    }

    /**
     * 检查系统资源
     */
    async checkSystemResources() {
        try {
            // 内存检查
            const memUsage = process.memoryUsage();
            const memUtilization = (memUsage.heapUsed / memUsage.heapTotal) * 100;
            
            if (memUtilization > this.config.alertThresholds.memoryUsage) {
                this.triggerAlert('HIGH_MEMORY_USAGE', {
                    current: memUtilization.toFixed(2),
                    threshold: this.config.alertThresholds.memoryUsage,
                    heapUsed: Math.round(memUsage.heapUsed / 1024 / 1024),
                    heapTotal: Math.round(memUsage.heapTotal / 1024 / 1024)
                });
            }

            // CPU 检查
            const cpuUsage = process.cpuUsage();
            const loadAvg = os.loadavg()[0]; // 1分钟平均负载
            const cpuCount = os.cpus().length;
            const cpuPercent = (loadAvg / cpuCount) * 100;
            
            if (cpuPercent > this.config.alertThresholds.cpuUsage) {
                this.triggerAlert('HIGH_CPU_USAGE', {
                    current: cpuPercent.toFixed(2),
                    threshold: this.config.alertThresholds.cpuUsage,
                    loadAvg: loadAvg.toFixed(2),
                    cpuCount
                });
            }

            // 磁盘检查 (简化版)
            this.checkDiskSpace();

        } catch (error) {
            console.error('[EnhancedAlertManager] 系统资源检查失败:', error);
        }
    }

    /**
     * 检查磁盘空间
     */
    checkDiskSpace() {
        // 这里使用简化的方法检查当前目录
        try {
            const stats = fs.statSync('./');
            // 实际生产环境应该使用更精确的磁盘空间检查
        } catch (error) {
            console.error('[EnhancedAlertManager] 磁盘检查失败:', error);
        }
    }

    /**
     * 检查应用性能
     */
    checkApplicationPerformance() {
        // 检查进程运行时间
        const uptime = process.uptime();
        
        // 检查事件循环延迟
        const start = process.hrtime.bigint();
        setImmediate(() => {
            const delay = Number(process.hrtime.bigint() - start) / 1000000; // 转换为毫秒
            
            if (delay > 100) { // 如果事件循环延迟超过100ms
                this.triggerAlert('HIGH_EVENT_LOOP_DELAY', {
                    delay: delay.toFixed(2),
                    threshold: 100
                });
            }
        });
    }

    /**
     * 检查错误日志
     */
    checkErrorLogs() {
        const logFiles = [
            './logs/error.log',
            './logs/security.log',
            './logs/application.log'
        ];

        logFiles.forEach(logFile => {
            if (fs.existsSync(logFile)) {
                try {
                    const stats = fs.statSync(logFile);
                    const now = Date.now();
                    const fileAge = now - stats.mtime.getTime();
                    
                    // 检查最近5分钟的错误
                    if (fileAge < 300000) {
                        const content = fs.readFileSync(logFile, 'utf8');
                        const lines = content.split('\n').filter(line => line.trim());
                        
                        const recentErrors = lines.filter(line => {
                            try {
                                const logEntry = JSON.parse(line);
                                const logTime = new Date(logEntry.timestamp).getTime();
                                return (now - logTime) < 300000 && 
                                       (logEntry.level === 'error' || logEntry.level === 'ERROR');
                            } catch {
                                return line.toLowerCase().includes('error');
                            }
                        });

                        if (recentErrors.length > 5) {
                            this.triggerAlert('HIGH_ERROR_RATE', {
                                errorCount: recentErrors.length,
                                logFile: path.basename(logFile),
                                timeWindow: '5分钟'
                            });
                        }
                    }
                } catch (error) {
                    console.error(`[EnhancedAlertManager] 检查日志文件失败 ${logFile}:`, error);
                }
            }
        });
    }

    /**
     * 触发告警
     */
    triggerAlert(alertType, data = {}) {
        const alertId = `${alertType}_${Date.now()}`;
        const now = Date.now();
        
        // 检查冷却期
        const lastAlert = this.lastAlertTime.get(alertType);
        if (lastAlert && (now - lastAlert) < this.config.alertCooldown) {
            return;
        }

        // 检查每小时告警限制
        const hourlyCount = this.getHourlyAlertCount(alertType);
        if (hourlyCount >= this.config.maxAlertsPerHour) {
            return;
        }

        const alert = {
            id: alertId,
            type: alertType,
            severity: this.getAlertSeverity(alertType),
            message: this.generateAlertMessage(alertType, data),
            data,
            timestamp: now,
            status: 'active',
            hostname: os.hostname(),
            pid: process.pid
        };

        // 存储告警
        this.alerts.set(alertId, alert);
        this.alertHistory.push(alert);
        this.lastAlertTime.set(alertType, now);
        
        // 增加计数
        const count = this.alertCount.get(alertType) || 0;
        this.alertCount.set(alertType, count + 1);

        // 发送告警
        this.sendAlert(alert);
        
        // 触发事件
        this.emit('alert', alert);
        
        console.warn(`[EnhancedAlertManager] 🚨 告警触发: ${alert.message}`);
    }

    /**
     * 获取告警严重程度
     */
    getAlertSeverity(alertType) {
        const severityMap = {
            'HIGH_MEMORY_USAGE': 'warning',
            'HIGH_CPU_USAGE': 'warning',
            'HIGH_DISK_USAGE': 'critical',
            'HIGH_ERROR_RATE': 'critical',
            'HIGH_EVENT_LOOP_DELAY': 'warning',
            'SERVICE_DOWN': 'critical',
            'DATABASE_ERROR': 'critical',
            'SECURITY_BREACH': 'critical',
            'TEST_ALERT': 'info'
        };
        
        return severityMap[alertType] || 'info';
    }

    /**
     * 生成告警消息
     */
    generateAlertMessage(alertType, data) {
        const messages = {
            'HIGH_MEMORY_USAGE': `内存使用率过高: ${data.current}% (阈值: ${data.threshold}%) - 堆内存: ${data.heapUsed}MB/${data.heapTotal}MB`,
            'HIGH_CPU_USAGE': `CPU使用率过高: ${data.current}% (阈值: ${data.threshold}%) - 负载: ${data.loadAvg}`,
            'HIGH_DISK_USAGE': `磁盘使用率过高: ${data.current}% (阈值: ${data.threshold}%)`,
            'HIGH_ERROR_RATE': `错误率过高: ${data.errorCount} 个错误在 ${data.timeWindow} 内 (日志: ${data.logFile})`,
            'HIGH_EVENT_LOOP_DELAY': `事件循环延迟过高: ${data.delay}ms (阈值: ${data.threshold}ms)`,
            'SERVICE_DOWN': `服务不可用: ${data.service}`,
            'DATABASE_ERROR': `数据库错误: ${data.error}`,
            'SECURITY_BREACH': `安全威胁检测: ${data.threat}`,
            'TEST_ALERT': `测试告警: ${data.message || '系统测试'}`
        };
        
        return messages[alertType] || `未知告警类型: ${alertType}`;
    }

    /**
     * 发送告警
     */
    async sendAlert(alert) {
        // 控制台输出
        const severityIcon = {
            'info': 'ℹ️',
            'warning': '⚠️',
            'critical': '🚨'
        };
        
        console.warn(`${severityIcon[alert.severity]} [${alert.severity.toUpperCase()}] ${alert.message}`);
        
        // 邮件通知
        if (this.emailTransporter && this.config.alertEmail) {
            try {
                await this.sendEmailAlert(alert);
            } catch (error) {
                console.error('[EnhancedAlertManager] 邮件发送失败:', error);
            }
        }
        
        // 写入告警日志
        this.writeAlertLog(alert);
    }

    /**
     * 发送邮件告警
     */
    async sendEmailAlert(alert) {
        const mailOptions = {
            from: this.config.smtp.auth.user,
            to: this.config.alertEmail,
            subject: `[All-Agent Alert] ${alert.severity.toUpperCase()}: ${alert.type}`,
            html: this.generateEmailTemplate(alert)
        };

        await this.emailTransporter.sendMail(mailOptions);
        console.log(`[EnhancedAlertManager] 告警邮件已发送: ${alert.id}`);
    }

    /**
     * 生成邮件模板
     */
    generateEmailTemplate(alert) {
        const severityColors = {
            'info': '#2196F3',
            'warning': '#FF9800',
            'critical': '#F44336'
        };

        return `
        <html>
        <body style="font-family: Arial, sans-serif; margin: 0; padding: 0;">
            <div style="background-color: ${severityColors[alert.severity]}; color: white; padding: 20px; text-align: center;">
                <h2>🚨 All-Agent 系统告警</h2>
                <p style="margin: 0; font-size: 18px;">${alert.severity.toUpperCase()} - ${alert.type}</p>
            </div>
            
            <div style="padding: 20px; background-color: #f5f5f5;">
                <div style="background-color: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                    <h3 style="color: #333; margin-top: 0;">告警详情</h3>
                    
                    <table style="width: 100%; border-collapse: collapse; margin-bottom: 20px;">
                        <tr>
                            <td style="padding: 12px; border-bottom: 1px solid #eee; font-weight: bold; width: 150px;">告警ID:</td>
                            <td style="padding: 12px; border-bottom: 1px solid #eee;">${alert.id}</td>
                        </tr>
                        <tr>
                            <td style="padding: 12px; border-bottom: 1px solid #eee; font-weight: bold;">类型:</td>
                            <td style="padding: 12px; border-bottom: 1px solid #eee;">${alert.type}</td>
                        </tr>
                        <tr>
                            <td style="padding: 12px; border-bottom: 1px solid #eee; font-weight: bold;">严重程度:</td>
                            <td style="padding: 12px; border-bottom: 1px solid #eee;">
                                <span style="background-color: ${severityColors[alert.severity]}; color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px;">
                                    ${alert.severity.toUpperCase()}
                                </span>
                            </td>
                        </tr>
                        <tr>
                            <td style="padding: 12px; border-bottom: 1px solid #eee; font-weight: bold;">消息:</td>
                            <td style="padding: 12px; border-bottom: 1px solid #eee;">${alert.message}</td>
                        </tr>
                        <tr>
                            <td style="padding: 12px; border-bottom: 1px solid #eee; font-weight: bold;">时间:</td>
                            <td style="padding: 12px; border-bottom: 1px solid #eee;">${new Date(alert.timestamp).toLocaleString('zh-CN')}</td>
                        </tr>
                        <tr>
                            <td style="padding: 12px; border-bottom: 1px solid #eee; font-weight: bold;">主机:</td>
                            <td style="padding: 12px; border-bottom: 1px solid #eee;">${alert.hostname}</td>
                        </tr>
                        <tr>
                            <td style="padding: 12px; font-weight: bold;">进程ID:</td>
                            <td style="padding: 12px;">${alert.pid}</td>
                        </tr>
                    </table>
                    
                    ${alert.data && Object.keys(alert.data).length > 0 ? `
                    <h3 style="color: #333;">详细数据</h3>
                    <div style="background-color: #f8f9fa; padding: 15px; border-radius: 4px; border-left: 4px solid ${severityColors[alert.severity]};">
                        <pre style="margin: 0; font-family: 'Courier New', monospace; font-size: 14px; white-space: pre-wrap;">${JSON.stringify(alert.data, null, 2)}</pre>
                    </div>
                    ` : ''}
                    
                    <div style="margin-top: 30px; padding: 15px; background-color: #e3f2fd; border-radius: 4px;">
                        <h4 style="margin: 0 0 10px 0; color: #1976d2;">建议操作</h4>
                        <p style="margin: 0; color: #333;">
                            请立即检查系统状态并采取相应措施。如需帮助，请联系系统管理员。
                        </p>
                    </div>
                </div>
            </div>
            
            <div style="background-color: #333; color: white; padding: 15px; text-align: center; font-size: 12px;">
                <p style="margin: 0;">此邮件由 All-Agent 监控系统自动发送</p>
                <p style="margin: 5px 0 0 0;">请勿直接回复此邮件</p>
            </div>
        </body>
        </html>
        `;
    }

    /**
     * 写入告警日志
     */
    writeAlertLog(alert) {
        const logDir = './logs';
        if (!fs.existsSync(logDir)) {
            fs.mkdirSync(logDir, { recursive: true });
        }
        
        const logFile = path.join(logDir, `alerts-${new Date().toISOString().split('T')[0]}.log`);
        const logEntry = JSON.stringify({
            ...alert,
            loggedAt: new Date().toISOString()
        }) + '\n';
        
        fs.appendFileSync(logFile, logEntry);
    }

    /**
     * 获取每小时告警计数
     */
    getHourlyAlertCount(alertType) {
        const oneHourAgo = Date.now() - 3600000;
        return this.alertHistory.filter(alert => 
            alert.type === alertType && alert.timestamp > oneHourAgo
        ).length;
    }

    /**
     * 清理过期告警
     */
    cleanupExpiredAlerts() {
        const oneDayAgo = Date.now() - 86400000;
        
        // 清理活跃告警
        for (const [id, alert] of this.alerts.entries()) {
            if (alert.timestamp < oneDayAgo) {
                this.alerts.delete(id);
            }
        }
        
        // 清理告警历史 (保留最近7天)
        const oneWeekAgo = Date.now() - 604800000;
        this.alertHistory = this.alertHistory.filter(alert => 
            alert.timestamp > oneWeekAgo
        );
        
        console.log(`[EnhancedAlertManager] 清理过期告警完成，当前活跃告警: ${this.alerts.size}，历史告警: ${this.alertHistory.length}`);
    }

    /**
     * 获取活跃告警
     */
    getActiveAlerts() {
        return Array.from(this.alerts.values())
            .sort((a, b) => b.timestamp - a.timestamp);
    }

    /**
     * 获取告警历史
     */
    getAlertHistory(limit = 100) {
        return this.alertHistory
            .sort((a, b) => b.timestamp - a.timestamp)
            .slice(0, limit);
    }

    /**
     * 获取告警统计
     */
    getAlertStats() {
        const now = Date.now();
        const oneHourAgo = now - 3600000;
        const oneDayAgo = now - 86400000;
        
        const recentAlerts = this.alertHistory.filter(alert => alert.timestamp > oneHourAgo);
        const dailyAlerts = this.alertHistory.filter(alert => alert.timestamp > oneDayAgo);
        
        return {
            activeAlerts: this.alerts.size,
            recentAlerts: recentAlerts.length,
            dailyAlerts: dailyAlerts.length,
            totalAlerts: this.alertHistory.length,
            alertsByType: this.getAlertsByType(),
            alertsBySeverity: this.getAlertsBySeverity(),
            systemInfo: {
                hostname: os.hostname(),
                platform: os.platform(),
                uptime: process.uptime(),
                nodeVersion: process.version,
                pid: process.pid
            }
        };
    }

    /**
     * 按类型统计告警
     */
    getAlertsByType() {
        const stats = {};
        this.alertHistory.forEach(alert => {
            stats[alert.type] = (stats[alert.type] || 0) + 1;
        });
        return stats;
    }

    /**
     * 按严重程度统计告警
     */
    getAlertsBySeverity() {
        const stats = {};
        this.alertHistory.forEach(alert => {
            stats[alert.severity] = (stats[alert.severity] || 0) + 1;
        });
        return stats;
    }

    /**
     * 手动触发测试告警
     */
    triggerTestAlert(message = '这是一个测试告警') {
        this.triggerAlert('TEST_ALERT', {
            message,
            timestamp: Date.now(),
            testData: {
                memoryUsage: process.memoryUsage(),
                uptime: process.uptime(),
                platform: os.platform()
            }
        });
    }

    /**
     * 健康检查
     */
    healthCheck() {
        return {
            status: this.isMonitoring ? 'healthy' : 'stopped',
            activeAlerts: this.alerts.size,
            totalAlerts: this.alertHistory.length,
            emailConfigured: !!this.emailTransporter,
            uptime: process.uptime(),
            lastCleanup: this.lastCleanup || null
        };
    }
}

module.exports = EnhancedAlertManager;
