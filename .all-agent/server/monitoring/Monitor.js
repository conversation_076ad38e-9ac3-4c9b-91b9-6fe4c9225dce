const EventEmitter = require('events');
const os = require('os');
const fs = require('fs').promises;
const path = require('path');

/**
 * 系统监控器
 * 监控系统性能、资源使用、错误率等指标
 */
class Monitor extends EventEmitter {
  constructor(options = {}) {
    super();

    this.options = {
      interval: options.interval || 30000, // 30秒
      alertThresholds: {
        cpuUsage: options.cpuThreshold || 80, // CPU 使用率阈值
        memoryUsage: options.memoryThreshold || 95, // 内存使用率阈值 (提高到95%避免频繁告警)
        diskUsage: options.diskThreshold || 90, // 磁盘使用率阈值
        errorRate: options.errorThreshold || 5, // 错误率阈值 (%)
        responseTime: options.responseThreshold || 5000, // 响应时间阈值 (ms)
        connectionCount: options.connectionThreshold || 1000 // 连接数阈值
      },
      retentionDays: options.retentionDays || 7,
      ...options
    };

    this.metrics = {
      system: {
        cpu: [],
        memory: [],
        disk: [],
        network: []
      },
      application: {
        requests: [],
        errors: [],
        responseTimes: [],
        activeConnections: 0,
        cacheHitRate: 0,
        databaseConnections: 0
      },
      alerts: []
    };

    this.isRunning = false;
    this.intervalId = null;
    this.startTime = Date.now();

    // 请求计数器
    this.requestCount = 0;
    this.errorCount = 0;
    this.responseTimes = [];
  }

  /**
   * 启动监控
   */
  start() {
    if (this.isRunning) {
      return;
    }

    this.isRunning = true;
    this.intervalId = setInterval(() => {
      this.collectMetrics();
    }, this.options.interval);

    console.log('📊 系统监控已启动');
    this.emit('monitor_started');
  }

  /**
   * 停止监控
   */
  stop() {
    if (!this.isRunning) {
      return;
    }

    this.isRunning = false;
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }

    console.log('📊 系统监控已停止');
    this.emit('monitor_stopped');
  }

  /**
   * 收集系统指标
   */
  async collectMetrics() {
    try {
      const timestamp = Date.now();

      // 收集系统指标
      const systemMetrics = await this.collectSystemMetrics();
      const appMetrics = this.collectApplicationMetrics();

      // 存储指标
      this.storeMetrics(timestamp, systemMetrics, appMetrics);

      // 检查告警条件
      this.checkAlerts(systemMetrics, appMetrics);

      // 清理过期数据
      this.cleanupOldMetrics();

      this.emit('metrics_collected', {
        timestamp,
        system: systemMetrics,
        application: appMetrics
      });
    } catch (error) {
      console.error('指标收集失败:', error);
      this.emit('monitor_error', error);
    }
  }

  /**
   * 收集系统指标
   */
  async collectSystemMetrics() {
    // CPU 使用率
    const cpuUsage = await this.getCPUUsage();

    // 内存使用率
    const totalMemory = os.totalmem();
    const freeMemory = os.freemem();
    const memoryUsage = ((totalMemory - freeMemory) / totalMemory) * 100;

    // 磁盘使用率
    const diskUsage = await this.getDiskUsage();

    // 网络信息
    const networkInterfaces = os.networkInterfaces();

    // 负载平均值
    const loadAverage = os.loadavg();

    return {
      cpu: {
        usage: cpuUsage,
        cores: os.cpus().length
      },
      memory: {
        total: totalMemory,
        free: freeMemory,
        used: totalMemory - freeMemory,
        usage: memoryUsage
      },
      disk: diskUsage,
      network: networkInterfaces,
      load: {
        avg1: loadAverage[0],
        avg5: loadAverage[1],
        avg15: loadAverage[2]
      },
      uptime: os.uptime(),
      platform: os.platform(),
      arch: os.arch()
    };
  }

  /**
   * 获取 CPU 使用率
   */
  async getCPUUsage() {
    return new Promise((resolve) => {
      const startMeasure = this.cpuAverage();

      setTimeout(() => {
        const endMeasure = this.cpuAverage();
        const idleDifference = endMeasure.idle - startMeasure.idle;
        const totalDifference = endMeasure.total - startMeasure.total;
        const usage = 100 - ~~((100 * idleDifference) / totalDifference);
        resolve(usage);
      }, 1000);
    });
  }

  /**
   * CPU 平均值计算
   */
  cpuAverage() {
    const cpus = os.cpus();
    let totalIdle = 0;
    let totalTick = 0;

    for (const cpu of cpus) {
      for (const type in cpu.times) {
        totalTick += cpu.times[type];
      }
      totalIdle += cpu.times.idle;
    }

    return {
      idle: totalIdle / cpus.length,
      total: totalTick / cpus.length
    };
  }

  /**
   * 获取磁盘使用率
   */
  async getDiskUsage() {
    try {
      const stats = await fs.stat(process.cwd());
      // 简化的磁盘使用率计算
      // 在实际应用中，可能需要使用更复杂的方法
      return {
        total: 0,
        used: 0,
        free: 0,
        usage: 0
      };
    } catch (error) {
      return {
        total: 0,
        used: 0,
        free: 0,
        usage: 0
      };
    }
  }

  /**
   * 收集应用指标
   */
  collectApplicationMetrics() {
    const now = Date.now();
    const windowSize = 60000; // 1分钟窗口

    // 计算最近1分钟的请求数
    const recentRequests = this.metrics.application.requests.filter((req) => now - req.timestamp < windowSize);

    // 计算最近1分钟的错误数
    const recentErrors = this.metrics.application.errors.filter((err) => now - err.timestamp < windowSize);

    // 计算错误率
    const errorRate = recentRequests.length > 0 ? (recentErrors.length / recentRequests.length) * 100 : 0;

    // 计算平均响应时间
    const recentResponseTimes = this.responseTimes.filter((rt) => now - rt.timestamp < windowSize);

    const avgResponseTime =
      recentResponseTimes.length > 0
        ? recentResponseTimes.reduce((sum, rt) => sum + rt.time, 0) / recentResponseTimes.length
        : 0;

    // 内存使用情况
    const memoryUsage = process.memoryUsage();

    return {
      requests: {
        total: this.requestCount,
        recent: recentRequests.length,
        rate: recentRequests.length / (windowSize / 1000) // 每秒请求数
      },
      errors: {
        total: this.errorCount,
        recent: recentErrors.length,
        rate: errorRate
      },
      responseTime: {
        average: avgResponseTime,
        recent: recentResponseTimes
      },
      memory: {
        rss: memoryUsage.rss,
        heapTotal: memoryUsage.heapTotal,
        heapUsed: memoryUsage.heapUsed,
        external: memoryUsage.external
      },
      activeConnections: this.metrics.application.activeConnections,
      cacheHitRate: this.metrics.application.cacheHitRate,
      uptime: now - this.startTime
    };
  }

  /**
   * 存储指标数据
   */
  storeMetrics(timestamp, systemMetrics, appMetrics) {
    // 存储系统指标
    this.metrics.system.cpu.push({
      timestamp,
      usage: systemMetrics.cpu.usage
    });

    this.metrics.system.memory.push({
      timestamp,
      usage: systemMetrics.memory.usage,
      used: systemMetrics.memory.used,
      free: systemMetrics.memory.free
    });

    this.metrics.system.disk.push({
      timestamp,
      usage: systemMetrics.disk.usage
    });

    // 限制数据点数量
    const maxDataPoints = 1000;
    if (this.metrics.system.cpu.length > maxDataPoints) {
      this.metrics.system.cpu = this.metrics.system.cpu.slice(-maxDataPoints);
    }
    if (this.metrics.system.memory.length > maxDataPoints) {
      this.metrics.system.memory = this.metrics.system.memory.slice(-maxDataPoints);
    }
    if (this.metrics.system.disk.length > maxDataPoints) {
      this.metrics.system.disk = this.metrics.system.disk.slice(-maxDataPoints);
    }
  }

  /**
   * 检查告警条件
   */
  checkAlerts(systemMetrics, appMetrics) {
    const alerts = [];
    const now = Date.now();

    // CPU 使用率告警
    if (systemMetrics.cpu.usage > this.options.alertThresholds.cpuUsage) {
      alerts.push({
        type: 'cpu_high',
        level: 'warning',
        message: `CPU 使用率过高: ${systemMetrics.cpu.usage.toFixed(1)}%`,
        value: systemMetrics.cpu.usage,
        threshold: this.options.alertThresholds.cpuUsage,
        timestamp: now
      });
    }

    // 内存使用率告警
    if (systemMetrics.memory.usage > this.options.alertThresholds.memoryUsage) {
      alerts.push({
        type: 'memory_high',
        level: 'warning',
        message: `内存使用率过高: ${systemMetrics.memory.usage.toFixed(1)}%`,
        value: systemMetrics.memory.usage,
        threshold: this.options.alertThresholds.memoryUsage,
        timestamp: now
      });
    }

    // 错误率告警
    if (appMetrics.errors.rate > this.options.alertThresholds.errorRate) {
      alerts.push({
        type: 'error_rate_high',
        level: 'critical',
        message: `错误率过高: ${appMetrics.errors.rate.toFixed(1)}%`,
        value: appMetrics.errors.rate,
        threshold: this.options.alertThresholds.errorRate,
        timestamp: now
      });
    }

    // 响应时间告警
    if (appMetrics.responseTime.average > this.options.alertThresholds.responseTime) {
      alerts.push({
        type: 'response_time_high',
        level: 'warning',
        message: `平均响应时间过长: ${appMetrics.responseTime.average.toFixed(0)}ms`,
        value: appMetrics.responseTime.average,
        threshold: this.options.alertThresholds.responseTime,
        timestamp: now
      });
    }

    // 发送告警
    for (const alert of alerts) {
      this.sendAlert(alert);
    }
  }

  /**
   * 发送告警
   */
  sendAlert(alert) {
    // 检查是否是重复告警（避免告警风暴）
    const recentAlerts = this.metrics.alerts.filter(
      (a) => a.type === alert.type && Date.now() - a.timestamp < 300000 // 5分钟内
    );

    if (recentAlerts.length > 0) {
      return; // 跳过重复告警
    }

    // 存储告警
    this.metrics.alerts.push(alert);

    // 发出告警事件
    this.emit('alert', alert);

    // 记录告警日志
    console.warn(`🚨 告警: ${alert.message}`);

    // 这里可以集成外部告警系统
    // 如：发送邮件、Slack 通知、短信等
  }

  /**
   * 清理过期数据
   */
  cleanupOldMetrics() {
    const cutoffTime = Date.now() - this.options.retentionDays * 24 * 60 * 60 * 1000;

    // 清理系统指标
    this.metrics.system.cpu = this.metrics.system.cpu.filter((m) => m.timestamp > cutoffTime);
    this.metrics.system.memory = this.metrics.system.memory.filter((m) => m.timestamp > cutoffTime);
    this.metrics.system.disk = this.metrics.system.disk.filter((m) => m.timestamp > cutoffTime);

    // 清理应用指标
    this.metrics.application.requests = this.metrics.application.requests.filter((r) => r.timestamp > cutoffTime);
    this.metrics.application.errors = this.metrics.application.errors.filter((e) => e.timestamp > cutoffTime);
    this.responseTimes = this.responseTimes.filter((rt) => rt.timestamp > cutoffTime);

    // 清理告警
    this.metrics.alerts = this.metrics.alerts.filter((a) => a.timestamp > cutoffTime);
  }

  /**
   * 记录请求
   */
  recordRequest(req, res, responseTime) {
    const timestamp = Date.now();

    this.requestCount++;
    this.metrics.application.requests.push({
      timestamp,
      method: req.method,
      url: req.url,
      statusCode: res.statusCode,
      responseTime,
      userAgent: req.get('User-Agent'),
      ip: req.ip
    });

    this.responseTimes.push({
      timestamp,
      time: responseTime
    });

    // 记录错误
    if (res.statusCode >= 400) {
      this.recordError(req, res);
    }
  }

  /**
   * 记录错误
   */
  recordError(req, res, error = null) {
    this.errorCount++;
    this.metrics.application.errors.push({
      timestamp: Date.now(),
      method: req.method,
      url: req.url,
      statusCode: res.statusCode,
      error: error ? error.message : null,
      stack: error ? error.stack : null
    });
  }

  /**
   * 更新连接数
   */
  updateConnectionCount(count) {
    this.metrics.application.activeConnections = count;
  }

  /**
   * 更新缓存命中率
   */
  updateCacheHitRate(hitRate) {
    this.metrics.application.cacheHitRate = hitRate;
  }

  /**
   * 获取监控数据
   */
  getMetrics() {
    return {
      ...this.metrics,
      summary: this.getSummary()
    };
  }

  /**
   * 获取监控摘要
   */
  getSummary() {
    const now = Date.now();
    const windowSize = 300000; // 5分钟窗口

    const recentRequests = this.metrics.application.requests.filter((r) => now - r.timestamp < windowSize);

    const recentErrors = this.metrics.application.errors.filter((e) => now - e.timestamp < windowSize);

    const recentAlerts = this.metrics.alerts.filter((a) => now - a.timestamp < windowSize);

    return {
      uptime: now - this.startTime,
      totalRequests: this.requestCount,
      totalErrors: this.errorCount,
      recentRequests: recentRequests.length,
      recentErrors: recentErrors.length,
      recentAlerts: recentAlerts.length,
      activeConnections: this.metrics.application.activeConnections,
      cacheHitRate: this.metrics.application.cacheHitRate
    };
  }

  /**
   * 导出监控数据
   */
  async exportMetrics(filePath) {
    try {
      const data = {
        exportTime: new Date().toISOString(),
        metrics: this.getMetrics()
      };

      await fs.writeFile(filePath, JSON.stringify(data, null, 2));
      console.log(`📊 监控数据已导出: ${filePath}`);
    } catch (error) {
      console.error('导出监控数据失败:', error);
      throw error;
    }
  }
}

module.exports = Monitor;
