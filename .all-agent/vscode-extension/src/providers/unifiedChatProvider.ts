import * as vscode from 'vscode';
import { AllAgentClient } from '../client';

/**
 * 统一聊天提供者 - 类似 Augment 的单一聊天入口
 */
export class UnifiedChatProvider implements vscode.WebviewViewProvider {
    public static readonly viewType = 'all-agent-unified-chat';
    private _view?: vscode.WebviewView;
    private _context: any = {};
    private _conversationHistory: any[] = [];

    constructor(
        private readonly _extensionContext: vscode.ExtensionContext,
        private readonly _client: AllAgentClient
    ) {
        // 监听文件变化以更新上下文
        this.setupContextTracking();
    }

    public resolveWebviewView(
        webviewView: vscode.WebviewView,
        context: vscode.WebviewViewResolveContext,
        _token: vscode.CancellationToken,
    ) {
        this._view = webviewView;

        webviewView.webview.options = {
            enableScripts: true,
            localResourceRoots: [this._extensionContext.extensionUri]
        };

        webviewView.webview.html = this._getHtmlForWebview(webviewView.webview);

        // 处理消息
        webviewView.webview.onDidReceiveMessage(async (data) => {
            await this.handleMessage(data);
        });

        // 初始化上下文
        this.updateContext();
    }

    private async handleMessage(data: any) {
        switch (data.type) {
            case 'sendMessage':
                await this.processUserMessage(data.message);
                break;
            case 'executeCommand':
                await this.executeCommand(data.command);
                break;
            case 'getContext':
                await this.sendContextUpdate();
                break;
            case 'clearHistory':
                this._conversationHistory = [];
                this.sendMessage({ type: 'historyCleared' });
                break;
            case 'getPerformanceStats':
                await this.sendPerformanceStats();
                break;
            case 'exportHistory':
                await this.exportConversationHistory();
                break;
            case 'quickAction':
                await this.handleQuickAction(data.action);
                break;
        }
    }

    private async processUserMessage(message: string) {
        try {
            // 添加到历史记录
            this._conversationHistory.push({
                role: 'user',
                content: message,
                timestamp: new Date().toISOString(),
                context: { ...this._context }
            });

            // 发送用户消息到 UI
            this.sendMessage({
                type: 'userMessage',
                message: message,
                timestamp: new Date().toISOString()
            });

            // 分析消息意图并路由到合适的处理器
            const intent = await this.analyzeIntent(message);
            const response = await this.routeMessage(message, intent);

            // 添加 AI 响应到历史
            this._conversationHistory.push({
                role: 'assistant',
                content: response,
                timestamp: new Date().toISOString(),
                intent: intent
            });

            // 发送响应到 UI
            this.sendMessage({
                type: 'assistantMessage',
                message: response,
                timestamp: new Date().toISOString(),
                intent: intent
            });

        } catch (error) {
            this.sendMessage({
                type: 'error',
                message: `Error: ${error instanceof Error ? error.message : String(error)}`
            });
        }
    }

    private async analyzeIntent(message: string): Promise<string> {
        const lowerMessage = message.toLowerCase();

        // 终端相关关键词
        const terminalKeywords = ['terminal', 'command', 'run', 'execute', 'npm', 'node', 'git', 'install', 'start', 'test', 'build', 'deploy'];
        if (terminalKeywords.some(keyword => lowerMessage.includes(keyword))) {
            return 'terminal';
        }

        // 文件操作关键词
        const fileKeywords = ['file', 'edit', 'code', 'create', 'delete', 'modify', 'refactor', 'generate'];
        if (fileKeywords.some(keyword => lowerMessage.includes(keyword))) {
            return 'file';
        }

        // 项目分析关键词
        const analyzeKeywords = ['analyze', 'project', 'structure', 'dependencies', 'overview', 'summary'];
        if (analyzeKeywords.some(keyword => lowerMessage.includes(keyword))) {
            return 'analyze';
        }

        // 浏览器相关关键词
        const browserKeywords = ['browser', 'page', 'web', 'server', 'localhost', 'port', 'url', 'website'];
        if (browserKeywords.some(keyword => lowerMessage.includes(keyword))) {
            return 'browser';
        }

        // 调试相关关键词
        const debugKeywords = ['debug', 'error', 'fix', 'bug', 'issue', 'problem', 'crash', 'fail'];
        if (debugKeywords.some(keyword => lowerMessage.includes(keyword))) {
            return 'debug';
        }

        // 帮助相关关键词
        const helpKeywords = ['help', 'how', 'what', 'explain', 'guide', 'tutorial'];
        if (helpKeywords.some(keyword => lowerMessage.includes(keyword))) {
            return 'help';
        }

        return 'general';
    }

    private async routeMessage(message: string, intent: string): Promise<string> {
        const contextualMessage = this.buildContextualMessage(message);

        try {
            switch (intent) {
                case 'terminal':
                    return await this._client.handleTerminalRequest(contextualMessage);
                case 'file':
                    return await this._client.handleFileRequest(contextualMessage, this._context);
                case 'analyze':
                    return await this._client.handleAnalysisRequest(contextualMessage, this._context);
                case 'browser':
                    return await this._client.handleBrowserRequest(contextualMessage);
                case 'debug':
                    return await this._client.handleDebugRequest(contextualMessage, this._context);
                case 'help':
                    return this.generateHelpResponse(message);
                default:
                    return await this._client.sendMessage(contextualMessage);
            }
        } catch (error) {
            console.error('Message routing error:', error);
            return `❌ Sorry, I encountered an error processing your request: ${error instanceof Error ? error.message : String(error)}`;
        }
    }

    private generateHelpResponse(message: string): string {
        return `🤖 **All-Agent Help**

I'm your AI assistant that can help you with various development tasks:

**🔧 What I can do:**
• **Terminal Operations**: Run commands, manage processes, execute scripts
• **Code Analysis**: Analyze project structure, dependencies, and code quality
• **File Management**: Create, edit, and refactor code files
• **Browser Control**: Monitor local servers, check page status
• **Debug Assistance**: Help fix errors and troubleshoot issues
• **Project Management**: Start/stop services, run tests, build projects

**💬 How to interact with me:**
• Just type naturally - I'll understand your intent
• Use commands like "run npm install", "analyze project", "fix errors"
• Ask questions like "what's wrong with my code?", "how do I start the server?"

**🚀 Quick actions:**
• "analyze project" - Get project overview
• "run tests" - Execute test suite
• "fix errors" - Help resolve current issues
• "start server" - Launch development server
• "open browser" - Check local servers

**Need specific help?** Just ask me anything about your project!`;
    }

    private buildContextualMessage(message: string): string {
        const ctx = this._context;
        const contextParts: string[] = [];

        // 工作区信息
        if (ctx.workspacePath) {
            contextParts.push(`📁 Workspace: ${ctx.workspacePath.split('/').pop()}`);
        }

        // 当前文件信息
        if (ctx.activeFile) {
            const fileName = ctx.activeFile.split('/').pop();
            const language = ctx.language || 'unknown';
            contextParts.push(`📄 Current file: ${fileName} (${language})`);
        }

        // 选中文本信息
        if (ctx.selectedText) {
            const preview = ctx.selectedText.length > 100
                ? ctx.selectedText.substring(0, 100) + '...'
                : ctx.selectedText;
            contextParts.push(`✂️ Selected: "${preview}"`);
        }

        // 错误信息
        if (ctx.recentErrors && ctx.recentErrors.length > 0) {
            contextParts.push(`❌ Errors: ${ctx.recentErrors.length} active`);
            // 添加最重要的错误
            const topError = ctx.recentErrors[0];
            if (topError) {
                contextParts.push(`   Latest: ${topError.message} (${topError.file}:${topError.line})`);
            }
        }

        // 项目类型信息
        if (ctx.projectType && ctx.projectType !== 'unknown') {
            contextParts.push(`🏗️ Project: ${ctx.projectType}`);
        }

        // 技术栈信息
        if (ctx.technologies && ctx.technologies.length > 0) {
            contextParts.push(`⚡ Tech: ${ctx.technologies.slice(0, 3).join(', ')}`);
        }

        const contextInfo = contextParts.length > 0
            ? contextParts.join('\n')
            : 'No specific context available';

        return `Context:\n${contextInfo}\n\nUser request: ${message}`;
    }

    private setupContextTracking() {
        // 监听活动编辑器变化
        vscode.window.onDidChangeActiveTextEditor((editor) => {
            if (editor) {
                this._context.activeFile = editor.document.fileName;
                this._context.language = editor.document.languageId;
                this.updateContext();
            }
        });

        // 监听选择变化
        vscode.window.onDidChangeTextEditorSelection((event) => {
            const selection = event.textEditor.selection;
            if (!selection.isEmpty) {
                this._context.selectedText = event.textEditor.document.getText(selection);
            } else {
                this._context.selectedText = null;
            }
            this.updateContext();
        });

        // 监听工作区变化
        vscode.workspace.onDidChangeWorkspaceFolders(() => {
            this.updateWorkspaceContext();
        });

        // 监听诊断变化（错误、警告）
        vscode.languages.onDidChangeDiagnostics((event) => {
            this.updateDiagnosticsContext(event);
        });
    }

    private updateContext() {
        this._context.timestamp = new Date().toISOString();
        this._context.workspacePath = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;

        if (this._view) {
            this.sendContextUpdate();
        }
    }

    private updateWorkspaceContext() {
        this._context.workspacePath = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;
        this._context.workspaceName = vscode.workspace.name;
        this.updateContext();
    }

    private updateDiagnosticsContext(event: vscode.DiagnosticChangeEvent) {
        const errors: any[] = [];
        event.uris.forEach(uri => {
            const diagnostics = vscode.languages.getDiagnostics(uri);
            diagnostics.forEach(diagnostic => {
                if (diagnostic.severity === vscode.DiagnosticSeverity.Error) {
                    errors.push({
                        file: uri.fsPath,
                        message: diagnostic.message,
                        line: diagnostic.range.start.line,
                        column: diagnostic.range.start.character
                    });
                }
            });
        });

        this._context.recentErrors = errors.slice(0, 10); // 保留最近 10 个错误
        this.updateContext();
    }

    private async executeCommand(command: string) {
        try {
            await vscode.commands.executeCommand(command);
            this.sendMessage({
                type: 'commandExecuted',
                command: command
            });
        } catch (error) {
            this.sendMessage({
                type: 'error',
                message: `Failed to execute command: ${error}`
            });
        }
    }

    private sendMessage(message: any) {
        if (this._view) {
            this._view.webview.postMessage(message);
        }
    }

    private async sendContextUpdate() {
        this.sendMessage({
            type: 'contextUpdate',
            context: this._context
        });
    }

    private async sendPerformanceStats() {
        const stats = this._client.getPerformanceService().getPerformanceStats();
        const memUsage = this._client.getPerformanceService().getMemoryUsage();
        const recommendations = this._client.getPerformanceService().getPerformanceRecommendations();

        this.sendMessage({
            type: 'performanceStats',
            stats: {
                operations: stats,
                memory: memUsage,
                recommendations
            }
        });
    }

    private async exportConversationHistory() {
        const history = this._conversationHistory;
        const exportData = {
            timestamp: new Date().toISOString(),
            context: this._context,
            conversations: history
        };

        // 创建下载链接
        const dataStr = JSON.stringify(exportData, null, 2);
        this.sendMessage({
            type: 'exportReady',
            data: dataStr,
            filename: `all-agent-history-${new Date().toISOString().split('T')[0]}.json`
        });
    }

    private async handleQuickAction(action: string) {
        try {
            let response = '';

            switch (action) {
                case 'analyze':
                    response = await this._client.handleAnalysisRequest('Analyze current project', this._context);
                    break;
                case 'test':
                    response = await this._client.handleTerminalRequest('run tests');
                    break;
                case 'fix':
                    response = await this._client.handleDebugRequest('fix errors', this._context);
                    break;
                case 'terminal':
                    response = await this._client.handleTerminalRequest('open terminal');
                    break;
                case 'servers':
                    response = await this._client.handleBrowserRequest('check local servers');
                    break;
                default:
                    response = `Unknown quick action: ${action}`;
            }

            this.sendMessage({
                type: 'quickActionResponse',
                action,
                response
            });
        } catch (error) {
            this.sendMessage({
                type: 'error',
                message: `Quick action failed: ${error instanceof Error ? error.message : String(error)}`
            });
        }
    }

    private _getHtmlForWebview(webview: vscode.Webview): string {
        return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>All-Agent Unified Chat</title>
    <style>
        body {
            font-family: var(--vscode-font-family);
            font-size: var(--vscode-font-size);
            background-color: var(--vscode-editor-background);
            color: var(--vscode-editor-foreground);
            margin: 0;
            padding: 0;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .header {
            padding: 10px;
            border-bottom: 1px solid var(--vscode-panel-border);
            background-color: var(--vscode-sideBar-background);
        }

        .context-info {
            font-size: 11px;
            color: var(--vscode-descriptionForeground);
            margin-bottom: 5px;
        }

        .chat-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .messages {
            flex: 1;
            overflow-y: auto;
            padding: 10px;
        }

        .message {
            margin-bottom: 15px;
            padding: 10px;
            border-radius: 8px;
            max-width: 90%;
            word-wrap: break-word;
        }

        .user-message {
            background-color: var(--vscode-button-background);
            color: var(--vscode-button-foreground);
            margin-left: auto;
            text-align: right;
        }

        .assistant-message {
            background-color: var(--vscode-input-background);
            border: 1px solid var(--vscode-input-border);
            margin-right: auto;
        }

        .message-meta {
            font-size: 10px;
            color: var(--vscode-descriptionForeground);
            margin-top: 5px;
        }

        .input-container {
            padding: 10px;
            border-top: 1px solid var(--vscode-panel-border);
            background-color: var(--vscode-sideBar-background);
        }

        .input-row {
            display: flex;
            gap: 8px;
            margin-bottom: 8px;
        }

        .message-input {
            flex: 1;
            padding: 8px 12px;
            background-color: var(--vscode-input-background);
            color: var(--vscode-input-foreground);
            border: 1px solid var(--vscode-input-border);
            border-radius: 4px;
            outline: none;
            resize: vertical;
            min-height: 20px;
        }

        .send-button, .action-button {
            padding: 8px 12px;
            background-color: var(--vscode-button-background);
            color: var(--vscode-button-foreground);
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }

        .send-button:hover, .action-button:hover {
            background-color: var(--vscode-button-hoverBackground);
        }

        .quick-actions {
            display: flex;
            gap: 5px;
            flex-wrap: wrap;
        }

        .intent-indicator {
            display: inline-block;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 10px;
            margin-left: 5px;
        }

        .intent-terminal { background-color: #4CAF50; color: white; }
        .intent-file { background-color: #2196F3; color: white; }
        .intent-analyze { background-color: #FF9800; color: white; }
        .intent-browser { background-color: #9C27B0; color: white; }
        .intent-debug { background-color: #F44336; color: white; }
        .intent-general { background-color: #607D8B; color: white; }
    </style>
</head>
<body>
    <div class="header">
        <div class="context-info" id="contextInfo">Loading context...</div>
    </div>

    <div class="chat-container">
        <div class="messages" id="messages">
            <div class="message assistant-message">
                Welcome to All-Agent! I'm your unified AI assistant. I can help you with:
                <br>• Code analysis and generation
                <br>• Terminal operations
                <br>• File management
                <br>• Debugging and error fixing
                <br>• Browser automation
                <br><br>Just tell me what you need!
            </div>
        </div>

        <div class="input-container">
            <div class="quick-actions">
                <button class="action-button" onclick="quickAction('analyze')">📊 Analyze</button>
                <button class="action-button" onclick="quickAction('test')">🧪 Test</button>
                <button class="action-button" onclick="quickAction('fix')">🔧 Fix</button>
                <button class="action-button" onclick="quickAction('terminal')">💻 Terminal</button>
                <button class="action-button" onclick="quickAction('servers')">🌐 Servers</button>
                <button class="action-button" onclick="showPerformanceStats()">📈 Stats</button>
                <button class="action-button" onclick="exportHistory()">💾 Export</button>
                <button class="action-button" onclick="clearHistory()">🗑️ Clear</button>
            </div>
            <div class="input-row">
                <textarea class="message-input" id="messageInput" placeholder="Ask me anything about your project..." rows="2"></textarea>
                <button class="send-button" onclick="sendMessage()">Send</button>
            </div>
        </div>
    </div>

    <script>
        const vscode = acquireVsCodeApi();
        let currentContext = {};

        function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();

            if (!message) return;

            vscode.postMessage({
                type: 'sendMessage',
                message: message
            });

            input.value = '';
        }

        function quickAction(action) {
            vscode.postMessage({
                type: 'quickAction',
                action: action
            });
        }

        function clearHistory() {
            if (confirm('Are you sure you want to clear the conversation history?')) {
                vscode.postMessage({ type: 'clearHistory' });
            }
        }

        function showPerformanceStats() {
            vscode.postMessage({ type: 'getPerformanceStats' });
        }

        function exportHistory() {
            vscode.postMessage({ type: 'exportHistory' });
        }

        function downloadFile(content, filename) {
            const blob = new Blob([content], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }

        function addMessage(content, type, meta = {}) {
            const messages = document.getElementById('messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = \`message \${type}-message\`;

            let intentBadge = '';
            if (meta.intent) {
                intentBadge = \`<span class="intent-indicator intent-\${meta.intent}">\${meta.intent}</span>\`;
            }

            messageDiv.innerHTML = \`
                \${content}\${intentBadge}
                <div class="message-meta">\${meta.timestamp || new Date().toLocaleTimeString()}</div>
            \`;

            messages.appendChild(messageDiv);
            messages.scrollTop = messages.scrollHeight;
        }

        function updateContextInfo(context) {
            const contextInfo = document.getElementById('contextInfo');
            const parts = [];

            if (context.workspacePath) {
                parts.push(\`📁 \${context.workspacePath.split('/').pop()}\`);
            }
            if (context.activeFile) {
                parts.push(\`📄 \${context.activeFile.split('/').pop()}\`);
            }
            if (context.selectedText) {
                parts.push(\`✂️ Text selected\`);
            }
            if (context.recentErrors && context.recentErrors.length > 0) {
                parts.push(\`❌ \${context.recentErrors.length} errors\`);
            }

            contextInfo.textContent = parts.join(' • ') || 'No active context';
        }

        // 监听来自扩展的消息
        window.addEventListener('message', event => {
            const message = event.data;

            switch (message.type) {
                case 'userMessage':
                    addMessage(message.message, 'user', { timestamp: message.timestamp });
                    break;
                case 'assistantMessage':
                    addMessage(message.message, 'assistant', {
                        timestamp: message.timestamp,
                        intent: message.intent
                    });
                    break;
                case 'error':
                    addMessage(message.message, 'assistant');
                    break;
                case 'contextUpdate':
                    currentContext = message.context;
                    updateContextInfo(message.context);
                    break;
                case 'historyCleared':
                    document.getElementById('messages').innerHTML = '';
                    addMessage('Conversation history cleared.', 'assistant');
                    break;
                case 'performanceStats':
                    showPerformanceModal(message.stats);
                    break;
                case 'exportReady':
                    downloadFile(message.data, message.filename);
                    addMessage('Conversation history exported successfully.', 'assistant');
                    break;
                case 'quickActionResponse':
                    addMessage(message.response, 'assistant', {
                        timestamp: new Date().toLocaleTimeString(),
                        quickAction: message.action
                    });
                    break;
            }
        });

        // 回车发送消息
        document.getElementById('messageInput').addEventListener('keydown', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendMessage();
            }
        });

        // 请求初始上下文
        vscode.postMessage({ type: 'getContext' });
    </script>
</body>
</html>`;
    }
}
