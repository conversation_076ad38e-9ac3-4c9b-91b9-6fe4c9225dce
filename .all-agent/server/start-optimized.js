#!/usr/bin/env node

/**
 * 优化的 All-Agent 服务器启动脚本
 * 包含内存优化和监控
 */

const v8 = require('v8');
const fs = require('fs');

// 设置 V8 优化选项
v8.setFlagsFromString('--optimize-for-size');
v8.setFlagsFromString('--max-old-space-size=512');
v8.setFlagsFromString('--gc-interval=100');

// 内存监控
let memoryMonitorInterval;

function startMemoryMonitoring() {
    memoryMonitorInterval = setInterval(() => {
        const memUsage = process.memoryUsage();
        const heapUsed = memUsage.heapUsed / 1024 / 1024;
        const heapTotal = memUsage.heapTotal / 1024 / 1024;
        const utilization = (heapUsed / heapTotal * 100).toFixed(2);
        
        console.log(`[MEMORY] 堆内存: ${heapUsed.toFixed(2)}MB/${heapTotal.toFixed(2)}MB (${utilization}%)`);
        
        // 内存使用率过高时触发垃圾回收
        if (utilization > 80) {
            console.log('[MEMORY] 内存使用率过高，触发垃圾回收...');
            if (global.gc) {
                global.gc();
            }
        }
        
        // 记录内存使用历史
        const logEntry = {
            timestamp: new Date().toISOString(),
            memoryUsage: memUsage,
            utilization: parseFloat(utilization)
        };
        
        fs.appendFileSync('./logs/memory.log', JSON.stringify(logEntry) + '\n');
        
    }, 30000); // 每30秒检查一次
}

function stopMemoryMonitoring() {
    if (memoryMonitorInterval) {
        clearInterval(memoryMonitorInterval);
    }
}

// 优雅关闭
process.on('SIGINT', () => {
    console.log('\n[SHUTDOWN] 正在优雅关闭服务器...');
    stopMemoryMonitoring();
    process.exit(0);
});

process.on('SIGTERM', () => {
    console.log('\n[SHUTDOWN] 收到终止信号，正在关闭...');
    stopMemoryMonitoring();
    process.exit(0);
});

// 未捕获异常处理
process.on('uncaughtException', (error) => {
    console.error('[ERROR] 未捕获异常:', error);
    stopMemoryMonitoring();
    process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
    console.error('[ERROR] 未处理的 Promise 拒绝:', reason);
    // 不退出进程，只记录错误
});

// 启动内存监控
startMemoryMonitoring();

// 启动主应用
console.log('[STARTUP] 启动优化的 All-Agent 服务器...');
require('./app-new.js');
