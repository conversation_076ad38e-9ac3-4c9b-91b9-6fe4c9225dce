#!/usr/bin/env node

require('dotenv').config({ path: './.env' });
const fetch = require('node-fetch');

async function testMistralSmall() {
    console.log('🧪 测试 Mistral Small 模型...');
    console.log('API Key:', process.env.MISTRAL_API_KEY ? process.env.MISTRAL_API_KEY.substring(0, 10) + '...' : '未设置');
    console.log('Model:', process.env.MISTRAL_MODEL);
    
    try {
        const response = await fetch('https://api.mistral.ai/v1/chat/completions', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${process.env.MISTRAL_API_KEY}`
            },
            body: JSON.stringify({
                model: process.env.MISTRAL_MODEL,
                messages: [{ role: 'user', content: '你好，请简单介绍一下你自己' }],
                max_tokens: 50
            })
        });

        console.log('响应状态:', response.status);
        
        if (response.ok) {
            const data = await response.json();
            console.log('✅ Mistral Small API 成功!');
            console.log('响应:', data.choices[0].message.content);
        } else {
            const error = await response.text();
            console.log('❌ Mistral API 失败:', error);
        }
    } catch (error) {
        console.log('❌ 请求异常:', error.message);
    }
}

testMistralSmall();
