#!/usr/bin/env node

require('dotenv').config({ path: './.env' });
const fetch = require('node-fetch');

async function testDeepSeekDirect() {
    console.log('🧪 直接测试 DeepSeek API...');
    console.log('API Key:', process.env.DEEPSEEK_API_KEY ? process.env.DEEPSEEK_API_KEY.substring(0, 10) + '...' : '未设置');
    
    try {
        // 尝试不同的方法来解决 DNS 问题
        const urls = [
            'https://api.deepseek.ai/v1/chat/completions',
            'https://api.deepseek.com/v1/chat/completions'  // 尝试 .com 域名
        ];
        
        for (const url of urls) {
            console.log(`\n尝试连接: ${url}`);
            
            try {
                const response = await fetch(url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${process.env.DEEPSEEK_API_KEY}`
                    },
                    body: JSON.stringify({
                        model: 'deepseek-chat',
                        messages: [{ role: 'user', content: '你好' }],
                        max_tokens: 10
                    }),
                    timeout: 10000
                });

                console.log('响应状态:', response.status);
                
                if (response.ok) {
                    const data = await response.json();
                    console.log('✅ DeepSeek API 成功!');
                    console.log('响应:', data.choices[0].message.content);
                    return true;
                } else {
                    const error = await response.text();
                    console.log('❌ API 错误:', error);
                }
            } catch (error) {
                console.log('❌ 连接失败:', error.message);
            }
        }
        
        return false;
    } catch (error) {
        console.log('❌ 测试异常:', error.message);
        return false;
    }
}

testDeepSeekDirect();
