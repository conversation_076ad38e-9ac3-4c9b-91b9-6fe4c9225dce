apiVersion: v1
kind: Namespace
metadata:
  name: argocd
  labels:
    name: argocd
---
apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: all-agent-app
  namespace: argocd
  labels:
    app.kubernetes.io/name: all-agent
    app.kubernetes.io/component: gitops
  finalizers:
    - resources-finalizer.argocd.argoproj.io
spec:
  project: default
  source:
    repoURL: https://github.com/all-agent/all-agent.git
    targetRevision: main
    path: k8s
    helm:
      valueFiles:
        - values.yaml
        - values-production.yaml
  destination:
    server: https://kubernetes.default.svc
    namespace: all-agent
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
      allowEmpty: false
    syncOptions:
      - CreateNamespace=true
      - PrunePropagationPolicy=foreground
      - PruneLast=true
    retry:
      limit: 5
      backoff:
        duration: 5s
        factor: 2
        maxDuration: 3m
  revisionHistoryLimit: 10
---
apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: all-agent-monitoring
  namespace: argocd
  labels:
    app.kubernetes.io/name: all-agent
    app.kubernetes.io/component: monitoring
spec:
  project: default
  source:
    repoURL: https://github.com/all-agent/all-agent.git
    targetRevision: main
    path: observability
  destination:
    server: https://kubernetes.default.svc
    namespace: observability
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
    syncOptions:
      - CreateNamespace=true
---
apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: all-agent-istio
  namespace: argocd
  labels:
    app.kubernetes.io/name: all-agent
    app.kubernetes.io/component: service-mesh
spec:
  project: default
  source:
    repoURL: https://github.com/all-agent/all-agent.git
    targetRevision: main
    path: istio
  destination:
    server: https://kubernetes.default.svc
    namespace: all-agent
  syncPolicy:
    automated:
      prune: false
      selfHeal: true
    syncOptions:
      - CreateNamespace=true
---
apiVersion: argoproj.io/v1alpha1
kind: AppProject
metadata:
  name: all-agent-project
  namespace: argocd
  labels:
    app.kubernetes.io/name: all-agent
    app.kubernetes.io/component: project
spec:
  description: All-Agent GitOps Project
  sourceRepos:
  - 'https://github.com/all-agent/all-agent.git'
  - 'https://charts.bitnami.com/bitnami'
  - 'https://prometheus-community.github.io/helm-charts'
  - 'https://grafana.github.io/helm-charts'
  destinations:
  - namespace: all-agent
    server: https://kubernetes.default.svc
  - namespace: observability
    server: https://kubernetes.default.svc
  - namespace: istio-system
    server: https://kubernetes.default.svc
  - namespace: chaos-mesh
    server: https://kubernetes.default.svc
  - namespace: knative-serving
    server: https://kubernetes.default.svc
  clusterResourceWhitelist:
  - group: ''
    kind: Namespace
  - group: ''
    kind: ClusterRole
  - group: ''
    kind: ClusterRoleBinding
  - group: 'rbac.authorization.k8s.io'
    kind: ClusterRole
  - group: 'rbac.authorization.k8s.io'
    kind: ClusterRoleBinding
  - group: 'apiextensions.k8s.io'
    kind: CustomResourceDefinition
  - group: 'networking.istio.io'
    kind: '*'
  - group: 'security.istio.io'
    kind: '*'
  namespaceResourceWhitelist:
  - group: ''
    kind: '*'
  - group: 'apps'
    kind: '*'
  - group: 'networking.k8s.io'
    kind: '*'
  - group: 'extensions'
    kind: '*'
  - group: 'batch'
    kind: '*'
  - group: 'autoscaling'
    kind: '*'
  - group: 'policy'
    kind: '*'
  - group: 'metrics.k8s.io'
    kind: '*'
  roles:
  - name: admin
    description: Admin access to all-agent project
    policies:
    - p, proj:all-agent-project:admin, applications, *, all-agent-project/*, allow
    - p, proj:all-agent-project:admin, repositories, *, *, allow
    - p, proj:all-agent-project:admin, clusters, *, *, allow
    groups:
    - all-agent:admin
  - name: developer
    description: Developer access to all-agent project
    policies:
    - p, proj:all-agent-project:developer, applications, get, all-agent-project/*, allow
    - p, proj:all-agent-project:developer, applications, sync, all-agent-project/*, allow
    - p, proj:all-agent-project:developer, applications, action/*, all-agent-project/*, allow
    - p, proj:all-agent-project:developer, repositories, get, *, allow
    groups:
    - all-agent:developer
  - name: readonly
    description: Read-only access to all-agent project
    policies:
    - p, proj:all-agent-project:readonly, applications, get, all-agent-project/*, allow
    - p, proj:all-agent-project:readonly, repositories, get, *, allow
    groups:
    - all-agent:readonly
---
apiVersion: argoproj.io/v1alpha1
kind: ApplicationSet
metadata:
  name: all-agent-environments
  namespace: argocd
  labels:
    app.kubernetes.io/name: all-agent
    app.kubernetes.io/component: applicationset
spec:
  generators:
  - clusters:
      selector:
        matchLabels:
          environment: production
  - git:
      repoURL: https://github.com/all-agent/all-agent.git
      revision: main
      directories:
      - path: environments/*
  template:
    metadata:
      name: 'all-agent-{{path.basename}}'
      labels:
        environment: '{{path.basename}}'
    spec:
      project: all-agent-project
      source:
        repoURL: https://github.com/all-agent/all-agent.git
        targetRevision: main
        path: '{{path}}'
        helm:
          valueFiles:
          - values.yaml
          - 'values-{{path.basename}}.yaml'
      destination:
        server: '{{server}}'
        namespace: 'all-agent-{{path.basename}}'
      syncPolicy:
        automated:
          prune: true
          selfHeal: true
        syncOptions:
        - CreateNamespace=true
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: argocd-notifications-cm
  namespace: argocd
  labels:
    app.kubernetes.io/name: argocd-notifications
    app.kubernetes.io/part-of: argocd
data:
  service.slack: |
    token: $slack-token
    username: ArgoCD
    icon: ":argo:"
  service.email: |
    host: smtp.gmail.com
    port: 587
    from: $email-username
    username: $email-username
    password: $email-password
  template.app-deployed: |
    email:
      subject: Application {{.app.metadata.name}} is now running new version.
    message: |
      {{if eq .serviceType "slack"}}:white_check_mark:{{end}} Application {{.app.metadata.name}} is now running new version of {{.app.status.sync.revision}}.
  template.app-health-degraded: |
    email:
      subject: Application {{.app.metadata.name}} has degraded.
    message: |
      {{if eq .serviceType "slack"}}:exclamation:{{end}} Application {{.app.metadata.name}} has degraded.
      Application details: {{.context.argocdUrl}}/applications/{{.app.metadata.name}}.
  template.app-sync-failed: |
    email:
      subject: Application {{.app.metadata.name}} sync is failed.
    message: |
      {{if eq .serviceType "slack"}}:exclamation:{{end}} Application {{.app.metadata.name}} sync is failed.
      Application details: {{.context.argocdUrl}}/applications/{{.app.metadata.name}}.
      {{if ne .serviceType "slack"}}
      {{range $c := .app.status.conditions}}
          * {{$c.message}}
      {{end}}
      {{end}}
  trigger.on-deployed: |
    - description: Application is synced and healthy. Triggered once per commit.
      oncePer: app.status.sync.revision
      send:
      - app-deployed
      when: app.status.operationState.phase in ['Succeeded'] and app.status.health.status == 'Healthy'
  trigger.on-health-degraded: |
    - description: Application has degraded
      send:
      - app-health-degraded
      when: app.status.health.status == 'Degraded'
  trigger.on-sync-failed: |
    - description: Application syncing has failed
      send:
      - app-sync-failed
      when: app.status.operationState.phase in ['Error', 'Failed']
  subscriptions: |
    - recipients:
      - slack:all-agent-alerts
      - email:<EMAIL>
      triggers:
      - on-deployed
      - on-health-degraded
      - on-sync-failed
---
apiVersion: v1
kind: Secret
metadata:
  name: argocd-notifications-secret
  namespace: argocd
type: Opaque
stringData:
  slack-token: "xoxb-your-slack-bot-token"
  email-username: "<EMAIL>"
  email-password: "your-app-password"
