{"version": 3, "file": "client.js", "sourceRoot": "", "sources": ["../src/client.ts"], "names": [], "mappings": ";;;AAAA,iCAAiC;AACjC,iCAA0B;AAC1B,gCAAgC;AAEhC,MAAa,cAAc;IAKvB;QAHQ,OAAE,GAAqB,IAAI,CAAC;QAC5B,cAAS,GAAkB,IAAI,CAAC;QAGpC,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,WAAW,CAAC,CAAC;QAC9D,MAAM,IAAI,GAAG,MAAM,CAAC,GAAG,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC;QAC5C,IAAI,CAAC,OAAO,GAAG,oBAAoB,IAAI,EAAE,CAAC;IAC9C,CAAC;IAED,KAAK,CAAC,OAAO;QACT,IAAI;YACA,eAAe;YACf,MAAM,IAAI,CAAC,YAAY,EAAE,CAAC;YAE1B,kBAAkB;YAClB,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;YACjD,IAAI,CAAC,EAAE,GAAG,IAAI,SAAS,CAAC,KAAK,CAAC,CAAC;YAE/B,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;gBACnC,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE;oBACV,MAAM,CAAC,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC,CAAC;oBAC/C,OAAO;iBACV;gBAED,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,MAAM,EAAE,GAAG,EAAE;oBACpB,OAAO,CAAC,GAAG,CAAC,+BAA+B,CAAC,CAAC;oBAE7C,SAAS;oBACT,IAAI,IAAI,CAAC,EAAE,IAAI,IAAI,CAAC,SAAS,EAAE;wBAC3B,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC;4BACxB,IAAI,EAAE,MAAM;4BACZ,KAAK,EAAE,IAAI,CAAC,SAAS;yBACxB,CAAC,CAAC,CAAC;qBACP;oBAED,OAAO,EAAE,CAAC;gBACd,CAAC,CAAC,CAAC;gBAEH,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,KAAY,EAAE,EAAE;oBACjC,OAAO,CAAC,KAAK,CAAC,kBAAkB,EAAE,KAAK,CAAC,CAAC;oBACzC,MAAM,CAAC,KAAK,CAAC,CAAC;gBAClB,CAAC,CAAC,CAAC;gBAEH,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,OAAO,EAAE,GAAG,EAAE;oBACrB,OAAO,CAAC,GAAG,CAAC,oCAAoC,CAAC,CAAC;oBAClD,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC;gBACnB,CAAC,CAAC,CAAC;YACP,CAAC,CAAC,CAAC;SACN;QAAC,OAAO,KAAK,EAAE;YACZ,MAAM,IAAI,KAAK,CAAC,0CAA0C,KAAK,EAAE,CAAC,CAAC;SACtE;IACL,CAAC;IAED,KAAK,CAAC,UAAU;QACZ,IAAI,IAAI,CAAC,EAAE,EAAE;YACT,IAAI,CAAC,EAAE,CAAC,KAAK,EAAE,CAAC;YAChB,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC;SAClB;IACL,CAAC;IAED,KAAK,CAAC,WAAW,CAAC,OAAe;QAC7B,IAAI;YACA,MAAM,QAAQ,GAAG,MAAM,eAAK,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,OAAO,WAAW,EAAE;gBAC1D,OAAO,EAAE,OAAO;gBAChB,SAAS,EAAE,UAAU,CAAC,eAAe;aACxC,EAAE;gBACC,OAAO,EAAE;oBACL,eAAe,EAAE,UAAU,IAAI,CAAC,SAAS,EAAE;oBAC3C,cAAc,EAAE,kBAAkB;iBACrC;aACJ,CAAC,CAAC;YAEH,OAAO,QAAQ,CAAC,IAAI,CAAC,QAAQ,IAAI,sBAAsB,CAAC;SAC3D;QAAC,OAAO,KAAK,EAAE;YACZ,MAAM,IAAI,KAAK,CAAC,2BAA2B,KAAK,EAAE,CAAC,CAAC;SACvD;IACL,CAAC;IAED,KAAK,CAAC,cAAc,CAAC,WAAmB;QACpC,IAAI;YACA,MAAM,QAAQ,GAAG,MAAM,eAAK,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,OAAO,cAAc,EAAE;gBAC7D,IAAI,EAAE,WAAW;gBACjB,OAAO,EAAE;oBACL,YAAY,EAAE,IAAI;oBAClB,mBAAmB,EAAE,IAAI;oBACzB,gBAAgB,EAAE,IAAI;iBACzB;aACJ,EAAE;gBACC,OAAO,EAAE;oBACL,eAAe,EAAE,UAAU,IAAI,CAAC,SAAS,EAAE;oBAC3C,cAAc,EAAE,kBAAkB;iBACrC;aACJ,CAAC,CAAC;YAEH,OAAO,QAAQ,CAAC,IAAI,CAAC;SACxB;QAAC,OAAO,KAAK,EAAE;YACZ,MAAM,IAAI,KAAK,CAAC,8BAA8B,KAAK,EAAE,CAAC,CAAC;SAC1D;IACL,CAAC;IAED,KAAK,CAAC,YAAY,CAAC,OAAe,EAAE,MAAc;QAC9C,IAAI;YACA,MAAM,QAAQ,GAAG,MAAM,eAAK,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,OAAO,eAAe,EAAE;gBAC9D,OAAO,EAAE,OAAO;gBAChB,MAAM,EAAE,MAAM;gBACd,IAAI,EAAE,MAAM;aACf,EAAE;gBACC,OAAO,EAAE;oBACL,eAAe,EAAE,UAAU,IAAI,CAAC,SAAS,EAAE;oBAC3C,cAAc,EAAE,kBAAkB;iBACrC;aACJ,CAAC,CAAC;YAEH,OAAO,QAAQ,CAAC,IAAI,CAAC,IAAI,IAAI,QAAQ,CAAC,IAAI,CAAC,MAAM,IAAI,mBAAmB,CAAC;SAC5E;QAAC,OAAO,KAAK,EAAE;YACZ,MAAM,IAAI,KAAK,CAAC,4BAA4B,KAAK,EAAE,CAAC,CAAC;SACxD;IACL,CAAC;IAED,KAAK,CAAC,SAAS;QACX,IAAI;YACA,MAAM,QAAQ,GAAG,MAAM,eAAK,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,OAAO,aAAa,EAAE;gBAC3D,OAAO,EAAE;oBACL,eAAe,EAAE,UAAU,IAAI,CAAC,SAAS,EAAE;iBAC9C;aACJ,CAAC,CAAC;YAEH,OAAO,QAAQ,CAAC,IAAI,CAAC,MAAM,IAAI,EAAE,CAAC;SACrC;QAAC,OAAO,KAAK,EAAE;YACZ,OAAO,CAAC,KAAK,CAAC,uBAAuB,EAAE,KAAK,CAAC,CAAC;YAC9C,OAAO,EAAE,CAAC;SACb;IACL,CAAC;IAED,KAAK,CAAC,QAAQ;QACV,IAAI;YACA,MAAM,QAAQ,GAAG,MAAM,eAAK,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,OAAO,YAAY,EAAE;gBAC1D,OAAO,EAAE;oBACL,eAAe,EAAE,UAAU,IAAI,CAAC,SAAS,EAAE;iBAC9C;aACJ,CAAC,CAAC;YAEH,OAAO,QAAQ,CAAC,IAAI,CAAC,KAAK,IAAI,EAAE,CAAC;SACpC;QAAC,OAAO,KAAK,EAAE;YACZ,OAAO,CAAC,KAAK,CAAC,sBAAsB,EAAE,KAAK,CAAC,CAAC;YAC7C,OAAO,EAAE,CAAC;SACb;IACL,CAAC;IAEO,KAAK,CAAC,YAAY;QACtB,IAAI;YACA,gBAAgB;YAChB,MAAM,QAAQ,GAAG,MAAM,eAAK,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,OAAO,aAAa,EAAE;gBAC5D,KAAK,EAAE,oBAAoB;gBAC3B,QAAQ,EAAE,aAAa;aAC1B,CAAC,CAAC;YAEH,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC;SACxC;QAAC,OAAO,KAAK,EAAE;YACZ,qBAAqB;YACrB,IAAI;gBACA,MAAM,gBAAgB,GAAG,MAAM,eAAK,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,OAAO,gBAAgB,EAAE;oBACvE,QAAQ,EAAE,kBAAkB;oBAC5B,KAAK,EAAE,wBAAwB;oBAC/B,QAAQ,EAAE,cAAc;iBAC3B,CAAC,CAAC;gBAEH,IAAI,gBAAgB,CAAC,IAAI,CAAC,KAAK,EAAE;oBAC7B,IAAI,CAAC,SAAS,GAAG,gBAAgB,CAAC,IAAI,CAAC,KAAK,CAAC;iBAChD;qBAAM;oBACH,UAAU;oBACV,MAAM,aAAa,GAAG,MAAM,eAAK,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,OAAO,aAAa,EAAE;wBACjE,QAAQ,EAAE,kBAAkB;wBAC5B,QAAQ,EAAE,cAAc;qBAC3B,CAAC,CAAC;oBACH,IAAI,CAAC,SAAS,GAAG,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC;iBAC7C;aACJ;YAAC,OAAO,SAAS,EAAE;gBAChB,MAAM,IAAI,KAAK,CAAC,0BAA0B,SAAS,EAAE,CAAC,CAAC;aAC1D;SACJ;IACL,CAAC;IAED,WAAW;QACP,OAAO,IAAI,CAAC,EAAE,KAAK,IAAI,IAAI,IAAI,CAAC,EAAE,CAAC,UAAU,KAAK,SAAS,CAAC,IAAI,CAAC;IACrE,CAAC;IAED,mBAAmB;QACf,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,WAAW,CAAC,CAAC;QAC9D,MAAM,IAAI,GAAG,MAAM,CAAC,GAAG,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC;QAC5C,IAAI,CAAC,OAAO,GAAG,oBAAoB,IAAI,EAAE,CAAC;QAE1C,mBAAmB;QACnB,IAAI,IAAI,CAAC,WAAW,EAAE,EAAE;YACpB,IAAI,CAAC,UAAU,EAAE,CAAC,IAAI,CAAC,GAAG,EAAE;gBACxB,IAAI,CAAC,OAAO,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;oBACzB,OAAO,CAAC,KAAK,CAAC,sBAAsB,EAAE,KAAK,CAAC,CAAC;gBACjD,CAAC,CAAC,CAAC;YACP,CAAC,CAAC,CAAC;SACN;IACL,CAAC;CACJ;AA3MD,wCA2MC"}