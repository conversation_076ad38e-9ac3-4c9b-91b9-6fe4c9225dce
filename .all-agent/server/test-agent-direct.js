#!/usr/bin/env node

/**
 * 直接测试 Agent 消息处理
 */

require('dotenv').config({ path: '../.env' });

// 导入必要的模块
const AnalyzerAgent = require('./agents/AnalyzerAgent');
const PlannerAgent = require('./agents/PlannerAgent');
const ExecutorAgent = require('./agents/ExecutorAgent');
const MockLLMService = require('./core/MockLLMService');

// 简单的日志器
const logger = {
  info: (msg, data) => console.log(`[INFO] ${msg}`, data || ''),
  warn: (msg, data) => console.warn(`[WARN] ${msg}`, data || ''),
  error: (msg, data) => console.error(`[ERROR] ${msg}`, data || ''),
  debug: (msg, data) => console.debug(`[DEBUG] ${msg}`, data || '')
};

// 测试配置
const agentConfigs = {
  analyzer: {
    name: '分析 Agent',
    type: 'analyzer',
    capabilities: ['analysis', 'code-review', 'architecture'],
    max_concurrent_tasks: 5
  },
  planner: {
    name: '规划 Agent',
    type: 'planner',
    capabilities: ['planning', 'task-breakdown', 'estimation'],
    max_concurrent_tasks: 3
  },
  executor: {
    name: '执行 Agent',
    type: 'executor',
    capabilities: ['code-generation', 'file-operations', 'deployment'],
    max_concurrent_tasks: 2
  }
};

async function testAgent(AgentClass, config, llmService, testMessage) {
  try {
    console.log(`\n🧪 测试 ${config.name}...`);
    
    // 创建 Agent 实例
    const agent = new AgentClass(config, llmService);
    
    // 初始化
    await agent.initialize();
    console.log(`✅ ${config.name} 初始化成功`);
    
    // 测试消息处理
    console.log(`📤 发送测试消息: "${testMessage}"`);
    const startTime = Date.now();
    
    const response = await agent.onProcessMessage(testMessage, {
      userId: 1,
      timestamp: new Date().toISOString(),
      test: true
    });
    
    const duration = Date.now() - startTime;
    
    console.log(`✅ ${config.name} 响应成功 (${duration}ms)`);
    console.log(`📝 响应内容: ${response.substring(0, 200)}...`);
    
    return { success: true, response, duration };
    
  } catch (error) {
    console.error(`❌ ${config.name} 测试失败:`, error.message);
    console.error('错误堆栈:', error.stack);
    return { success: false, error: error.message };
  }
}

async function runTests() {
  console.log('🚀 开始直接测试 Agent 消息处理...\n');
  
  // 创建模拟 LLM 服务
  const mockLLMService = new MockLLMService(logger);
  console.log('✅ 模拟 LLM 服务已创建');
  
  const testMessage = "你好！请介绍一下你自己，并告诉我你能做什么。";
  const results = [];
  
  // 测试分析 Agent
  const analyzerResult = await testAgent(
    AnalyzerAgent, 
    agentConfigs.analyzer, 
    mockLLMService, 
    testMessage
  );
  results.push({ agent: '分析 Agent', ...analyzerResult });
  
  // 测试规划 Agent
  const plannerResult = await testAgent(
    PlannerAgent, 
    agentConfigs.planner, 
    mockLLMService, 
    testMessage
  );
  results.push({ agent: '规划 Agent', ...plannerResult });
  
  // 测试执行 Agent
  const executorResult = await testAgent(
    ExecutorAgent, 
    agentConfigs.executor, 
    mockLLMService, 
    testMessage
  );
  results.push({ agent: '执行 Agent', ...executorResult });
  
  // 汇总结果
  console.log('\n📊 测试结果汇总:');
  console.log('='.repeat(50));
  
  const successful = results.filter(r => r.success);
  const failed = results.filter(r => !r.success);
  
  console.log(`✅ 成功: ${successful.length} 个 Agent`);
  successful.forEach(r => {
    console.log(`  - ${r.agent}: ${r.duration}ms`);
  });
  
  console.log(`❌ 失败: ${failed.length} 个 Agent`);
  failed.forEach(r => {
    console.log(`  - ${r.agent}: ${r.error}`);
  });
  
  if (successful.length === 3) {
    console.log('\n🎉 所有 Agent 都工作正常！');
    console.log('问题可能在于 WebSocket 消息路由或 AgentManager 集成。');
  } else {
    console.log('\n❌ 部分 Agent 存在问题！');
    console.log('需要检查 Agent 实现和依赖。');
  }
  
  return successful.length === 3;
}

// 运行测试
if (require.main === module) {
  runTests().catch(error => {
    console.error('测试过程中发生错误:', error);
    process.exit(1);
  });
}

module.exports = { runTests };
