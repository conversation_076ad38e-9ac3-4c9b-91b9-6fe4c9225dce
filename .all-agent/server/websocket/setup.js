/**
 * WebSocket 设置模块
 */
function setupWebSocket(io, { authService, agentManager, logger }) {
  // WebSocket 认证中间件
  io.use(async (socket, next) => {
    try {
      const token = socket.handshake.auth.token;
      if (token) {
        const user = await authService.verifyToken(token);
        socket.user = user;
      }
      next();
    } catch (error) {
      next(new Error('Authentication failed'));
    }
  });

  // 连接处理
  io.on('connection', (socket) => {
    if (logger) {
      logger.info(`WebSocket client connected: ${socket.id}`, {
        userId: socket.user?.id,
        ip: socket.handshake.address
      });
    }

    // 加入用户房间（如果已认证）
    if (socket.user) {
      socket.join(`user:${socket.user.id}`);
    }

    // 任务状态更新
    socket.on('subscribe:tasks', (data) => {
      if (socket.user) {
        socket.join(`tasks:${socket.user.id}`);
        if (logger) {
          logger.info(`User ${socket.user.id} subscribed to task updates`);
        }
      }
    });

    // Agent 状态更新
    socket.on('subscribe:agents', () => {
      socket.join('agents:status');
      if (logger) {
        logger.info(`Client ${socket.id} subscribed to agent updates`);
      }
    });

    // 系统状态更新
    socket.on('subscribe:system', () => {
      socket.join('system:status');
      if (logger) {
        logger.info(`Client ${socket.id} subscribed to system updates`);
      }
    });

    // 断开连接处理
    socket.on('disconnect', (reason) => {
      if (logger) {
        logger.info(`WebSocket client disconnected: ${socket.id}`, {
          reason,
          userId: socket.user?.id
        });
      }
    });

    // 错误处理
    socket.on('error', (error) => {
      if (logger) {
        logger.error(`WebSocket error for client ${socket.id}:`, error);
      }
    });
  });

  // 定期广播系统状态
  setInterval(() => {
    const systemStatus = {
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      agents: agentManager ? agentManager.getAgentStatus() : { active: 0 }
    };

    io.to('system:status').emit('system:update', systemStatus);
  }, 30000); // 每30秒更新一次
}

module.exports = {
  setupWebSocket
};
