/**
 * 简化的追踪中间件
 * 避免复杂的 OpenTelemetry 依赖问题
 */
class SimpleTracingMiddleware {
    constructor(options = {}) {
        this.options = {
            serviceName: options.serviceName || 'all-agent',
            serviceVersion: options.serviceVersion || '1.0.0',
            environment: options.environment || process.env.NODE_ENV || 'development',
            enableLogs: options.enableLogs !== false,
            ...options
        };

        this.initialized = true;
        this.requestCount = 0;
        this.errorCount = 0;
        this.stats = {
            totalRequests: 0,
            totalErrors: 0,
            averageResponseTime: 0,
            requestsByMethod: {},
            requestsByStatus: {}
        };
    }

    /**
     * 初始化追踪系统 - 简化版本
     */
    async initialize() {
        console.log(`✅ 简化追踪系统已初始化: ${this.options.serviceName}`);
        return Promise.resolve();
    }

    /**
     * Express 中间件 - 简化版本
     */
    middleware() {
        return (req, res, next) => {
            const startTime = Date.now();
            
            // 生成请求 ID
            const requestId = req.headers['x-request-id'] || this.generateRequestId();
            req.requestId = requestId;
            res.setHeader('x-request-id', requestId);

            // 简化的追踪信息
            req.traceId = this.generateTraceId();
            req.spanId = this.generateSpanId();

            // 增加请求计数
            this.requestCount++;
            this.stats.totalRequests++;

            // 统计请求方法
            this.stats.requestsByMethod[req.method] = (this.stats.requestsByMethod[req.method] || 0) + 1;

            // 监听响应结束
            res.on('finish', () => {
                const duration = Date.now() - startTime;
                
                // 更新平均响应时间
                this.updateAverageResponseTime(duration);

                // 统计状态码
                const statusCode = res.statusCode;
                this.stats.requestsByStatus[statusCode] = (this.stats.requestsByStatus[statusCode] || 0) + 1;

                // 记录错误
                if (statusCode >= 400) {
                    this.errorCount++;
                    this.stats.totalErrors++;
                }

                // 日志记录
                if (this.options.enableLogs) {
                    const logLevel = statusCode >= 400 ? 'ERROR' : 'INFO';
                    console.log(`[${new Date().toISOString()}] [${logLevel}] ${req.method} ${req.path} - ${statusCode} - ${duration}ms - ${requestId}`);
                }
            });

            next();
        };
    }

    /**
     * 更新平均响应时间
     */
    updateAverageResponseTime(duration) {
        const currentAvg = this.stats.averageResponseTime;
        const totalRequests = this.stats.totalRequests;
        this.stats.averageResponseTime = ((currentAvg * (totalRequests - 1)) + duration) / totalRequests;
    }

    /**
     * 创建子 span - 简化版本
     */
    createSpan(name, options = {}) {
        return {
            name,
            spanId: this.generateSpanId(),
            startTime: Date.now(),
            attributes: {},
            setAttributes: (attrs) => {
                Object.assign(this.attributes, attrs);
            },
            recordException: (error) => {
                console.error(`[SPAN:${name}] Exception:`, error.message);
            },
            setStatus: (status) => {
                // 简化的状态设置
            },
            end: () => {
                const duration = Date.now() - this.startTime;
                console.log(`[SPAN:${name}] Duration: ${duration}ms`);
            }
        };
    }

    /**
     * 包装异步函数进行追踪 - 简化版本
     */
    traceAsync(name, fn, options = {}) {
        return async (...args) => {
            const startTime = Date.now();
            
            try {
                const result = await fn(...args);
                const duration = Date.now() - startTime;
                
                if (this.options.enableLogs) {
                    console.log(`[TRACE:${name}] Success - ${duration}ms`);
                }
                
                return result;
                
            } catch (error) {
                const duration = Date.now() - startTime;
                
                console.error(`[TRACE:${name}] Error - ${duration}ms:`, error.message);
                this.errorCount++;
                this.stats.totalErrors++;
                
                throw error;
            }
        };
    }

    /**
     * 记录数据库查询 - 简化版本
     */
    recordDatabaseQuery(operation, table, duration) {
        if (this.options.enableLogs) {
            console.log(`[DB] ${operation} on ${table} - ${duration}ms`);
        }
    }

    /**
     * 记录缓存操作 - 简化版本
     */
    recordCacheOperation(operation, hit, duration) {
        if (this.options.enableLogs) {
            console.log(`[CACHE] ${operation} - ${hit ? 'HIT' : 'MISS'} - ${duration}ms`);
        }
    }

    /**
     * 记录自定义事件 - 简化版本
     */
    recordEvent(name, attributes = {}) {
        if (this.options.enableLogs) {
            console.log(`[EVENT] ${name}:`, attributes);
        }
    }

    /**
     * 添加 span 属性 - 简化版本
     */
    addAttributes(attributes) {
        // 简化实现，仅记录日志
        if (this.options.enableLogs) {
            console.log(`[ATTRIBUTES]`, attributes);
        }
    }

    /**
     * 生成请求 ID
     */
    generateRequestId() {
        return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    /**
     * 生成追踪 ID
     */
    generateTraceId() {
        return `trace_${Date.now()}_${Math.random().toString(36).substr(2, 16)}`;
    }

    /**
     * 生成 Span ID
     */
    generateSpanId() {
        return `span_${Date.now()}_${Math.random().toString(36).substr(2, 8)}`;
    }

    /**
     * 获取当前追踪上下文 - 简化版本
     */
    getCurrentContext() {
        return {
            traceId: this.generateTraceId(),
            spanId: this.generateSpanId(),
            traceFlags: 1
        };
    }

    /**
     * 注入追踪头到 HTTP 请求 - 简化版本
     */
    injectHeaders(headers = {}) {
        headers['x-trace-id'] = this.generateTraceId();
        headers['x-span-id'] = this.generateSpanId();
        return headers;
    }

    /**
     * 从 HTTP 请求提取追踪上下文 - 简化版本
     */
    extractContext(headers) {
        return {
            traceId: headers['x-trace-id'] || this.generateTraceId(),
            spanId: headers['x-span-id'] || this.generateSpanId()
        };
    }

    /**
     * 关闭追踪系统 - 简化版本
     */
    async shutdown() {
        console.log('✅ 简化追踪系统已关闭');
        return Promise.resolve();
    }

    /**
     * 获取追踪统计
     */
    getStats() {
        return {
            serviceName: this.options.serviceName,
            serviceVersion: this.options.serviceVersion,
            environment: this.options.environment,
            initialized: this.initialized,
            requestCount: this.requestCount,
            errorCount: this.errorCount,
            stats: this.stats
        };
    }

    /**
     * 重置统计信息
     */
    resetStats() {
        this.requestCount = 0;
        this.errorCount = 0;
        this.stats = {
            totalRequests: 0,
            totalErrors: 0,
            averageResponseTime: 0,
            requestsByMethod: {},
            requestsByStatus: {}
        };
    }

    /**
     * 获取健康状态
     */
    getHealthStatus() {
        const errorRate = this.stats.totalRequests > 0 ? 
            (this.stats.totalErrors / this.stats.totalRequests) * 100 : 0;

        return {
            healthy: errorRate < 10, // 错误率低于10%认为健康
            errorRate: errorRate.toFixed(2) + '%',
            totalRequests: this.stats.totalRequests,
            totalErrors: this.stats.totalErrors,
            averageResponseTime: Math.round(this.stats.averageResponseTime) + 'ms'
        };
    }
}

module.exports = SimpleTracingMiddleware;
