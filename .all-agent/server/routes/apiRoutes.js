const express = require('express');
const { body, param, query, validationResult } = require('express-validator');

/**
 * API 路由模块
 */
class APIRoutes {
  constructor({ authService, agentManager, llmService, logger }) {
    this.authService = authService;
    this.agentManager = agentManager;
    this.llmService = llmService;
    this.logger = logger;
    this.router = express.Router();
    this.setupRoutes();
  }

  setupRoutes() {
    // 项目分析 API
    this.router.post('/analyze',
      this.authenticateToken.bind(this),
      [
        body('projectPath').optional().isString(),
        body('options').optional().isObject()
      ],
      this.analyzeProject.bind(this)
    );

    // Agent 状态 API
    this.router.get('/agents', this.getAgents.bind(this));

    // 任务提交 API
    this.router.post('/tasks',
      this.authenticateToken.bind(this),
      [
        body('agentType').isString().notEmpty(),
        body('action').isString().notEmpty(),
        body('input').notEmpty(),
        body('options').optional().isObject()
      ],
      this.submitTask.bind(this)
    );

    // 任务状态查询 API
    this.router.get('/tasks/:taskId',
      [param('taskId').isUUID()],
      this.getTaskStatus.bind(this)
    );

    // 代码生成 API
    this.router.post('/generate',
      [
        body('template').isString().notEmpty(),
        body('parameters').isObject(),
        body('context').optional().isObject()
      ],
      this.generateCode.bind(this)
    );

    // AI 模型测试接口
    this.router.post('/test-ai-model',
      [
        body('action').isIn(['test_connection', 'test_prompt', 'check_environment']),
        body('config').optional().isObject(),
        body('prompt').optional().isString()
      ],
      this.testAIModel.bind(this)
    );

    // 获取项目配置
    this.router.get('/config', this.getProjectConfig.bind(this));

    // 用户任务历史
    this.router.get('/tasks',
      this.authenticateToken.bind(this),
      [
        query('limit').optional().isInt({ min: 1, max: 100 }),
        query('offset').optional().isInt({ min: 0 })
      ],
      this.getUserTasks.bind(this)
    );
  }

  /**
   * 认证中间件
   */
  async authenticateToken(req, res, next) {
    try {
      const token = this.extractToken(req);
      if (!token) {
        return res.status(401).json({
          success: false,
          error: 'Authentication required'
        });
      }

      const user = await this.authService.verifyToken(token);
      req.user = user;
      req.token = token;
      next();
    } catch (error) {
      res.status(401).json({
        success: false,
        error: error.message
      });
    }
  }

  /**
   * 项目分析
   */
  async analyzeProject(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          error: 'Validation failed',
          details: errors.array()
        });
      }

      const { projectPath, options = {} } = req.body;
      const analysisPath = projectPath || process.cwd();

      // 模拟项目分析（实际实现需要 ProjectAnalyzer）
      const analysis = {
        summary: {
          totalFiles: 150,
          totalLines: 12500,
          languages: ['JavaScript', 'TypeScript', 'JSON'],
          frameworks: ['Node.js', 'Express.js', 'React']
        },
        structure: {
          directories: 25,
          files: 150,
          depth: 4
        },
        quality: {
          score: 85,
          issues: 12,
          warnings: 5
        },
        dependencies: {
          total: 45,
          outdated: 3,
          vulnerable: 1
        },
        timestamp: new Date().toISOString(),
        projectPath: analysisPath
      };

      res.json({
        success: true,
        data: analysis,
        message: 'Project analysis completed'
      });
    } catch (error) {
      this.logger.error('Project analysis failed:', error);
      res.status(500).json({
        success: false,
        error: error.message
      });
    }
  }

  /**
   * 获取 Agent 状态
   */
  async getAgents(req, res) {
    try {
      const agents = this.agentManager ? this.agentManager.getAllAgents() : [
        {
          id: 'analyzer-001',
          type: 'analyzer',
          status: 'active',
          lastActive: new Date().toISOString(),
          tasksCompleted: 125
        },
        {
          id: 'planner-001',
          type: 'planner',
          status: 'active',
          lastActive: new Date().toISOString(),
          tasksCompleted: 89
        },
        {
          id: 'executor-001',
          type: 'executor',
          status: 'idle',
          lastActive: new Date(Date.now() - 300000).toISOString(),
          tasksCompleted: 67
        }
      ];

      res.json({
        success: true,
        data: agents,
        message: 'Agent status retrieved'
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        error: error.message
      });
    }
  }

  /**
   * 提交任务
   */
  async submitTask(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          error: 'Validation failed',
          details: errors.array()
        });
      }

      const { agentType, action, input, options = {} } = req.body;
      
      // 生成任务 ID
      const taskId = require('crypto').randomUUID();
      
      // 模拟任务提交
      const task = {
        id: taskId,
        agentType,
        action,
        input,
        options,
        userId: req.user.id,
        status: 'pending',
        createdAt: new Date().toISOString(),
        estimatedDuration: '2-5 minutes'
      };

      res.status(202).json({
        success: true,
        data: { taskId, task },
        message: 'Task submitted successfully'
      });
    } catch (error) {
      this.logger.error('Task submission failed:', error);
      res.status(500).json({
        success: false,
        error: error.message
      });
    }
  }

  /**
   * 获取任务状态
   */
  async getTaskStatus(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          error: 'Invalid task ID format'
        });
      }

      const { taskId } = req.params;
      
      // 模拟任务状态
      const task = {
        id: taskId,
        status: 'completed',
        progress: 100,
        result: {
          success: true,
          output: 'Task completed successfully',
          artifacts: []
        },
        createdAt: new Date(Date.now() - 300000).toISOString(),
        completedAt: new Date().toISOString(),
        duration: 285000
      };

      res.json({
        success: true,
        data: task
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        error: error.message
      });
    }
  }

  /**
   * 代码生成
   */
  async generateCode(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          error: 'Validation failed',
          details: errors.array()
        });
      }

      const { template, parameters, context = {} } = req.body;
      
      // 模拟代码生成
      const result = {
        template,
        parameters,
        generatedCode: `// Generated code for template: ${template}\nconst example = ${JSON.stringify(parameters, null, 2)};`,
        metadata: {
          language: 'javascript',
          framework: context.framework || 'vanilla',
          generatedAt: new Date().toISOString()
        }
      };

      res.json({
        success: true,
        data: result,
        message: 'Code generated successfully'
      });
    } catch (error) {
      this.logger.error('Code generation failed:', error);
      res.status(500).json({
        success: false,
        error: error.message
      });
    }
  }

  /**
   * AI 模型测试
   */
  async testAIModel(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          error: 'Validation failed',
          details: errors.array()
        });
      }

      const { action, config, prompt } = req.body;

      let result;
      switch (action) {
        case 'test_connection':
          result = {
            success: true,
            provider: config?.provider || 'mock',
            latency: Math.floor(Math.random() * 100) + 50,
            status: 'connected'
          };
          break;

        case 'test_prompt':
          result = {
            success: true,
            prompt,
            response: 'This is a mock AI response for testing purposes.',
            tokens: {
              input: prompt ? prompt.split(' ').length : 0,
              output: 12
            }
          };
          break;

        case 'check_environment':
          result = {
            success: true,
            environment: {
              nodeVersion: process.version,
              platform: process.platform,
              memory: process.memoryUsage(),
              uptime: process.uptime()
            },
            apiKeys: {
              deepseek: !!process.env.DEEPSEEK_API_KEY,
              mistral: !!process.env.MISTRAL_API_KEY,
              openai: !!process.env.OPENAI_API_KEY,
              anthropic: !!process.env.ANTHROPIC_API_KEY
            }
          };
          break;

        default:
          return res.status(400).json({
            success: false,
            error: `Unsupported test action: ${action}`
          });
      }

      res.json({
        success: true,
        data: result,
        message: 'AI model test completed'
      });
    } catch (error) {
      this.logger.error('AI model test failed:', error);
      res.status(500).json({
        success: false,
        error: error.message
      });
    }
  }

  /**
   * 获取项目配置
   */
  async getProjectConfig(req, res) {
    try {
      const fs = require('fs').promises;
      const path = require('path');
      
      try {
        const configPath = path.join(process.cwd(), 'agents.json');
        const configContent = await fs.readFile(configPath, 'utf8');
        const config = JSON.parse(configContent);
        
        res.json({
          success: true,
          data: config,
          message: 'Project configuration retrieved'
        });
      } catch (fileError) {
        // 返回默认配置
        const defaultConfig = {
          version: '1.0.0',
          agents: {
            analyzer: { enabled: true },
            planner: { enabled: true },
            executor: { enabled: true }
          },
          settings: {
            autoSave: true,
            debugMode: false
          }
        };
        
        res.json({
          success: true,
          data: defaultConfig,
          message: 'Default configuration returned'
        });
      }
    } catch (error) {
      res.status(500).json({
        success: false,
        error: 'Failed to read project configuration'
      });
    }
  }

  /**
   * 获取用户任务历史
   */
  async getUserTasks(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          error: 'Validation failed',
          details: errors.array()
        });
      }

      const limit = parseInt(req.query.limit) || 20;
      const offset = parseInt(req.query.offset) || 0;

      // 模拟用户任务历史
      const tasks = Array.from({ length: limit }, (_, i) => ({
        id: require('crypto').randomUUID(),
        agentType: ['analyzer', 'planner', 'executor'][i % 3],
        action: ['analyze', 'plan', 'execute'][i % 3],
        status: ['completed', 'failed', 'pending'][i % 3],
        createdAt: new Date(Date.now() - (i * 3600000)).toISOString(),
        duration: Math.floor(Math.random() * 300000) + 30000
      }));

      res.json({
        success: true,
        data: {
          tasks,
          pagination: {
            limit,
            offset,
            total: 150,
            hasMore: offset + limit < 150
          }
        },
        message: 'User tasks retrieved'
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        error: error.message
      });
    }
  }

  /**
   * 从请求中提取 JWT 令牌
   */
  extractToken(req) {
    const authHeader = req.headers.authorization;
    if (authHeader && authHeader.startsWith('Bearer ')) {
      return authHeader.substring(7);
    }
    return null;
  }

  /**
   * 获取路由器实例
   */
  getRouter() {
    return this.router;
  }
}

module.exports = APIRoutes;
