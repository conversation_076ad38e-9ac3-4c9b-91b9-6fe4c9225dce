{"version": 3, "file": "server.js", "sourceRoot": "", "sources": ["../src/server.ts"], "names": [], "mappings": ";;;AAAA,iCAAiC;AACjC,iDAAoD;AACpD,6BAA6B;AAC7B,yBAAyB;AAEzB,MAAa,cAAc;IAKvB,YAAY,OAAgC;QAJpC,YAAO,GAAwB,IAAI,CAAC;QACpC,cAAS,GAAG,KAAK,CAAC;QAItB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;IAC3B,CAAC;IAED,KAAK,CAAC,KAAK;QACP,IAAI,IAAI,CAAC,SAAS,EAAE;YAChB,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;SAChD;QAED,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,WAAW,CAAC,CAAC;QAC9D,MAAM,IAAI,GAAG,MAAM,CAAC,GAAG,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC;QAE5C,qBAAqB;QACrB,MAAM,UAAU,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;QACzC,IAAI,CAAC,UAAU,EAAE;YACb,MAAM,IAAI,KAAK,CAAC,uFAAuF,CAAC,CAAC;SAC5G;QAED,SAAS;QACT,MAAM,GAAG,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAErC,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACnC,IAAI;gBACA,UAAU;gBACV,IAAI,CAAC,OAAO,GAAG,IAAA,qBAAK,EAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE;oBACrC,GAAG,EAAE,UAAU;oBACf,GAAG,EAAE,EAAE,GAAG,OAAO,CAAC,GAAG,EAAE,GAAG,GAAG,EAAE;oBAC/B,KAAK,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;iBAClC,CAAC,CAAC;gBAEH,OAAO;gBACP,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,EAAE;oBACrC,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;oBAC/B,OAAO,CAAC,GAAG,CAAC,oBAAoB,EAAE,MAAM,CAAC,CAAC;oBAE1C,cAAc;oBACd,IAAI,MAAM,CAAC,QAAQ,CAAC,uCAAuC,CAAC,EAAE;wBAC1D,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;wBACtB,OAAO,EAAE,CAAC;qBACb;gBACL,CAAC,CAAC,CAAC;gBAEH,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,EAAE;oBACrC,OAAO,CAAC,KAAK,CAAC,0BAA0B,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;gBAC/D,CAAC,CAAC,CAAC;gBAEH,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,KAAK,EAAE,EAAE;oBAC/B,OAAO,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC,CAAC;oBACzD,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;oBACvB,MAAM,CAAC,KAAK,CAAC,CAAC;gBAClB,CAAC,CAAC,CAAC;gBAEH,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,EAAE;oBAC7B,OAAO,CAAC,GAAG,CAAC,+CAA+C,IAAI,EAAE,CAAC,CAAC;oBACnE,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;oBACvB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;gBACxB,CAAC,CAAC,CAAC;gBAEH,OAAO;gBACP,UAAU,CAAC,GAAG,EAAE;oBACZ,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;wBACjB,MAAM,CAAC,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC,CAAC;qBAC/C;gBACL,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,QAAQ;aAEtB;YAAC,OAAO,KAAK,EAAE;gBACZ,MAAM,CAAC,KAAK,CAAC,CAAC;aACjB;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IAED,KAAK,CAAC,IAAI;QACN,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YAClC,OAAO;SACV;QAED,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YAC3B,IAAI,IAAI,CAAC,OAAO,EAAE;gBACd,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,MAAM,EAAE,GAAG,EAAE;oBACzB,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;oBACvB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;oBACpB,OAAO,EAAE,CAAC;gBACd,CAAC,CAAC,CAAC;gBAEH,SAAS;gBACT,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;gBAE7B,iBAAiB;gBACjB,UAAU,CAAC,GAAG,EAAE;oBACZ,IAAI,IAAI,CAAC,OAAO,EAAE;wBACd,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;qBAChC;gBACL,CAAC,EAAE,IAAI,CAAC,CAAC;aACZ;iBAAM;gBACH,OAAO,EAAE,CAAC;aACb;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IAED,mBAAmB;QACf,qBAAqB;QACrB,IAAI,IAAI,CAAC,SAAS,EAAE;YAChB,IAAI,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC,GAAG,EAAE;gBAClB,IAAI,CAAC,KAAK,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;oBACvB,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,6BAA6B,KAAK,EAAE,CAAC,CAAC;gBACzE,CAAC,CAAC,CAAC;YACP,CAAC,CAAC,CAAC;SACN;IACL,CAAC;IAEO,cAAc;QAClB,wBAAwB;QACxB,MAAM,gBAAgB,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC;QAC3D,IAAI,CAAC,gBAAgB,EAAE;YACnB,OAAO,IAAI,CAAC;SACf;QAED,KAAK,MAAM,MAAM,IAAI,gBAAgB,EAAE;YACnC,wBAAwB;YACxB,MAAM,YAAY,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,EAAE,YAAY,EAAE,QAAQ,CAAC,CAAC;YAC1E,IAAI,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC,EAAE;gBAClD,OAAO,YAAY,CAAC;aACvB;YAED,gBAAgB;YAChB,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;YAC1D,IAAI,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC,EAAE;gBAChD,OAAO,UAAU,CAAC;aACrB;YAED,QAAQ;YACR,MAAM,OAAO,GAAG,CAAC,WAAW,EAAE,YAAY,CAAC,CAAC;YAC5C,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE;gBAC1B,MAAM,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAC;gBAC/D,IAAI,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC,EAAE;oBAC7C,OAAO,OAAO,CAAC;iBAClB;aACJ;SACJ;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAEO,iBAAiB;QACrB,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,WAAW,CAAC,CAAC;QAC9D,MAAM,GAAG,GAA2B,EAAE,CAAC;QAEvC,OAAO;QACP,GAAG,CAAC,IAAI,GAAG,MAAM,CAAC,GAAG,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC,QAAQ,EAAE,CAAC;QAErD,aAAa;QACb,GAAG,CAAC,oBAAoB,GAAG,MAAM,CAAC,GAAG,CAAC,aAAa,EAAE,UAAU,CAAC,CAAC;QAEjE,cAAc;QACd,MAAM,WAAW,GAAG,MAAM,CAAC,GAAG,CAAC,gBAAgB,EAAE,EAAE,CAAC,CAAC;QACrD,IAAI,WAAW,EAAE;YACb,GAAG,CAAC,gBAAgB,GAAG,WAAW,CAAC;SACtC;QAED,MAAM,UAAU,GAAG,MAAM,CAAC,GAAG,CAAC,eAAe,EAAE,EAAE,CAAC,CAAC;QACnD,IAAI,UAAU,EAAE;YACZ,GAAG,CAAC,eAAe,GAAG,UAAU,CAAC;SACpC;QAED,MAAM,SAAS,GAAG,MAAM,CAAC,GAAG,CAAC,cAAc,EAAE,EAAE,CAAC,CAAC;QACjD,IAAI,SAAS,EAAE;YACX,GAAG,CAAC,cAAc,GAAG,SAAS,CAAC;SAClC;QAED,OAAO,GAAG,CAAC;IACf,CAAC;IAED,eAAe;QACX,OAAO,IAAI,CAAC,SAAS,CAAC;IAC1B,CAAC;CACJ;AApLD,wCAoLC"}