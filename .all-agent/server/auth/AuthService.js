const bcrypt = require('bcrypt');
const jwt = require('jsonwebtoken');
const crypto = require('crypto');

/**
 * 认证服务
 * 提供用户注册、登录、JWT 令牌管理等功能
 */
class AuthService {
    constructor(database) {
        this.db = database;
        this.jwtSecret = process.env.JWT_SECRET || 'all-agent-default-secret-change-in-production';
        this.jwtExpiresIn = process.env.JWT_EXPIRES_IN || '7d';
        this.saltRounds = 12;
    }

    /**
     * 用户注册
     */
    async register(userData) {
        const { username, email, password, role = 'user' } = userData;

        // 验证输入
        this.validateUserInput({ username, email, password });

        // 检查用户是否已存在
        const existingUser = await this.db.get(
            'SELECT id FROM users WHERE username = ? OR email = ?',
            [username, email]
        );

        if (existingUser) {
            throw new Error('用户名或邮箱已存在');
        }

        // 加密密码
        const passwordHash = await bcrypt.hash(password, this.saltRounds);

        // 创建用户
        const result = await this.db.run(
            `INSERT INTO users (username, email, password_hash, role, created_at, updated_at)
             VALUES (?, ?, ?, ?, datetime('now'), datetime('now'))`,
            [username, email, passwordHash, role]
        );

        const user = await this.getUserById(result.id);
        return this.sanitizeUser(user);
    }

    /**
     * 用户登录
     */
    async login(credentials) {
        const { username, password, rememberMe = false } = credentials;

        // 查找用户
        const user = await this.db.get(
            'SELECT * FROM users WHERE username = ? OR email = ? AND is_active = 1',
            [username, username]
        );

        if (!user) {
            throw new Error('用户不存在或已被禁用');
        }

        // 验证密码
        const isValidPassword = await bcrypt.compare(password, user.password_hash);
        if (!isValidPassword) {
            throw new Error('密码错误');
        }

        // 更新最后登录时间
        await this.db.run(
            'UPDATE users SET last_login = datetime("now") WHERE id = ?',
            [user.id]
        );

        // 生成 JWT 令牌
        const expiresIn = rememberMe ? '30d' : this.jwtExpiresIn;
        const token = this.generateJWT(user, expiresIn);

        // 创建会话记录
        await this.createSession(user.id, token, expiresIn);

        return {
            user: this.sanitizeUser(user),
            token,
            expiresIn
        };
    }

    /**
     * 刷新令牌
     */
    async refreshToken(oldToken) {
        try {
            const decoded = jwt.verify(oldToken, this.jwtSecret);
            const user = await this.getUserById(decoded.userId);

            if (!user || !user.is_active) {
                throw new Error('用户不存在或已被禁用');
            }

            // 生成新令牌
            const newToken = this.generateJWT(user);
            
            // 更新会话
            await this.updateSession(oldToken, newToken);

            return {
                user: this.sanitizeUser(user),
                token: newToken
            };
        } catch (error) {
            throw new Error('令牌刷新失败: ' + error.message);
        }
    }

    /**
     * 用户登出
     */
    async logout(token) {
        try {
            // 删除会话记录
            await this.db.run(
                'DELETE FROM user_sessions WHERE session_token = ?',
                [token]
            );
            return { success: true };
        } catch (error) {
            throw new Error('登出失败: ' + error.message);
        }
    }

    /**
     * 验证令牌
     */
    async verifyToken(token) {
        try {
            const decoded = jwt.verify(token, this.jwtSecret);
            
            // 检查会话是否存在且未过期
            const session = await this.db.get(
                'SELECT * FROM user_sessions WHERE session_token = ? AND expires_at > datetime("now")',
                [token]
            );

            if (!session) {
                throw new Error('会话已过期或不存在');
            }

            // 更新最后访问时间
            await this.db.run(
                'UPDATE user_sessions SET last_accessed = datetime("now") WHERE session_token = ?',
                [token]
            );

            const user = await this.getUserById(decoded.userId);
            if (!user || !user.is_active) {
                throw new Error('用户不存在或已被禁用');
            }

            return this.sanitizeUser(user);
        } catch (error) {
            throw new Error('令牌验证失败: ' + error.message);
        }
    }

    /**
     * 修改密码
     */
    async changePassword(userId, oldPassword, newPassword) {
        const user = await this.getUserById(userId);
        if (!user) {
            throw new Error('用户不存在');
        }

        // 验证旧密码
        const isValidPassword = await bcrypt.compare(oldPassword, user.password_hash);
        if (!isValidPassword) {
            throw new Error('原密码错误');
        }

        // 验证新密码
        this.validatePassword(newPassword);

        // 加密新密码
        const newPasswordHash = await bcrypt.hash(newPassword, this.saltRounds);

        // 更新密码
        await this.db.run(
            'UPDATE users SET password_hash = ?, updated_at = datetime("now") WHERE id = ?',
            [newPasswordHash, userId]
        );

        // 删除所有会话，强制重新登录
        await this.db.run('DELETE FROM user_sessions WHERE user_id = ?', [userId]);

        return { success: true };
    }

    /**
     * 重置密码
     */
    async resetPassword(email) {
        const user = await this.db.get('SELECT * FROM users WHERE email = ?', [email]);
        if (!user) {
            throw new Error('邮箱不存在');
        }

        // 生成重置令牌
        const resetToken = crypto.randomBytes(32).toString('hex');
        const resetExpires = new Date(Date.now() + 3600000); // 1小时后过期

        // 这里应该发送邮件，暂时返回重置令牌
        // 在实际应用中，应该将重置令牌存储在数据库中
        console.log(`密码重置令牌: ${resetToken}`);

        return { 
            success: true, 
            message: '密码重置邮件已发送',
            resetToken // 仅用于开发测试
        };
    }

    /**
     * 获取用户信息
     */
    async getUserById(userId) {
        return await this.db.get('SELECT * FROM users WHERE id = ?', [userId]);
    }

    /**
     * 更新用户信息
     */
    async updateUser(userId, updateData) {
        const allowedFields = ['username', 'email', 'avatar_url'];
        const updates = [];
        const values = [];

        for (const [key, value] of Object.entries(updateData)) {
            if (allowedFields.includes(key) && value !== undefined) {
                updates.push(`${key} = ?`);
                values.push(value);
            }
        }

        if (updates.length === 0) {
            throw new Error('没有有效的更新字段');
        }

        updates.push('updated_at = datetime("now")');
        values.push(userId);

        await this.db.run(
            `UPDATE users SET ${updates.join(', ')} WHERE id = ?`,
            values
        );

        return await this.getUserById(userId);
    }

    /**
     * 生成 JWT 令牌
     */
    generateJWT(user, expiresIn = this.jwtExpiresIn) {
        const payload = {
            userId: user.id,
            username: user.username,
            email: user.email,
            role: user.role
        };

        return jwt.sign(payload, this.jwtSecret, { expiresIn });
    }

    /**
     * 创建会话记录
     */
    async createSession(userId, token, expiresIn) {
        const expiresAt = new Date();
        
        // 解析过期时间
        if (expiresIn.endsWith('d')) {
            expiresAt.setDate(expiresAt.getDate() + parseInt(expiresIn));
        } else if (expiresIn.endsWith('h')) {
            expiresAt.setHours(expiresAt.getHours() + parseInt(expiresIn));
        } else {
            expiresAt.setDate(expiresAt.getDate() + 7); // 默认7天
        }

        await this.db.run(
            `INSERT INTO user_sessions (user_id, session_token, expires_at, created_at, last_accessed)
             VALUES (?, ?, ?, datetime('now'), datetime('now'))`,
            [userId, token, expiresAt.toISOString()]
        );
    }

    /**
     * 更新会话记录
     */
    async updateSession(oldToken, newToken) {
        await this.db.run(
            'UPDATE user_sessions SET session_token = ?, last_accessed = datetime("now") WHERE session_token = ?',
            [newToken, oldToken]
        );
    }

    /**
     * 验证用户输入
     */
    validateUserInput({ username, email, password }) {
        if (!username || username.length < 3 || username.length > 50) {
            throw new Error('用户名长度必须在3-50个字符之间');
        }

        if (!email || !this.isValidEmail(email)) {
            throw new Error('邮箱格式不正确');
        }

        this.validatePassword(password);
    }

    /**
     * 验证密码
     */
    validatePassword(password) {
        if (!password || password.length < 6) {
            throw new Error('密码长度至少6个字符');
        }

        if (!/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(password)) {
            throw new Error('密码必须包含大小写字母和数字');
        }
    }

    /**
     * 验证邮箱格式
     */
    isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    /**
     * 清理用户数据（移除敏感信息）
     */
    sanitizeUser(user) {
        if (!user) return null;
        
        const { password_hash, ...sanitizedUser } = user;
        return sanitizedUser;
    }

    /**
     * 获取用户统计信息
     */
    async getUserStats() {
        const stats = await this.db.get(`
            SELECT 
                COUNT(*) as total_users,
                COUNT(CASE WHEN is_active = 1 THEN 1 END) as active_users,
                COUNT(CASE WHEN last_login > datetime('now', '-30 days') THEN 1 END) as recent_users
            FROM users
        `);

        return stats;
    }

    /**
     * 清理过期会话
     */
    async cleanupExpiredSessions() {
        const result = await this.db.run(
            'DELETE FROM user_sessions WHERE expires_at < datetime("now")'
        );
        
        console.log(`清理了 ${result.changes} 个过期会话`);
        return result.changes;
    }
}

module.exports = AuthService;
