const crypto = require('crypto');

/**
 * 缓存管理器
 * 提供内存缓存和数据库缓存支持
 */
class Cache {
  constructor(database = null) {
    this.db = database;
    this.memoryCache = new Map();
    this.cacheStats = {
      hits: 0,
      misses: 0,
      sets: 0,
      deletes: 0
    };

    // 默认配置 - 优化内存使用
    this.config = {
      defaultTTL: 1800, // 30分钟 (减少缓存时间)
      maxMemoryItems: 500, // 减少最大缓存项数
      cleanupInterval: 60000 // 1分钟 (更频繁清理)
    };

    // 启动清理定时器
    this.startCleanupTimer();
  }

  /**
   * 设置缓存
   */
  async set(key, value, ttl = this.config.defaultTTL, persistent = false) {
    const cacheKey = this.generateKey(key);
    const expiresAt = new Date(Date.now() + ttl * 1000);

    const cacheItem = {
      key: cacheKey,
      value: JSON.stringify(value),
      expiresAt,
      createdAt: new Date(),
      accessCount: 0
    };

    // 内存缓存
    this.memoryCache.set(cacheKey, cacheItem);
    this.cacheStats.sets++;

    // 检查内存缓存大小
    if (this.memoryCache.size > this.config.maxMemoryItems) {
      this.evictLRU();
    }

    // 持久化缓存（如果需要）
    if (persistent && this.db) {
      try {
        await this.db.run(
          `INSERT OR REPLACE INTO analysis_cache
                     (cache_key, analysis_data, created_at, expires_at, project_path)
                     VALUES (?, ?, ?, ?, ?)`,
          [cacheKey, JSON.stringify(value), new Date().toISOString(), expiresAt.toISOString(), key]
        );
      } catch (error) {
        console.warn('持久化缓存失败:', error.message);
      }
    }

    return true;
  }

  /**
   * 获取缓存
   */
  async get(key, checkPersistent = true) {
    const cacheKey = this.generateKey(key);

    // 先检查内存缓存
    const memoryItem = this.memoryCache.get(cacheKey);
    if (memoryItem && !this.isExpired(memoryItem)) {
      memoryItem.accessCount++;
      memoryItem.lastAccessed = new Date();
      this.cacheStats.hits++;

      try {
        return JSON.parse(memoryItem.value);
      } catch (error) {
        console.warn('缓存数据解析失败:', error.message);
        this.memoryCache.delete(cacheKey);
      }
    }

    // 检查持久化缓存
    if (checkPersistent && this.db) {
      try {
        const dbItem = await this.db.get(
          'SELECT * FROM analysis_cache WHERE cache_key = ? AND expires_at > datetime("now")',
          [cacheKey]
        );

        if (dbItem) {
          const value = JSON.parse(dbItem.analysis_data);

          // 重新加载到内存缓存
          const cacheItem = {
            key: cacheKey,
            value: dbItem.analysis_data,
            expiresAt: new Date(dbItem.expires_at),
            createdAt: new Date(dbItem.created_at),
            accessCount: 1,
            lastAccessed: new Date()
          };
          this.memoryCache.set(cacheKey, cacheItem);
          this.cacheStats.hits++;

          return value;
        }
      } catch (error) {
        console.warn('持久化缓存读取失败:', error.message);
      }
    }

    this.cacheStats.misses++;
    return null;
  }

  /**
   * 删除缓存
   */
  async delete(key) {
    const cacheKey = this.generateKey(key);

    // 删除内存缓存
    const deleted = this.memoryCache.delete(cacheKey);

    // 删除持久化缓存
    if (this.db) {
      try {
        await this.db.run('DELETE FROM analysis_cache WHERE cache_key = ?', [cacheKey]);
      } catch (error) {
        console.warn('持久化缓存删除失败:', error.message);
      }
    }

    if (deleted) {
      this.cacheStats.deletes++;
    }

    return deleted;
  }

  /**
   * 检查缓存是否存在
   */
  async has(key) {
    const value = await this.get(key);
    return value !== null;
  }

  /**
   * 清空所有缓存
   */
  async clear() {
    this.memoryCache.clear();

    if (this.db) {
      try {
        await this.db.run('DELETE FROM analysis_cache');
      } catch (error) {
        console.warn('清空持久化缓存失败:', error.message);
      }
    }

    // 重置统计
    this.cacheStats = {
      hits: 0,
      misses: 0,
      sets: 0,
      deletes: 0
    };
  }

  /**
   * 获取或设置缓存（如果不存在则执行函数）
   */
  async getOrSet(key, fn, ttl = this.config.defaultTTL, persistent = false) {
    let value = await this.get(key);

    if (value === null) {
      value = await fn();
      if (value !== null && value !== undefined) {
        await this.set(key, value, ttl, persistent);
      }
    }

    return value;
  }

  /**
   * 批量获取缓存
   */
  async mget(keys) {
    const results = {};

    for (const key of keys) {
      results[key] = await this.get(key);
    }

    return results;
  }

  /**
   * 批量设置缓存
   */
  async mset(items, ttl = this.config.defaultTTL, persistent = false) {
    const promises = [];

    for (const [key, value] of Object.entries(items)) {
      promises.push(this.set(key, value, ttl, persistent));
    }

    return Promise.all(promises);
  }

  /**
   * 生成缓存键
   */
  generateKey(key) {
    if (typeof key === 'string') {
      return crypto.createHash('md5').update(key).digest('hex');
    }

    return crypto.createHash('md5').update(JSON.stringify(key)).digest('hex');
  }

  /**
   * 检查缓存项是否过期
   */
  isExpired(item) {
    return new Date() > item.expiresAt;
  }

  /**
   * LRU 淘汰策略
   */
  evictLRU() {
    let oldestKey = null;
    let oldestTime = Date.now();

    for (const [key, item] of this.memoryCache.entries()) {
      const lastAccessed = item.lastAccessed || item.createdAt;
      if (lastAccessed < oldestTime) {
        oldestTime = lastAccessed;
        oldestKey = key;
      }
    }

    if (oldestKey) {
      this.memoryCache.delete(oldestKey);
    }
  }

  /**
   * 清理过期缓存
   */
  cleanup() {
    const now = new Date();
    const expiredKeys = [];

    for (const [key, item] of this.memoryCache.entries()) {
      if (this.isExpired(item)) {
        expiredKeys.push(key);
      }
    }

    for (const key of expiredKeys) {
      this.memoryCache.delete(key);
    }

    console.log(`清理了 ${expiredKeys.length} 个过期缓存项`);
  }

  /**
   * 启动清理定时器
   */
  startCleanupTimer() {
    setInterval(() => {
      this.cleanup();
    }, this.config.cleanupInterval);
  }

  /**
   * 获取缓存统计信息
   */
  getStats() {
    const hitRate =
      this.cacheStats.hits + this.cacheStats.misses > 0
        ? ((this.cacheStats.hits / (this.cacheStats.hits + this.cacheStats.misses)) * 100).toFixed(2)
        : 0;

    return {
      ...this.cacheStats,
      hitRate: `${hitRate}%`,
      memoryItems: this.memoryCache.size,
      memoryUsage: this.getMemoryUsage()
    };
  }

  /**
   * 获取内存使用情况
   */
  getMemoryUsage() {
    let totalSize = 0;

    for (const item of this.memoryCache.values()) {
      totalSize += JSON.stringify(item).length;
    }

    return {
      bytes: totalSize,
      kb: (totalSize / 1024).toFixed(2),
      mb: (totalSize / 1024 / 1024).toFixed(2)
    };
  }

  /**
   * 设置配置
   */
  setConfig(config) {
    this.config = { ...this.config, ...config };
  }

  /**
   * 获取所有缓存键
   */
  keys() {
    return Array.from(this.memoryCache.keys());
  }

  /**
   * 获取缓存大小
   */
  size() {
    return this.memoryCache.size;
  }

  /**
   * 预热缓存
   */
  async warmup(items) {
    console.log('开始缓存预热...');

    for (const [key, value] of Object.entries(items)) {
      await this.set(key, value, this.config.defaultTTL, true);
    }

    console.log(`缓存预热完成，加载了 ${Object.keys(items).length} 个项目`);
  }
}

module.exports = Cache;
