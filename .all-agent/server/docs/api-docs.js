/**
 * @swagger
 * /health:
 *   get:
 *     summary: 系统健康检查
 *     description: 检查系统各组件的健康状态
 *     tags: [System]
 *     security: []
 *     responses:
 *       200:
 *         description: 系统健康状态
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: healthy
 *                 timestamp:
 *                   type: string
 *                   format: date-time
 *                 version:
 *                   type: string
 *                   example: "1.0.0"
 *                 database:
 *                   type: object
 *                   properties:
 *                     healthy:
 *                       type: boolean
 *                     message:
 *                       type: string
 *                 cache:
 *                   type: object
 *                   properties:
 *                     hits:
 *                       type: integer
 *                     misses:
 *                       type: integer
 *                     hitRate:
 *                       type: string
 *                 agents:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Agent'
 */

/**
 * @swagger
 * /auth/register:
 *   post:
 *     summary: 用户注册
 *     description: 创建新用户账户
 *     tags: [Authentication]
 *     security: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - username
 *               - email
 *               - password
 *             properties:
 *               username:
 *                 type: string
 *                 minLength: 3
 *                 maxLength: 50
 *                 example: "testuser"
 *               email:
 *                 type: string
 *                 format: email
 *                 example: "<EMAIL>"
 *               password:
 *                 type: string
 *                 minLength: 6
 *                 example: "Test123456"
 *               role:
 *                 type: string
 *                 enum: [user, admin]
 *                 default: user
 *     responses:
 *       200:
 *         description: 注册成功
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/Success'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       $ref: '#/components/schemas/User'
 *       400:
 *         description: 注册失败
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */

/**
 * @swagger
 * /auth/login:
 *   post:
 *     summary: 用户登录
 *     description: 用户身份验证并获取访问令牌
 *     tags: [Authentication]
 *     security: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - username
 *               - password
 *             properties:
 *               username:
 *                 type: string
 *                 description: 用户名或邮箱
 *                 example: "<EMAIL>"
 *               password:
 *                 type: string
 *                 example: "Test123456"
 *               rememberMe:
 *                 type: boolean
 *                 default: false
 *                 description: 是否记住登录状态
 *     responses:
 *       200:
 *         description: 登录成功
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/Success'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         user:
 *                           $ref: '#/components/schemas/User'
 *                         token:
 *                           type: string
 *                           description: JWT 访问令牌
 *                         expiresIn:
 *                           type: string
 *                           description: 令牌过期时间
 *       401:
 *         description: 登录失败
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */

/**
 * @swagger
 * /auth/me:
 *   get:
 *     summary: 获取当前用户信息
 *     description: 获取当前登录用户的详细信息
 *     tags: [Authentication]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 用户信息
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/Success'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       $ref: '#/components/schemas/User'
 *       401:
 *         description: 未授权
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */

/**
 * @swagger
 * /auth/logout:
 *   post:
 *     summary: 用户登出
 *     description: 注销当前用户会话
 *     tags: [Authentication]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 登出成功
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Success'
 *       401:
 *         description: 未授权
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */

/**
 * @swagger
 * /api/analyze:
 *   post:
 *     summary: 项目分析
 *     description: 分析项目结构、技术栈和依赖关系
 *     tags: [Project Analysis]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               projectPath:
 *                 type: string
 *                 description: 项目路径
 *                 example: "/path/to/project"
 *               options:
 *                 type: object
 *                 properties:
 *                   maxDepth:
 *                     type: integer
 *                     default: 10
 *                     description: 最大扫描深度
 *                   includeHidden:
 *                     type: boolean
 *                     default: false
 *                     description: 是否包含隐藏文件
 *                   forceRefresh:
 *                     type: boolean
 *                     default: false
 *                     description: 是否强制刷新缓存
 *     responses:
 *       200:
 *         description: 分析成功
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/Success'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       $ref: '#/components/schemas/ProjectAnalysis'
 *       401:
 *         description: 未授权
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: 分析失败
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */

/**
 * @swagger
 * /api/tasks:
 *   post:
 *     summary: 提交任务
 *     description: 向指定 Agent 提交执行任务
 *     tags: [Task Management]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - agentType
 *               - action
 *               - input
 *             properties:
 *               agentType:
 *                 type: string
 *                 enum: [analyzer, planner, executor]
 *                 description: Agent 类型
 *               action:
 *                 type: string
 *                 description: 执行动作
 *                 example: "analyze_code"
 *               input:
 *                 type: object
 *                 description: 输入数据
 *               options:
 *                 type: object
 *                 description: 可选参数
 *     responses:
 *       200:
 *         description: 任务提交成功
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/Success'
 *                 - type: object
 *                   properties:
 *                     taskId:
 *                       type: string
 *                       description: 任务ID
 *       401:
 *         description: 未授权
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: 任务提交失败
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */

/**
 * @swagger
 * /api/agents:
 *   get:
 *     summary: 获取 Agent 状态
 *     description: 获取所有 Agent 的当前状态信息
 *     tags: [Agent Management]
 *     security: []
 *     responses:
 *       200:
 *         description: Agent 状态列表
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/Success'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/Agent'
 */

/**
 * @swagger
 * tags:
 *   - name: System
 *     description: 系统相关接口
 *   - name: Authentication
 *     description: 用户认证相关接口
 *   - name: Project Analysis
 *     description: 项目分析相关接口
 *   - name: Task Management
 *     description: 任务管理相关接口
 *   - name: Agent Management
 *     description: Agent 管理相关接口
 */
