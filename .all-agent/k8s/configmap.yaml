apiVersion: v1
kind: ConfigMap
metadata:
  name: all-agent-config
  namespace: all-agent
  labels:
    app.kubernetes.io/name: all-agent
    app.kubernetes.io/component: config
data:
  NODE_ENV: "production"
  PORT: "3000"
  USE_REDIS: "true"
  REDIS_HOST: "all-agent-redis"
  REDIS_PORT: "6379"
  DATABASE_URL: "sqlite:/app/data/all-agent.db"
  MONITOR_INTERVAL: "30000"
  CPU_THRESHOLD: "80"
  MEMORY_THRESHOLD: "85"
  DISK_THRESHOLD: "90"
  ERROR_THRESHOLD: "5"
  RESPONSE_THRESHOLD: "5000"
  LOG_LEVEL: "info"
  CORS_ORIGIN: "*"
  JWT_EXPIRES_IN: "7d"
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: nginx-config
  namespace: all-agent
  labels:
    app.kubernetes.io/name: all-agent
    app.kubernetes.io/component: nginx
data:
  nginx.conf: |
    user nginx;
    worker_processes auto;
    error_log /var/log/nginx/error.log warn;
    pid /var/run/nginx.pid;

    events {
        worker_connections 1024;
        use epoll;
        multi_accept on;
    }

    http {
        include /etc/nginx/mime.types;
        default_type application/octet-stream;

        log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                        '$status $body_bytes_sent "$http_referer" '
                        '"$http_user_agent" "$http_x_forwarded_for"';

        access_log /var/log/nginx/access.log main;

        sendfile on;
        tcp_nopush on;
        tcp_nodelay on;
        keepalive_timeout 65;
        types_hash_max_size 2048;

        gzip on;
        gzip_vary on;
        gzip_min_length 10240;
        gzip_proxied expired no-cache no-store private must-revalidate auth;
        gzip_types
            text/plain
            text/css
            text/xml
            text/javascript
            application/x-javascript
            application/xml+rss
            application/javascript
            application/json;

        upstream all-agent-backend {
            server all-agent-app:3000;
            keepalive 32;
        }

        server {
            listen 80;
            server_name _;

            location /health {
                access_log off;
                return 200 "healthy\n";
                add_header Content-Type text/plain;
            }

            location / {
                proxy_pass http://all-agent-backend;
                proxy_http_version 1.1;
                proxy_set_header Upgrade $http_upgrade;
                proxy_set_header Connection 'upgrade';
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                proxy_cache_bypass $http_upgrade;
                proxy_connect_timeout 30s;
                proxy_send_timeout 30s;
                proxy_read_timeout 30s;
            }

            location /socket.io/ {
                proxy_pass http://all-agent-backend;
                proxy_http_version 1.1;
                proxy_set_header Upgrade $http_upgrade;
                proxy_set_header Connection "upgrade";
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
            }
        }
    }
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: prometheus-config
  namespace: all-agent
  labels:
    app.kubernetes.io/name: all-agent
    app.kubernetes.io/component: prometheus
data:
  prometheus.yml: |
    global:
      scrape_interval: 15s
      evaluation_interval: 15s

    rule_files:
      # - "first_rules.yml"
      # - "second_rules.yml"

    scrape_configs:
      - job_name: 'prometheus'
        static_configs:
          - targets: ['localhost:9090']

      - job_name: 'all-agent'
        static_configs:
          - targets: ['all-agent-app:3000']
        metrics_path: '/metrics'
        scrape_interval: 30s

      - job_name: 'node-exporter'
        static_configs:
          - targets: ['node-exporter:9100']

      - job_name: 'redis'
        static_configs:
          - targets: ['all-agent-redis:6379']

    alerting:
      alertmanagers:
        - static_configs:
            - targets:
              # - alertmanager:9093
