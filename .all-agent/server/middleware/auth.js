/**
 * 认证中间件
 * 提供 JWT 令牌验证和权限控制
 */

/**
 * JWT 认证中间件
 */
const authenticateToken = (authService) => {
    return async (req, res, next) => {
        try {
            const authHeader = req.headers['authorization'];
            const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

            if (!token) {
                return res.status(401).json({
                    success: false,
                    error: '访问令牌缺失'
                });
            }

            const user = await authService.verifyToken(token);
            req.user = user;
            req.token = token;
            next();
        } catch (error) {
            return res.status(403).json({
                success: false,
                error: '令牌无效或已过期'
            });
        }
    };
};

/**
 * 可选认证中间件（允许匿名访问）
 */
const optionalAuth = (authService) => {
    return async (req, res, next) => {
        try {
            const authHeader = req.headers['authorization'];
            const token = authHeader && authHeader.split(' ')[1];

            if (token) {
                const user = await authService.verifyToken(token);
                req.user = user;
                req.token = token;
            }
            next();
        } catch (error) {
            // 忽略认证错误，继续处理请求
            next();
        }
    };
};

/**
 * 角色权限中间件
 */
const requireRole = (...roles) => {
    return (req, res, next) => {
        if (!req.user) {
            return res.status(401).json({
                success: false,
                error: '需要登录'
            });
        }

        if (!roles.includes(req.user.role)) {
            return res.status(403).json({
                success: false,
                error: '权限不足'
            });
        }

        next();
    };
};

/**
 * 管理员权限中间件
 */
const requireAdmin = requireRole('admin');

/**
 * 用户权限中间件（用户或管理员）
 */
const requireUser = requireRole('user', 'admin');

/**
 * 资源所有者验证中间件
 */
const requireOwnership = (resourceIdParam = 'id', userIdField = 'user_id') => {
    return async (req, res, next) => {
        if (!req.user) {
            return res.status(401).json({
                success: false,
                error: '需要登录'
            });
        }

        // 管理员可以访问所有资源
        if (req.user.role === 'admin') {
            return next();
        }

        try {
            const resourceId = req.params[resourceIdParam];
            
            // 这里需要根据具体的资源类型来查询
            // 暂时简化处理，实际应用中需要传入数据库查询函数
            if (req.resourceUserId && req.resourceUserId !== req.user.id) {
                return res.status(403).json({
                    success: false,
                    error: '无权访问此资源'
                });
            }

            next();
        } catch (error) {
            return res.status(500).json({
                success: false,
                error: '权限验证失败'
            });
        }
    };
};

/**
 * 速率限制中间件
 */
const rateLimit = (options = {}) => {
    const {
        windowMs = 15 * 60 * 1000, // 15分钟
        maxRequests = 100,
        message = '请求过于频繁，请稍后再试'
    } = options;

    const requests = new Map();

    return (req, res, next) => {
        const key = req.ip || req.connection.remoteAddress;
        const now = Date.now();
        const windowStart = now - windowMs;

        // 清理过期记录
        if (requests.has(key)) {
            const userRequests = requests.get(key).filter(time => time > windowStart);
            requests.set(key, userRequests);
        }

        // 检查请求数量
        const userRequests = requests.get(key) || [];
        if (userRequests.length >= maxRequests) {
            return res.status(429).json({
                success: false,
                error: message,
                retryAfter: Math.ceil(windowMs / 1000)
            });
        }

        // 记录当前请求
        userRequests.push(now);
        requests.set(key, userRequests);

        next();
    };
};

/**
 * API 密钥验证中间件
 */
const validateApiKey = (req, res, next) => {
    const apiKey = req.headers['x-api-key'];
    const validApiKey = process.env.API_KEY;

    if (!validApiKey) {
        // 如果没有配置 API 密钥，跳过验证
        return next();
    }

    if (!apiKey || apiKey !== validApiKey) {
        return res.status(401).json({
            success: false,
            error: 'API 密钥无效'
        });
    }

    next();
};

/**
 * CORS 中间件
 */
const corsMiddleware = (req, res, next) => {
    const allowedOrigins = process.env.CORS_ORIGIN ? 
        process.env.CORS_ORIGIN.split(',') : ['*'];

    const origin = req.headers.origin;
    
    if (allowedOrigins.includes('*') || allowedOrigins.includes(origin)) {
        res.header('Access-Control-Allow-Origin', origin || '*');
    }

    res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization, X-API-Key');
    res.header('Access-Control-Allow-Credentials', 'true');

    if (req.method === 'OPTIONS') {
        res.sendStatus(200);
    } else {
        next();
    }
};

/**
 * 错误处理中间件
 */
const errorHandler = (err, req, res, next) => {
    console.error('API 错误:', err);

    // JWT 错误
    if (err.name === 'JsonWebTokenError') {
        return res.status(401).json({
            success: false,
            error: '令牌格式错误'
        });
    }

    if (err.name === 'TokenExpiredError') {
        return res.status(401).json({
            success: false,
            error: '令牌已过期'
        });
    }

    // 验证错误
    if (err.name === 'ValidationError') {
        return res.status(400).json({
            success: false,
            error: err.message
        });
    }

    // 数据库错误
    if (err.code === 'SQLITE_CONSTRAINT') {
        return res.status(400).json({
            success: false,
            error: '数据约束违反'
        });
    }

    // 默认错误
    res.status(500).json({
        success: false,
        error: process.env.NODE_ENV === 'production' ? '服务器内部错误' : err.message
    });
};

/**
 * 请求日志中间件
 */
const requestLogger = (logger) => {
    return (req, res, next) => {
        const start = Date.now();
        
        res.on('finish', () => {
            const duration = Date.now() - start;
            const logData = {
                method: req.method,
                url: req.url,
                status: res.statusCode,
                duration: `${duration}ms`,
                ip: req.ip,
                userAgent: req.get('User-Agent'),
                userId: req.user?.id
            };

            if (res.statusCode >= 400) {
                logger.warn('HTTP 请求', logData);
            } else {
                logger.info('HTTP 请求', logData);
            }
        });

        next();
    };
};

/**
 * 输入验证中间件
 */
const validateInput = (schema) => {
    return (req, res, next) => {
        const { error } = schema.validate(req.body);
        
        if (error) {
            return res.status(400).json({
                success: false,
                error: error.details[0].message
            });
        }

        next();
    };
};

module.exports = {
    authenticateToken,
    optionalAuth,
    requireRole,
    requireAdmin,
    requireUser,
    requireOwnership,
    rateLimit,
    validateApiKey,
    corsMiddleware,
    errorHandler,
    requestLogger,
    validateInput
};
