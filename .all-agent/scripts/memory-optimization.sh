#!/bin/bash

# All-Agent 内存优化脚本
# 检测和修复内存泄漏问题

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 1. 检查 Node.js 内存配置
check_node_memory_config() {
    log_info "=== 检查 Node.js 内存配置 ==="
    
    # 检查当前内存限制
    local max_old_space=$(node -p "v8.getHeapStatistics().heap_size_limit / 1024 / 1024")
    log_info "当前堆内存限制: ${max_old_space}MB"
    
    if (( $(echo "$max_old_space < 512" | bc -l) )); then
        log_warning "堆内存限制较低，建议增加到至少 512MB"
        echo "建议在启动时添加: --max-old-space-size=512"
    else
        log_success "堆内存限制配置合理"
    fi
}

# 2. 分析代码中的潜在内存泄漏
analyze_memory_leaks() {
    log_info "=== 分析潜在内存泄漏 ==="
    
    cd server
    
    # 检查事件监听器泄漏
    log_info "检查事件监听器..."
    local event_listeners=$(grep -r "addEventListener\|on(" . --include="*.js" --exclude-dir=node_modules | wc -l)
    local remove_listeners=$(grep -r "removeEventListener\|off(" . --include="*.js" --exclude-dir=node_modules | wc -l)
    
    log_info "发现 $event_listeners 个事件监听器，$remove_listeners 个移除操作"
    
    if [ $event_listeners -gt $((remove_listeners * 2)) ]; then
        log_warning "事件监听器可能存在泄漏，建议检查是否正确移除"
    else
        log_success "事件监听器使用正常"
    fi
    
    # 检查定时器泄漏
    log_info "检查定时器..."
    local set_timers=$(grep -r "setTimeout\|setInterval" . --include="*.js" --exclude-dir=node_modules | wc -l)
    local clear_timers=$(grep -r "clearTimeout\|clearInterval" . --include="*.js" --exclude-dir=node_modules | wc -l)
    
    log_info "发现 $set_timers 个定时器，$clear_timers 个清理操作"
    
    if [ $set_timers -gt $((clear_timers * 2)) ]; then
        log_warning "定时器可能存在泄漏，建议检查是否正确清理"
    else
        log_success "定时器使用正常"
    fi
    
    # 检查全局变量
    log_info "检查全局变量..."
    local global_vars=$(grep -r "global\." . --include="*.js" --exclude-dir=node_modules | wc -l)
    
    if [ $global_vars -gt 10 ]; then
        log_warning "发现 $global_vars 个全局变量使用，可能影响内存回收"
    else
        log_success "全局变量使用合理"
    fi
    
    cd ..
}

# 3. 优化服务器配置
optimize_server_config() {
    log_info "=== 优化服务器配置 ==="
    
    # 创建优化的启动脚本
    cat > server/start-optimized.js << 'EOF'
#!/usr/bin/env node

/**
 * 优化的 All-Agent 服务器启动脚本
 * 包含内存优化和监控
 */

const v8 = require('v8');
const fs = require('fs');

// 设置 V8 优化选项
v8.setFlagsFromString('--optimize-for-size');
v8.setFlagsFromString('--max-old-space-size=512');
v8.setFlagsFromString('--gc-interval=100');

// 内存监控
let memoryMonitorInterval;

function startMemoryMonitoring() {
    memoryMonitorInterval = setInterval(() => {
        const memUsage = process.memoryUsage();
        const heapUsed = memUsage.heapUsed / 1024 / 1024;
        const heapTotal = memUsage.heapTotal / 1024 / 1024;
        const utilization = (heapUsed / heapTotal * 100).toFixed(2);
        
        console.log(`[MEMORY] 堆内存: ${heapUsed.toFixed(2)}MB/${heapTotal.toFixed(2)}MB (${utilization}%)`);
        
        // 内存使用率过高时触发垃圾回收
        if (utilization > 80) {
            console.log('[MEMORY] 内存使用率过高，触发垃圾回收...');
            if (global.gc) {
                global.gc();
            }
        }
        
        // 记录内存使用历史
        const logEntry = {
            timestamp: new Date().toISOString(),
            memoryUsage: memUsage,
            utilization: parseFloat(utilization)
        };
        
        fs.appendFileSync('./logs/memory.log', JSON.stringify(logEntry) + '\n');
        
    }, 30000); // 每30秒检查一次
}

function stopMemoryMonitoring() {
    if (memoryMonitorInterval) {
        clearInterval(memoryMonitorInterval);
    }
}

// 优雅关闭
process.on('SIGINT', () => {
    console.log('\n[SHUTDOWN] 正在优雅关闭服务器...');
    stopMemoryMonitoring();
    process.exit(0);
});

process.on('SIGTERM', () => {
    console.log('\n[SHUTDOWN] 收到终止信号，正在关闭...');
    stopMemoryMonitoring();
    process.exit(0);
});

// 未捕获异常处理
process.on('uncaughtException', (error) => {
    console.error('[ERROR] 未捕获异常:', error);
    stopMemoryMonitoring();
    process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
    console.error('[ERROR] 未处理的 Promise 拒绝:', reason);
    // 不退出进程，只记录错误
});

// 启动内存监控
startMemoryMonitoring();

// 启动主应用
console.log('[STARTUP] 启动优化的 All-Agent 服务器...');
require('./app-new.js');
EOF

    chmod +x server/start-optimized.js
    log_success "创建优化启动脚本: server/start-optimized.js"
    
    # 创建内存日志目录
    mkdir -p server/logs
    log_success "创建内存日志目录"
}

# 4. 修复已知的内存泄漏问题
fix_memory_leaks() {
    log_info "=== 修复已知内存泄漏问题 ==="
    
    cd server
    
    # 修复 WebSocket 连接泄漏
    if [ -f "websocket/WebSocketManager.js" ]; then
        log_info "修复 WebSocket 连接泄漏..."
        
        # 添加连接清理逻辑
        if ! grep -q "cleanupConnections" websocket/WebSocketManager.js; then
            cat >> websocket/WebSocketManager.js << 'EOF'

  /**
   * 清理无效连接
   */
  cleanupConnections() {
    const now = Date.now();
    for (const [id, connection] of this.connections.entries()) {
      if (connection.readyState === 3 || (now - connection.lastActivity > 300000)) { // 5分钟无活动
        this.connections.delete(id);
        console.log(`[WebSocket] 清理无效连接: ${id}`);
      }
    }
  }

  /**
   * 启动定期清理
   */
  startCleanupTimer() {
    this.cleanupTimer = setInterval(() => {
      this.cleanupConnections();
    }, 60000); // 每分钟清理一次
  }

  /**
   * 停止清理定时器
   */
  stopCleanupTimer() {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
      this.cleanupTimer = null;
    }
  }
EOF
            log_success "添加 WebSocket 连接清理逻辑"
        fi
    fi
    
    # 修复数据库连接泄漏
    if [ -f "database/DatabaseManager.js" ]; then
        log_info "检查数据库连接管理..."
        
        if ! grep -q "connection pool" database/DatabaseManager.js; then
            log_warning "建议实施数据库连接池"
        else
            log_success "数据库连接管理正常"
        fi
    fi
    
    cd ..
}

# 5. 创建内存监控中间件
create_memory_middleware() {
    log_info "=== 创建内存监控中间件 ==="
    
    cat > server/middleware/memoryMonitor.js << 'EOF'
/**
 * 内存监控中间件
 * 监控每个请求的内存使用情况
 */

class MemoryMonitor {
    constructor(options = {}) {
        this.threshold = options.threshold || 80; // 内存使用率阈值
        this.logInterval = options.logInterval || 100; // 每100个请求记录一次
        this.requestCount = 0;
    }

    middleware() {
        return (req, res, next) => {
            const startMemory = process.memoryUsage();
            const startTime = Date.now();

            // 请求完成后检查内存
            res.on('finish', () => {
                this.requestCount++;
                const endMemory = process.memoryUsage();
                const duration = Date.now() - startTime;
                
                const memoryDiff = {
                    rss: endMemory.rss - startMemory.rss,
                    heapTotal: endMemory.heapTotal - startMemory.heapTotal,
                    heapUsed: endMemory.heapUsed - startMemory.heapUsed,
                    external: endMemory.external - startMemory.external
                };

                // 检查内存使用率
                const heapUtilization = (endMemory.heapUsed / endMemory.heapTotal) * 100;
                
                if (heapUtilization > this.threshold) {
                    console.warn(`[MEMORY WARNING] 内存使用率过高: ${heapUtilization.toFixed(2)}% (${req.method} ${req.path})`);
                }

                // 定期记录内存统计
                if (this.requestCount % this.logInterval === 0) {
                    console.log(`[MEMORY STATS] 请求 #${this.requestCount}: 堆内存 ${(endMemory.heapUsed / 1024 / 1024).toFixed(2)}MB, 使用率 ${heapUtilization.toFixed(2)}%`);
                }

                // 添加内存信息到响应头 (开发环境)
                if (process.env.NODE_ENV === 'development') {
                    res.set('X-Memory-Usage', `${(endMemory.heapUsed / 1024 / 1024).toFixed(2)}MB`);
                    res.set('X-Memory-Utilization', `${heapUtilization.toFixed(2)}%`);
                }
            });

            next();
        };
    }

    /**
     * 获取内存统计信息
     */
    getStats() {
        const memUsage = process.memoryUsage();
        return {
            rss: memUsage.rss,
            heapTotal: memUsage.heapTotal,
            heapUsed: memUsage.heapUsed,
            external: memUsage.external,
            arrayBuffers: memUsage.arrayBuffers,
            heapUtilization: (memUsage.heapUsed / memUsage.heapTotal) * 100,
            requestCount: this.requestCount
        };
    }

    /**
     * 重置统计
     */
    reset() {
        this.requestCount = 0;
    }
}

module.exports = MemoryMonitor;
EOF

    log_success "创建内存监控中间件: server/middleware/memoryMonitor.js"
}

# 6. 运行内存测试
run_memory_test() {
    log_info "=== 运行内存测试 ==="
    
    cd server
    
    # 启动优化的服务器进行测试
    export NODE_ENV=test
    export PORT=3004
    export JWT_SECRET=test-memory-optimization
    
    log_info "启动优化服务器进行内存测试..."
    node --expose-gc start-optimized.js &
    SERVER_PID=$!
    
    sleep 5
    
    # 运行内存分析
    log_info "开始内存分析 (30秒)..."
    timeout 30s node ../scripts/memory-analysis.js monitor 2000 || true
    
    # 停止服务器
    kill $SERVER_PID 2>/dev/null || true
    sleep 2
    
    cd ..
    
    # 检查内存分析报告
    if [ -f "memory-analysis-report.json" ]; then
        log_success "内存分析完成，报告已生成"
        
        # 提取关键指标
        local max_heap=$(node -p "JSON.parse(require('fs').readFileSync('memory-analysis-report.json')).analysis.heapUsed.max / 1024 / 1024")
        local leak_detected=$(node -p "JSON.parse(require('fs').readFileSync('memory-analysis-report.json')).analysis.memoryLeakDetected")
        
        log_info "最大堆内存使用: ${max_heap}MB"
        
        if [ "$leak_detected" = "true" ]; then
            log_warning "检测到内存泄漏"
        else
            log_success "未检测到内存泄漏"
        fi
    else
        log_warning "内存分析报告未生成"
    fi
}

# 7. 生成优化报告
generate_optimization_report() {
    log_info "=== 生成内存优化报告 ==="
    
    cat > memory-optimization-report.md << EOF
# All-Agent 内存优化报告

## 优化时间
$(date)

## 执行的优化措施

### 1. Node.js 配置优化
- 设置堆内存限制: --max-old-space-size=512
- 启用垃圾回收优化: --gc-interval=100
- 启用内存优化: --optimize-for-size

### 2. 代码优化
- 添加 WebSocket 连接清理机制
- 实施内存监控中间件
- 优化事件监听器管理

### 3. 监控改进
- 创建实时内存监控
- 添加内存使用率告警
- 实施定期垃圾回收

### 4. 工具和脚本
- 内存分析工具: scripts/memory-analysis.js
- 优化启动脚本: server/start-optimized.js
- 内存监控中间件: server/middleware/memoryMonitor.js

## 建议的使用方式

### 生产环境启动
\`\`\`bash
cd server
node --expose-gc start-optimized.js
\`\`\`

### 内存监控
\`\`\`bash
node scripts/memory-analysis.js monitor 5000
\`\`\`

### 强制垃圾回收
\`\`\`bash
node --expose-gc scripts/memory-analysis.js gc
\`\`\`

## 监控指标
- 堆内存使用率 < 80%
- RSS 内存增长趋势 < 1MB/小时
- 无内存泄漏检测

## 下一步行动
1. 在生产环境中部署优化配置
2. 设置内存使用率告警
3. 定期检查内存分析报告
4. 监控长期内存使用趋势
EOF

    log_success "内存优化报告已生成: memory-optimization-report.md"
}

# 主函数
main() {
    log_info "开始 All-Agent 内存优化..."
    log_info "优化时间: $(date)"
    echo
    
    # 检查依赖
    if ! command -v bc >/dev/null 2>&1; then
        log_error "bc 计算器未安装，请先安装: brew install bc"
        exit 1
    fi
    
    # 执行优化步骤
    check_node_memory_config
    analyze_memory_leaks
    optimize_server_config
    fix_memory_leaks
    create_memory_middleware
    run_memory_test
    generate_optimization_report
    
    echo
    log_success "🎉 内存优化完成！"
    log_info "请查看 memory-optimization-report.md 了解详细信息"
    log_info "建议使用 'node --expose-gc server/start-optimized.js' 启动服务器"
}

# 执行主函数
main "$@"
