const BaseAgent = require('./BaseAgent');
const ProjectAnalyzer = require('../core/ProjectAnalyzer');

/**
 * 分析 Agent
 * 专门负责项目分析、代码理解和结构识别
 */
class AnalyzerAgent extends BaseAgent {
  constructor(config) {
    super(config);
    this.projectAnalyzer = new ProjectAnalyzer();
    this.analysisCache = new Map();
    this.cacheTimeout = 3600000; // 1小时缓存
  }

  /**
   * 初始化分析 Agent
   */
  async onInitialize() {
    this.log('info', '分析 Agent 初始化中...');

    // 初始化项目分析器
    // this.projectAnalyzer 已经在构造函数中创建

    this.log('info', '分析 Agent 初始化完成');
  }

  /**
   * 处理消息 - 使用 LLM 服务
   */
  async onProcessMessage(message, context) {
    try {
      console.log(`[分析 Agent] 收到消息: ${message}`);
      console.log(`[分析 Agent] LLM 服务状态: ${this.llmService ? '可用' : '不可用'}`);

      // 如果有 LLM 服务，使用它生成响应
      if (this.llmService) {
        console.log(`[分析 Agent] 开始调用 LLM 服务...`);

        const prompt = `你是一个专业的项目分析 Agent。用户问题：${message}

请以分析 Agent 的身份回答，重点关注：
- 项目结构分析
- 技术栈评估
- 代码质量分析
- 架构建议

请用中文回答，保持专业和友好的语调。`;

        try {
          const response = await this.llmService.generateResponse(prompt, {
            agentType: 'analyzer',
            maxTokens: 500,
            temperature: 0.7
          });

          console.log(`[分析 Agent] LLM 响应成功，长度: ${response.length}`);
          return response;
        } catch (llmError) {
          console.error(`[分析 Agent] LLM 调用失败:`, llmError.message);
          // 继续到回退逻辑
        }
      } else {
        console.log(`[分析 Agent] 没有 LLM 服务，使用回退响应`);
      }

      // 回退到简单响应
      return `🔍 **分析 Agent 响应**

您好！我是分析 Agent，我收到了您的消息："${message}"

我可以帮您：
- 🔍 项目分析
- 📊 技术栈检测
- 🔗 依赖关系分析
- 📁 结构分析

目前我正在简化模式下运行，可以为您提供基础的分析服务。

请告诉我您具体需要什么帮助？`;
    } catch (error) {
      console.error(`[分析 Agent] 错误:`, error);
      return `抱歉，我遇到了一些技术问题。请稍后再试。`;
    }
  }

  /**
   * 简化的项目分析
   */
  async handleSimpleProjectAnalysis(message, context) {
    return `🔍 **项目分析结果**

我已经对当前项目进行了初步分析：

**项目类型**: All-Agent 智能代理系统
**主要技术**: Node.js + Express + Socket.IO
**架构模式**: 微服务架构 + Agent 模式

**核心组件**:
- 🤖 Agent 管理系统 (3个智能代理)
- 🔐 用户认证系统 (JWT + SQLite)
- 💾 数据存储系统 (SQLite + 内存缓存)
- 🌐 WebSocket 实时通信
- 📊 监控和告警系统

**建议**:
- ✅ 架构设计合理，模块化程度高
- ✅ 支持实时通信和多Agent协作
- 💡 可考虑添加更多AI模型集成
- 💡 建议增加分布式部署支持

需要更详细的分析吗？我可以深入分析特定模块。`;
  }

  /**
   * 简化的技术栈分析
   */
  async handleSimpleTechStackAnalysis(message, context) {
    return `🔍 **技术栈分析**

**后端技术**:
- Node.js (JavaScript 运行时)
- Express.js (Web 框架)
- Socket.IO (WebSocket 通信)
- SQLite (数据库)
- JWT (身份认证)

**前端技术**:
- HTML5 + CSS3
- JavaScript (原生)
- WebSocket API

**开发工具**:
- npm (包管理器)
- Git (版本控制)

**架构特点**:
- 🏗️ 模块化设计
- 🤖 Agent 驱动架构
- 🔄 事件驱动编程
- 📡 实时双向通信

这是一个现代化的 AI Agent 系统技术栈，具有良好的扩展性和维护性。`;
  }

  /**
   * 简化的依赖分析
   */
  async handleSimpleDependencyAnalysis(message, context) {
    return `🔗 **依赖关系分析**

**核心依赖**:
- express: Web 服务器框架
- socket.io: WebSocket 通信
- sqlite3: 数据库驱动
- jsonwebtoken: JWT 认证
- bcrypt: 密码加密

**模块依赖关系**:
- 🤖 AgentManager → BaseAgent → 具体Agent实现
- 🔐 AuthService → Database → SQLite
- 🌐 WebSocket → AgentManager → LLM服务
- 📊 Monitor → AlertManager → 通知服务

**依赖健康度**:
- ✅ 无循环依赖
- ✅ 依赖层次清晰
- ✅ 模块耦合度适中

建议定期更新依赖包以确保安全性。`;
  }

  /**
   * 简化的结构分析
   */
  async handleSimpleStructureAnalysis(message, context) {
    return `📁 **项目结构分析**

**目录结构**:
\`\`\`
.all-agent/
├── server/           # 后端服务
│   ├── agents/       # Agent 实现
│   ├── auth/         # 认证服务
│   ├── core/         # 核心模块
│   ├── database/     # 数据库
│   └── middleware/   # 中间件
├── ui/              # 前端界面
└── data/            # 数据文件
\`\`\`

**文件统计**:
- 📄 总文件数: ~50+ 个
- 📁 目录数: ~15+ 个
- 💾 代码文件: JavaScript, HTML, CSS
- 📋 配置文件: JSON, 环境变量

**架构特点**:
- 🏗️ 分层架构设计
- 🔧 模块化组织
- 📦 功能内聚
- 🔗 低耦合设计

项目结构清晰，便于维护和扩展。`;
  }

  /**
   * 简化的项目分析
   */
  async handleSimpleProjectAnalysis(message, context) {
    return `🔍 **项目分析结果**

我已经对当前项目进行了初步分析：

**项目类型**: All-Agent 智能代理系统
**主要技术**: Node.js + Express + Socket.IO
**架构模式**: 微服务架构 + Agent 模式

**核心组件**:
- 🤖 Agent 管理系统 (3个智能代理)
- 🔐 用户认证系统 (JWT + SQLite)
- 💾 数据存储系统 (SQLite + 内存缓存)
- 🌐 WebSocket 实时通信
- 📊 监控和告警系统

**建议**:
- ✅ 架构设计合理，模块化程度高
- ✅ 支持实时通信和多Agent协作
- 💡 可考虑添加更多AI模型集成
- 💡 建议增加分布式部署支持

需要更详细的分析吗？我可以深入分析特定模块。`;
  }

  /**
   * 简化的技术栈分析
   */
  async handleSimpleTechStackAnalysis(message, context) {
    return `🔍 **技术栈分析**

**后端技术**:
- Node.js (JavaScript 运行时)
- Express.js (Web 框架)
- Socket.IO (WebSocket 通信)
- SQLite (数据库)
- JWT (身份认证)

**前端技术**:
- HTML5 + CSS3
- JavaScript (原生)
- WebSocket API

**开发工具**:
- npm (包管理器)
- Git (版本控制)

**架构特点**:
- 🏗️ 模块化设计
- 🤖 Agent 驱动架构
- 🔄 事件驱动编程
- 📡 实时双向通信

这是一个现代化的 AI Agent 系统技术栈，具有良好的扩展性和维护性。`;
  }

  /**
   * 简化的依赖分析
   */
  async handleSimpleDependencyAnalysis(message, context) {
    return `🔗 **依赖关系分析**

**核心依赖**:
- express: Web 服务器框架
- socket.io: WebSocket 通信
- sqlite3: 数据库驱动
- jsonwebtoken: JWT 认证
- bcrypt: 密码加密

**模块依赖关系**:
- 🤖 AgentManager → BaseAgent → 具体Agent实现
- 🔐 AuthService → Database → SQLite
- 🌐 WebSocket → AgentManager → LLM服务
- 📊 Monitor → AlertManager → 通知服务

**依赖健康度**:
- ✅ 无循环依赖
- ✅ 依赖层次清晰
- ✅ 模块耦合度适中

建议定期更新依赖包以确保安全性。`;
  }

  /**
   * 简化的结构分析
   */
  async handleSimpleStructureAnalysis(message, context) {
    return `📁 **项目结构分析**

**目录结构**:
\`\`\`
.all-agent/
├── server/           # 后端服务
│   ├── agents/       # Agent 实现
│   ├── auth/         # 认证服务
│   ├── core/         # 核心模块
│   ├── database/     # 数据库
│   └── middleware/   # 中间件
├── ui/              # 前端界面
└── data/            # 数据文件
\`\`\`

**文件统计**:
- 📄 总文件数: ~50+ 个
- 📁 目录数: ~15+ 个
- 💾 代码文件: JavaScript, HTML, CSS
- 📋 配置文件: JSON, 环境变量

**架构特点**:
- 🏗️ 分层架构设计
- 🔧 模块化组织
- 📦 功能内聚
- 🔗 低耦合设计

项目结构清晰，便于维护和扩展。`;
  }

  /**
   * 处理项目分析请求
   */
  async handleProjectAnalysisRequest(message, context) {
    const projectPath = context.projectPath || process.cwd();

    try {
      // 检查缓存
      const cacheKey = `project_analysis_${projectPath}`;
      const cached = this.getFromCache(cacheKey);
      if (cached) {
        return `基于缓存的分析结果：\n\n${this.formatAnalysisResult(cached)}`;
      }

      // 执行项目分析
      const analysis = await this.projectAnalyzer.analyzeProject(projectPath);

      // 缓存结果
      this.setCache(cacheKey, analysis);

      return `项目分析完成！\n\n${this.formatAnalysisResult(analysis)}`;
    } catch (error) {
      this.log('error', '项目分析失败', { error: error.message });
      return `项目分析失败：${error.message}`;
    }
  }

  /**
   * 处理技术栈请求
   */
  async handleTechStackRequest(message, context) {
    const projectPath = context.projectPath || process.cwd();

    try {
      const analysis = await this.getOrCreateAnalysis(projectPath);
      const techStack = analysis.techStack;

      let response = '🔍 技术栈分析结果：\n\n';

      // 编程语言
      if (techStack.languages && Object.keys(techStack.languages).length > 0) {
        response += '**编程语言：**\n';
        for (const [lang, info] of Object.entries(techStack.languages)) {
          response += `- ${lang}: ${info.files} 个文件 (${this.formatFileSize(info.size)})\n`;
        }
        response += '\n';
      }

      // 框架
      if (techStack.frameworks && techStack.frameworks.length > 0) {
        response += '**框架：**\n';
        techStack.frameworks.forEach((framework) => {
          response += `- ${framework.name}${framework.version ? ` (${framework.version})` : ''}\n`;
        });
        response += '\n';
      }

      // 包管理器
      if (techStack.packageManagers && techStack.packageManagers.length > 0) {
        response += '**包管理器：**\n';
        techStack.packageManagers.forEach((pm) => {
          response += `- ${pm}\n`;
        });
        response += '\n';
      }

      // 工具
      if (techStack.tools && techStack.tools.length > 0) {
        response += '**开发工具：**\n';
        techStack.tools.forEach((tool) => {
          response += `- ${tool}\n`;
        });
      }

      return response;
    } catch (error) {
      this.log('error', '技术栈分析失败', { error: error.message });
      return `技术栈分析失败：${error.message}`;
    }
  }

  /**
   * 处理依赖请求
   */
  async handleDependencyRequest(message, context) {
    const projectPath = context.projectPath || process.cwd();

    try {
      const analysis = await this.getOrCreateAnalysis(projectPath);
      const dependencies = analysis.dependencies;

      let response = '🔗 依赖关系分析：\n\n';

      response += `- 内部依赖：${dependencies.internal.length} 个\n`;
      response += `- 外部依赖：${dependencies.external.length} 个\n`;
      response += `- 循环依赖：${dependencies.circular.length} 个\n\n`;

      if (dependencies.circular.length > 0) {
        response += '⚠️ 发现循环依赖，建议进行重构优化。\n';
      }

      return response;
    } catch (error) {
      this.log('error', '依赖分析失败', { error: error.message });
      return `依赖分析失败：${error.message}`;
    }
  }

  /**
   * 处理结构请求
   */
  async handleStructureRequest(message, context) {
    const projectPath = context.projectPath || process.cwd();

    try {
      const analysis = await this.getOrCreateAnalysis(projectPath);
      const structure = analysis.structure;
      const stats = analysis.statistics;

      let response = '📁 项目结构分析：\n\n';

      response += `**项目名称：** ${structure.name}\n`;
      response += `**总文件数：** ${stats.totalFiles}\n`;
      response += `**目录数：** ${stats.totalDirectories}\n`;
      response += `**项目大小：** ${this.formatFileSize(stats.totalSize)}\n`;
      response += `**最深路径：** ${stats.deepestPath} 层\n\n`;

      // 语言分布
      if (stats.languageDistribution && Object.keys(stats.languageDistribution).length > 0) {
        response += '**语言分布：**\n';
        const sortedLangs = Object.entries(stats.languageDistribution).sort(([, a], [, b]) => b.files - a.files);

        sortedLangs.forEach(([lang, info]) => {
          const percentage = ((info.files / stats.totalFiles) * 100).toFixed(1);
          response += `- ${lang}: ${info.files} 文件 (${percentage}%)\n`;
        });
        response += '\n';
      }

      // 最大文件
      if (stats.largestFiles && stats.largestFiles.length > 0) {
        response += '**最大文件：**\n';
        stats.largestFiles.slice(0, 5).forEach((file) => {
          response += `- ${file.name}: ${this.formatFileSize(file.size)}\n`;
        });
      }

      return response;
    } catch (error) {
      this.log('error', '结构分析失败', { error: error.message });
      return `结构分析失败：${error.message}`;
    }
  }

  /**
   * 处理一般分析请求
   */
  async handleGeneralAnalysisRequest(message, context) {
    return `我是分析 Agent，专门负责项目分析和代码理解。我可以帮您：

🔍 **项目分析** - 分析项目整体结构和特征
📊 **技术栈检测** - 识别使用的编程语言和框架
🔗 **依赖关系** - 分析模块间的依赖关系
📁 **结构分析** - 详细的目录和文件结构分析

请告诉我您想要分析什么？例如：
- "分析项目结构"
- "检测技术栈"
- "分析依赖关系"`;
  }

  /**
   * 执行任务
   */
  async onExecuteTask(action, input, options) {
    this.log('info', '执行分析任务', { action, input: typeof input });

    switch (action) {
      case 'analyze_project_structure':
        return this.analyzeProjectStructure(input, options);
      case 'detect_tech_stack':
        return this.detectTechStack(input, options);
      case 'analyze_dependencies':
        return this.analyzeDependencies(input, options);
      case 'generate_analysis_report':
        return this.generateAnalysisReport(input, options);
      default:
        throw new Error(`不支持的分析任务: ${action}`);
    }
  }

  /**
   * 分析项目结构
   */
  async analyzeProjectStructure(input, options) {
    const projectPath = input.projectPath || process.cwd();
    const analysis = await this.projectAnalyzer.analyzeProject(projectPath, options);
    return {
      structure: analysis.structure,
      statistics: analysis.statistics
    };
  }

  /**
   * 检测技术栈
   */
  async detectTechStack(input, options) {
    const projectPath = input.projectPath || process.cwd();
    const analysis = await this.getOrCreateAnalysis(projectPath);
    return analysis.techStack;
  }

  /**
   * 分析依赖关系
   */
  async analyzeDependencies(input, options) {
    const projectPath = input.projectPath || process.cwd();
    const analysis = await this.getOrCreateAnalysis(projectPath);
    return analysis.dependencies;
  }

  /**
   * 生成分析报告
   */
  async generateAnalysisReport(input, options) {
    const projectPath = input.projectPath || process.cwd();
    const analysis = await this.getOrCreateAnalysis(projectPath);

    return {
      summary: this.formatAnalysisResult(analysis),
      details: analysis,
      recommendations: analysis.recommendations
    };
  }

  /**
   * 获取或创建分析结果
   */
  async getOrCreateAnalysis(projectPath) {
    const cacheKey = `project_analysis_${projectPath}`;
    let analysis = this.getFromCache(cacheKey);

    if (!analysis) {
      analysis = await this.projectAnalyzer.analyzeProject(projectPath);
      this.setCache(cacheKey, analysis);
    }

    return analysis;
  }

  /**
   * 格式化分析结果
   */
  formatAnalysisResult(analysis) {
    const stats = analysis.statistics;
    const techStack = analysis.techStack;

    let result = `📊 **项目分析报告**\n\n`;
    result += `**基本信息：**\n`;
    result += `- 项目路径：${analysis.projectPath}\n`;
    result += `- 分析时间：${analysis.timestamp}\n`;
    result += `- 总文件数：${stats.totalFiles}\n`;
    result += `- 总目录数：${stats.totalDirectories}\n`;
    result += `- 项目大小：${this.formatFileSize(stats.totalSize)}\n\n`;

    // 主要编程语言
    if (techStack.languages && Object.keys(techStack.languages).length > 0) {
      const mainLang = Object.entries(techStack.languages).sort(([, a], [, b]) => b.files - a.files)[0];
      result += `**主要语言：** ${mainLang[0]} (${mainLang[1].files} 文件)\n\n`;
    }

    // 建议
    if (analysis.recommendations && analysis.recommendations.length > 0) {
      result += `**建议：**\n`;
      analysis.recommendations.forEach((rec) => {
        result += `- ${rec.message}\n`;
      });
    }

    return result;
  }

  /**
   * 格式化文件大小
   */
  formatFileSize(bytes) {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * 缓存操作
   */
  setCache(key, value) {
    this.analysisCache.set(key, {
      value,
      timestamp: Date.now()
    });
  }

  getFromCache(key) {
    const cached = this.analysisCache.get(key);
    if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
      return cached.value;
    }
    this.analysisCache.delete(key);
    return null;
  }

  /**
   * 清理缓存
   */
  clearCache() {
    this.analysisCache.clear();
    this.log('info', '分析缓存已清理');
  }
}

module.exports = AnalyzerAgent;
