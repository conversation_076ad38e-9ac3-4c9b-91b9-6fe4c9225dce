/**
 * 内存监控中间件
 * 监控每个请求的内存使用情况
 */

class MemoryMonitor {
    constructor(options = {}) {
        this.threshold = options.threshold || 80; // 内存使用率阈值
        this.logInterval = options.logInterval || 100; // 每100个请求记录一次
        this.requestCount = 0;
    }

    middleware() {
        return (req, res, next) => {
            const startMemory = process.memoryUsage();
            const startTime = Date.now();

            // 请求完成后检查内存
            res.on('finish', () => {
                this.requestCount++;
                const endMemory = process.memoryUsage();
                const duration = Date.now() - startTime;
                
                const memoryDiff = {
                    rss: endMemory.rss - startMemory.rss,
                    heapTotal: endMemory.heapTotal - startMemory.heapTotal,
                    heapUsed: endMemory.heapUsed - startMemory.heapUsed,
                    external: endMemory.external - startMemory.external
                };

                // 检查内存使用率
                const heapUtilization = (endMemory.heapUsed / endMemory.heapTotal) * 100;
                
                if (heapUtilization > this.threshold) {
                    console.warn(`[MEMORY WARNING] 内存使用率过高: ${heapUtilization.toFixed(2)}% (${req.method} ${req.path})`);
                }

                // 定期记录内存统计
                if (this.requestCount % this.logInterval === 0) {
                    console.log(`[MEMORY STATS] 请求 #${this.requestCount}: 堆内存 ${(endMemory.heapUsed / 1024 / 1024).toFixed(2)}MB, 使用率 ${heapUtilization.toFixed(2)}%`);
                }

                // 添加内存信息到响应头 (开发环境)
                if (process.env.NODE_ENV === 'development') {
                    res.set('X-Memory-Usage', `${(endMemory.heapUsed / 1024 / 1024).toFixed(2)}MB`);
                    res.set('X-Memory-Utilization', `${heapUtilization.toFixed(2)}%`);
                }
            });

            next();
        };
    }

    /**
     * 获取内存统计信息
     */
    getStats() {
        const memUsage = process.memoryUsage();
        return {
            rss: memUsage.rss,
            heapTotal: memUsage.heapTotal,
            heapUsed: memUsage.heapUsed,
            external: memUsage.external,
            arrayBuffers: memUsage.arrayBuffers,
            heapUtilization: (memUsage.heapUsed / memUsage.heapTotal) * 100,
            requestCount: this.requestCount
        };
    }

    /**
     * 重置统计
     */
    reset() {
        this.requestCount = 0;
    }
}

module.exports = MemoryMonitor;
