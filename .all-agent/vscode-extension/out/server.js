"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AllAgentServer = void 0;
const vscode = require("vscode");
const child_process_1 = require("child_process");
const path = require("path");
const fs = require("fs");
class AllAgentServer {
    constructor(context) {
        this.process = null;
        this.isRunning = false;
        this.context = context;
    }
    async start() {
        if (this.isRunning) {
            throw new Error('Server is already running');
        }
        const config = vscode.workspace.getConfiguration('all-agent');
        const port = config.get('serverPort', 3000);
        // 查找 All-Agent 服务器路径
        const serverPath = this.findServerPath();
        if (!serverPath) {
            throw new Error('All-Agent server not found. Please ensure the All-Agent project is in your workspace.');
        }
        // 创建环境变量
        const env = this.createEnvironment();
        return new Promise((resolve, reject) => {
            try {
                // 启动服务器进程
                this.process = (0, child_process_1.spawn)('node', ['app.js'], {
                    cwd: serverPath,
                    env: { ...process.env, ...env },
                    stdio: ['pipe', 'pipe', 'pipe']
                });
                // 监听输出
                this.process.stdout?.on('data', (data) => {
                    const output = data.toString();
                    console.log('[All-Agent Server]', output);
                    // 检查服务器是否启动成功
                    if (output.includes('All-Agent Server Started Successfully')) {
                        this.isRunning = true;
                        resolve();
                    }
                });
                this.process.stderr?.on('data', (data) => {
                    console.error('[All-Agent Server Error]', data.toString());
                });
                this.process.on('error', (error) => {
                    console.error('[All-Agent Server Process Error]', error);
                    this.isRunning = false;
                    reject(error);
                });
                this.process.on('exit', (code) => {
                    console.log(`[All-Agent Server] Process exited with code ${code}`);
                    this.isRunning = false;
                    this.process = null;
                });
                // 设置超时
                setTimeout(() => {
                    if (!this.isRunning) {
                        reject(new Error('Server startup timeout'));
                    }
                }, 30000); // 30秒超时
            }
            catch (error) {
                reject(error);
            }
        });
    }
    async stop() {
        if (!this.process || !this.isRunning) {
            return;
        }
        return new Promise((resolve) => {
            if (this.process) {
                this.process.on('exit', () => {
                    this.isRunning = false;
                    this.process = null;
                    resolve();
                });
                // 尝试优雅关闭
                this.process.kill('SIGTERM');
                // 如果5秒后还没关闭，强制杀死
                setTimeout(() => {
                    if (this.process) {
                        this.process.kill('SIGKILL');
                    }
                }, 5000);
            }
            else {
                resolve();
            }
        });
    }
    updateConfiguration() {
        // 如果服务器正在运行，重启以应用新配置
        if (this.isRunning) {
            this.stop().then(() => {
                this.start().catch(error => {
                    vscode.window.showErrorMessage(`Failed to restart server: ${error}`);
                });
            });
        }
    }
    findServerPath() {
        // 在工作区中查找 All-Agent 服务器
        const workspaceFolders = vscode.workspace.workspaceFolders;
        if (!workspaceFolders) {
            return null;
        }
        for (const folder of workspaceFolders) {
            // 检查是否是 All-Agent 项目根目录
            const allAgentPath = path.join(folder.uri.fsPath, '.all-agent', 'server');
            if (fs.existsSync(path.join(allAgentPath, 'app.js'))) {
                return allAgentPath;
            }
            // 检查是否直接在服务器目录中
            const serverPath = path.join(folder.uri.fsPath, 'server');
            if (fs.existsSync(path.join(serverPath, 'app.js'))) {
                return serverPath;
            }
            // 检查子目录
            const subDirs = ['all-agent', '.all-agent'];
            for (const subDir of subDirs) {
                const subPath = path.join(folder.uri.fsPath, subDir, 'server');
                if (fs.existsSync(path.join(subPath, 'app.js'))) {
                    return subPath;
                }
            }
        }
        return null;
    }
    createEnvironment() {
        const config = vscode.workspace.getConfiguration('all-agent');
        const env = {};
        // 设置端口
        env.PORT = config.get('serverPort', 3000).toString();
        // 设置 LLM 提供商
        env.DEFAULT_LLM_PROVIDER = config.get('llmProvider', 'deepseek');
        // 设置 API Keys
        const deepseekKey = config.get('deepseekApiKey', '');
        if (deepseekKey) {
            env.DEEPSEEK_API_KEY = deepseekKey;
        }
        const mistralKey = config.get('mistralApiKey', '');
        if (mistralKey) {
            env.MISTRAL_API_KEY = mistralKey;
        }
        const openaiKey = config.get('openaiApiKey', '');
        if (openaiKey) {
            env.OPENAI_API_KEY = openaiKey;
        }
        return env;
    }
    isServerRunning() {
        return this.isRunning;
    }
}
exports.AllAgentServer = AllAgentServer;
//# sourceMappingURL=server.js.map