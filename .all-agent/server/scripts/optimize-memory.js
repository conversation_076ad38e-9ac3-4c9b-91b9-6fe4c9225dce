#!/usr/bin/env node

/**
 * All-Agent 内存优化脚本
 * 清理缓存、优化内存使用、释放资源
 */

const path = require('path');
const fs = require('fs').promises;

class MemoryOptimizer {
    constructor() {
        this.stats = {
            initialMemory: process.memoryUsage(),
            cleanedItems: 0,
            freedMemory: 0
        };
    }

    /**
     * 执行内存优化
     */
    async optimize() {
        console.log('🧹 开始内存优化...');
        console.log(`📊 初始内存使用: ${this.formatMemory(this.stats.initialMemory.heapUsed)}`);

        try {
            // 1. 清理临时文件
            await this.cleanTempFiles();

            // 2. 清理日志文件
            await this.cleanLogFiles();

            // 3. 强制垃圾回收
            this.forceGarbageCollection();

            // 4. 显示优化结果
            await this.showResults();

        } catch (error) {
            console.error('❌ 内存优化失败:', error);
        }
    }

    /**
     * 清理临时文件
     */
    async cleanTempFiles() {
        console.log('🗑️ 清理临时文件...');
        
        const tempDirs = [
            path.join(__dirname, '../temp'),
            path.join(__dirname, '../uploads'),
            path.join(__dirname, '../cache'),
            '/tmp/all-agent'
        ];

        let cleanedFiles = 0;

        for (const dir of tempDirs) {
            try {
                const exists = await fs.access(dir).then(() => true).catch(() => false);
                if (exists) {
                    const files = await fs.readdir(dir);
                    for (const file of files) {
                        const filePath = path.join(dir, file);
                        const stats = await fs.stat(filePath);
                        
                        // 删除超过1小时的临时文件
                        if (Date.now() - stats.mtime.getTime() > 3600000) {
                            await fs.unlink(filePath);
                            cleanedFiles++;
                        }
                    }
                }
            } catch (error) {
                console.warn(`清理目录失败 ${dir}:`, error.message);
            }
        }

        console.log(`✅ 清理了 ${cleanedFiles} 个临时文件`);
        this.stats.cleanedItems += cleanedFiles;
    }

    /**
     * 清理日志文件
     */
    async cleanLogFiles() {
        console.log('📝 清理旧日志文件...');
        
        const logDir = path.join(__dirname, '../logs');
        let cleanedLogs = 0;

        try {
            const exists = await fs.access(logDir).then(() => true).catch(() => false);
            if (exists) {
                const files = await fs.readdir(logDir);
                
                for (const file of files) {
                    if (file.endsWith('.log')) {
                        const filePath = path.join(logDir, file);
                        const stats = await fs.stat(filePath);
                        
                        // 删除超过7天的日志文件
                        if (Date.now() - stats.mtime.getTime() > 7 * 24 * 3600000) {
                            await fs.unlink(filePath);
                            cleanedLogs++;
                        }
                        // 压缩超过1天的大日志文件
                        else if (stats.size > 10 * 1024 * 1024 && Date.now() - stats.mtime.getTime() > 24 * 3600000) {
                            await this.compressLogFile(filePath);
                        }
                    }
                }
            }
        } catch (error) {
            console.warn('清理日志文件失败:', error.message);
        }

        console.log(`✅ 清理了 ${cleanedLogs} 个旧日志文件`);
        this.stats.cleanedItems += cleanedLogs;
    }

    /**
     * 压缩日志文件
     */
    async compressLogFile(filePath) {
        try {
            const content = await fs.readFile(filePath, 'utf8');
            const lines = content.split('\n');
            
            // 只保留最后1000行
            const compressedContent = lines.slice(-1000).join('\n');
            await fs.writeFile(filePath, compressedContent);
            
            console.log(`📦 压缩日志文件: ${path.basename(filePath)}`);
        } catch (error) {
            console.warn(`压缩日志文件失败 ${filePath}:`, error.message);
        }
    }

    /**
     * 强制垃圾回收
     */
    forceGarbageCollection() {
        console.log('♻️ 执行垃圾回收...');
        
        if (global.gc) {
            const beforeGC = process.memoryUsage();
            global.gc();
            const afterGC = process.memoryUsage();
            
            const freedMemory = beforeGC.heapUsed - afterGC.heapUsed;
            this.stats.freedMemory = freedMemory;
            
            console.log(`✅ 垃圾回收完成，释放内存: ${this.formatMemory(freedMemory)}`);
        } else {
            console.log('⚠️ 垃圾回收不可用 (需要 --expose-gc 参数)');
        }
    }

    /**
     * 显示优化结果
     */
    async showResults() {
        const finalMemory = process.memoryUsage();
        const totalFreed = this.stats.initialMemory.heapUsed - finalMemory.heapUsed;
        
        console.log('\n📊 内存优化结果:');
        console.log('================================');
        console.log(`🗑️ 清理项目: ${this.stats.cleanedItems}`);
        console.log(`💾 初始内存: ${this.formatMemory(this.stats.initialMemory.heapUsed)}`);
        console.log(`💾 当前内存: ${this.formatMemory(finalMemory.heapUsed)}`);
        console.log(`📉 释放内存: ${this.formatMemory(Math.max(0, totalFreed))}`);
        console.log(`📈 内存使用率: ${this.getMemoryUsagePercent()}%`);
        
        // 内存使用详情
        console.log('\n📋 详细内存信息:');
        console.log(`   堆内存: ${this.formatMemory(finalMemory.heapUsed)} / ${this.formatMemory(finalMemory.heapTotal)}`);
        console.log(`   外部内存: ${this.formatMemory(finalMemory.external)}`);
        console.log(`   数组缓冲区: ${this.formatMemory(finalMemory.arrayBuffers)}`);
        
        // 系统内存信息
        try {
            const os = require('os');
            const totalMem = os.totalmem();
            const freeMem = os.freemem();
            const usedMem = totalMem - freeMem;
            
            console.log('\n🖥️ 系统内存信息:');
            console.log(`   总内存: ${this.formatMemory(totalMem)}`);
            console.log(`   已用内存: ${this.formatMemory(usedMem)}`);
            console.log(`   可用内存: ${this.formatMemory(freeMem)}`);
            console.log(`   使用率: ${((usedMem / totalMem) * 100).toFixed(1)}%`);
        } catch (error) {
            console.warn('获取系统内存信息失败:', error.message);
        }
    }

    /**
     * 格式化内存大小
     */
    formatMemory(bytes) {
        if (bytes < 1024) return `${bytes} B`;
        if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(2)} KB`;
        if (bytes < 1024 * 1024 * 1024) return `${(bytes / 1024 / 1024).toFixed(2)} MB`;
        return `${(bytes / 1024 / 1024 / 1024).toFixed(2)} GB`;
    }

    /**
     * 获取内存使用百分比
     */
    getMemoryUsagePercent() {
        try {
            const os = require('os');
            const totalMem = os.totalmem();
            const freeMem = os.freemem();
            const usedMem = totalMem - freeMem;
            return ((usedMem / totalMem) * 100).toFixed(1);
        } catch (error) {
            return 'N/A';
        }
    }

    /**
     * 获取内存优化建议
     */
    getOptimizationTips() {
        const tips = [
            '💡 定期重启应用以释放内存',
            '💡 关闭不必要的浏览器标签页',
            '💡 使用 --max-old-space-size 参数限制 Node.js 内存',
            '💡 启用 Redis 缓存以减少内存使用',
            '💡 优化数据库查询以减少内存占用',
            '💡 使用流式处理大文件',
            '💡 定期清理临时文件和日志'
        ];

        console.log('\n💡 内存优化建议:');
        tips.forEach(tip => console.log(`   ${tip}`));
    }
}

// 主函数
async function main() {
    const optimizer = new MemoryOptimizer();
    
    try {
        await optimizer.optimize();
        optimizer.getOptimizationTips();
        
        console.log('\n🎉 内存优化完成！');
        
    } catch (error) {
        console.error('❌ 内存优化失败:', error);
        process.exit(1);
    }
}

// 如果直接运行此脚本
if (require.main === module) {
    main();
}

module.exports = MemoryOptimizer;
