import * as vscode from 'vscode';
import { TerminalService } from './terminalService';

/**
 * 浏览器服务 - 提供浏览器感知和控制能力
 */
export class BrowserService {
    private terminalService: TerminalService;
    private activeServers: Map<string, {
        port: number;
        url: string;
        process?: any;
    }> = new Map();

    constructor(terminalService: TerminalService) {
        this.terminalService = terminalService;
    }

    /**
     * 检测本地服务器
     */
    async detectLocalServers(): Promise<Array<{
        port: number;
        url: string;
        status: 'running' | 'stopped';
        service?: string;
    }>> {
        const commonPorts = [3000, 3001, 4200, 5000, 8000, 8080, 8888, 9000];
        const servers: Array<{
            port: number;
            url: string;
            status: 'running' | 'stopped';
            service?: string;
        }> = [];

        for (const port of commonPorts) {
            const isRunning = await this.checkPort(port);
            if (isRunning) {
                const url = `http://localhost:${port}`;
                const service = await this.identifyService(port);
                
                servers.push({
                    port,
                    url,
                    status: 'running',
                    service
                });
            }
        }

        return servers;
    }

    /**
     * 检查端口是否被占用
     */
    private async checkPort(port: number): Promise<boolean> {
        const result = await this.terminalService.executeCommand(
            `curl -s -o /dev/null -w "%{http_code}" http://localhost:${port} || echo "000"`,
            { timeout: 3000 }
        );

        if (result.success) {
            const httpCode = result.output.trim();
            return httpCode !== '000' && httpCode !== '';
        }

        return false;
    }

    /**
     * 识别服务类型
     */
    private async identifyService(port: number): Promise<string | undefined> {
        try {
            const result = await this.terminalService.executeCommand(
                `curl -s -I http://localhost:${port}`,
                { timeout: 5000 }
            );

            if (result.success) {
                const headers = result.output.toLowerCase();
                
                if (headers.includes('server: nginx')) return 'Nginx';
                if (headers.includes('server: apache')) return 'Apache';
                if (headers.includes('express')) return 'Express.js';
                if (headers.includes('next.js')) return 'Next.js';
                if (headers.includes('react')) return 'React Dev Server';
                if (headers.includes('webpack')) return 'Webpack Dev Server';
                if (headers.includes('vite')) return 'Vite';
                
                // 检查特定端口的常见服务
                switch (port) {
                    case 3000: return 'React/Next.js';
                    case 4200: return 'Angular';
                    case 8080: return 'Spring Boot/Tomcat';
                    case 5000: return 'Flask/Express';
                    default: return 'Web Server';
                }
            }
        } catch (error) {
            // 忽略错误
        }

        return undefined;
    }

    /**
     * 获取页面内容
     */
    async getPageContent(url: string): Promise<{
        success: boolean;
        title?: string;
        content?: string;
        status?: number;
        error?: string;
    }> {
        try {
            const result = await this.terminalService.executeCommand(
                `curl -s -L "${url}"`,
                { timeout: 10000 }
            );

            if (result.success) {
                const content = result.output;
                const title = this.extractTitle(content);
                
                return {
                    success: true,
                    title,
                    content,
                    status: 200
                };
            } else {
                return {
                    success: false,
                    error: result.error || 'Failed to fetch page'
                };
            }
        } catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : String(error)
            };
        }
    }

    /**
     * 从 HTML 中提取标题
     */
    private extractTitle(html: string): string | undefined {
        const titleMatch = html.match(/<title[^>]*>([^<]+)<\/title>/i);
        return titleMatch ? titleMatch[1].trim() : undefined;
    }

    /**
     * 检查页面状态
     */
    async checkPageStatus(url: string): Promise<{
        status: number;
        responseTime: number;
        accessible: boolean;
        error?: string;
    }> {
        const startTime = Date.now();
        
        try {
            const result = await this.terminalService.executeCommand(
                `curl -s -o /dev/null -w "%{http_code},%{time_total}" "${url}"`,
                { timeout: 10000 }
            );

            const responseTime = Date.now() - startTime;

            if (result.success) {
                const [statusCode, totalTime] = result.output.split(',');
                const status = parseInt(statusCode);
                
                return {
                    status,
                    responseTime: parseFloat(totalTime) * 1000,
                    accessible: status >= 200 && status < 400
                };
            } else {
                return {
                    status: 0,
                    responseTime,
                    accessible: false,
                    error: result.error
                };
            }
        } catch (error) {
            return {
                status: 0,
                responseTime: Date.now() - startTime,
                accessible: false,
                error: error instanceof Error ? error.message : String(error)
            };
        }
    }

    /**
     * 启动开发服务器
     */
    async startDevServer(command: string = 'npm start'): Promise<{
        success: boolean;
        port?: number;
        url?: string;
        error?: string;
    }> {
        try {
            // 在后台启动服务器
            const result = await this.terminalService.executeInTerminal(command, 'Dev Server');
            
            // 等待服务器启动
            await new Promise(resolve => setTimeout(resolve, 3000));
            
            // 检测启动的服务器
            const servers = await this.detectLocalServers();
            const newServer = servers.find(s => s.status === 'running');
            
            if (newServer) {
                this.activeServers.set('dev', {
                    port: newServer.port,
                    url: newServer.url
                });
                
                return {
                    success: true,
                    port: newServer.port,
                    url: newServer.url
                };
            } else {
                return {
                    success: false,
                    error: 'Server started but not detected on common ports'
                };
            }
        } catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : String(error)
            };
        }
    }

    /**
     * 打开浏览器
     */
    async openBrowser(url: string): Promise<boolean> {
        try {
            let command: string;
            
            switch (process.platform) {
                case 'darwin':
                    command = `open "${url}"`;
                    break;
                case 'win32':
                    command = `start "${url}"`;
                    break;
                default:
                    command = `xdg-open "${url}"`;
                    break;
            }
            
            const result = await this.terminalService.executeCommand(command, { timeout: 5000 });
            return result.success;
        } catch (error) {
            return false;
        }
    }

    /**
     * 监控页面变化
     */
    async monitorPage(url: string, interval: number = 5000): Promise<{
        stop: () => void;
        onStatusChange: (callback: (status: any) => void) => void;
    }> {
        let monitoring = true;
        let lastStatus: any = null;
        const callbacks: Array<(status: any) => void> = [];

        const monitor = async () => {
            while (monitoring) {
                try {
                    const status = await this.checkPageStatus(url);
                    
                    if (!lastStatus || 
                        status.status !== lastStatus.status || 
                        status.accessible !== lastStatus.accessible) {
                        
                        lastStatus = status;
                        callbacks.forEach(callback => callback(status));
                    }
                } catch (error) {
                    // 忽略监控错误
                }
                
                await new Promise(resolve => setTimeout(resolve, interval));
            }
        };

        // 开始监控
        monitor();

        return {
            stop: () => {
                monitoring = false;
            },
            onStatusChange: (callback: (status: any) => void) => {
                callbacks.push(callback);
            }
        };
    }

    /**
     * 获取页面性能信息
     */
    async getPagePerformance(url: string): Promise<{
        loadTime: number;
        size: number;
        requests: number;
        error?: string;
    }> {
        try {
            const result = await this.terminalService.executeCommand(
                `curl -s -o /dev/null -w "time_total:%{time_total},size_download:%{size_download},num_redirects:%{num_redirects}" "${url}"`,
                { timeout: 15000 }
            );

            if (result.success) {
                const metrics = result.output;
                const timeMatch = metrics.match(/time_total:([0-9.]+)/);
                const sizeMatch = metrics.match(/size_download:([0-9]+)/);
                const redirectsMatch = metrics.match(/num_redirects:([0-9]+)/);

                return {
                    loadTime: timeMatch ? parseFloat(timeMatch[1]) * 1000 : 0,
                    size: sizeMatch ? parseInt(sizeMatch[1]) : 0,
                    requests: redirectsMatch ? parseInt(redirectsMatch[1]) + 1 : 1
                };
            } else {
                return {
                    loadTime: 0,
                    size: 0,
                    requests: 0,
                    error: result.error
                };
            }
        } catch (error) {
            return {
                loadTime: 0,
                size: 0,
                requests: 0,
                error: error instanceof Error ? error.message : String(error)
            };
        }
    }

    /**
     * 清理资源
     */
    dispose(): void {
        this.activeServers.clear();
    }
}
