const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const compression = require('compression');
const morgan = require('morgan');
const rateLimit = require('express-rate-limit');

/**
 * 中间件设置模块
 */
function setupMiddleware(app, { logger, authService, monitor }) {
  // 安全中间件 - Helmet
  app.use(helmet({
    contentSecurityPolicy: {
      directives: {
        defaultSrc: ["'self'"],
        styleSrc: ["'self'", "'unsafe-inline'"],
        scriptSrc: ["'self'", "'unsafe-inline'"],
        imgSrc: ["'self'", "data:", "https:"],
        connectSrc: ["'self'"],
        fontSrc: ["'self'"],
        objectSrc: ["'none'"],
        mediaSrc: ["'self'"],
        frameSrc: ["'none'"],
      },
    },
    crossOriginEmbedderPolicy: false
  }));

  // CORS 中间件
  const corsOptions = {
    origin: process.env.CORS_ORIGIN || '*',
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization'],
    credentials: true
  };
  app.use(cors(corsOptions));

  // 压缩中间件
  app.use(compression());

  // 请求体解析中间件
  app.use(express.json({ 
    limit: '10mb',
    verify: (req, res, buf) => {
      // 验证 JSON 格式
      try {
        JSON.parse(buf);
      } catch (e) {
        res.status(400).json({
          success: false,
          error: 'Invalid JSON format'
        });
        return;
      }
    }
  }));
  
  app.use(express.urlencoded({ 
    extended: true, 
    limit: '10mb' 
  }));

  // 请求日志中间件
  if (logger) {
    const morganFormat = process.env.NODE_ENV === 'production' ? 'combined' : 'dev';
    app.use(morgan(morganFormat, {
      stream: {
        write: (message) => {
          logger.info(message.trim());
        }
      }
    }));
  }

  // 全局速率限制
  const globalLimiter = rateLimit({
    windowMs: parseInt(process.env.RATE_LIMIT_WINDOW) || 15 * 60 * 1000, // 15分钟
    max: parseInt(process.env.RATE_LIMIT_MAX) || 1000, // 每个IP最多1000个请求
    message: {
      success: false,
      error: 'Too many requests from this IP, please try again later.',
      retryAfter: '15 minutes'
    },
    standardHeaders: true,
    legacyHeaders: false,
    skip: (req) => {
      // 跳过健康检查和监控端点
      return req.path === '/health' || 
             req.path.startsWith('/monitor') ||
             req.path === '/info';
    }
  });
  app.use(globalLimiter);

  // 请求追踪中间件
  app.use((req, res, next) => {
    req.requestId = require('crypto').randomUUID();
    req.startTime = Date.now();
    
    // 添加请求 ID 到响应头
    res.setHeader('X-Request-ID', req.requestId);
    
    // 记录请求开始
    if (logger) {
      logger.info(`Request started: ${req.method} ${req.path}`, {
        requestId: req.requestId,
        ip: req.ip,
        userAgent: req.get('User-Agent')
      });
    }
    
    next();
  });

  // 响应时间中间件
  app.use((req, res, next) => {
    const originalSend = res.send;
    
    res.send = function(data) {
      const duration = Date.now() - req.startTime;
      
      // 记录响应时间
      if (logger) {
        logger.info(`Request completed: ${req.method} ${req.path}`, {
          requestId: req.requestId,
          statusCode: res.statusCode,
          duration: `${duration}ms`
        });
      }
      
      // 添加响应时间到头部
      res.setHeader('X-Response-Time', `${duration}ms`);
      
      // 记录到监控系统
      if (monitor && monitor.recordMetric) {
        monitor.recordMetric('http_request_duration', duration, {
          method: req.method,
          path: req.path,
          statusCode: res.statusCode
        });
      }
      
      return originalSend.call(this, data);
    };
    
    next();
  });

  // 错误处理中间件
  app.use((error, req, res, next) => {
    // 记录错误
    if (logger) {
      logger.error('Request error:', {
        requestId: req.requestId,
        error: error.message,
        stack: error.stack,
        path: req.path,
        method: req.method
      });
    }

    // 检查是否是操作性错误
    if (error.isOperational) {
      return res.status(error.statusCode || 500).json({
        success: false,
        error: {
          code: error.code,
          message: error.message,
          requestId: req.requestId
        }
      });
    }

    // 处理程序错误
    const response = {
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'An internal server error occurred',
        requestId: req.requestId
      }
    };

    // 在开发环境中包含错误详情
    if (process.env.NODE_ENV === 'development') {
      response.error.message = error.message;
      response.error.stack = error.stack;
    }

    res.status(500).json(response);
  });

  // 输入清理中间件
  app.use((req, res, next) => {
    if (req.body && typeof req.body === 'object') {
      req.body = sanitizeObject(req.body);
    }
    next();
  });
}

/**
 * 清理对象中的恶意内容
 */
function sanitizeObject(obj) {
  if (typeof obj !== 'object' || obj === null) {
    return obj;
  }

  const sanitized = Array.isArray(obj) ? [] : {};

  for (const [key, value] of Object.entries(obj)) {
    if (typeof value === 'string') {
      // 移除潜在的恶意脚本
      sanitized[key] = value
        .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
        .replace(/javascript:/gi, '')
        .replace(/on\w+\s*=/gi, '');
    } else if (typeof value === 'object' && value !== null) {
      sanitized[key] = sanitizeObject(value);
    } else {
      sanitized[key] = value;
    }
  }

  return sanitized;
}

/**
 * WebSocket 设置
 */
function setupWebSocket(io, { authService, agentManager, logger }) {
  // WebSocket 认证中间件
  io.use(async (socket, next) => {
    try {
      const token = socket.handshake.auth.token;
      if (token) {
        const user = await authService.verifyToken(token);
        socket.user = user;
      }
      next();
    } catch (error) {
      next(new Error('Authentication failed'));
    }
  });

  // 连接处理
  io.on('connection', (socket) => {
    if (logger) {
      logger.info(`WebSocket client connected: ${socket.id}`, {
        userId: socket.user?.id,
        ip: socket.handshake.address
      });
    }

    // 加入用户房间（如果已认证）
    if (socket.user) {
      socket.join(`user:${socket.user.id}`);
    }

    // 任务状态更新
    socket.on('subscribe:tasks', (data) => {
      if (socket.user) {
        socket.join(`tasks:${socket.user.id}`);
        if (logger) {
          logger.info(`User ${socket.user.id} subscribed to task updates`);
        }
      }
    });

    // Agent 状态更新
    socket.on('subscribe:agents', () => {
      socket.join('agents:status');
      if (logger) {
        logger.info(`Client ${socket.id} subscribed to agent updates`);
      }
    });

    // 系统状态更新
    socket.on('subscribe:system', () => {
      socket.join('system:status');
      if (logger) {
        logger.info(`Client ${socket.id} subscribed to system updates`);
      }
    });

    // 断开连接处理
    socket.on('disconnect', (reason) => {
      if (logger) {
        logger.info(`WebSocket client disconnected: ${socket.id}`, {
          reason,
          userId: socket.user?.id
        });
      }
    });

    // 错误处理
    socket.on('error', (error) => {
      if (logger) {
        logger.error(`WebSocket error for client ${socket.id}:`, error);
      }
    });
  });

  // 定期广播系统状态
  setInterval(() => {
    const systemStatus = {
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      agents: agentManager ? agentManager.getAgentStatus() : { active: 0 }
    };

    io.to('system:status').emit('system:update', systemStatus);
  }, 30000); // 每30秒更新一次
}

module.exports = {
  setupMiddleware,
  setupWebSocket
};
