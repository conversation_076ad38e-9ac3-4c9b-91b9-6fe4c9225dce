#!/usr/bin/env node

/**
 * 测试 AI 响应 - 直接测试 LLMService
 */

// 加载环境变量
const path = require('path');
const fs = require('fs');

function loadEnvironmentVariables() {
  const possibleEnvPaths = [
    path.join(__dirname, '.env'),
    path.join(__dirname, '../.env'),
    path.join(process.cwd(), '.all-agent/.env'),
    path.join(process.cwd(), '.all-agent/server/.env')
  ];

  for (const envPath of possibleEnvPaths) {
    if (fs.existsSync(envPath)) {
      require('dotenv').config({ path: envPath });
      console.log(`✅ 环境变量已从 ${envPath} 加载`);
      break;
    }
  }
}

loadEnvironmentVariables();

const LLMService = require('./api/LLMService');

// 简单的日志器
const logger = {
  info: (msg, data) => console.log(`[INFO] ${msg}`, data || ''),
  warn: (msg, data) => console.warn(`[WARN] ${msg}`, data || ''),
  error: (msg, data) => console.error(`[ERROR] ${msg}`, data || ''),
  debug: (msg, data) => console.debug(`[DEBUG] ${msg}`, data || '')
};

async function testAIResponse() {
  console.log('🧪 测试 AI 响应生成...\n');

  try {
    // 创建 LLM 服务实例
    const llmService = new LLMService(logger);
    
    // 初始化服务
    await llmService.initialize();
    
    console.log('\n📋 当前配置:');
    console.log('默认提供商:', llmService.defaultProvider);
    console.log('可用提供商:', Array.from(llmService.providers.keys()).filter(key => llmService.providers.get(key).available));
    
    // 测试消息
    const testMessages = [
      "你好！请简单介绍一下你自己。",
      "请帮我分析一下这个项目的架构特点。",
      "我需要制定一个开发计划，你有什么建议？"
    ];
    
    for (let i = 0; i < testMessages.length; i++) {
      const message = testMessages[i];
      console.log(`\n🧪 测试 ${i + 1}: "${message}"`);
      console.log('='.repeat(60));
      
      try {
        const startTime = Date.now();
        
        // 调用 generateResponse 方法
        const response = await llmService.generateResponse(message, {
          agentType: 'test',
          maxTokens: 200,
          temperature: 0.7
        });
        
        const duration = Date.now() - startTime;
        
        console.log(`✅ 响应成功 (${duration}ms)`);
        console.log(`📝 响应内容:`);
        console.log(response);
        console.log('');
        
      } catch (error) {
        console.error(`❌ 响应失败:`, error.message);
      }
    }
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    console.error('错误堆栈:', error.stack);
  }
}

// 运行测试
if (require.main === module) {
  testAIResponse().catch(error => {
    console.error('测试过程中发生错误:', error);
    process.exit(1);
  });
}

module.exports = { testAIResponse };
