{"template_id": "refactor_code", "name": "代码重构优化", "description": "智能分析代码并提供重构建议和优化方案", "category": "code_optimization", "version": "1.0.0", "author": "All-Agent System", "tags": ["refactor", "optimization", "code_quality", "best_practices"], "prompt_template": "你是一个资深的软件架构师和代码重构专家。请分析以下代码并提供专业的重构建议。\n\n## 代码分析目标\n文件路径: {{file_path}}\n编程语言: {{programming_language}}\n重构目标: {{refactor_goals}}\n性能要求: {{performance_requirements}}\n\n## 待分析代码\n```{{programming_language}}\n{{source_code}}\n```\n\n## 分析维度\n请从以下维度进行分析：\n\n### 1. 代码质量评估\n- 可读性和可维护性\n- 代码复杂度分析\n- 命名规范检查\n- 注释完整性\n\n### 2. 架构设计分析\n- 设计模式应用\n- 职责分离原则\n- 依赖关系优化\n- 模块化程度\n\n### 3. 性能优化建议\n- 算法效率分析\n- 内存使用优化\n- 数据库查询优化\n- 缓存策略建议\n\n### 4. 安全性检查\n- 潜在安全漏洞\n- 输入验证检查\n- 权限控制分析\n- 数据保护建议\n\n## 输出格式\n\n### 📊 代码质量评分\n- 整体评分: [1-10分]\n- 可读性: [1-10分]\n- 可维护性: [1-10分]\n- 性能: [1-10分]\n- 安全性: [1-10分]\n\n### 🔍 问题识别\n1. **严重问题** (需要立即修复)\n   - [列出严重问题及影响]\n\n2. **一般问题** (建议优化)\n   - [列出一般问题及建议]\n\n3. **改进建议** (可选优化)\n   - [列出改进建议]\n\n### 🛠️ 重构方案\n\n#### 方案一：渐进式重构 (推荐)\n```{{programming_language}}\n[提供重构后的代码示例]\n```\n\n#### 方案二：完全重写 (如果需要)\n```{{programming_language}}\n[提供重写后的代码示例]\n```\n\n### 📋 实施步骤\n1. [具体的重构步骤]\n2. [测试验证方法]\n3. [风险控制措施]\n\n### 🎯 预期收益\n- 性能提升: [具体数据]\n- 维护成本降低: [具体说明]\n- 代码质量提升: [具体指标]", "parameters": {"file_path": {"type": "string", "description": "文件路径", "required": true, "placeholder": "例如: src/components/UserProfile.js"}, "programming_language": {"type": "string", "description": "编程语言", "default": "javascript", "options": ["javascript", "typescript", "python", "java", "go", "rust", "php", "c#"]}, "source_code": {"type": "text", "description": "源代码", "required": true, "placeholder": "粘贴需要重构的代码"}, "refactor_goals": {"type": "array", "description": "重构目标", "default": ["提高可读性", "优化性能"], "options": ["提高可读性", "优化性能", "减少复杂度", "提高安全性", "改善架构", "减少重复代码"]}, "performance_requirements": {"type": "string", "description": "性能要求", "default": "一般", "options": ["低", "一般", "高", "极高"]}}, "example_usage": {"input": {"file_path": "src/utils/dataProcessor.js", "programming_language": "javascript", "source_code": "function processData(data) { /* 复杂的数据处理逻辑 */ }", "refactor_goals": ["提高可读性", "优化性能"], "performance_requirements": "高"}, "expected_output": "详细的代码分析报告，包含质量评分、问题识别、重构方案和实施步骤"}, "validation_rules": ["必须提供具体的代码质量评分", "必须识别出至少3个改进点", "重构代码必须保持原有功能", "必须提供详细的实施步骤", "必须评估重构的风险和收益"], "related_templates": ["debug_code_issues", "optimize_performance", "security_audit", "code_review"], "metadata": {"created_at": "2024-12-19", "last_updated": "2024-12-19", "usage_count": 0, "success_rate": 0, "estimated_execution_time": "5-15 minutes"}}