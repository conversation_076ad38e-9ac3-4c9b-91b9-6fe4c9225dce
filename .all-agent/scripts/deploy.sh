#!/bin/bash

# All-Agent 部署脚本
# 支持 Docker Compose 和 Kubernetes 部署

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
DEPLOYMENT_TYPE=${1:-docker-compose}
ENVIRONMENT=${2:-development}

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    cat << EOF
All-Agent 部署脚本

用法:
    $0 [部署类型] [环境]

部署类型:
    docker-compose  使用 Docker Compose 部署 (默认)
    kubernetes      使用 Kubernetes 部署
    local           本地开发部署

环境:
    development     开发环境 (默认)
    staging         测试环境
    production      生产环境

示例:
    $0 docker-compose development
    $0 kubernetes production
    $0 local development

选项:
    -h, --help      显示此帮助信息
    --clean         清理现有部署
    --build         强制重新构建镜像
    --logs          显示部署日志
EOF
}

# 检查依赖
check_dependencies() {
    log_info "检查部署依赖..."
    
    case $DEPLOYMENT_TYPE in
        docker-compose)
            if ! command -v docker &> /dev/null; then
                log_error "Docker 未安装"
                exit 1
            fi
            if ! command -v docker-compose &> /dev/null; then
                log_error "Docker Compose 未安装"
                exit 1
            fi
            ;;
        kubernetes)
            if ! command -v kubectl &> /dev/null; then
                log_error "kubectl 未安装"
                exit 1
            fi
            if ! command -v helm &> /dev/null; then
                log_warning "Helm 未安装，将使用原生 Kubernetes 清单"
            fi
            ;;
        local)
            if ! command -v node &> /dev/null; then
                log_error "Node.js 未安装"
                exit 1
            fi
            if ! command -v npm &> /dev/null; then
                log_error "npm 未安装"
                exit 1
            fi
            ;;
    esac
    
    log_success "依赖检查完成"
}

# 准备环境配置
prepare_environment() {
    log_info "准备 $ENVIRONMENT 环境配置..."
    
    cd "$PROJECT_ROOT"
    
    # 复制环境配置文件
    if [ ! -f ".env" ]; then
        if [ -f "server/.env.example" ]; then
            cp server/.env.example .env
            log_info "已创建 .env 文件，请根据需要修改配置"
        else
            log_warning "未找到 .env.example 文件"
        fi
    fi
    
    # 根据环境设置特定配置
    case $ENVIRONMENT in
        development)
            export NODE_ENV=development
            export LOG_LEVEL=debug
            ;;
        staging)
            export NODE_ENV=staging
            export LOG_LEVEL=info
            ;;
        production)
            export NODE_ENV=production
            export LOG_LEVEL=warn
            ;;
    esac
    
    log_success "环境配置准备完成"
}

# Docker Compose 部署
deploy_docker_compose() {
    log_info "使用 Docker Compose 部署..."
    
    cd "$PROJECT_ROOT"
    
    # 构建镜像
    if [ "$BUILD_IMAGES" = "true" ]; then
        log_info "构建 Docker 镜像..."
        docker-compose build --no-cache
    fi
    
    # 启动服务
    log_info "启动服务..."
    docker-compose up -d
    
    # 等待服务启动
    log_info "等待服务启动..."
    sleep 30
    
    # 健康检查
    check_health_docker_compose
    
    log_success "Docker Compose 部署完成"
}

# Kubernetes 部署
deploy_kubernetes() {
    log_info "使用 Kubernetes 部署..."
    
    cd "$PROJECT_ROOT"
    
    # 检查集群连接
    if ! kubectl cluster-info &> /dev/null; then
        log_error "无法连接到 Kubernetes 集群"
        exit 1
    fi
    
    # 创建命名空间
    kubectl apply -f k8s/namespace.yaml
    
    # 创建配置和密钥
    kubectl apply -f k8s/configmap.yaml
    kubectl apply -f k8s/secrets.yaml
    
    # 创建存储
    kubectl apply -f k8s/pvc.yaml
    
    # 部署服务
    kubectl apply -f k8s/deployment.yaml
    kubectl apply -f k8s/service.yaml
    
    # 部署 Ingress（如果存在）
    if [ -f "k8s/ingress.yaml" ]; then
        kubectl apply -f k8s/ingress.yaml
    fi
    
    # 等待部署完成
    log_info "等待部署完成..."
    kubectl wait --for=condition=available --timeout=300s deployment/all-agent-app -n all-agent
    
    # 健康检查
    check_health_kubernetes
    
    log_success "Kubernetes 部署完成"
}

# 本地部署
deploy_local() {
    log_info "本地开发部署..."
    
    cd "$PROJECT_ROOT/server"
    
    # 安装依赖
    log_info "安装依赖..."
    npm install
    
    # 运行测试
    if [ "$SKIP_TESTS" != "true" ]; then
        log_info "运行测试..."
        npm test
    fi
    
    # 启动服务
    log_info "启动服务..."
    if [ "$ENVIRONMENT" = "development" ]; then
        npm run dev &
    else
        npm start &
    fi
    
    # 等待服务启动
    sleep 10
    
    # 健康检查
    check_health_local
    
    log_success "本地部署完成"
}

# Docker Compose 健康检查
check_health_docker_compose() {
    log_info "执行健康检查..."
    
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -f http://localhost:3000/health &> /dev/null; then
            log_success "服务健康检查通过"
            return 0
        fi
        
        log_info "等待服务启动... ($attempt/$max_attempts)"
        sleep 10
        ((attempt++))
    done
    
    log_error "健康检查失败"
    docker-compose logs
    return 1
}

# Kubernetes 健康检查
check_health_kubernetes() {
    log_info "执行健康检查..."
    
    # 检查 Pod 状态
    kubectl get pods -n all-agent
    
    # 端口转发进行健康检查
    kubectl port-forward svc/all-agent-app 3000:3000 -n all-agent &
    local port_forward_pid=$!
    
    sleep 10
    
    if curl -f http://localhost:3000/health &> /dev/null; then
        log_success "服务健康检查通过"
        kill $port_forward_pid
        return 0
    else
        log_error "健康检查失败"
        kubectl logs -l app.kubernetes.io/name=all-agent -n all-agent
        kill $port_forward_pid
        return 1
    fi
}

# 本地健康检查
check_health_local() {
    log_info "执行健康检查..."
    
    local max_attempts=10
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -f http://localhost:3000/health &> /dev/null; then
            log_success "服务健康检查通过"
            return 0
        fi
        
        log_info "等待服务启动... ($attempt/$max_attempts)"
        sleep 5
        ((attempt++))
    done
    
    log_error "健康检查失败"
    return 1
}

# 清理部署
cleanup_deployment() {
    log_info "清理现有部署..."
    
    case $DEPLOYMENT_TYPE in
        docker-compose)
            cd "$PROJECT_ROOT"
            docker-compose down -v
            docker system prune -f
            ;;
        kubernetes)
            kubectl delete namespace all-agent --ignore-not-found=true
            ;;
        local)
            pkill -f "node.*app.js" || true
            ;;
    esac
    
    log_success "清理完成"
}

# 显示部署信息
show_deployment_info() {
    log_success "部署完成！"
    
    case $DEPLOYMENT_TYPE in
        docker-compose)
            echo ""
            echo "🌐 服务访问地址:"
            echo "   主应用: http://localhost:3000"
            echo "   API 文档: http://localhost:3000/api-docs"
            echo "   监控面板: http://localhost:3000/monitor"
            echo "   Grafana: http://localhost:3001"
            echo ""
            echo "📋 管理命令:"
            echo "   查看日志: docker-compose logs -f"
            echo "   停止服务: docker-compose down"
            echo "   重启服务: docker-compose restart"
            ;;
        kubernetes)
            echo ""
            echo "🌐 服务访问地址:"
            echo "   主应用: kubectl port-forward svc/all-agent-app 3000:3000 -n all-agent"
            echo ""
            echo "📋 管理命令:"
            echo "   查看状态: kubectl get pods -n all-agent"
            echo "   查看日志: kubectl logs -l app.kubernetes.io/name=all-agent -n all-agent"
            echo "   删除部署: kubectl delete namespace all-agent"
            ;;
        local)
            echo ""
            echo "🌐 服务访问地址:"
            echo "   主应用: http://localhost:3000"
            echo "   API 文档: http://localhost:3000/api-docs"
            echo "   监控面板: http://localhost:3000/monitor"
            ;;
    esac
}

# 主函数
main() {
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            --clean)
                CLEAN_DEPLOYMENT=true
                shift
                ;;
            --build)
                BUILD_IMAGES=true
                shift
                ;;
            --logs)
                SHOW_LOGS=true
                shift
                ;;
            --skip-tests)
                SKIP_TESTS=true
                shift
                ;;
            *)
                if [ -z "$DEPLOYMENT_TYPE" ]; then
                    DEPLOYMENT_TYPE=$1
                elif [ -z "$ENVIRONMENT" ]; then
                    ENVIRONMENT=$1
                fi
                shift
                ;;
        esac
    done
    
    # 验证参数
    case $DEPLOYMENT_TYPE in
        docker-compose|kubernetes|local)
            ;;
        *)
            log_error "无效的部署类型: $DEPLOYMENT_TYPE"
            show_help
            exit 1
            ;;
    esac
    
    case $ENVIRONMENT in
        development|staging|production)
            ;;
        *)
            log_error "无效的环境: $ENVIRONMENT"
            show_help
            exit 1
            ;;
    esac
    
    log_info "开始部署 All-Agent"
    log_info "部署类型: $DEPLOYMENT_TYPE"
    log_info "环境: $ENVIRONMENT"
    
    # 清理现有部署
    if [ "$CLEAN_DEPLOYMENT" = "true" ]; then
        cleanup_deployment
    fi
    
    # 检查依赖
    check_dependencies
    
    # 准备环境
    prepare_environment
    
    # 执行部署
    case $DEPLOYMENT_TYPE in
        docker-compose)
            deploy_docker_compose
            ;;
        kubernetes)
            deploy_kubernetes
            ;;
        local)
            deploy_local
            ;;
    esac
    
    # 显示部署信息
    show_deployment_info
    
    # 显示日志
    if [ "$SHOW_LOGS" = "true" ]; then
        case $DEPLOYMENT_TYPE in
            docker-compose)
                docker-compose logs -f
                ;;
            kubernetes)
                kubectl logs -f -l app.kubernetes.io/name=all-agent -n all-agent
                ;;
        esac
    fi
}

# 执行主函数
main "$@"
