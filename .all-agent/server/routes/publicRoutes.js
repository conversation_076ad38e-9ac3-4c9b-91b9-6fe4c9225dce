const express = require('express');
const path = require('path');

/**
 * 公共路由模块
 */
class PublicRoutes {
  constructor() {
    this.router = express.Router();
    this.setupRoutes();
  }

  setupRoutes() {
    // 根路径重定向
    this.router.get('/', this.redirectToUI.bind(this));

    // API 文档
    this.router.get('/docs', this.getAPIDocs.bind(this));

    // 系统信息
    this.router.get('/info', this.getSystemInfo.bind(this));

    // 静态文件服务
    this.router.use('/ui', express.static(path.join(__dirname, '../../ui')));

    // Socket.IO 客户端库
    this.router.use('/socket.io', express.static(path.join(__dirname, '../node_modules/socket.io/client-dist')));

    // 404 处理
    this.router.use('*', this.handle404.bind(this));
  }

  /**
   * 重定向到 UI
   */
  redirectToUI(req, res) {
    res.redirect('/ui/');
  }

  /**
   * API 文档
   */
  getAPIDocs(req, res) {
    const docsHtml = `
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>All-Agent API 文档</title>
        <style>
            * { margin: 0; padding: 0; box-sizing: border-box; }
            body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; color: #333; }
            .header { background: #2c3e50; color: white; padding: 2rem; text-align: center; }
            .container { max-width: 1000px; margin: 0 auto; padding: 2rem; }
            .endpoint { background: #f8f9fa; border-left: 4px solid #007bff; padding: 1rem; margin: 1rem 0; border-radius: 4px; }
            .method { display: inline-block; padding: 0.25rem 0.5rem; border-radius: 3px; color: white; font-weight: bold; margin-right: 0.5rem; }
            .get { background: #28a745; }
            .post { background: #007bff; }
            .put { background: #ffc107; color: #212529; }
            .delete { background: #dc3545; }
            .code { background: #f1f3f4; padding: 0.5rem; border-radius: 3px; font-family: monospace; }
            .section { margin: 2rem 0; }
            h2 { color: #2c3e50; border-bottom: 2px solid #ecf0f1; padding-bottom: 0.5rem; }
            h3 { color: #34495e; margin: 1rem 0 0.5rem 0; }
        </style>
    </head>
    <body>
        <div class="header">
            <h1>📚 All-Agent API 文档</h1>
            <p>RESTful API 接口文档</p>
        </div>
        
        <div class="container">
            <div class="section">
                <h2>🔐 认证接口</h2>
                
                <div class="endpoint">
                    <h3><span class="method post">POST</span>/auth/register</h3>
                    <p>用户注册</p>
                    <div class="code">
{
  "username": "string",
  "email": "string",
  "password": "string"
}
                    </div>
                </div>
                
                <div class="endpoint">
                    <h3><span class="method post">POST</span>/auth/login</h3>
                    <p>用户登录</p>
                    <div class="code">
{
  "username": "string",
  "password": "string"
}
                    </div>
                </div>
                
                <div class="endpoint">
                    <h3><span class="method get">GET</span>/auth/me</h3>
                    <p>获取当前用户信息（需要认证）</p>
                </div>
                
                <div class="endpoint">
                    <h3><span class="method post">POST</span>/auth/logout</h3>
                    <p>用户登出（需要认证）</p>
                </div>
            </div>
            
            <div class="section">
                <h2>🤖 Agent 接口</h2>
                
                <div class="endpoint">
                    <h3><span class="method get">GET</span>/api/agents</h3>
                    <p>获取所有 Agent 状态</p>
                </div>
                
                <div class="endpoint">
                    <h3><span class="method post">POST</span>/api/tasks</h3>
                    <p>提交任务（需要认证）</p>
                    <div class="code">
{
  "agentType": "analyzer|planner|executor",
  "action": "string",
  "input": "any",
  "options": {}
}
                    </div>
                </div>
                
                <div class="endpoint">
                    <h3><span class="method get">GET</span>/api/tasks/:taskId</h3>
                    <p>获取任务状态</p>
                </div>
            </div>
            
            <div class="section">
                <h2>📊 分析接口</h2>
                
                <div class="endpoint">
                    <h3><span class="method post">POST</span>/api/analyze</h3>
                    <p>项目分析（需要认证）</p>
                    <div class="code">
{
  "projectPath": "string",
  "options": {
    "includeTests": true,
    "depth": 3
  }
}
                    </div>
                </div>
                
                <div class="endpoint">
                    <h3><span class="method post">POST</span>/api/generate</h3>
                    <p>代码生成</p>
                    <div class="code">
{
  "template": "string",
  "parameters": {},
  "context": {}
}
                    </div>
                </div>
            </div>
            
            <div class="section">
                <h2>🔧 系统接口</h2>
                
                <div class="endpoint">
                    <h3><span class="method get">GET</span>/health</h3>
                    <p>健康检查</p>
                </div>
                
                <div class="endpoint">
                    <h3><span class="method get">GET</span>/api/config</h3>
                    <p>获取项目配置</p>
                </div>
                
                <div class="endpoint">
                    <h3><span class="method post">POST</span>/api/test-ai-model</h3>
                    <p>AI 模型测试</p>
                    <div class="code">
{
  "action": "test_connection|test_prompt|check_environment",
  "config": {},
  "prompt": "string"
}
                    </div>
                </div>
            </div>
            
            <div class="section">
                <h2>📈 监控接口</h2>
                
                <div class="endpoint">
                    <h3><span class="method get">GET</span>/monitor</h3>
                    <p>监控仪表板</p>
                </div>
                
                <div class="endpoint">
                    <h3><span class="method get">GET</span>/monitor/metrics</h3>
                    <p>系统指标</p>
                </div>
                
                <div class="endpoint">
                    <h3><span class="method get">GET</span>/monitor/health</h3>
                    <p>健康检查详情</p>
                </div>
            </div>
            
            <div class="section">
                <h2>🔑 认证说明</h2>
                <p>需要认证的接口需要在请求头中包含 JWT 令牌：</p>
                <div class="code">
Authorization: Bearer &lt;your-jwt-token&gt;
                </div>
            </div>
            
            <div class="section">
                <h2>📝 响应格式</h2>
                <p>所有接口都返回统一的 JSON 格式：</p>
                <div class="code">
{
  "success": true|false,
  "data": {},
  "message": "string",
  "error": "string"
}
                </div>
            </div>
        </div>
    </body>
    </html>
    `;

    res.send(docsHtml);
  }

  /**
   * 系统信息
   */
  getSystemInfo(req, res) {
    const systemInfo = {
      name: 'All-Agent',
      version: '1.0.0',
      description: 'AI-powered development assistant',
      node: {
        version: process.version,
        platform: process.platform,
        arch: process.arch
      },
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      environment: process.env.NODE_ENV || 'development',
      features: {
        authentication: true,
        monitoring: true,
        agents: true,
        analysis: true,
        codeGeneration: true
      },
      endpoints: {
        health: '/health',
        docs: '/docs',
        monitor: '/monitor',
        api: '/api',
        auth: '/auth'
      },
      timestamp: new Date().toISOString()
    };

    res.json({
      success: true,
      data: systemInfo
    });
  }

  /**
   * 404 处理
   */
  handle404(req, res) {
    const notFoundHtml = `
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>404 - 页面未找到</title>
        <style>
            * { margin: 0; padding: 0; box-sizing: border-box; }
            body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; background: #f8f9fa; display: flex; align-items: center; justify-content: center; min-height: 100vh; }
            .container { text-align: center; max-width: 500px; padding: 2rem; }
            .error-code { font-size: 6rem; font-weight: bold; color: #e74c3c; margin-bottom: 1rem; }
            .error-message { font-size: 1.5rem; color: #2c3e50; margin-bottom: 2rem; }
            .description { color: #7f8c8d; margin-bottom: 2rem; }
            .links { display: flex; gap: 1rem; justify-content: center; flex-wrap: wrap; }
            .link { display: inline-block; padding: 0.75rem 1.5rem; background: #3498db; color: white; text-decoration: none; border-radius: 5px; transition: background 0.3s; }
            .link:hover { background: #2980b9; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="error-code">404</div>
            <div class="error-message">页面未找到</div>
            <div class="description">
                抱歉，您访问的页面不存在。请检查 URL 是否正确，或者访问以下链接：
            </div>
            <div class="links">
                <a href="/" class="link">🏠 首页</a>
                <a href="/docs" class="link">📚 API 文档</a>
                <a href="/monitor" class="link">📊 监控面板</a>
                <a href="/health" class="link">💚 健康检查</a>
            </div>
        </div>
    </body>
    </html>
    `;

    res.status(404).send(notFoundHtml);
  }

  /**
   * 获取路由器实例
   */
  getRouter() {
    return this.router;
  }
}

module.exports = PublicRoutes;
