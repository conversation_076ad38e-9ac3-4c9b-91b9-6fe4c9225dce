name: All-Agent CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]
  release:
    types: [ published ]

env:
  NODE_VERSION: '18'
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  # 代码质量检查
  lint-and-format:
    name: 代码质量检查
    runs-on: ubuntu-latest
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: '.all-agent/server/package-lock.json'

    - name: Install dependencies
      working-directory: .all-agent/server
      run: npm ci

    - name: Run ESLint
      working-directory: .all-agent/server
      run: npx eslint . --ext .js --format json --output-file eslint-report.json || true

    - name: Run Prettier check
      working-directory: .all-agent/server
      run: npx prettier --check "**/*.{js,json,md}"

    - name: Upload ESLint report
      uses: actions/upload-artifact@v3
      with:
        name: eslint-report
        path: .all-agent/server/eslint-report.json

  # 安全扫描
  security-scan:
    name: 安全扫描
    runs-on: ubuntu-latest
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: '.all-agent/server/package-lock.json'

    - name: Install dependencies
      working-directory: .all-agent/server
      run: npm ci

    - name: Run npm audit
      working-directory: .all-agent/server
      run: npm audit --audit-level=moderate

    - name: Run Snyk security scan
      uses: snyk/actions/node@master
      env:
        SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}
      with:
        args: --file=.all-agent/server/package.json

  # 单元测试和集成测试
  test:
    name: 测试
    runs-on: ubuntu-latest
    strategy:
      matrix:
        node-version: [16, 18, 20]
    
    services:
      redis:
        image: redis:7-alpine
        ports:
          - 6379:6379
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v4
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'npm'
        cache-dependency-path: '.all-agent/server/package-lock.json'

    - name: Install dependencies
      working-directory: .all-agent/server
      run: npm ci

    - name: Create test environment
      working-directory: .all-agent/server
      run: |
        mkdir -p data reports logs
        cp .env.example .env

    - name: Run unit tests
      working-directory: .all-agent/server
      run: npm run test:db
      env:
        NODE_ENV: test
        USE_REDIS: true
        REDIS_HOST: localhost
        REDIS_PORT: 6379

    - name: Run integration tests
      working-directory: .all-agent/server
      run: npm run test:integration
      env:
        NODE_ENV: test
        USE_REDIS: true
        REDIS_HOST: localhost
        REDIS_PORT: 6379

    - name: Upload test reports
      uses: actions/upload-artifact@v3
      with:
        name: test-reports-node-${{ matrix.node-version }}
        path: .all-agent/server/reports/

    - name: Upload coverage reports
      uses: codecov/codecov-action@v3
      with:
        file: .all-agent/server/coverage/lcov.info
        flags: unittests
        name: codecov-umbrella

  # 性能测试
  performance-test:
    name: 性能测试
    runs-on: ubuntu-latest
    needs: [test]
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    
    services:
      redis:
        image: redis:7-alpine
        ports:
          - 6379:6379

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: '.all-agent/server/package-lock.json'

    - name: Install dependencies
      working-directory: .all-agent/server
      run: npm ci

    - name: Start application
      working-directory: .all-agent/server
      run: |
        cp .env.example .env
        npm start &
        sleep 30
      env:
        NODE_ENV: production
        USE_REDIS: true
        REDIS_HOST: localhost

    - name: Run performance tests
      working-directory: .all-agent/server
      run: npm run test:performance
      timeout-minutes: 10

    - name: Upload performance reports
      uses: actions/upload-artifact@v3
      with:
        name: performance-reports
        path: .all-agent/server/reports/performance-*.json

  # 构建 Docker 镜像
  build-image:
    name: 构建镜像
    runs-on: ubuntu-latest
    needs: [lint-and-format, security-scan, test]
    outputs:
      image-digest: ${{ steps.build.outputs.digest }}
      image-tag: ${{ steps.meta.outputs.tags }}

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Log in to Container Registry
      uses: docker/login-action@v3
      with:
        registry: ${{ env.REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}

    - name: Extract metadata
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=semver,pattern={{version}}
          type=semver,pattern={{major}}.{{minor}}
          type=sha,prefix={{branch}}-

    - name: Build and push Docker image
      id: build
      uses: docker/build-push-action@v5
      with:
        context: .all-agent
        file: .all-agent/Dockerfile
        push: true
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
        cache-from: type=gha
        cache-to: type=gha,mode=max
        platforms: linux/amd64,linux/arm64

  # 容器安全扫描
  container-scan:
    name: 容器安全扫描
    runs-on: ubuntu-latest
    needs: [build-image]
    if: github.event_name == 'push'

    steps:
    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        image-ref: ${{ needs.build-image.outputs.image-tag }}
        format: 'sarif'
        output: 'trivy-results.sarif'

    - name: Upload Trivy scan results
      uses: github/codeql-action/upload-sarif@v2
      with:
        sarif_file: 'trivy-results.sarif'

  # 部署到开发环境
  deploy-dev:
    name: 部署到开发环境
    runs-on: ubuntu-latest
    needs: [build-image, container-scan]
    if: github.ref == 'refs/heads/develop'
    environment: development

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup kubectl
      uses: azure/setup-kubectl@v3
      with:
        version: 'latest'

    - name: Configure kubectl
      run: |
        echo "${{ secrets.KUBE_CONFIG_DEV }}" | base64 -d > kubeconfig
        export KUBECONFIG=kubeconfig

    - name: Deploy to development
      run: |
        export KUBECONFIG=kubeconfig
        kubectl set image deployment/all-agent-app all-agent=${{ needs.build-image.outputs.image-tag }} -n all-agent-dev
        kubectl rollout status deployment/all-agent-app -n all-agent-dev --timeout=300s

    - name: Run smoke tests
      run: |
        kubectl wait --for=condition=ready pod -l app.kubernetes.io/name=all-agent -n all-agent-dev --timeout=300s
        kubectl port-forward svc/all-agent-app 3000:3000 -n all-agent-dev &
        sleep 10
        curl -f http://localhost:3000/health || exit 1

  # 部署到生产环境
  deploy-prod:
    name: 部署到生产环境
    runs-on: ubuntu-latest
    needs: [build-image, container-scan]
    if: github.event_name == 'release'
    environment: production

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup kubectl
      uses: azure/setup-kubectl@v3
      with:
        version: 'latest'

    - name: Configure kubectl
      run: |
        echo "${{ secrets.KUBE_CONFIG_PROD }}" | base64 -d > kubeconfig
        export KUBECONFIG=kubeconfig

    - name: Deploy to production
      run: |
        export KUBECONFIG=kubeconfig
        kubectl set image deployment/all-agent-app all-agent=${{ needs.build-image.outputs.image-tag }} -n all-agent-prod
        kubectl rollout status deployment/all-agent-app -n all-agent-prod --timeout=600s

    - name: Run production smoke tests
      run: |
        kubectl wait --for=condition=ready pod -l app.kubernetes.io/name=all-agent -n all-agent-prod --timeout=300s
        # 生产环境健康检查
        curl -f https://api.all-agent.com/health || exit 1

  # 通知
  notify:
    name: 通知
    runs-on: ubuntu-latest
    needs: [deploy-dev, deploy-prod]
    if: always()

    steps:
    - name: Notify Slack
      uses: 8398a7/action-slack@v3
      with:
        status: ${{ job.status }}
        channel: '#deployments'
        webhook_url: ${{ secrets.SLACK_WEBHOOK }}
        fields: repo,message,commit,author,action,eventName,ref,workflow
