#!/bin/bash

# All-Agent 性能测试脚本
# 执行负载测试验证性能指标

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 性能指标
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0
WARNING_TESTS=0

# 性能基准
MAX_RESPONSE_TIME=1000  # 1秒
MAX_MEMORY_USAGE=512    # 512MB
MIN_THROUGHPUT=100      # 100 req/s

# 执行性能测试
run_performance_test() {
    local test_name="$1"
    local test_command="$2"
    local expected_threshold="$3"
    local comparison_type="${4:-lt}"  # lt, gt, eq
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    log_info "性能测试: $test_name"
    
    local result
    result=$(eval "$test_command")
    
    case $comparison_type in
        "lt")
            if (( $(echo "$result < $expected_threshold" | bc -l) )); then
                log_success "✅ $test_name - 通过 ($result < $expected_threshold)"
                PASSED_TESTS=$((PASSED_TESTS + 1))
                return 0
            else
                log_error "❌ $test_name - 失败 ($result >= $expected_threshold)"
                FAILED_TESTS=$((FAILED_TESTS + 1))
                return 1
            fi
            ;;
        "gt")
            if (( $(echo "$result > $expected_threshold" | bc -l) )); then
                log_success "✅ $test_name - 通过 ($result > $expected_threshold)"
                PASSED_TESTS=$((PASSED_TESTS + 1))
                return 0
            else
                log_error "❌ $test_name - 失败 ($result <= $expected_threshold)"
                FAILED_TESTS=$((FAILED_TESTS + 1))
                return 1
            fi
            ;;
        "eq")
            if (( $(echo "$result == $expected_threshold" | bc -l) )); then
                log_success "✅ $test_name - 通过 ($result == $expected_threshold)"
                PASSED_TESTS=$((PASSED_TESTS + 1))
                return 0
            else
                log_warning "⚠️ $test_name - 警告 ($result != $expected_threshold)"
                WARNING_TESTS=$((WARNING_TESTS + 1))
                return 0
            fi
            ;;
    esac
}

# 1. 启动测试服务器
start_test_server() {
    log_info "=== 启动测试服务器 ==="
    
    cd server
    export NODE_ENV=test
    export PORT=3003
    export JWT_SECRET=test-jwt-secret-for-performance-testing
    export DB_PATH=./data/performance-test.db
    
    log_info "启动性能测试服务器..."
    node app-new.js &
    SERVER_PID=$!
    
    # 等待服务器启动
    sleep 8
    
    # 检查服务器是否启动成功
    if curl -f http://localhost:3003/health >/dev/null 2>&1; then
        log_success "测试服务器启动成功 (PID: $SERVER_PID)"
        return 0
    else
        log_error "测试服务器启动失败"
        return 1
    fi
}

# 2. 响应时间测试
response_time_test() {
    log_info "=== 响应时间测试 ==="
    
    # 健康检查响应时间
    local health_time
    health_time=$(curl -o /dev/null -s -w '%{time_total}' http://localhost:3003/health)
    health_time=$(echo "$health_time * 1000" | bc)  # 转换为毫秒
    
    run_performance_test "健康检查响应时间" "echo $health_time" "$MAX_RESPONSE_TIME" "lt"
    
    # API 端点响应时间
    local api_time
    api_time=$(curl -o /dev/null -s -w '%{time_total}' http://localhost:3003/api/agents)
    api_time=$(echo "$api_time * 1000" | bc)  # 转换为毫秒
    
    run_performance_test "API 端点响应时间" "echo $api_time" "$MAX_RESPONSE_TIME" "lt"
    
    # 监控面板响应时间
    local monitor_time
    monitor_time=$(curl -o /dev/null -s -w '%{time_total}' http://localhost:3003/monitor)
    monitor_time=$(echo "$monitor_time * 1000" | bc)  # 转换为毫秒
    
    run_performance_test "监控面板响应时间" "echo $monitor_time" "$MAX_RESPONSE_TIME" "lt"
}

# 3. 并发测试
concurrency_test() {
    log_info "=== 并发测试 ==="
    
    # 创建临时文件存储结果
    local temp_file="/tmp/perf_test_$$"
    
    # 并发请求测试
    log_info "执行并发请求测试 (50个并发请求)..."
    
    local start_time=$(date +%s.%N)
    
    # 启动50个并发请求
    for i in {1..50}; do
        curl -s http://localhost:3003/health > /dev/null &
    done
    
    # 等待所有请求完成
    wait
    
    local end_time=$(date +%s.%N)
    local total_time=$(echo "$end_time - $start_time" | bc)
    local throughput=$(echo "50 / $total_time" | bc -l)
    
    run_performance_test "并发处理能力" "echo $throughput" "$MIN_THROUGHPUT" "gt"
    
    # 清理临时文件
    rm -f "$temp_file"
}

# 4. 内存使用测试
memory_usage_test() {
    log_info "=== 内存使用测试 ==="
    
    # 获取服务器进程的内存使用
    local memory_usage
    if command -v ps >/dev/null 2>&1; then
        memory_usage=$(ps -p $SERVER_PID -o rss= | tr -d ' ')
        memory_usage=$((memory_usage / 1024))  # 转换为MB
        
        run_performance_test "内存使用量" "echo $memory_usage" "$MAX_MEMORY_USAGE" "lt"
    else
        log_warning "无法获取内存使用信息 (ps 命令不可用)"
        WARNING_TESTS=$((WARNING_TESTS + 1))
        TOTAL_TESTS=$((TOTAL_TESTS + 1))
    fi
    
    # 通过 API 获取内存信息
    local api_memory
    api_memory=$(curl -s http://localhost:3003/monitor/health | grep -o '"heapUsed":[0-9]*' | cut -d':' -f2)
    if [ -n "$api_memory" ]; then
        api_memory=$((api_memory / 1024 / 1024))  # 转换为MB
        run_performance_test "堆内存使用量" "echo $api_memory" "$MAX_MEMORY_USAGE" "lt"
    else
        log_warning "无法通过 API 获取内存信息"
        WARNING_TESTS=$((WARNING_TESTS + 1))
        TOTAL_TESTS=$((TOTAL_TESTS + 1))
    fi
}

# 5. 压力测试
stress_test() {
    log_info "=== 压力测试 ==="
    
    log_info "执行持续压力测试 (30秒，每秒10个请求)..."
    
    local start_time=$(date +%s)
    local end_time=$((start_time + 30))
    local success_count=0
    local error_count=0
    
    while [ $(date +%s) -lt $end_time ]; do
        for i in {1..10}; do
            if curl -s -f http://localhost:3003/health >/dev/null 2>&1; then
                success_count=$((success_count + 1))
            else
                error_count=$((error_count + 1))
            fi &
        done
        sleep 1
        wait
    done
    
    local total_requests=$((success_count + error_count))
    local success_rate=$(echo "scale=2; $success_count * 100 / $total_requests" | bc)
    
    log_info "压力测试结果: 总请求 $total_requests, 成功 $success_count, 失败 $error_count"
    log_info "成功率: $success_rate%"
    
    run_performance_test "压力测试成功率" "echo $success_rate" "95" "gt"
}

# 6. 数据库性能测试
database_performance_test() {
    log_info "=== 数据库性能测试 ==="
    
    # 测试数据库查询响应时间
    local db_query_time
    db_query_time=$(curl -o /dev/null -s -w '%{time_total}' \
        -X POST http://localhost:3003/api/test-ai-model \
        -H "Content-Type: application/json" \
        -d '{"action":"check_environment"}')
    db_query_time=$(echo "$db_query_time * 1000" | bc)  # 转换为毫秒
    
    run_performance_test "数据库查询响应时间" "echo $db_query_time" "$MAX_RESPONSE_TIME" "lt"
}

# 7. 停止测试服务器
stop_test_server() {
    log_info "=== 停止测试服务器 ==="
    
    if [ -n "$SERVER_PID" ]; then
        log_info "停止测试服务器 (PID: $SERVER_PID)..."
        kill $SERVER_PID 2>/dev/null || true
        sleep 3
        
        # 强制杀死进程（如果还在运行）
        kill -9 $SERVER_PID 2>/dev/null || true
        
        log_success "测试服务器已停止"
    fi
    
    # 清理测试数据
    rm -f server/data/performance-test.db
    
    cd ..
}

# 8. 生成性能报告
generate_performance_report() {
    log_info "=== 生成性能报告 ==="
    
    local performance_score=$(( (PASSED_TESTS * 100) / TOTAL_TESTS ))
    
    cat > performance-report.txt << EOF
All-Agent 性能测试报告
====================

测试时间: $(date)
总测试项: $TOTAL_TESTS
通过: $PASSED_TESTS
警告: $WARNING_TESTS
失败: $FAILED_TESTS
性能评分: $performance_score%

性能基准:
- 最大响应时间: ${MAX_RESPONSE_TIME}ms
- 最大内存使用: ${MAX_MEMORY_USAGE}MB
- 最小吞吐量: ${MIN_THROUGHPUT} req/s

测试结果:
$(if [ $FAILED_TESTS -eq 0 ]; then
    echo "✅ 所有性能测试通过"
else
    echo "❌ $FAILED_TESTS 个测试失败"
fi)

建议:
1. 监控生产环境性能指标
2. 定期进行性能测试
3. 优化响应时间较慢的端点
4. 考虑使用缓存提升性能
5. 实施自动化性能监控

EOF
    
    log_success "性能报告已生成: performance-report.txt"
}

# 主函数
main() {
    log_info "开始 All-Agent 性能测试..."
    log_info "测试时间: $(date)"
    echo
    
    # 检查依赖
    if ! command -v bc >/dev/null 2>&1; then
        log_error "bc 计算器未安装，请先安装: brew install bc"
        exit 1
    fi
    
    # 执行性能测试
    if start_test_server; then
        response_time_test || true
        concurrency_test || true
        memory_usage_test || true
        stress_test || true
        database_performance_test || true
        
        stop_test_server
    else
        log_error "无法启动测试服务器，性能测试终止"
        exit 1
    fi
    
    # 生成报告
    generate_performance_report
    
    # 输出测试结果
    echo
    log_info "=== 性能测试结果汇总 ==="
    echo "总测试项: $TOTAL_TESTS"
    echo -e "通过: ${GREEN}$PASSED_TESTS${NC}"
    echo -e "警告: ${YELLOW}$WARNING_TESTS${NC}"
    echo -e "失败: ${RED}$FAILED_TESTS${NC}"
    
    # 计算性能评分
    local performance_score=$(( (PASSED_TESTS * 100) / TOTAL_TESTS ))
    echo "性能评分: $performance_score%"
    
    if [ $FAILED_TESTS -eq 0 ]; then
        log_success "🎉 性能测试全部通过！系统性能良好。"
        exit 0
    else
        log_error "❌ 性能测试发现问题，请优化后重新测试。"
        exit 1
    fi
}

# 执行主函数
main "$@"
