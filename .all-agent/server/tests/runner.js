#!/usr/bin/env node

const path = require('path');
const fs = require('fs').promises;

// 导入测试套件
const DatabaseTests = require('./database.test');
const AuthTests = require('./auth.test');
const IntegrationTests = require('./integration.test');

/**
 * 测试运行器
 */
class TestRunner {
    constructor() {
        this.results = [];
        this.startTime = Date.now();
    }

    /**
     * 运行所有测试
     */
    async runAllTests() {
        console.log('🧪 All-Agent 测试套件');
        console.log('='.repeat(60));
        console.log(`开始时间: ${new Date().toLocaleString()}`);
        console.log('');

        try {
            // 运行数据库测试
            console.log('📊 运行数据库测试...');
            const databaseTests = new DatabaseTests();
            const dbResults = await databaseTests.runAllTests();
            this.results.push(dbResults);

            // 运行认证测试
            console.log('\n🔐 运行认证测试...');
            const authTests = new AuthTests();
            const authResults = await authTests.runAllTests();
            this.results.push(authResults);

            // 运行集成测试
            console.log('\n🔗 运行集成测试...');
            const integrationTests = new IntegrationTests();
            const integrationResults = await integrationTests.runAllTests();
            this.results.push(integrationResults);

            // 生成测试报告
            await this.generateReport();

        } catch (error) {
            console.error('❌ 测试运行失败:', error);
            process.exit(1);
        }
    }

    /**
     * 运行特定测试套件
     */
    async runSpecificTest(testName) {
        console.log(`🧪 运行 ${testName} 测试`);
        console.log('='.repeat(40));

        let results;
        switch (testName.toLowerCase()) {
            case 'database':
            case 'db':
                const databaseTests = new DatabaseTests();
                results = await databaseTests.runAllTests();
                break;
            
            case 'auth':
            case 'authentication':
                const authTests = new AuthTests();
                results = await authTests.runAllTests();
                break;
            
            case 'integration':
            case 'int':
                const integrationTests = new IntegrationTests();
                results = await integrationTests.runAllTests();
                break;
            
            default:
                console.error(`❌ 未知的测试套件: ${testName}`);
                console.log('可用的测试套件: database, auth, integration');
                process.exit(1);
        }

        this.results.push(results);
        await this.generateReport();
    }

    /**
     * 生成测试报告
     */
    async generateReport() {
        const endTime = Date.now();
        const duration = endTime - this.startTime;

        console.log('\n📋 测试报告');
        console.log('='.repeat(60));

        let totalTests = 0;
        let totalPassed = 0;
        let totalFailed = 0;

        // 汇总结果
        for (const result of this.results) {
            console.log(`\n📦 ${result.suiteName}:`);
            console.log(`   总计: ${result.total}`);
            console.log(`   通过: ${result.passed} ✅`);
            console.log(`   失败: ${result.failed} ❌`);
            
            if (result.failed > 0) {
                console.log('   失败详情:');
                result.results.filter(r => !r.success).forEach(r => {
                    console.log(`     - ${r.name}: ${r.error}`);
                });
            }

            totalTests += result.total;
            totalPassed += result.passed;
            totalFailed += result.failed;
        }

        // 总体统计
        console.log('\n🎯 总体统计:');
        console.log(`   总测试数: ${totalTests}`);
        console.log(`   通过: ${totalPassed} ✅`);
        console.log(`   失败: ${totalFailed} ❌`);
        console.log(`   成功率: ${((totalPassed / totalTests) * 100).toFixed(1)}%`);
        console.log(`   耗时: ${(duration / 1000).toFixed(2)}秒`);

        // 生成JSON报告
        const reportData = {
            timestamp: new Date().toISOString(),
            duration: duration,
            summary: {
                total: totalTests,
                passed: totalPassed,
                failed: totalFailed,
                successRate: (totalPassed / totalTests) * 100
            },
            suites: this.results
        };

        await this.saveReport(reportData);

        // 生成HTML报告
        await this.generateHTMLReport(reportData);

        // 退出状态
        if (totalFailed > 0) {
            console.log('\n❌ 测试失败');
            process.exit(1);
        } else {
            console.log('\n✅ 所有测试通过');
            process.exit(0);
        }
    }

    /**
     * 保存JSON报告
     */
    async saveReport(reportData) {
        try {
            const reportsDir = path.join(__dirname, '../reports');
            await fs.mkdir(reportsDir, { recursive: true });

            const reportFile = path.join(reportsDir, `test-report-${Date.now()}.json`);
            await fs.writeFile(reportFile, JSON.stringify(reportData, null, 2));

            console.log(`\n📄 JSON报告已保存: ${reportFile}`);
        } catch (error) {
            console.warn('⚠️ 保存JSON报告失败:', error.message);
        }
    }

    /**
     * 生成HTML报告
     */
    async generateHTMLReport(reportData) {
        try {
            const htmlContent = this.generateHTMLContent(reportData);
            const reportsDir = path.join(__dirname, '../reports');
            const htmlFile = path.join(reportsDir, `test-report-${Date.now()}.html`);
            
            await fs.writeFile(htmlFile, htmlContent);
            console.log(`📄 HTML报告已保存: ${htmlFile}`);
        } catch (error) {
            console.warn('⚠️ 生成HTML报告失败:', error.message);
        }
    }

    /**
     * 生成HTML内容
     */
    generateHTMLContent(reportData) {
        const { summary, suites, timestamp, duration } = reportData;
        
        return `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>All-Agent 测试报告</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; border-radius: 8px 8px 0 0; }
        .header h1 { margin: 0; font-size: 28px; }
        .header .meta { margin-top: 10px; opacity: 0.9; }
        .summary { padding: 30px; border-bottom: 1px solid #eee; }
        .summary-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; }
        .summary-card { background: #f8f9fa; padding: 20px; border-radius: 6px; text-align: center; }
        .summary-card h3 { margin: 0 0 10px 0; color: #495057; }
        .summary-card .value { font-size: 32px; font-weight: bold; margin: 10px 0; }
        .passed { color: #28a745; }
        .failed { color: #dc3545; }
        .suite { padding: 30px; border-bottom: 1px solid #eee; }
        .suite:last-child { border-bottom: none; }
        .suite h2 { margin: 0 0 20px 0; color: #343a40; }
        .suite-stats { display: flex; gap: 20px; margin-bottom: 20px; }
        .stat { background: #e9ecef; padding: 10px 15px; border-radius: 4px; }
        .test-list { margin-top: 20px; }
        .test-item { padding: 10px; margin: 5px 0; border-radius: 4px; display: flex; justify-content: space-between; align-items: center; }
        .test-item.passed { background: #d4edda; color: #155724; }
        .test-item.failed { background: #f8d7da; color: #721c24; }
        .error-message { font-size: 12px; color: #6c757d; margin-top: 5px; }
        .footer { padding: 20px; text-align: center; color: #6c757d; font-size: 14px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 All-Agent 测试报告</h1>
            <div class="meta">
                <div>生成时间: ${new Date(timestamp).toLocaleString()}</div>
                <div>执行耗时: ${(duration / 1000).toFixed(2)}秒</div>
            </div>
        </div>

        <div class="summary">
            <h2>📊 测试概览</h2>
            <div class="summary-grid">
                <div class="summary-card">
                    <h3>总测试数</h3>
                    <div class="value">${summary.total}</div>
                </div>
                <div class="summary-card">
                    <h3>通过</h3>
                    <div class="value passed">${summary.passed}</div>
                </div>
                <div class="summary-card">
                    <h3>失败</h3>
                    <div class="value failed">${summary.failed}</div>
                </div>
                <div class="summary-card">
                    <h3>成功率</h3>
                    <div class="value">${summary.successRate.toFixed(1)}%</div>
                </div>
            </div>
        </div>

        ${suites.map(suite => `
        <div class="suite">
            <h2>📦 ${suite.suiteName}</h2>
            <div class="suite-stats">
                <div class="stat">总计: ${suite.total}</div>
                <div class="stat passed">通过: ${suite.passed}</div>
                <div class="stat failed">失败: ${suite.failed}</div>
            </div>
            <div class="test-list">
                ${suite.results.map(test => `
                <div class="test-item ${test.success ? 'passed' : 'failed'}">
                    <span>${test.success ? '✅' : '❌'} ${test.name}</span>
                    ${test.error ? `<div class="error-message">${test.error}</div>` : ''}
                </div>
                `).join('')}
            </div>
        </div>
        `).join('')}

        <div class="footer">
            <p>由 All-Agent 测试框架生成</p>
        </div>
    </div>
</body>
</html>`;
    }

    /**
     * 显示帮助信息
     */
    showHelp() {
        console.log(`
🧪 All-Agent 测试运行器

用法:
  node runner.js [选项] [测试套件]

选项:
  --help, -h     显示帮助信息
  --version, -v  显示版本信息

测试套件:
  all            运行所有测试 (默认)
  database, db   运行数据库测试
  auth           运行认证测试
  integration    运行集成测试

示例:
  node runner.js                # 运行所有测试
  node runner.js database       # 只运行数据库测试
  node runner.js auth           # 只运行认证测试
  node runner.js integration    # 只运行集成测试
        `);
    }
}

// 主函数
async function main() {
    const args = process.argv.slice(2);
    const runner = new TestRunner();

    // 处理命令行参数
    if (args.includes('--help') || args.includes('-h')) {
        runner.showHelp();
        return;
    }

    if (args.includes('--version') || args.includes('-v')) {
        console.log('All-Agent Test Runner v1.0.0');
        return;
    }

    const testSuite = args[0] || 'all';

    if (testSuite === 'all') {
        await runner.runAllTests();
    } else {
        await runner.runSpecificTest(testSuite);
    }
}

// 运行主函数
if (require.main === module) {
    main().catch(error => {
        console.error('❌ 测试运行器失败:', error);
        process.exit(1);
    });
}

module.exports = TestRunner;
