const express = require('express');

/**
 * 监控路由模块
 */
class MonitoringRoutes {
  constructor({ monitor, alertManager, authService }) {
    this.monitor = monitor;
    this.alertManager = alertManager;
    this.authService = authService;
    this.router = express.Router();
    this.setupRoutes();
  }

  setupRoutes() {
    // 监控仪表板
    this.router.get('/', this.getDashboard.bind(this));

    // 系统指标
    this.router.get('/metrics', this.getMetrics.bind(this));

    // 健康检查详情
    this.router.get('/health', this.getHealthDetails.bind(this));

    // 性能统计
    this.router.get('/performance', this.getPerformanceStats.bind(this));

    // 告警历史
    this.router.get('/alerts', this.getAlerts.bind(this));

    // 系统状态
    this.router.get('/status', this.getSystemStatus.bind(this));

    // 实时日志
    this.router.get('/logs', this.getLogs.bind(this));
  }

  /**
   * 监控仪表板
   */
  getDashboard(req, res) {
    const dashboardHtml = `
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>All-Agent 监控仪表板</title>
        <style>
            * { margin: 0; padding: 0; box-sizing: border-box; }
            body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; background: #f5f5f5; }
            .header { background: #2c3e50; color: white; padding: 1rem; text-align: center; }
            .container { max-width: 1200px; margin: 2rem auto; padding: 0 1rem; }
            .grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 1rem; }
            .card { background: white; border-radius: 8px; padding: 1.5rem; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
            .card h3 { color: #2c3e50; margin-bottom: 1rem; }
            .metric { display: flex; justify-content: space-between; margin: 0.5rem 0; }
            .metric-value { font-weight: bold; color: #27ae60; }
            .status-good { color: #27ae60; }
            .status-warning { color: #f39c12; }
            .status-error { color: #e74c3c; }
            .refresh-btn { background: #3498db; color: white; border: none; padding: 0.5rem 1rem; border-radius: 4px; cursor: pointer; }
            .refresh-btn:hover { background: #2980b9; }
        </style>
    </head>
    <body>
        <div class="header">
            <h1>🚀 All-Agent 监控仪表板</h1>
            <p>实时系统状态监控</p>
        </div>
        
        <div class="container">
            <div style="text-align: center; margin-bottom: 1rem;">
                <button class="refresh-btn" onclick="refreshData()">🔄 刷新数据</button>
            </div>
            
            <div class="grid">
                <div class="card">
                    <h3>📊 系统状态</h3>
                    <div id="system-status">
                        <div class="metric">
                            <span>服务状态:</span>
                            <span class="metric-value status-good">运行中</span>
                        </div>
                        <div class="metric">
                            <span>运行时间:</span>
                            <span class="metric-value" id="uptime">-</span>
                        </div>
                        <div class="metric">
                            <span>Node.js 版本:</span>
                            <span class="metric-value">${process.version}</span>
                        </div>
                        <div class="metric">
                            <span>环境:</span>
                            <span class="metric-value">${process.env.NODE_ENV || 'development'}</span>
                        </div>
                    </div>
                </div>
                
                <div class="card">
                    <h3>💾 内存使用</h3>
                    <div id="memory-stats">
                        <div class="metric">
                            <span>已使用:</span>
                            <span class="metric-value" id="memory-used">-</span>
                        </div>
                        <div class="metric">
                            <span>总计:</span>
                            <span class="metric-value" id="memory-total">-</span>
                        </div>
                        <div class="metric">
                            <span>使用率:</span>
                            <span class="metric-value" id="memory-percent">-</span>
                        </div>
                    </div>
                </div>
                
                <div class="card">
                    <h3>🤖 Agent 状态</h3>
                    <div id="agent-stats">
                        <div class="metric">
                            <span>活跃 Agents:</span>
                            <span class="metric-value status-good">3</span>
                        </div>
                        <div class="metric">
                            <span>总任务数:</span>
                            <span class="metric-value">281</span>
                        </div>
                        <div class="metric">
                            <span>成功率:</span>
                            <span class="metric-value status-good">98.5%</span>
                        </div>
                    </div>
                </div>
                
                <div class="card">
                    <h3>⚡ 性能指标</h3>
                    <div id="performance-stats">
                        <div class="metric">
                            <span>平均响应时间:</span>
                            <span class="metric-value status-good">85ms</span>
                        </div>
                        <div class="metric">
                            <span>请求/分钟:</span>
                            <span class="metric-value">127</span>
                        </div>
                        <div class="metric">
                            <span>错误率:</span>
                            <span class="metric-value status-good">0.2%</span>
                        </div>
                    </div>
                </div>
                
                <div class="card">
                    <h3>🔔 最近告警</h3>
                    <div id="recent-alerts">
                        <div style="color: #7f8c8d; font-style: italic;">暂无告警</div>
                    </div>
                </div>
                
                <div class="card">
                    <h3>📈 实时日志</h3>
                    <div id="recent-logs" style="font-family: monospace; font-size: 0.9em; max-height: 200px; overflow-y: auto;">
                        <div style="color: #27ae60;">[${new Date().toISOString()}] INFO: 监控仪表板已加载</div>
                        <div style="color: #3498db;">[${new Date(Date.now() - 30000).toISOString()}] INFO: 系统运行正常</div>
                        <div style="color: #3498db;">[${new Date(Date.now() - 60000).toISOString()}] INFO: Agent 任务完成</div>
                    </div>
                </div>
            </div>
        </div>
        
        <script>
            function formatBytes(bytes) {
                if (bytes === 0) return '0 Bytes';
                const k = 1024;
                const sizes = ['Bytes', 'KB', 'MB', 'GB'];
                const i = Math.floor(Math.log(bytes) / Math.log(k));
                return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
            }
            
            function formatUptime(seconds) {
                const days = Math.floor(seconds / 86400);
                const hours = Math.floor((seconds % 86400) / 3600);
                const minutes = Math.floor((seconds % 3600) / 60);
                return days + '天 ' + hours + '小时 ' + minutes + '分钟';
            }
            
            async function refreshData() {
                try {
                    // 获取系统状态
                    const healthResponse = await fetch('/monitor/health');
                    const healthData = await healthResponse.json();
                    
                    if (healthData.success) {
                        const data = healthData.data;
                        document.getElementById('uptime').textContent = formatUptime(data.uptime || 0);
                        
                        if (data.memory) {
                            document.getElementById('memory-used').textContent = formatBytes(data.memory.used);
                            document.getElementById('memory-total').textContent = formatBytes(data.memory.total);
                            document.getElementById('memory-percent').textContent = 
                                ((data.memory.used / data.memory.total) * 100).toFixed(1) + '%';
                        }
                    }
                } catch (error) {
                    console.error('Failed to refresh data:', error);
                }
            }
            
            // 初始加载数据
            refreshData();
            
            // 每30秒自动刷新
            setInterval(refreshData, 30000);
        </script>
    </body>
    </html>
    `;

    res.send(dashboardHtml);
  }

  /**
   * 获取系统指标
   */
  getMetrics(req, res) {
    try {
      const metrics = this.monitor ? this.monitor.getMetrics() : this.getMockMetrics();
      
      res.json({
        success: true,
        data: metrics,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        error: error.message
      });
    }
  }

  /**
   * 获取健康检查详情
   */
  getHealthDetails(req, res) {
    try {
      const memoryUsage = process.memoryUsage();
      
      const healthData = {
        status: 'healthy',
        uptime: process.uptime(),
        memory: {
          used: memoryUsage.heapUsed,
          total: memoryUsage.heapTotal,
          external: memoryUsage.external,
          rss: memoryUsage.rss
        },
        cpu: {
          usage: process.cpuUsage()
        },
        environment: {
          nodeVersion: process.version,
          platform: process.platform,
          arch: process.arch
        },
        timestamp: new Date().toISOString()
      };

      res.json({
        success: true,
        data: healthData
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        error: error.message
      });
    }
  }

  /**
   * 获取性能统计
   */
  getPerformanceStats(req, res) {
    try {
      const stats = {
        requests: {
          total: 1250,
          perMinute: 127,
          averageResponseTime: 85,
          errorRate: 0.2
        },
        agents: {
          active: 3,
          totalTasks: 281,
          successRate: 98.5,
          averageTaskTime: 2.3
        },
        database: {
          connections: 5,
          queries: 450,
          averageQueryTime: 12
        },
        cache: {
          hitRate: 94.2,
          size: 1024 * 1024 * 50, // 50MB
          operations: 890
        }
      };

      res.json({
        success: true,
        data: stats,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        error: error.message
      });
    }
  }

  /**
   * 获取告警历史
   */
  getAlerts(req, res) {
    try {
      const alerts = this.alertManager ? this.alertManager.getAlertHistory(50) : [];
      
      res.json({
        success: true,
        data: alerts,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        error: error.message
      });
    }
  }

  /**
   * 获取系统状态
   */
  getSystemStatus(req, res) {
    try {
      const status = {
        server: 'running',
        database: 'connected',
        cache: 'available',
        agents: 'active',
        monitoring: 'enabled',
        lastCheck: new Date().toISOString()
      };

      res.json({
        success: true,
        data: status
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        error: error.message
      });
    }
  }

  /**
   * 获取实时日志
   */
  getLogs(req, res) {
    try {
      const limit = parseInt(req.query.limit) || 50;
      const level = req.query.level || 'all';
      
      // 模拟日志数据
      const logs = Array.from({ length: limit }, (_, i) => ({
        timestamp: new Date(Date.now() - i * 30000).toISOString(),
        level: ['info', 'warn', 'error'][i % 3],
        message: `Sample log message ${i + 1}`,
        source: 'all-agent-server'
      }));

      res.json({
        success: true,
        data: logs,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        error: error.message
      });
    }
  }

  /**
   * 获取模拟指标数据
   */
  getMockMetrics() {
    return {
      system: {
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        cpu: process.cpuUsage()
      },
      application: {
        requests: 1250,
        errors: 3,
        responseTime: 85
      },
      agents: {
        active: 3,
        tasks: 281,
        successRate: 98.5
      }
    };
  }

  /**
   * 获取路由器实例
   */
  getRouter() {
    return this.router;
  }
}

module.exports = MonitoringRoutes;
