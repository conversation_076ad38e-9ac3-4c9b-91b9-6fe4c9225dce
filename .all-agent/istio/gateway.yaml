apiVersion: networking.istio.io/v1beta1
kind: Gateway
metadata:
  name: all-agent-gateway
  namespace: all-agent
  labels:
    app.kubernetes.io/name: all-agent
    app.kubernetes.io/component: gateway
spec:
  selector:
    istio: ingressgateway
  servers:
  - port:
      number: 80
      name: http
      protocol: HTTP
    hosts:
    - all-agent.local
    - "*.all-agent.com"
  - port:
      number: 443
      name: https
      protocol: HTTPS
    tls:
      mode: SIMPLE
      credentialName: all-agent-tls
    hosts:
    - all-agent.local
    - "*.all-agent.com"
---
apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  name: all-agent-vs
  namespace: all-agent
  labels:
    app.kubernetes.io/name: all-agent
    app.kubernetes.io/component: virtualservice
spec:
  hosts:
  - all-agent.local
  - "*.all-agent.com"
  gateways:
  - all-agent-gateway
  http:
  # API 路由
  - match:
    - uri:
        prefix: /api/
    route:
    - destination:
        host: all-agent-app
        port:
          number: 3000
    fault:
      delay:
        percentage:
          value: 0.1
        fixedDelay: 5s
    retries:
      attempts: 3
      perTryTimeout: 10s
    timeout: 30s
  
  # 认证路由
  - match:
    - uri:
        prefix: /auth/
    route:
    - destination:
        host: auth-service
        port:
          number: 3001
    retries:
      attempts: 3
      perTryTimeout: 5s
    timeout: 15s
  
  # 监控路由
  - match:
    - uri:
        prefix: /monitor/
    route:
    - destination:
        host: monitor-service
        port:
          number: 3005
    headers:
      request:
        add:
          x-monitoring: "true"
  
  # WebSocket 路由
  - match:
    - uri:
        prefix: /socket.io/
    - headers:
        upgrade:
          exact: websocket
    route:
    - destination:
        host: all-agent-app
        port:
          number: 3000
  
  # 默认路由到主应用
  - match:
    - uri:
        prefix: /
    route:
    - destination:
        host: all-agent-app
        port:
          number: 3000
        subset: v1
      weight: 90
    - destination:
        host: all-agent-app
        port:
          number: 3000
        subset: v2
      weight: 10
    headers:
      response:
        add:
          x-served-by: all-agent-istio
---
apiVersion: networking.istio.io/v1beta1
kind: DestinationRule
metadata:
  name: all-agent-app-dr
  namespace: all-agent
  labels:
    app.kubernetes.io/name: all-agent
    app.kubernetes.io/component: destinationrule
spec:
  host: all-agent-app
  trafficPolicy:
    connectionPool:
      tcp:
        maxConnections: 100
      http:
        http1MaxPendingRequests: 50
        http2MaxRequests: 100
        maxRequestsPerConnection: 10
        maxRetries: 3
        consecutiveGatewayErrors: 5
        interval: 30s
        baseEjectionTime: 30s
        maxEjectionPercent: 50
    loadBalancer:
      simple: LEAST_CONN
    outlierDetection:
      consecutiveGatewayErrors: 5
      interval: 30s
      baseEjectionTime: 30s
      maxEjectionPercent: 50
      minHealthPercent: 30
  subsets:
  - name: v1
    labels:
      version: v1
    trafficPolicy:
      connectionPool:
        tcp:
          maxConnections: 50
  - name: v2
    labels:
      version: v2
    trafficPolicy:
      connectionPool:
        tcp:
          maxConnections: 20
---
apiVersion: networking.istio.io/v1beta1
kind: DestinationRule
metadata:
  name: auth-service-dr
  namespace: all-agent
spec:
  host: auth-service
  trafficPolicy:
    connectionPool:
      tcp:
        maxConnections: 50
      http:
        http1MaxPendingRequests: 20
        maxRequestsPerConnection: 5
    loadBalancer:
      simple: ROUND_ROBIN
    outlierDetection:
      consecutiveGatewayErrors: 3
      interval: 30s
      baseEjectionTime: 30s
---
apiVersion: networking.istio.io/v1beta1
kind: ServiceEntry
metadata:
  name: external-apis
  namespace: all-agent
spec:
  hosts:
  - api.openai.com
  - api.anthropic.com
  - api.google.com
  ports:
  - number: 443
    name: https
    protocol: HTTPS
  location: MESH_EXTERNAL
  resolution: DNS
---
apiVersion: security.istio.io/v1beta1
kind: PeerAuthentication
metadata:
  name: default
  namespace: all-agent
spec:
  mtls:
    mode: STRICT
---
apiVersion: security.istio.io/v1beta1
kind: AuthorizationPolicy
metadata:
  name: all-agent-authz
  namespace: all-agent
spec:
  selector:
    matchLabels:
      app.kubernetes.io/name: all-agent
  rules:
  - from:
    - source:
        principals: ["cluster.local/ns/all-agent/sa/all-agent-sa"]
  - to:
    - operation:
        methods: ["GET"]
        paths: ["/health", "/metrics"]
  - to:
    - operation:
        methods: ["POST"]
        paths: ["/auth/*"]
    when:
    - key: request.headers[content-type]
      values: ["application/json"]
---
apiVersion: telemetry.istio.io/v1alpha1
kind: Telemetry
metadata:
  name: all-agent-metrics
  namespace: all-agent
spec:
  metrics:
  - providers:
    - name: prometheus
  - overrides:
    - match:
        metric: ALL_METRICS
      tagOverrides:
        request_id:
          value: "%{REQUEST_ID}"
        user_id:
          value: "%{REQUEST_HEADERS['x-user-id']}"
  accessLogging:
  - providers:
    - name: otel
