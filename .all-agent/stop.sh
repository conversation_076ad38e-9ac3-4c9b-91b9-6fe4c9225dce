#!/bin/bash

# All-Agent 停止脚本
# 用于停止 All-Agent 服务器

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置
LOG_DIR="logs"
PID_FILE="$LOG_DIR/all-agent.pid"

print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 停止服务器
stop_server() {
    print_step "停止 All-Agent 服务器..."
    
    local stopped=false
    
    # 方法1: 通过 PID 文件停止
    if [ -f "$PID_FILE" ]; then
        local pid=$(cat "$PID_FILE")
        
        if kill -0 $pid 2>/dev/null; then
            print_status "找到服务器进程 (PID: $pid)"
            
            # 发送 SIGTERM 信号
            kill -TERM $pid
            print_status "已发送停止信号..."
            
            # 等待进程结束
            local count=0
            while kill -0 $pid 2>/dev/null && [ $count -lt 15 ]; do
                echo -n "."
                sleep 1
                count=$((count + 1))
            done
            echo
            
            # 检查进程是否已结束
            if kill -0 $pid 2>/dev/null; then
                print_warning "进程未响应，强制终止..."
                kill -9 $pid
                sleep 1
                
                if kill -0 $pid 2>/dev/null; then
                    print_error "无法终止进程 $pid"
                else
                    print_status "进程已强制终止"
                    stopped=true
                fi
            else
                print_status "服务器已正常停止"
                stopped=true
            fi
        else
            print_warning "PID 文件存在但进程不在运行"
        fi
        
        # 清理 PID 文件
        rm -f "$PID_FILE"
    fi
    
    # 方法2: 通过端口查找并停止进程
    if [ "$stopped" = false ]; then
        print_step "通过端口查找服务器进程..."
        
        local pids=$(lsof -ti:3000 2>/dev/null || true)
        
        if [ ! -z "$pids" ]; then
            for pid in $pids; do
                local process_name=$(ps -p $pid -o comm= 2>/dev/null || echo "unknown")
                print_status "找到占用端口 3000 的进程: $process_name (PID: $pid)"
                
                # 检查是否是 Node.js 进程
                if [[ "$process_name" == *"node"* ]] || [[ "$process_name" == *"npm"* ]]; then
                    kill -TERM $pid
                    sleep 2
                    
                    if kill -0 $pid 2>/dev/null; then
                        kill -9 $pid
                        print_warning "强制终止进程 $pid"
                    else
                        print_status "进程 $pid 已停止"
                    fi
                    stopped=true
                fi
            done
        fi
    fi
    
    # 方法3: 查找所有相关的 Node.js 进程
    if [ "$stopped" = false ]; then
        print_step "查找 All-Agent 相关进程..."
        
        local node_pids=$(pgrep -f "all-agent\|app\.js" 2>/dev/null || true)
        
        if [ ! -z "$node_pids" ]; then
            for pid in $node_pids; do
                local cmd=$(ps -p $pid -o args= 2>/dev/null || echo "")
                if [[ "$cmd" == *"all-agent"* ]] || [[ "$cmd" == *"app.js"* ]]; then
                    print_status "找到 All-Agent 进程: $pid"
                    kill -TERM $pid
                    sleep 1
                    
                    if kill -0 $pid 2>/dev/null; then
                        kill -9 $pid
                        print_warning "强制终止进程 $pid"
                    else
                        print_status "进程 $pid 已停止"
                    fi
                    stopped=true
                fi
            done
        fi
    fi
    
    if [ "$stopped" = true ]; then
        print_status "All-Agent 服务器已停止"
        
        # 验证端口是否已释放
        sleep 1
        if ! lsof -Pi :3000 -sTCP:LISTEN -t >/dev/null 2>&1; then
            print_status "端口 3000 已释放"
        else
            print_warning "端口 3000 仍被占用"
        fi
    else
        print_status "没有找到运行中的 All-Agent 服务器"
    fi
}

# 清理资源
cleanup_resources() {
    print_step "清理临时资源..."
    
    # 清理临时文件
    if [ -d "temp" ]; then
        rm -rf temp
        print_status "已清理临时目录"
    fi
    
    # 清理上传文件（可选）
    if [ "$1" = "--clean-uploads" ]; then
        if [ -d "uploads" ]; then
            rm -rf uploads/*
            print_status "已清理上传文件"
        fi
    fi
    
    # 清理日志文件（可选）
    if [ "$1" = "--clean-logs" ]; then
        if [ -d "$LOG_DIR" ]; then
            rm -f "$LOG_DIR"/*.log
            print_status "已清理日志文件"
        fi
    fi
}

# 显示帮助信息
show_help() {
    echo "All-Agent 停止脚本"
    echo
    echo "用法: $0 [选项]"
    echo
    echo "选项:"
    echo "  --clean-uploads    同时清理上传文件"
    echo "  --clean-logs       同时清理日志文件"
    echo "  --force           强制停止所有相关进程"
    echo "  --help            显示此帮助信息"
    echo
}

# 强制停止所有相关进程
force_stop() {
    print_step "强制停止所有相关进程..."
    
    # 停止所有 Node.js 进程（谨慎使用）
    local node_pids=$(pgrep node 2>/dev/null || true)
    
    if [ ! -z "$node_pids" ]; then
        print_warning "找到以下 Node.js 进程:"
        ps -p $node_pids -o pid,ppid,cmd 2>/dev/null || true
        
        read -p "确定要停止所有 Node.js 进程吗? (y/N): " -n 1 -r
        echo
        
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            for pid in $node_pids; do
                kill -9 $pid 2>/dev/null || true
            done
            print_status "已强制停止所有 Node.js 进程"
        else
            print_status "取消操作"
        fi
    else
        print_status "没有找到 Node.js 进程"
    fi
}

# 主函数
main() {
    echo -e "${BLUE}🛑 All-Agent 停止脚本${NC}"
    echo
    
    case "${1}" in
        "--help"|"-h")
            show_help
            ;;
        "--force")
            force_stop
            ;;
        "--clean-uploads")
            stop_server
            cleanup_resources "--clean-uploads"
            ;;
        "--clean-logs")
            stop_server
            cleanup_resources "--clean-logs"
            ;;
        "")
            stop_server
            ;;
        *)
            print_error "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
    
    echo
    echo -e "${GREEN}✅ 停止操作完成${NC}"
}

# 执行主函数
main "$@"
