#!/usr/bin/env node

/**
 * All-Agent 内存分析工具
 * 用于检测内存泄漏和优化内存使用
 */

const fs = require('fs');
const path = require('path');
const { performance } = require('perf_hooks');

class MemoryAnalyzer {
    constructor() {
        this.snapshots = [];
        this.startTime = Date.now();
        this.intervalId = null;
        this.reportPath = './memory-analysis-report.json';
    }

    /**
     * 开始内存监控
     */
    startMonitoring(intervalMs = 5000) {
        console.log('🔍 开始内存监控...');
        
        this.intervalId = setInterval(() => {
            this.takeSnapshot();
        }, intervalMs);

        // 监听进程退出事件
        process.on('SIGINT', () => {
            this.stopMonitoring();
            this.generateReport();
            process.exit(0);
        });
    }

    /**
     * 停止内存监控
     */
    stopMonitoring() {
        if (this.intervalId) {
            clearInterval(this.intervalId);
            this.intervalId = null;
            console.log('⏹️ 内存监控已停止');
        }
    }

    /**
     * 拍摄内存快照
     */
    takeSnapshot() {
        const memUsage = process.memoryUsage();
        const timestamp = Date.now();
        const uptime = timestamp - this.startTime;

        const snapshot = {
            timestamp,
            uptime,
            rss: memUsage.rss,
            heapTotal: memUsage.heapTotal,
            heapUsed: memUsage.heapUsed,
            external: memUsage.external,
            arrayBuffers: memUsage.arrayBuffers,
            heapUtilization: (memUsage.heapUsed / memUsage.heapTotal * 100).toFixed(2)
        };

        this.snapshots.push(snapshot);

        // 实时输出内存使用情况
        console.log(`[${new Date(timestamp).toISOString()}] 内存使用: ${this.formatBytes(memUsage.heapUsed)}/${this.formatBytes(memUsage.heapTotal)} (${snapshot.heapUtilization}%)`);

        // 检测内存泄漏
        this.detectMemoryLeak();
    }

    /**
     * 检测内存泄漏
     */
    detectMemoryLeak() {
        if (this.snapshots.length < 10) return;

        const recent = this.snapshots.slice(-10);
        const trend = this.calculateTrend(recent.map(s => s.heapUsed));

        if (trend > 1024 * 1024) { // 如果内存增长超过1MB
            console.warn(`⚠️ 检测到潜在内存泄漏: 内存增长趋势 ${this.formatBytes(trend)}/snapshot`);
        }
    }

    /**
     * 计算趋势
     */
    calculateTrend(values) {
        if (values.length < 2) return 0;
        
        const n = values.length;
        const sumX = n * (n - 1) / 2;
        const sumY = values.reduce((a, b) => a + b, 0);
        const sumXY = values.reduce((sum, y, x) => sum + x * y, 0);
        const sumXX = n * (n - 1) * (2 * n - 1) / 6;

        return (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
    }

    /**
     * 格式化字节数
     */
    formatBytes(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    /**
     * 生成分析报告
     */
    generateReport() {
        if (this.snapshots.length === 0) {
            console.log('❌ 没有内存快照数据');
            return;
        }

        const analysis = this.analyzeSnapshots();
        const report = {
            generatedAt: new Date().toISOString(),
            duration: Date.now() - this.startTime,
            snapshotCount: this.snapshots.length,
            analysis,
            snapshots: this.snapshots
        };

        // 保存报告
        fs.writeFileSync(this.reportPath, JSON.stringify(report, null, 2));
        console.log(`📊 内存分析报告已生成: ${this.reportPath}`);

        // 输出摘要
        this.printSummary(analysis);
    }

    /**
     * 分析快照数据
     */
    analyzeSnapshots() {
        const heapUsed = this.snapshots.map(s => s.heapUsed);
        const heapTotal = this.snapshots.map(s => s.heapTotal);
        const rss = this.snapshots.map(s => s.rss);

        return {
            heapUsed: {
                min: Math.min(...heapUsed),
                max: Math.max(...heapUsed),
                avg: heapUsed.reduce((a, b) => a + b, 0) / heapUsed.length,
                trend: this.calculateTrend(heapUsed)
            },
            heapTotal: {
                min: Math.min(...heapTotal),
                max: Math.max(...heapTotal),
                avg: heapTotal.reduce((a, b) => a + b, 0) / heapTotal.length
            },
            rss: {
                min: Math.min(...rss),
                max: Math.max(...rss),
                avg: rss.reduce((a, b) => a + b, 0) / rss.length,
                trend: this.calculateTrend(rss)
            },
            memoryLeakDetected: this.calculateTrend(heapUsed) > 1024 * 1024,
            recommendations: this.generateRecommendations()
        };
    }

    /**
     * 生成优化建议
     */
    generateRecommendations() {
        const recommendations = [];
        const latest = this.snapshots[this.snapshots.length - 1];

        if (latest && latest.heapUtilization > 80) {
            recommendations.push('堆内存使用率过高，建议增加内存限制或优化代码');
        }

        if (this.calculateTrend(this.snapshots.map(s => s.heapUsed)) > 1024 * 1024) {
            recommendations.push('检测到内存泄漏趋势，建议检查事件监听器和定时器');
        }

        if (this.snapshots.some(s => s.external > 100 * 1024 * 1024)) {
            recommendations.push('外部内存使用过高，检查文件句柄和网络连接');
        }

        return recommendations;
    }

    /**
     * 打印摘要
     */
    printSummary(analysis) {
        console.log('\n📊 内存分析摘要:');
        console.log('================');
        console.log(`堆内存使用: ${this.formatBytes(analysis.heapUsed.min)} - ${this.formatBytes(analysis.heapUsed.max)} (平均: ${this.formatBytes(analysis.heapUsed.avg)})`);
        console.log(`RSS 内存: ${this.formatBytes(analysis.rss.min)} - ${this.formatBytes(analysis.rss.max)} (平均: ${this.formatBytes(analysis.rss.avg)})`);
        console.log(`内存泄漏检测: ${analysis.memoryLeakDetected ? '❌ 检测到泄漏' : '✅ 正常'}`);
        
        if (analysis.recommendations.length > 0) {
            console.log('\n💡 优化建议:');
            analysis.recommendations.forEach((rec, i) => {
                console.log(`${i + 1}. ${rec}`);
            });
        }
    }

    /**
     * 强制垃圾回收 (如果可用)
     */
    forceGC() {
        if (global.gc) {
            console.log('🗑️ 执行垃圾回收...');
            global.gc();
            this.takeSnapshot();
        } else {
            console.log('⚠️ 垃圾回收不可用，请使用 --expose-gc 标志启动');
        }
    }
}

// 命令行接口
if (require.main === module) {
    const analyzer = new MemoryAnalyzer();
    
    const args = process.argv.slice(2);
    const command = args[0];

    switch (command) {
        case 'monitor':
            const interval = parseInt(args[1]) || 5000;
            analyzer.startMonitoring(interval);
            break;
        case 'gc':
            analyzer.forceGC();
            break;
        case 'snapshot':
            analyzer.takeSnapshot();
            analyzer.generateReport();
            break;
        default:
            console.log('使用方法:');
            console.log('  node memory-analysis.js monitor [interval]  - 开始监控 (默认5秒间隔)');
            console.log('  node memory-analysis.js snapshot            - 拍摄单次快照');
            console.log('  node memory-analysis.js gc                  - 强制垃圾回收');
            break;
    }
}

module.exports = MemoryAnalyzer;
