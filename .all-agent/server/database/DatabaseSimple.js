const Database = require('better-sqlite3');
const path = require('path');
const fs = require('fs').promises;

/**
 * 简化数据库管理类
 * 使用 better-sqlite3 提供数据持久化支持
 */
class DatabaseSimple {
  constructor(options = {}) {
    this.dbPath = options.dbPath || path.join(process.cwd(), 'data/all-agent.db');
    this.db = null;
    this.isConnected = false;

    // 确保数据目录存在
    this.ensureDataDirectory();
  }

  /**
   * 确保数据目录存在
   */
  async ensureDataDirectory() {
    const dataDir = path.dirname(this.dbPath);
    try {
      await fs.mkdir(dataDir, { recursive: true });
    } catch (error) {
      console.error('创建数据目录失败:', error);
    }
  }

  /**
   * 连接数据库
   */
  async connect() {
    try {
      this.db = new Database(this.dbPath);
      console.log('✅ 数据库连接成功');
      this.isConnected = true;
    } catch (err) {
      console.error('数据库连接失败:', err);
      throw err;
    }
  }

  /**
   * 初始化数据库表
   */
  async initialize() {
    if (!this.isConnected) {
      await this.connect();
    }

    try {
      await this.createTables();
      console.log('✅ 数据库表初始化完成');
    } catch (error) {
      console.error('❌ 数据库初始化失败:', error);
      throw error;
    }
  }

  /**
   * 创建数据库表
   */
  async createTables() {
    const tables = [
      // 用户表
      `CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username VARCHAR(50) UNIQUE NOT NULL,
                email VARCHAR(100) UNIQUE NOT NULL,
                password_hash VARCHAR(255) NOT NULL,
                role VARCHAR(20) DEFAULT 'user',
                last_login DATETIME,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                is_active BOOLEAN DEFAULT 1
            )`,

      // 项目表
      `CREATE TABLE IF NOT EXISTS projects (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                name VARCHAR(100) NOT NULL,
                description TEXT,
                project_path VARCHAR(500),
                status VARCHAR(20) DEFAULT 'active',
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )`,

      // 任务表
      `CREATE TABLE IF NOT EXISTS tasks (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                task_id VARCHAR(100) UNIQUE NOT NULL,
                user_id INTEGER NOT NULL,
                project_id INTEGER,
                agent_type VARCHAR(50) NOT NULL,
                action VARCHAR(100) NOT NULL,
                input_data TEXT,
                result_data TEXT,
                status VARCHAR(20) DEFAULT 'pending',
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id),
                FOREIGN KEY (project_id) REFERENCES projects (id)
            )`,

      // 聊天记录表
      `CREATE TABLE IF NOT EXISTS chat_messages (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                agent_type VARCHAR(50) NOT NULL,
                message_type VARCHAR(20) NOT NULL,
                content TEXT NOT NULL,
                context_data TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )`,

      // 用户会话表
      `CREATE TABLE IF NOT EXISTS user_sessions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                session_token VARCHAR(500) UNIQUE NOT NULL,
                expires_at DATETIME NOT NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                last_accessed DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )`,

      // 系统配置表
      `CREATE TABLE IF NOT EXISTS system_config (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                config_key VARCHAR(100) UNIQUE NOT NULL,
                config_value TEXT,
                description TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )`
    ];

    for (const sql of tables) {
      this.db.exec(sql);
    }

    // 创建索引
    const indexes = [
      'CREATE INDEX IF NOT EXISTS idx_users_email ON users(email)',
      'CREATE INDEX IF NOT EXISTS idx_users_username ON users(username)',
      'CREATE INDEX IF NOT EXISTS idx_projects_user_id ON projects(user_id)',
      'CREATE INDEX IF NOT EXISTS idx_tasks_user_id ON tasks(user_id)',
      'CREATE INDEX IF NOT EXISTS idx_tasks_status ON tasks(status)',
      'CREATE INDEX IF NOT EXISTS idx_chat_messages_user_id ON chat_messages(user_id)',
      'CREATE INDEX IF NOT EXISTS idx_user_sessions_user_id ON user_sessions(user_id)',
      'CREATE INDEX IF NOT EXISTS idx_user_sessions_token ON user_sessions(session_token)',
      'CREATE INDEX IF NOT EXISTS idx_user_sessions_expires ON user_sessions(expires_at)'
    ];

    for (const sql of indexes) {
      this.db.exec(sql);
    }
  }

  /**
   * 执行 SQL 语句
   */
  run(sql, params = []) {
    try {
      const stmt = this.db.prepare(sql);
      const result = stmt.run(params);
      return { id: result.lastInsertRowid, changes: result.changes };
    } catch (error) {
      console.error('SQL 执行失败:', error);
      throw error;
    }
  }

  /**
   * 查询单条记录
   */
  get(sql, params = []) {
    try {
      const stmt = this.db.prepare(sql);
      return stmt.get(params);
    } catch (error) {
      console.error('SQL 查询失败:', error);
      throw error;
    }
  }

  /**
   * 查询多条记录
   */
  all(sql, params = []) {
    try {
      const stmt = this.db.prepare(sql);
      return stmt.all(params);
    } catch (error) {
      console.error('SQL 查询失败:', error);
      throw error;
    }
  }

  /**
   * 开始事务
   */
  beginTransaction() {
    return this.db.transaction(() => {});
  }

  /**
   * 关闭数据库连接
   */
  close() {
    if (this.db) {
      this.db.close();
      console.log('✅ 数据库连接已关闭');
      this.isConnected = false;
    }
  }

  /**
   * 获取数据库统计信息
   */
  getStats() {
    const stats = {};
    const tables = ['users', 'projects', 'tasks', 'chat_messages', 'system_config'];

    for (const table of tables) {
      try {
        const result = this.get(`SELECT COUNT(*) as count FROM ${table}`);
        stats[table] = result ? result.count : 0;
      } catch (error) {
        stats[table] = 0;
      }
    }

    return stats;
  }

  /**
   * 检查数据库健康状态
   */
  healthCheck() {
    try {
      this.get('SELECT 1');
      return { healthy: true, message: '数据库连接正常' };
    } catch (error) {
      return { healthy: false, message: `数据库连接异常: ${error.message}` };
    }
  }
}

module.exports = DatabaseSimple;
