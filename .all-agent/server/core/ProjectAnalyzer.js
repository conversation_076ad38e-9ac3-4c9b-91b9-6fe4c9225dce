const fs = require('fs').promises;
const path = require('path');
const crypto = require('crypto');
const { Worker } = require('worker_threads');

/**
 * 项目分析引擎
 * 负责分析项目结构、技术栈检测、依赖关系分析等
 */
class ProjectAnalyzer {
    constructor(cache = null) {
        this.cache = cache;
        this.supportedLanguages = {
            '.js': 'javascript',
            '.jsx': 'javascript',
            '.ts': 'typescript',
            '.tsx': 'typescript',
            '.py': 'python',
            '.java': 'java',
            '.go': 'go',
            '.rs': 'rust',
            '.php': 'php',
            '.rb': 'ruby',
            '.cs': 'csharp',
            '.cpp': 'cpp',
            '.c': 'c',
            '.h': 'c',
            '.vue': 'vue',
            '.html': 'html',
            '.css': 'css',
            '.scss': 'scss',
            '.less': 'less',
            '.json': 'json',
            '.xml': 'xml',
            '.yaml': 'yaml',
            '.yml': 'yaml',
            '.md': 'markdown',
            '.sql': 'sql'
        };

        this.frameworkPatterns = {
            'react': [
                'react',
                'react-dom',
                'create-react-app',
                '@types/react'
            ],
            'vue': [
                'vue',
                '@vue/cli',
                'nuxt'
            ],
            'angular': [
                '@angular/core',
                '@angular/cli',
                'angular'
            ],
            'express': [
                'express',
                'express-generator'
            ],
            'django': [
                'Django',
                'django-rest-framework'
            ],
            'spring': [
                'spring-boot',
                'spring-framework'
            ],
            'laravel': [
                'laravel/framework'
            ],
            'rails': [
                'rails',
                'ruby on rails'
            ]
        };

        this.excludePatterns = [
            'node_modules',
            '.git',
            '.svn',
            'dist',
            'build',
            'target',
            '__pycache__',
            '.pytest_cache',
            'coverage',
            '.nyc_output',
            'logs',
            '*.log',
            '.DS_Store',
            'Thumbs.db'
        ];
    }

    /**
     * 分析项目
     */
    async analyzeProject(projectPath, options = {}, progressCallback = null) {
        const startTime = Date.now();

        try {
            // 验证项目路径
            await this.validateProjectPath(projectPath);

            // 生成缓存键
            const cacheKey = this.generateCacheKey(projectPath, options);

            // 检查缓存
            if (this.cache && !options.forceRefresh) {
                const cached = await this.cache.get(cacheKey);
                if (cached) {
                    console.log('使用缓存的分析结果');
                    if (progressCallback) progressCallback({ step: 5, message: '使用缓存结果', progress: 100 });
                    return cached;
                }
            }

            // 初始化分析结果
            const analysis = {
                projectPath,
                timestamp: new Date().toISOString(),
                structure: null,
                techStack: null,
                dependencies: null,
                statistics: null,
                recommendations: [],
                metadata: {
                    analysisTime: 0,
                    version: '1.0.0',
                    cached: false
                }
            };

            // 并行执行分析任务以提高性能
            const tasks = [];

            // 步骤 1: 分析项目结构
            if (progressCallback) progressCallback({ step: 1, message: '分析项目结构...', progress: 20 });
            tasks.push(this.analyzeStructure(projectPath, options));

            // 等待结构分析完成
            analysis.structure = await tasks[0];

            // 并行执行其他分析任务
            const parallelTasks = [
                this.detectTechStack(projectPath, analysis.structure),
                this.analyzeDependencies(projectPath, analysis.structure)
            ];

            if (progressCallback) progressCallback({ step: 2, message: '并行分析技术栈和依赖...', progress: 60 });

            const [techStack, dependencies] = await Promise.all(parallelTasks);
            analysis.techStack = techStack;
            analysis.dependencies = dependencies;

            // 步骤 4: 生成统计信息
            if (progressCallback) progressCallback({ step: 4, message: '生成统计信息...', progress: 80 });
            analysis.statistics = this.generateStatistics(analysis.structure);

            // 步骤 5: 生成建议
            if (progressCallback) progressCallback({ step: 5, message: '生成建议...', progress: 100 });
            analysis.recommendations = this.generateRecommendations(analysis);

            // 记录分析时间
            analysis.metadata.analysisTime = Date.now() - startTime;

            // 缓存结果
            if (this.cache) {
                const cacheTTL = options.cacheTTL || 3600; // 默认1小时
                await this.cache.set(cacheKey, analysis, cacheTTL, true);
            }

            return analysis;

        } catch (error) {
            console.error('项目分析失败:', error);
            throw error;
        }
    }

    /**
     * 验证项目路径
     */
    async validateProjectPath(projectPath) {
        try {
            const stats = await fs.stat(projectPath);
            if (!stats.isDirectory()) {
                throw new Error('项目路径必须是一个目录');
            }
        } catch (error) {
            if (error.code === 'ENOENT') {
                throw new Error('项目路径不存在');
            }
            throw error;
        }
    }

    /**
     * 分析项目结构
     */
    async analyzeStructure(projectPath, options = {}) {
        const maxDepth = options.maxDepth || 10;
        const includeHidden = options.includeHidden || false;

        const structure = {
            name: path.basename(projectPath),
            path: projectPath,
            type: 'directory',
            children: [],
            size: 0,
            fileCount: 0,
            directoryCount: 0
        };

        await this.scanDirectory(structure, projectPath, 0, maxDepth, includeHidden);
        return structure;
    }

    /**
     * 扫描目录（优化版本，支持并发）
     */
    async scanDirectory(node, dirPath, currentDepth, maxDepth, includeHidden) {
        if (currentDepth >= maxDepth) {
            return;
        }

        try {
            const entries = await fs.readdir(dirPath, { withFileTypes: true });
            const tasks = [];

            for (const entry of entries) {
                // 跳过隐藏文件（除非明确包含）
                if (!includeHidden && entry.name.startsWith('.')) {
                    continue;
                }

                // 跳过排除的模式
                if (this.shouldExclude(entry.name)) {
                    continue;
                }

                const entryPath = path.join(dirPath, entry.name);

                // 并行处理文件和目录
                tasks.push(this.processEntry(entry, entryPath, currentDepth, maxDepth, includeHidden));
            }

            // 等待所有任务完成
            const childNodes = await Promise.all(tasks);

            // 处理结果
            for (const childNode of childNodes) {
                if (childNode) {
                    if (childNode.type === 'directory') {
                        node.fileCount += childNode.fileCount;
                        node.directoryCount += childNode.directoryCount + 1;
                    } else {
                        node.fileCount += 1;
                    }

                    node.size += childNode.size;
                    node.children.push(childNode);
                }
            }

            // 按类型和名称排序
            node.children.sort((a, b) => {
                if (a.type !== b.type) {
                    return a.type === 'directory' ? -1 : 1;
                }
                return a.name.localeCompare(b.name);
            });

        } catch (error) {
            console.warn(`扫描目录失败: ${dirPath}`, error.message);
        }
    }

    /**
     * 处理单个文件或目录条目
     */
    async processEntry(entry, entryPath, currentDepth, maxDepth, includeHidden) {
        try {
            const stats = await fs.stat(entryPath);

            const childNode = {
                name: entry.name,
                path: entryPath,
                type: entry.isDirectory() ? 'directory' : 'file',
                size: stats.size,
                modified: stats.mtime,
                extension: entry.isFile() ? path.extname(entry.name) : null,
                language: entry.isFile() ? this.detectLanguage(entry.name) : null
            };

            if (entry.isDirectory()) {
                childNode.children = [];
                childNode.fileCount = 0;
                childNode.directoryCount = 0;

                await this.scanDirectory(childNode, entryPath, currentDepth + 1, maxDepth, includeHidden);
            }

            return childNode;
        } catch (error) {
            console.warn(`处理条目失败: ${entryPath}`, error.message);
            return null;
        }
    }

    /**
     * 检测文件语言
     */
    detectLanguage(filename) {
        const ext = path.extname(filename).toLowerCase();
        return this.supportedLanguages[ext] || 'unknown';
    }

    /**
     * 检查是否应该排除
     */
    shouldExclude(name) {
        return this.excludePatterns.some(pattern => {
            if (pattern.includes('*')) {
                const regex = new RegExp(pattern.replace(/\*/g, '.*'));
                return regex.test(name);
            }
            return name === pattern;
        });
    }

    /**
     * 检测技术栈
     */
    async detectTechStack(projectPath, structure) {
        const techStack = {
            languages: {},
            frameworks: [],
            tools: [],
            packageManagers: [],
            databases: [],
            confidence: {}
        };

        // 分析语言使用情况
        this.analyzeLanguageUsage(structure, techStack.languages);

        // 检测包管理器和配置文件
        await this.detectPackageManagers(projectPath, techStack);

        // 检测框架
        await this.detectFrameworks(projectPath, techStack);

        // 检测工具和数据库
        await this.detectToolsAndDatabases(projectPath, techStack);

        return techStack;
    }

    /**
     * 分析语言使用情况
     */
    analyzeLanguageUsage(node, languages) {
        if (node.type === 'file' && node.language && node.language !== 'unknown') {
            if (!languages[node.language]) {
                languages[node.language] = { files: 0, size: 0 };
            }
            languages[node.language].files += 1;
            languages[node.language].size += node.size;
        }

        if (node.children) {
            for (const child of node.children) {
                this.analyzeLanguageUsage(child, languages);
            }
        }
    }

    /**
     * 检测包管理器
     */
    async detectPackageManagers(projectPath, techStack) {
        const packageFiles = [
            { file: 'package.json', manager: 'npm' },
            { file: 'yarn.lock', manager: 'yarn' },
            { file: 'pnpm-lock.yaml', manager: 'pnpm' },
            { file: 'requirements.txt', manager: 'pip' },
            { file: 'Pipfile', manager: 'pipenv' },
            { file: 'poetry.lock', manager: 'poetry' },
            { file: 'Cargo.toml', manager: 'cargo' },
            { file: 'go.mod', manager: 'go modules' },
            { file: 'composer.json', manager: 'composer' },
            { file: 'Gemfile', manager: 'bundler' }
        ];

        for (const { file, manager } of packageFiles) {
            try {
                await fs.access(path.join(projectPath, file));
                techStack.packageManagers.push(manager);
            } catch (error) {
                // 文件不存在，跳过
            }
        }
    }

    /**
     * 检测框架
     */
    async detectFrameworks(projectPath, techStack) {
        try {
            // 检查 package.json
            const packageJsonPath = path.join(projectPath, 'package.json');
            const packageJson = JSON.parse(await fs.readFile(packageJsonPath, 'utf8'));

            const allDeps = {
                ...packageJson.dependencies,
                ...packageJson.devDependencies,
                ...packageJson.peerDependencies
            };

            for (const [framework, patterns] of Object.entries(this.frameworkPatterns)) {
                for (const pattern of patterns) {
                    if (allDeps[pattern]) {
                        techStack.frameworks.push({
                            name: framework,
                            version: allDeps[pattern],
                            confidence: 'high'
                        });
                        break;
                    }
                }
            }
        } catch (error) {
            // package.json 不存在或解析失败
        }

        // 检查其他框架特征文件
        const frameworkFiles = [
            { file: 'angular.json', framework: 'angular' },
            { file: 'vue.config.js', framework: 'vue' },
            { file: 'nuxt.config.js', framework: 'nuxt' },
            { file: 'next.config.js', framework: 'next.js' },
            { file: 'gatsby-config.js', framework: 'gatsby' },
            { file: 'svelte.config.js', framework: 'svelte' }
        ];

        for (const { file, framework } of frameworkFiles) {
            try {
                await fs.access(path.join(projectPath, file));
                if (!techStack.frameworks.find(f => f.name === framework)) {
                    techStack.frameworks.push({
                        name: framework,
                        confidence: 'medium'
                    });
                }
            } catch (error) {
                // 文件不存在，跳过
            }
        }
    }

    /**
     * 检测工具和数据库
     */
    async detectToolsAndDatabases(projectPath, techStack) {
        const toolFiles = [
            { file: 'webpack.config.js', tool: 'webpack' },
            { file: 'vite.config.js', tool: 'vite' },
            { file: 'rollup.config.js', tool: 'rollup' },
            { file: '.eslintrc.js', tool: 'eslint' },
            { file: 'prettier.config.js', tool: 'prettier' },
            { file: 'jest.config.js', tool: 'jest' },
            { file: 'cypress.json', tool: 'cypress' },
            { file: 'docker-compose.yml', tool: 'docker' },
            { file: 'Dockerfile', tool: 'docker' }
        ];

        for (const { file, tool } of toolFiles) {
            try {
                await fs.access(path.join(projectPath, file));
                techStack.tools.push(tool);
            } catch (error) {
                // 文件不存在，跳过
            }
        }
    }

    /**
     * 分析依赖关系
     */
    async analyzeDependencies(projectPath, structure) {
        const dependencies = {
            internal: [],
            external: [],
            graph: {},
            circular: []
        };

        // 这里可以实现更复杂的依赖分析逻辑
        // 目前返回基础结构
        return dependencies;
    }

    /**
     * 生成统计信息
     */
    generateStatistics(structure) {
        const stats = {
            totalFiles: structure.fileCount,
            totalDirectories: structure.directoryCount,
            totalSize: structure.size,
            languageDistribution: {},
            largestFiles: [],
            deepestPath: 0
        };

        // 计算语言分布
        this.calculateLanguageDistribution(structure, stats.languageDistribution);

        // 找出最大的文件
        this.findLargestFiles(structure, stats.largestFiles, 10);

        // 计算最深路径
        stats.deepestPath = this.calculateMaxDepth(structure);

        return stats;
    }

    /**
     * 计算语言分布
     */
    calculateLanguageDistribution(node, distribution) {
        if (node.type === 'file' && node.language && node.language !== 'unknown') {
            if (!distribution[node.language]) {
                distribution[node.language] = { files: 0, size: 0 };
            }
            distribution[node.language].files += 1;
            distribution[node.language].size += node.size;
        }

        if (node.children) {
            for (const child of node.children) {
                this.calculateLanguageDistribution(child, distribution);
            }
        }
    }

    /**
     * 找出最大的文件
     */
    findLargestFiles(node, largestFiles, limit) {
        if (node.type === 'file') {
            largestFiles.push({
                name: node.name,
                path: node.path,
                size: node.size,
                language: node.language
            });

            // 保持数组大小限制
            if (largestFiles.length > limit) {
                largestFiles.sort((a, b) => b.size - a.size);
                largestFiles.splice(limit);
            }
        }

        if (node.children) {
            for (const child of node.children) {
                this.findLargestFiles(child, largestFiles, limit);
            }
        }
    }

    /**
     * 计算最大深度
     */
    calculateMaxDepth(node, currentDepth = 0) {
        let maxDepth = currentDepth;

        if (node.children) {
            for (const child of node.children) {
                const childDepth = this.calculateMaxDepth(child, currentDepth + 1);
                maxDepth = Math.max(maxDepth, childDepth);
            }
        }

        return maxDepth;
    }

    /**
     * 生成建议
     */
    generateRecommendations(analysis) {
        const recommendations = [];

        // 基于技术栈的建议
        if (analysis.techStack.frameworks.length === 0) {
            recommendations.push({
                type: 'framework',
                priority: 'medium',
                message: '考虑使用现代框架来提高开发效率',
                suggestions: ['React', 'Vue.js', 'Angular']
            });
        }

        // 基于项目大小的建议
        if (analysis.statistics.totalFiles > 1000) {
            recommendations.push({
                type: 'structure',
                priority: 'high',
                message: '项目文件数量较多，建议优化项目结构',
                suggestions: ['模块化重构', '代码分割', '清理无用文件']
            });
        }

        // 基于语言分布的建议
        const languages = Object.keys(analysis.statistics.languageDistribution);
        if (languages.length > 5) {
            recommendations.push({
                type: 'language',
                priority: 'medium',
                message: '项目使用了多种编程语言，建议统一技术栈',
                suggestions: ['选择主要技术栈', '减少语言多样性']
            });
        }

        return recommendations;
    }

    /**
     * 生成缓存键
     */
    generateCacheKey(projectPath, options) {
        const keyData = {
            projectPath: path.resolve(projectPath),
            options: {
                maxDepth: options.maxDepth || 10,
                includeHidden: options.includeHidden || false
            }
        };

        return crypto.createHash('md5').update(JSON.stringify(keyData)).digest('hex');
    }

    /**
     * 获取项目指纹（用于检测项目变化）
     */
    async getProjectFingerprint(projectPath) {
        try {
            const stats = await fs.stat(projectPath);
            const packageJsonPath = path.join(projectPath, 'package.json');

            let packageJsonMtime = null;
            try {
                const packageStats = await fs.stat(packageJsonPath);
                packageJsonMtime = packageStats.mtime.getTime();
            } catch (error) {
                // package.json 不存在
            }

            return {
                projectMtime: stats.mtime.getTime(),
                packageJsonMtime,
                timestamp: Date.now()
            };
        } catch (error) {
            return { timestamp: Date.now() };
        }
    }

    /**
     * 检查项目是否有变化
     */
    async hasProjectChanged(projectPath, lastFingerprint) {
        const currentFingerprint = await this.getProjectFingerprint(projectPath);

        if (!lastFingerprint) {
            return true;
        }

        return currentFingerprint.projectMtime !== lastFingerprint.projectMtime ||
               currentFingerprint.packageJsonMtime !== lastFingerprint.packageJsonMtime;
    }

    /**
     * 清理分析缓存
     */
    async clearCache(projectPath = null) {
        if (!this.cache) {
            return;
        }

        if (projectPath) {
            const cacheKey = this.generateCacheKey(projectPath, {});
            await this.cache.delete(cacheKey);
        } else {
            await this.cache.clear();
        }
    }

    /**
     * 获取分析统计信息
     */
    getAnalysisStats() {
        if (!this.cache) {
            return null;
        }

        return this.cache.getStats();
    }
}

module.exports = ProjectAnalyzer;
