{"version": 3, "file": "browserService.js", "sourceRoot": "", "sources": ["../../src/services/browserService.ts"], "names": [], "mappings": ";;;AAGA;;GAEG;AACH,MAAa,cAAc;IAQvB,YAAY,eAAgC;QANpC,kBAAa,GAIhB,IAAI,GAAG,EAAE,CAAC;QAGX,IAAI,CAAC,eAAe,GAAG,eAAe,CAAC;IAC3C,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,kBAAkB;QAQpB,MAAM,WAAW,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;QACvF,MAAM,OAAO,GAOR,EAAE,CAAC;QAER,gBAAgB;QAChB,MAAM,UAAU,GAAG,WAAW,CAAC,GAAG,CAAC,KAAK,EAAE,IAAI,EAAE,EAAE;YAC9C,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAC7B,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;YAC7C,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAE5C,IAAI,SAAS,EAAE;gBACX,MAAM,GAAG,GAAG,oBAAoB,IAAI,EAAE,CAAC;gBACvC,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;gBACjD,MAAM,MAAM,GAAG,YAAY,GAAG,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,WAAW,CAAC;gBAE7D,OAAO;oBACH,IAAI;oBACJ,GAAG;oBACH,MAAM,EAAE,SAAkB;oBAC1B,OAAO;oBACP,MAAM;oBACN,YAAY;iBACf,CAAC;aACL;YACD,OAAO,IAAI,CAAC;QAChB,CAAC,CAAC,CAAC;QAEH,MAAM,OAAO,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QAC9C,OAAO,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,KAAK,IAAI,CAO7C,CAAC;IACP,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,SAAS,CAAC,IAAY;QAChC,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,cAAc,CACpD,2DAA2D,IAAI,gBAAgB,EAC/E,EAAE,OAAO,EAAE,IAAI,EAAE,CACpB,CAAC;QAEF,IAAI,MAAM,CAAC,OAAO,EAAE;YAChB,MAAM,QAAQ,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;YACtC,OAAO,QAAQ,KAAK,KAAK,IAAI,QAAQ,KAAK,EAAE,CAAC;SAChD;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,eAAe,CAAC,IAAY;QACtC,IAAI;YACA,kBAAkB;YAClB,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,cAAc,CACxD,4CAA4C,IAAI,EAAE,EAClD,EAAE,OAAO,EAAE,IAAI,EAAE,CACpB,CAAC;YAEF,IAAI,UAAU,CAAC,OAAO,IAAI,UAAU,CAAC,MAAM,EAAE;gBACzC,MAAM,OAAO,GAAG,UAAU,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC;gBAEhD,WAAW;gBACX,IAAI,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,aAAa,CAAC;oBAAE,OAAO,kBAAkB,CAAC;gBAC5F,IAAI,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC;oBAAE,OAAO,SAAS,CAAC;gBAChF,IAAI,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC;oBAAE,OAAO,MAAM,CAAC;gBAC3E,IAAI,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,YAAY,CAAC;oBAAE,OAAO,oBAAoB,CAAC;gBAC/F,IAAI,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,YAAY,CAAC;oBAAE,OAAO,SAAS,CAAC;gBACpF,IAAI,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,aAAa,CAAC;oBAAE,OAAO,gBAAgB,CAAC;gBACxF,IAAI,OAAO,CAAC,QAAQ,CAAC,WAAW,CAAC;oBAAE,OAAO,kBAAkB,CAAC;gBAC7D,IAAI,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC;oBAAE,OAAO,YAAY,CAAC;aACxF;YAED,kBAAkB;YAClB,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,cAAc,CAC1D,4CAA4C,IAAI,EAAE,EAClD,EAAE,OAAO,EAAE,IAAI,EAAE,CACpB,CAAC;YAEF,IAAI,YAAY,CAAC,OAAO,EAAE;gBACtB,MAAM,OAAO,GAAG,YAAY,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC;gBAElD,IAAI,OAAO,CAAC,QAAQ,CAAC,eAAe,CAAC;oBAAE,OAAO,OAAO,CAAC;gBACtD,IAAI,OAAO,CAAC,QAAQ,CAAC,gBAAgB,CAAC;oBAAE,OAAO,QAAQ,CAAC;gBACxD,IAAI,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC;oBAAE,OAAO,YAAY,CAAC;gBACrD,IAAI,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC;oBAAE,OAAO,WAAW,CAAC;gBACpD,IAAI,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC;oBAAE,OAAO,OAAO,CAAC;gBAC9C,IAAI,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC;oBAAE,OAAO,QAAQ,CAAC;aACnD;YAED,YAAY;YACZ,MAAM,YAAY,GAA8B;gBAC5C,IAAI,EAAE,uBAAuB;gBAC7B,IAAI,EAAE,qBAAqB;gBAC3B,IAAI,EAAE,aAAa;gBACnB,IAAI,EAAE,eAAe;gBACrB,IAAI,EAAE,MAAM;gBACZ,IAAI,EAAE,eAAe;gBACrB,IAAI,EAAE,oBAAoB;gBAC1B,IAAI,EAAE,qBAAqB;gBAC3B,IAAI,EAAE,oBAAoB;gBAC1B,IAAI,EAAE,0BAA0B;aACnC,CAAC;YAEF,OAAO,YAAY,CAAC,IAAI,CAAC,IAAI,YAAY,CAAC;SAC7C;QAAC,OAAO,KAAK,EAAE;YACZ,YAAY;YACZ,IAAI,IAAI,KAAK,IAAI;gBAAE,OAAO,oBAAoB,CAAC;YAC/C,OAAO,iBAAiB,CAAC;SAC5B;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,cAAc,CAAC,GAAW;QAO5B,IAAI;YACA,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,cAAc,CACpD,eAAe,GAAG,GAAG,EACrB,EAAE,OAAO,EAAE,KAAK,EAAE,CACrB,CAAC;YAEF,IAAI,MAAM,CAAC,OAAO,EAAE;gBAChB,MAAM,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC;gBAC9B,MAAM,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;gBAEzC,OAAO;oBACH,OAAO,EAAE,IAAI;oBACb,KAAK;oBACL,OAAO;oBACP,MAAM,EAAE,GAAG;iBACd,CAAC;aACL;iBAAM;gBACH,OAAO;oBACH,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,MAAM,CAAC,KAAK,IAAI,sBAAsB;iBAChD,CAAC;aACL;SACJ;QAAC,OAAO,KAAK,EAAE;YACZ,OAAO;gBACH,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;aAChE,CAAC;SACL;IACL,CAAC;IAED;;OAEG;IACK,YAAY,CAAC,IAAY;QAC7B,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,+BAA+B,CAAC,CAAC;QAC/D,OAAO,UAAU,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC;IACzD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,eAAe,CAAC,GAAW;QAM7B,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,IAAI;YACA,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,cAAc,CACpD,yDAAyD,GAAG,GAAG,EAC/D,EAAE,OAAO,EAAE,KAAK,EAAE,CACrB,CAAC;YAEF,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAE5C,IAAI,MAAM,CAAC,OAAO,EAAE;gBAChB,MAAM,CAAC,UAAU,EAAE,SAAS,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;gBACzD,MAAM,MAAM,GAAG,QAAQ,CAAC,UAAU,CAAC,CAAC;gBAEpC,OAAO;oBACH,MAAM;oBACN,YAAY,EAAE,UAAU,CAAC,SAAS,CAAC,GAAG,IAAI;oBAC1C,UAAU,EAAE,MAAM,IAAI,GAAG,IAAI,MAAM,GAAG,GAAG;iBAC5C,CAAC;aACL;iBAAM;gBACH,OAAO;oBACH,MAAM,EAAE,CAAC;oBACT,YAAY;oBACZ,UAAU,EAAE,KAAK;oBACjB,KAAK,EAAE,MAAM,CAAC,KAAK;iBACtB,CAAC;aACL;SACJ;QAAC,OAAO,KAAK,EAAE;YACZ,OAAO;gBACH,MAAM,EAAE,CAAC;gBACT,YAAY,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS;gBACpC,UAAU,EAAE,KAAK;gBACjB,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;aAChE,CAAC;SACL;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,cAAc,CAAC,UAAkB,WAAW;QAM9C,IAAI;YACA,WAAW;YACX,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,iBAAiB,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;YAEnF,UAAU;YACV,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC;YAExD,WAAW;YACX,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAChD,MAAM,SAAS,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,SAAS,CAAC,CAAC;YAE5D,IAAI,SAAS,EAAE;gBACX,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,KAAK,EAAE;oBAC1B,IAAI,EAAE,SAAS,CAAC,IAAI;oBACpB,GAAG,EAAE,SAAS,CAAC,GAAG;iBACrB,CAAC,CAAC;gBAEH,OAAO;oBACH,OAAO,EAAE,IAAI;oBACb,IAAI,EAAE,SAAS,CAAC,IAAI;oBACpB,GAAG,EAAE,SAAS,CAAC,GAAG;iBACrB,CAAC;aACL;iBAAM;gBACH,OAAO;oBACH,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,iDAAiD;iBAC3D,CAAC;aACL;SACJ;QAAC,OAAO,KAAK,EAAE;YACZ,OAAO;gBACH,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;aAChE,CAAC;SACL;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,WAAW,CAAC,GAAW;QACzB,IAAI;YACA,IAAI,OAAe,CAAC;YAEpB,QAAQ,OAAO,CAAC,QAAQ,EAAE;gBACtB,KAAK,QAAQ;oBACT,OAAO,GAAG,SAAS,GAAG,GAAG,CAAC;oBAC1B,MAAM;gBACV,KAAK,OAAO;oBACR,OAAO,GAAG,UAAU,GAAG,GAAG,CAAC;oBAC3B,MAAM;gBACV;oBACI,OAAO,GAAG,aAAa,GAAG,GAAG,CAAC;oBAC9B,MAAM;aACb;YAED,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,cAAc,CAAC,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;YACrF,OAAO,MAAM,CAAC,OAAO,CAAC;SACzB;QAAC,OAAO,KAAK,EAAE;YACZ,OAAO,KAAK,CAAC;SAChB;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,WAAW,CAAC,GAAW,EAAE,WAAmB,IAAI;QAIlD,IAAI,UAAU,GAAG,IAAI,CAAC;QACtB,IAAI,UAAU,GAAQ,IAAI,CAAC;QAC3B,MAAM,SAAS,GAAiC,EAAE,CAAC;QAEnD,MAAM,OAAO,GAAG,KAAK,IAAI,EAAE;YACvB,OAAO,UAAU,EAAE;gBACf,IAAI;oBACA,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC;oBAE/C,IAAI,CAAC,UAAU;wBACX,MAAM,CAAC,MAAM,KAAK,UAAU,CAAC,MAAM;wBACnC,MAAM,CAAC,UAAU,KAAK,UAAU,CAAC,UAAU,EAAE;wBAE7C,UAAU,GAAG,MAAM,CAAC;wBACpB,SAAS,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;qBACnD;iBACJ;gBAAC,OAAO,KAAK,EAAE;oBACZ,SAAS;iBACZ;gBAED,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC,CAAC;aAC/D;QACL,CAAC,CAAC;QAEF,OAAO;QACP,OAAO,EAAE,CAAC;QAEV,OAAO;YACH,IAAI,EAAE,GAAG,EAAE;gBACP,UAAU,GAAG,KAAK,CAAC;YACvB,CAAC;YACD,cAAc,EAAE,CAAC,QAA+B,EAAE,EAAE;gBAChD,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAC7B,CAAC;SACJ,CAAC;IACN,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,kBAAkB,CAAC,GAAW;QAMhC,IAAI;YACA,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,cAAc,CACpD,qHAAqH,GAAG,GAAG,EAC3H,EAAE,OAAO,EAAE,KAAK,EAAE,CACrB,CAAC;YAEF,IAAI,MAAM,CAAC,OAAO,EAAE;gBAChB,MAAM,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC;gBAC9B,MAAM,SAAS,GAAG,OAAO,CAAC,KAAK,CAAC,sBAAsB,CAAC,CAAC;gBACxD,MAAM,SAAS,GAAG,OAAO,CAAC,KAAK,CAAC,wBAAwB,CAAC,CAAC;gBAC1D,MAAM,cAAc,GAAG,OAAO,CAAC,KAAK,CAAC,wBAAwB,CAAC,CAAC;gBAE/D,OAAO;oBACH,QAAQ,EAAE,SAAS,CAAC,CAAC,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;oBACzD,IAAI,EAAE,SAAS,CAAC,CAAC,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC5C,QAAQ,EAAE,cAAc,CAAC,CAAC,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;iBACjE,CAAC;aACL;iBAAM;gBACH,OAAO;oBACH,QAAQ,EAAE,CAAC;oBACX,IAAI,EAAE,CAAC;oBACP,QAAQ,EAAE,CAAC;oBACX,KAAK,EAAE,MAAM,CAAC,KAAK;iBACtB,CAAC;aACL;SACJ;QAAC,OAAO,KAAK,EAAE;YACZ,OAAO;gBACH,QAAQ,EAAE,CAAC;gBACX,IAAI,EAAE,CAAC;gBACP,QAAQ,EAAE,CAAC;gBACX,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;aAChE,CAAC;SACL;IACL,CAAC;IAED;;OAEG;IACH,OAAO;QACH,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;IAC/B,CAAC;CACJ;AAvZD,wCAuZC"}