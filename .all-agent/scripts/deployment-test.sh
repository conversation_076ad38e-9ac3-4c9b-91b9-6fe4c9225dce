#!/bin/bash

# All-Agent 部署测试脚本
# 基于生产环境检查清单进行自动化部署测试

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查结果统计
TOTAL_CHECKS=0
PASSED_CHECKS=0
FAILED_CHECKS=0
WARNING_CHECKS=0

# 执行检查并记录结果
run_check() {
    local check_name="$1"
    local check_command="$2"
    local is_critical="${3:-true}"
    
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    log_info "检查: $check_name"
    
    if eval "$check_command"; then
        log_success "✅ $check_name - 通过"
        PASSED_CHECKS=$((PASSED_CHECKS + 1))
        return 0
    else
        if [ "$is_critical" = "true" ]; then
            log_error "❌ $check_name - 失败"
            FAILED_CHECKS=$((FAILED_CHECKS + 1))
            return 1
        else
            log_warning "⚠️ $check_name - 警告"
            WARNING_CHECKS=$((WARNING_CHECKS + 1))
            return 0
        fi
    fi
}

# 1. 环境检查
check_environment() {
    log_info "=== 环境检查 ==="
    
    # Node.js 版本检查
    run_check "Node.js 版本 (>=16)" "node --version | grep -E 'v1[6-9]|v[2-9][0-9]'"
    
    # npm 版本检查
    run_check "npm 版本 (>=8)" "npm --version | grep -E '^[8-9]|^[1-9][0-9]'"
    
    # 必需的系统工具
    run_check "curl 可用性" "command -v curl"
    run_check "git 可用性" "command -v git"
    
    # 端口可用性检查
    run_check "端口 3000 可用性" "! lsof -i :3000" "false"
    run_check "端口 9090 可用性" "! lsof -i :9090" "false"
}

# 2. 依赖检查
check_dependencies() {
    log_info "=== 依赖检查 ==="
    
    cd "$(dirname "$0")/../server"
    
    # package.json 存在性
    run_check "package.json 存在" "test -f package.json"
    
    # 依赖安装
    log_info "安装依赖..."
    if npm install --production; then
        log_success "依赖安装成功"
    else
        log_error "依赖安装失败"
        return 1
    fi
    
    # 关键依赖检查
    run_check "Express.js 依赖" "npm list express --depth=0"
    run_check "Socket.IO 依赖" "npm list socket.io --depth=0"
    run_check "SQLite 依赖" "npm list better-sqlite3 --depth=0"
}

# 3. 配置检查
check_configuration() {
    log_info "=== 配置检查 ==="
    
    # 环境变量检查
    run_check "NODE_ENV 设置" "test -n '$NODE_ENV'" "false"
    run_check "PORT 设置" "test -n '$PORT'" "false"
    
    # 配置文件检查
    run_check "agents.json 配置文件" "test -f ../agents.json" "false"
    
    # 创建测试环境变量文件
    cat > .env.test << EOF
NODE_ENV=test
PORT=3001
JWT_SECRET=test-jwt-secret-for-deployment-testing
DB_PATH=./data/test.db
LOG_LEVEL=info
ENABLE_MONITORING=true
EOF
    
    run_check "测试环境变量文件创建" "test -f .env.test"
}

# 4. 数据库检查
check_database() {
    log_info "=== 数据库检查 ==="
    
    # 创建测试数据目录
    mkdir -p ./data
    run_check "数据目录创建" "test -d ./data"
    
    # 数据库初始化测试
    if node -e "
        const Database = require('./database/DatabaseSimple');
        const db = new Database({ dbPath: './data/test.db' });
        db.initialize().then(() => {
            console.log('Database initialized successfully');
            process.exit(0);
        }).catch(err => {
            console.error('Database initialization failed:', err);
            process.exit(1);
        });
    "; then
        log_success "数据库初始化测试通过"
    else
        log_error "数据库初始化测试失败"
        return 1
    fi
}

# 5. 服务启动测试
check_service_startup() {
    log_info "=== 服务启动测试 ==="
    
    # 设置测试环境
    export NODE_ENV=test
    export PORT=3001
    export JWT_SECRET=test-jwt-secret
    export DB_PATH=./data/test.db
    
    # 启动服务（后台运行）
    log_info "启动测试服务..."
    node app-new.js &
    SERVER_PID=$!
    
    # 等待服务启动
    sleep 5
    
    # 检查进程是否运行
    if kill -0 $SERVER_PID 2>/dev/null; then
        log_success "服务进程启动成功 (PID: $SERVER_PID)"
    else
        log_error "服务进程启动失败"
        return 1
    fi
    
    # 健康检查
    run_check "健康检查端点" "curl -f http://localhost:3001/health"
    
    # 停止测试服务
    log_info "停止测试服务..."
    kill $SERVER_PID 2>/dev/null || true
    sleep 2
}

# 6. API 端点测试
check_api_endpoints() {
    log_info "=== API 端点测试 ==="
    
    # 重新启动服务进行 API 测试
    export NODE_ENV=test
    export PORT=3001
    node app-new.js &
    SERVER_PID=$!
    sleep 5
    
    # 基础端点测试
    run_check "健康检查 API" "curl -f http://localhost:3001/health"
    run_check "Agent 状态 API" "curl -f http://localhost:3001/api/agents"
    
    # 认证端点测试（应该返回错误，但不应该崩溃）
    run_check "认证端点响应" "curl -s http://localhost:3001/auth/me | grep -q 'error\\|success'" "false"
    
    # 停止服务
    kill $SERVER_PID 2>/dev/null || true
    sleep 2
}

# 7. 安全配置检查
check_security_config() {
    log_info "=== 安全配置检查 ==="
    
    # JWT 密钥检查
    if [ "$JWT_SECRET" = "all-agent-jwt-secret-change-in-production" ]; then
        log_warning "使用默认 JWT 密钥（生产环境需要更改）"
        WARNING_CHECKS=$((WARNING_CHECKS + 1))
    else
        log_success "JWT 密钥已自定义"
        PASSED_CHECKS=$((PASSED_CHECKS + 1))
    fi
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    
    # 文件权限检查
    run_check "配置文件权限" "test $(stat -c '%a' .env.test 2>/dev/null || echo '644') -le 600" "false"
    
    # 敏感文件检查
    run_check "无敏感文件暴露" "! find . -name '*.key' -o -name '*.pem' -o -name '.env' | grep -v '.env.test'" "false"
}

# 8. 性能基准测试
check_performance_baseline() {
    log_info "=== 性能基准测试 ==="
    
    # 启动服务进行性能测试
    export NODE_ENV=test
    export PORT=3001
    node app-new.js &
    SERVER_PID=$!
    sleep 5
    
    # 响应时间测试
    log_info "测试响应时间..."
    RESPONSE_TIME=$(curl -o /dev/null -s -w '%{time_total}' http://localhost:3001/health)
    
    if (( $(echo "$RESPONSE_TIME < 1.0" | bc -l) )); then
        log_success "响应时间测试通过: ${RESPONSE_TIME}s"
        PASSED_CHECKS=$((PASSED_CHECKS + 1))
    else
        log_warning "响应时间较慢: ${RESPONSE_TIME}s"
        WARNING_CHECKS=$((WARNING_CHECKS + 1))
    fi
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    
    # 并发测试（简单版本）
    log_info "测试并发处理..."
    for i in {1..5}; do
        curl -s http://localhost:3001/health > /dev/null &
    done
    wait
    
    log_success "并发测试完成"
    PASSED_CHECKS=$((PASSED_CHECKS + 1))
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    
    # 停止服务
    kill $SERVER_PID 2>/dev/null || true
    sleep 2
}

# 9. 清理测试环境
cleanup_test_environment() {
    log_info "=== 清理测试环境 ==="
    
    # 删除测试文件
    rm -f .env.test
    rm -f ./data/test.db
    
    # 停止可能残留的进程
    pkill -f "node app-new.js" 2>/dev/null || true
    
    log_success "测试环境清理完成"
}

# 主函数
main() {
    log_info "开始 All-Agent 部署测试..."
    log_info "测试时间: $(date)"
    echo
    
    # 执行所有检查
    check_environment || true
    check_dependencies || true
    check_configuration || true
    check_database || true
    check_service_startup || true
    check_api_endpoints || true
    check_security_config || true
    check_performance_baseline || true
    
    # 清理
    cleanup_test_environment
    
    # 输出测试结果
    echo
    log_info "=== 测试结果汇总 ==="
    echo "总检查项: $TOTAL_CHECKS"
    echo -e "通过: ${GREEN}$PASSED_CHECKS${NC}"
    echo -e "警告: ${YELLOW}$WARNING_CHECKS${NC}"
    echo -e "失败: ${RED}$FAILED_CHECKS${NC}"
    
    # 计算成功率
    SUCCESS_RATE=$(( (PASSED_CHECKS + WARNING_CHECKS) * 100 / TOTAL_CHECKS ))
    echo "成功率: $SUCCESS_RATE%"
    
    if [ $FAILED_CHECKS -eq 0 ]; then
        log_success "🎉 部署测试全部通过！系统可以部署到生产环境。"
        exit 0
    else
        log_error "❌ 部署测试发现问题，请修复后重新测试。"
        exit 1
    fi
}

# 执行主函数
main "$@"
