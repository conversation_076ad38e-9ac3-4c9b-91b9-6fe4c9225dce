/**
 * 服务器核心类 - 重构后的主要服务器逻辑
 */
const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const path = require('path');

const { loadEnvironmentVariables } = require('../utils/Environment');
const Logger = require('../utils/Logger');
const Database = require('../database/DatabaseSimple');
const AuthService = require('../auth/AuthService');
const AgentManager = require('./AgentManager');
const LLMService = require('../api/LLMService');
const Monitor = require('../monitoring/Monitor');
const AlertManager = require('../monitoring/AlertManager');

// 路由模块
const AuthRoutes = require('../routes/authRoutes');
const APIRoutes = require('../routes/apiRoutes');
const MonitoringRoutes = require('../routes/monitoringRoutes');
const PublicRoutes = require('../routes/publicRoutes');

// 中间件
const { setupMiddleware } = require('../middleware/setup');
const { setupWebSocket } = require('../websocket/setup');

class ServerCore {
  constructor(options = {}) {
    this.options = {
      port: process.env.PORT || 3000,
      projectRoot: process.cwd(),
      ...options
    };

    this.app = express();
    this.server = http.createServer(this.app);
    this.io = socketIo(this.server, {
      cors: {
        origin: process.env.CORS_ORIGIN || '*',
        methods: ['GET', 'POST']
      }
    });

    // 核心服务
    this.logger = new Logger();
    this.database = null;
    this.authService = null;
    this.agentManager = null;
    this.llmService = null;
    this.monitor = new Monitor();
    this.alertManager = new AlertManager();

    // 状态
    this.isInitialized = false;
    this.isRunning = false;
  }

  /**
   * 初始化服务器
   */
  async initialize() {
    if (this.isInitialized) {
      throw new Error('Server already initialized');
    }

    try {
      this.logger.info('🚀 Initializing All-Agent Server...');

      // 加载环境变量
      loadEnvironmentVariables();

      // 初始化数据库
      await this.initializeDatabase();

      // 初始化认证服务
      this.initializeAuthService();

      // 初始化 LLM 服务
      this.initializeLLMService();

      // 初始化 Agent 管理器
      this.initializeAgentManager();

      // 设置中间件
      setupMiddleware(this.app, {
        logger: this.logger,
        authService: this.authService,
        monitor: this.monitor
      });

      // 设置路由
      this.setupRoutes();

      // 设置 WebSocket
      setupWebSocket(this.io, {
        authService: this.authService,
        agentManager: this.agentManager,
        logger: this.logger
      });

      // 启动监控
      await this.startMonitoring();

      this.isInitialized = true;
      this.logger.info('✅ Server initialization completed');

    } catch (error) {
      this.logger.error('❌ Server initialization failed:', error);
      throw error;
    }
  }

  /**
   * 启动服务器
   */
  async start() {
    if (!this.isInitialized) {
      await this.initialize();
    }

    if (this.isRunning) {
      throw new Error('Server already running');
    }

    return new Promise((resolve, reject) => {
      this.server.listen(this.options.port, (error) => {
        if (error) {
          this.logger.error('❌ Failed to start server:', error);
          reject(error);
          return;
        }

        this.isRunning = true;
        this.logger.info(`🎉 All-Agent Server Started Successfully!`);
        this.logger.info(`📡 Server running on port ${this.options.port}`);
        this.logger.info(`🌐 Access UI at: http://localhost:${this.options.port}/ui/`);
        this.logger.info(`📊 Monitor at: http://localhost:${this.options.port}/monitor`);
        this.logger.info(`📚 API Docs at: http://localhost:${this.options.port}/api-docs`);

        resolve();
      });
    });
  }

  /**
   * 停止服务器
   */
  async stop() {
    if (!this.isRunning) {
      return;
    }

    this.logger.info('🛑 Stopping All-Agent Server...');

    // 停止监控
    await this.stopMonitoring();

    // 关闭 Agent 管理器
    if (this.agentManager) {
      await this.agentManager.cleanup();
    }

    // 关闭数据库连接
    if (this.database) {
      await this.database.close();
    }

    // 关闭服务器
    return new Promise((resolve) => {
      this.server.close(() => {
        this.isRunning = false;
        this.logger.info('✅ Server stopped successfully');
        resolve();
      });
    });
  }

  /**
   * 初始化数据库
   */
  async initializeDatabase() {
    this.database = new Database();
    await this.database.initialize();
    this.logger.info('✅ Database initialized');
  }

  /**
   * 初始化认证服务
   */
  initializeAuthService() {
    this.authService = new AuthService(this.database);
    this.logger.info('✅ Auth service initialized');
  }

  /**
   * 初始化 LLM 服务
   */
  initializeLLMService() {
    this.llmService = new LLMService(this.logger);
    this.logger.info('✅ LLM service initialized');
  }

  /**
   * 初始化 Agent 管理器
   */
  initializeAgentManager() {
    this.agentManager = new AgentManager(this.llmService);
    this.logger.info('✅ Agent manager initialized');
  }

  /**
   * 设置路由
   */
  setupRoutes() {
    // 健康检查
    this.app.get('/health', async (req, res) => {
      try {
        const health = await this.getHealthStatus();
        res.json(health);
      } catch (error) {
        res.status(500).json({
          status: 'unhealthy',
          error: error.message
        });
      }
    });

    // 认证路由
    this.app.use('/auth', new AuthRoutes(this.authService).getRouter());

    // API 路由
    this.app.use('/api', new APIRoutes({
      authService: this.authService,
      agentManager: this.agentManager,
      llmService: this.llmService,
      logger: this.logger
    }).getRouter());

    // 监控路由
    this.app.use('/monitor', new MonitoringRoutes({
      monitor: this.monitor,
      alertManager: this.alertManager,
      authService: this.authService
    }).getRouter());

    // 公共路由
    this.app.use('/', new PublicRoutes().getRouter());

    this.logger.info('✅ Routes configured');
  }

  /**
   * 启动监控
   */
  async startMonitoring() {
    await this.monitor.start();
    await this.alertManager.start();
    this.logger.info('✅ Monitoring started');
  }

  /**
   * 停止监控
   */
  async stopMonitoring() {
    await this.monitor.stop();
    await this.alertManager.stop();
    this.logger.info('✅ Monitoring stopped');
  }

  /**
   * 获取健康状态
   */
  async getHealthStatus() {
    const dbHealth = await this.database.healthCheck();
    const agentStatus = this.agentManager.getAgentStatus();
    const memoryUsage = process.memoryUsage();

    return {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      version: process.env.npm_package_version || '1.0.0',
      uptime: process.uptime(),
      database: dbHealth,
      agents: agentStatus,
      memory: {
        used: Math.round(memoryUsage.heapUsed / 1024 / 1024),
        total: Math.round(memoryUsage.heapTotal / 1024 / 1024),
        external: Math.round(memoryUsage.external / 1024 / 1024)
      },
      environment: process.env.NODE_ENV || 'development'
    };
  }

  /**
   * 获取服务器统计信息
   */
  getStats() {
    return {
      isInitialized: this.isInitialized,
      isRunning: this.isRunning,
      port: this.options.port,
      uptime: process.uptime(),
      memoryUsage: process.memoryUsage(),
      agentCount: this.agentManager ? this.agentManager.getAgentCount() : 0
    };
  }
}

module.exports = ServerCore;
