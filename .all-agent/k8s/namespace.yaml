apiVersion: v1
kind: Namespace
metadata:
  name: all-agent
  labels:
    name: all-agent
    app.kubernetes.io/name: all-agent
    app.kubernetes.io/version: "1.0.0"
    app.kubernetes.io/component: namespace
---
apiVersion: v1
kind: ResourceQuota
metadata:
  name: all-agent-quota
  namespace: all-agent
spec:
  hard:
    requests.cpu: "4"
    requests.memory: 8Gi
    limits.cpu: "8"
    limits.memory: 16Gi
    persistentvolumeclaims: "10"
    services: "10"
    secrets: "10"
    configmaps: "10"
---
apiVersion: v1
kind: LimitRange
metadata:
  name: all-agent-limits
  namespace: all-agent
spec:
  limits:
  - default:
      cpu: "1"
      memory: "1Gi"
    defaultRequest:
      cpu: "100m"
      memory: "128Mi"
    type: Container
