const nodemailer = require('nodemailer');
const axios = require('axios');

/**
 * 告警管理器
 * 处理告警通知的发送和管理
 */
class AlertManager {
    constructor(options = {}) {
        this.options = {
            email: {
                enabled: options.email?.enabled || false,
                smtp: {
                    host: options.email?.smtp?.host || process.env.EMAIL_SMTP_HOST,
                    port: options.email?.smtp?.port || process.env.EMAIL_SMTP_PORT || 587,
                    secure: options.email?.smtp?.secure || false,
                    auth: {
                        user: options.email?.smtp?.user || process.env.EMAIL_USERNAME,
                        pass: options.email?.smtp?.pass || process.env.EMAIL_PASSWORD
                    }
                },
                from: options.email?.from || process.env.EMAIL_FROM || '<EMAIL>',
                to: options.email?.to || process.env.EMAIL_ALERT_TO || []
            },
            slack: {
                enabled: options.slack?.enabled || false,
                webhookUrl: options.slack?.webhookUrl || process.env.SLACK_WEBHOOK_URL,
                channel: options.slack?.channel || '#alerts',
                username: options.slack?.username || 'All-Agent Monitor'
            },
            webhook: {
                enabled: options.webhook?.enabled || false,
                url: options.webhook?.url || process.env.ALERT_WEBHOOK_URL,
                headers: options.webhook?.headers || {}
            },
            ...options
        };

        this.emailTransporter = null;
        this.alertHistory = [];
        this.maxHistorySize = 1000;

        this.initializeEmailTransporter();
    }

    /**
     * 初始化邮件传输器
     */
    initializeEmailTransporter() {
        if (!this.options.email.enabled) {
            return;
        }

        try {
            this.emailTransporter = nodemailer.createTransporter(this.options.email.smtp);
            console.log('📧 邮件告警已配置');
        } catch (error) {
            console.warn('⚠️ 邮件告警配置失败:', error.message);
        }
    }

    /**
     * 发送告警
     */
    async sendAlert(alert) {
        try {
            // 记录告警历史
            this.recordAlert(alert);

            // 并行发送到所有配置的通道
            const promises = [];

            if (this.options.email.enabled) {
                promises.push(this.sendEmailAlert(alert));
            }

            if (this.options.slack.enabled) {
                promises.push(this.sendSlackAlert(alert));
            }

            if (this.options.webhook.enabled) {
                promises.push(this.sendWebhookAlert(alert));
            }

            // 等待所有通知发送完成
            const results = await Promise.allSettled(promises);
            
            // 检查发送结果
            const failures = results.filter(r => r.status === 'rejected');
            if (failures.length > 0) {
                console.warn('部分告警通知发送失败:', failures.map(f => f.reason));
            }

            return {
                success: true,
                sent: results.length - failures.length,
                failed: failures.length
            };

        } catch (error) {
            console.error('发送告警失败:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * 发送邮件告警
     */
    async sendEmailAlert(alert) {
        if (!this.emailTransporter) {
            throw new Error('邮件传输器未配置');
        }

        const subject = this.generateEmailSubject(alert);
        const html = this.generateEmailHTML(alert);
        const text = this.generateEmailText(alert);

        const mailOptions = {
            from: this.options.email.from,
            to: Array.isArray(this.options.email.to) 
                ? this.options.email.to.join(',') 
                : this.options.email.to,
            subject,
            text,
            html
        };

        await this.emailTransporter.sendMail(mailOptions);
        console.log('📧 邮件告警已发送');
    }

    /**
     * 发送 Slack 告警
     */
    async sendSlackAlert(alert) {
        if (!this.options.slack.webhookUrl) {
            throw new Error('Slack Webhook URL 未配置');
        }

        const payload = {
            channel: this.options.slack.channel,
            username: this.options.slack.username,
            icon_emoji: this.getAlertEmoji(alert.level),
            attachments: [{
                color: this.getAlertColor(alert.level),
                title: `🚨 ${alert.type.toUpperCase()} 告警`,
                text: alert.message,
                fields: [
                    {
                        title: '级别',
                        value: alert.level,
                        short: true
                    },
                    {
                        title: '当前值',
                        value: alert.value?.toString() || 'N/A',
                        short: true
                    },
                    {
                        title: '阈值',
                        value: alert.threshold?.toString() || 'N/A',
                        short: true
                    },
                    {
                        title: '时间',
                        value: new Date(alert.timestamp).toLocaleString(),
                        short: true
                    }
                ],
                footer: 'All-Agent Monitor',
                ts: Math.floor(alert.timestamp / 1000)
            }]
        };

        await axios.post(this.options.slack.webhookUrl, payload);
        console.log('💬 Slack 告警已发送');
    }

    /**
     * 发送 Webhook 告警
     */
    async sendWebhookAlert(alert) {
        if (!this.options.webhook.url) {
            throw new Error('Webhook URL 未配置');
        }

        const payload = {
            alert,
            timestamp: Date.now(),
            source: 'all-agent-monitor'
        };

        await axios.post(this.options.webhook.url, payload, {
            headers: this.options.webhook.headers
        });

        console.log('🔗 Webhook 告警已发送');
    }

    /**
     * 生成邮件主题
     */
    generateEmailSubject(alert) {
        const levelEmoji = {
            info: 'ℹ️',
            warning: '⚠️',
            critical: '🚨'
        };

        return `${levelEmoji[alert.level] || '🔔'} All-Agent 告警: ${alert.type}`;
    }

    /**
     * 生成邮件 HTML 内容
     */
    generateEmailHTML(alert) {
        const levelColor = {
            info: '#17a2b8',
            warning: '#ffc107',
            critical: '#dc3545'
        };

        return `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>All-Agent 告警</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background-color: #f5f5f5; }
        .container { max-width: 600px; margin: 0 auto; background: white; border-radius: 8px; overflow: hidden; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { background: ${levelColor[alert.level] || '#6c757d'}; color: white; padding: 20px; text-align: center; }
        .content { padding: 30px; }
        .alert-info { background: #f8f9fa; padding: 15px; border-radius: 4px; margin: 20px 0; }
        .alert-info h3 { margin: 0 0 10px 0; color: #495057; }
        .alert-info p { margin: 5px 0; }
        .footer { background: #f8f9fa; padding: 15px; text-align: center; color: #6c757d; font-size: 12px; }
        .btn { display: inline-block; padding: 10px 20px; background: ${levelColor[alert.level] || '#6c757d'}; color: white; text-decoration: none; border-radius: 4px; margin: 10px 0; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚨 All-Agent 系统告警</h1>
            <p>检测到系统异常，请及时处理</p>
        </div>
        <div class="content">
            <div class="alert-info">
                <h3>告警详情</h3>
                <p><strong>类型:</strong> ${alert.type}</p>
                <p><strong>级别:</strong> ${alert.level}</p>
                <p><strong>消息:</strong> ${alert.message}</p>
                <p><strong>当前值:</strong> ${alert.value || 'N/A'}</p>
                <p><strong>阈值:</strong> ${alert.threshold || 'N/A'}</p>
                <p><strong>时间:</strong> ${new Date(alert.timestamp).toLocaleString()}</p>
            </div>
            <p>请登录系统查看详细信息并采取相应措施。</p>
            <a href="http://localhost:3000/ui/chat_panel.html" class="btn">查看系统</a>
        </div>
        <div class="footer">
            <p>此邮件由 All-Agent 监控系统自动发送</p>
            <p>如需帮助，请联系系统管理员</p>
        </div>
    </div>
</body>
</html>`;
    }

    /**
     * 生成邮件文本内容
     */
    generateEmailText(alert) {
        return `
All-Agent 系统告警

告警详情:
- 类型: ${alert.type}
- 级别: ${alert.level}
- 消息: ${alert.message}
- 当前值: ${alert.value || 'N/A'}
- 阈值: ${alert.threshold || 'N/A'}
- 时间: ${new Date(alert.timestamp).toLocaleString()}

请及时登录系统查看详细信息并采取相应措施。

系统地址: http://localhost:3000/ui/chat_panel.html

此邮件由 All-Agent 监控系统自动发送。
        `.trim();
    }

    /**
     * 获取告警表情符号
     */
    getAlertEmoji(level) {
        const emojis = {
            info: ':information_source:',
            warning: ':warning:',
            critical: ':rotating_light:'
        };
        return emojis[level] || ':bell:';
    }

    /**
     * 获取告警颜色
     */
    getAlertColor(level) {
        const colors = {
            info: '#17a2b8',
            warning: '#ffc107',
            critical: '#dc3545'
        };
        return colors[level] || '#6c757d';
    }

    /**
     * 记录告警历史
     */
    recordAlert(alert) {
        this.alertHistory.push({
            ...alert,
            id: this.generateAlertId(),
            recordedAt: Date.now()
        });

        // 限制历史记录大小
        if (this.alertHistory.length > this.maxHistorySize) {
            this.alertHistory = this.alertHistory.slice(-this.maxHistorySize);
        }
    }

    /**
     * 生成告警ID
     */
    generateAlertId() {
        return `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    /**
     * 获取告警历史
     */
    getAlertHistory(limit = 50) {
        return this.alertHistory
            .slice(-limit)
            .reverse(); // 最新的在前面
    }

    /**
     * 清理告警历史
     */
    clearAlertHistory(olderThan = null) {
        if (olderThan) {
            this.alertHistory = this.alertHistory.filter(
                alert => alert.recordedAt > olderThan
            );
        } else {
            this.alertHistory = [];
        }
    }

    /**
     * 测试告警通知
     */
    async testAlert() {
        const testAlert = {
            type: 'test_alert',
            level: 'info',
            message: '这是一个测试告警，用于验证通知系统是否正常工作',
            value: 100,
            threshold: 80,
            timestamp: Date.now()
        };

        console.log('🧪 发送测试告警...');
        const result = await this.sendAlert(testAlert);
        
        if (result.success) {
            console.log('✅ 测试告警发送成功');
        } else {
            console.error('❌ 测试告警发送失败:', result.error);
        }

        return result;
    }

    /**
     * 获取配置状态
     */
    getConfigStatus() {
        return {
            email: {
                enabled: this.options.email.enabled,
                configured: !!this.emailTransporter,
                recipients: Array.isArray(this.options.email.to) 
                    ? this.options.email.to.length 
                    : (this.options.email.to ? 1 : 0)
            },
            slack: {
                enabled: this.options.slack.enabled,
                configured: !!this.options.slack.webhookUrl
            },
            webhook: {
                enabled: this.options.webhook.enabled,
                configured: !!this.options.webhook.url
            }
        };
    }

    /**
     * 更新配置
     */
    updateConfig(newOptions) {
        this.options = { ...this.options, ...newOptions };
        
        // 重新初始化邮件传输器
        if (newOptions.email) {
            this.initializeEmailTransporter();
        }
    }

    /**
     * 获取统计信息
     */
    getStats() {
        const now = Date.now();
        const last24h = now - 24 * 60 * 60 * 1000;
        const lastWeek = now - 7 * 24 * 60 * 60 * 1000;

        const recent24h = this.alertHistory.filter(a => a.recordedAt > last24h);
        const recentWeek = this.alertHistory.filter(a => a.recordedAt > lastWeek);

        return {
            total: this.alertHistory.length,
            last24h: recent24h.length,
            lastWeek: recentWeek.length,
            byLevel: {
                info: this.alertHistory.filter(a => a.level === 'info').length,
                warning: this.alertHistory.filter(a => a.level === 'warning').length,
                critical: this.alertHistory.filter(a => a.level === 'critical').length
            },
            byType: this.getAlertsByType()
        };
    }

    /**
     * 按类型统计告警
     */
    getAlertsByType() {
        const typeCount = {};
        
        for (const alert of this.alertHistory) {
            typeCount[alert.type] = (typeCount[alert.type] || 0) + 1;
        }
        
        return typeCount;
    }
}

module.exports = AlertManager;
