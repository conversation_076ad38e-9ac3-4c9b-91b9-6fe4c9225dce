{"version": 3, "file": "fileSystemService.js", "sourceRoot": "", "sources": ["../../src/services/fileSystemService.ts"], "names": [], "mappings": ";;;AAAA,iCAAiC;AACjC,6BAA6B;AAE7B;;GAEG;AACH,MAAa,iBAAiB;IAG1B;QACI,IAAI,CAAC,aAAa,GAAG,MAAM,CAAC,MAAM,CAAC,mBAAmB,CAAC,2BAA2B,CAAC,CAAC;IACxF,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,eAAe,CAAC,KAAa,EAAE,UAKjC,EAAE;QASF,MAAM,EACF,cAAc,GAAG,IAAI,EACrB,SAAS,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,IAAI,CAAC,EACtE,eAAe,GAAG,CAAC,oBAAoB,EAAE,YAAY,EAAE,aAAa,CAAC,EACrE,UAAU,GAAG,EAAE,EAClB,GAAG,OAAO,CAAC;QAEZ,MAAM,OAAO,GAQR,EAAE,CAAC;QAER,IAAI;YACA,SAAS;YACT,MAAM,aAAa,GAAG,SAAS,CAAC,MAAM,GAAG,CAAC;gBACtC,CAAC,CAAC,SAAS,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG;gBACjC,CAAC,CAAC,MAAM,CAAC;YAEb,MAAM,KAAK,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,SAAS,CAC1C,aAAa,EACb,IAAI,eAAe,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,EAChC,UAAU,GAAG,CAAC,CACjB,CAAC;YAEF,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE;gBACtB,IAAI;oBACA,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;oBAC/D,MAAM,OAAO,GAAG,QAAQ,CAAC,OAAO,EAAE,CAAC;oBACnC,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;oBAElC,MAAM,OAAO,GAIR,EAAE,CAAC;oBAER,IAAI,KAAK,GAAG,CAAC,CAAC;oBAEd,QAAQ;oBACR,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,WAAW,EAAE,CAAC;oBAC1D,IAAI,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC,EAAE;wBACxC,KAAK,IAAI,EAAE,CAAC;qBACf;oBAED,OAAO;oBACP,IAAI,cAAc,EAAE;wBAChB,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE;4BAC1B,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC,EAAE;gCAClD,OAAO,CAAC,IAAI,CAAC;oCACT,IAAI,EAAE,KAAK,GAAG,CAAC;oCACf,OAAO,EAAE,IAAI,CAAC,IAAI,EAAE;oCACpB,OAAO,EAAE,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,KAAK,CAAC;iCAC7C,CAAC,CAAC;gCACH,KAAK,IAAI,CAAC,CAAC;6BACd;wBACL,CAAC,CAAC,CAAC;qBACN;oBAED,IAAI,KAAK,GAAG,CAAC,EAAE;wBACX,OAAO,CAAC,IAAI,CAAC;4BACT,IAAI,EAAE,IAAI,CAAC,MAAM;4BACjB,OAAO;4BACP,KAAK;yBACR,CAAC,CAAC;qBACN;iBACJ;gBAAC,OAAO,KAAK,EAAE;oBACZ,YAAY;iBACf;aACJ;YAED,QAAQ;YACR,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC;YAC1C,OAAO,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC;SACvC;QAAC,OAAO,KAAK,EAAE;YACZ,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,iBAAiB,KAAK,EAAE,CAAC,CAAC;YACxD,OAAO,EAAE,CAAC;SACb;IACL,CAAC;IAED;;OAEG;IACK,cAAc,CAAC,KAAe,EAAE,SAAiB;QACrD,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,SAAS,GAAG,CAAC,CAAC,CAAC;QACzC,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,EAAE,SAAS,GAAG,CAAC,CAAC,CAAC;QAClD,OAAO,KAAK,CAAC,KAAK,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC9C,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,kBAAkB,CAAC,SAAkD,EAAE,KAI3E;QAQE,MAAM,OAAO,GAIR,EAAE,CAAC;QAER,KAAK,MAAM,MAAM,IAAI,KAAK,EAAE;YACxB,IAAI;gBACA,QAAQ,SAAS,EAAE;oBACf,KAAK,QAAQ;wBACT,MAAM,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,OAAO,IAAI,EAAE,CAAC,CAAC;wBAC3D,OAAO,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,MAAM,CAAC,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;wBACrD,MAAM;oBACV,KAAK,QAAQ;wBACT,MAAM,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;wBACrC,OAAO,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,MAAM,CAAC,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;wBACrD,MAAM;oBACV,KAAK,QAAQ;wBACT,IAAI,MAAM,CAAC,MAAM,EAAE;4BACf,MAAM,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;4BACpD,OAAO,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,MAAM,CAAC,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;yBACxD;wBACD,MAAM;oBACV,KAAK,MAAM;wBACP,IAAI,MAAM,CAAC,MAAM,EAAE;4BACf,MAAM,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;4BAClD,OAAO,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,MAAM,CAAC,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;yBACxD;wBACD,MAAM;iBACb;aACJ;YAAC,OAAO,KAAK,EAAE;gBACZ,OAAO,CAAC,IAAI,CAAC;oBACT,IAAI,EAAE,MAAM,CAAC,MAAM;oBACnB,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;iBAChE,CAAC,CAAC;aACN;SACJ;QAED,MAAM,YAAY,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC;QAC3D,OAAO;YACH,OAAO,EAAE,YAAY,GAAG,CAAC;YACzB,OAAO;SACV,CAAC;IACN,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,UAAU,CAAC,QAAgB,EAAE,OAAe;QACtD,MAAM,GAAG,GAAG,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACtC,MAAM,OAAO,GAAG,IAAI,WAAW,EAAE,CAAC;QAClC,MAAM,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,SAAS,CAAC,GAAG,EAAE,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC;QAClE,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,YAAY,QAAQ,EAAE,CAAC,CAAC;IAC1D,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,UAAU,CAAC,QAAgB;QACrC,MAAM,GAAG,GAAG,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACtC,MAAM,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;QACtC,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,YAAY,QAAQ,EAAE,CAAC,CAAC;IAC1D,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,UAAU,CAAC,OAAe,EAAE,OAAe;QACrD,MAAM,MAAM,GAAG,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACxC,MAAM,MAAM,GAAG,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACxC,MAAM,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QACjD,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,YAAY,OAAO,OAAO,OAAO,EAAE,CAAC,CAAC;IACvE,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,QAAQ,CAAC,UAAkB,EAAE,UAAkB;QACzD,MAAM,SAAS,GAAG,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAC9C,MAAM,SAAS,GAAG,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAC9C,MAAM,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,MAAM,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;QACvD,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,UAAU,UAAU,OAAO,UAAU,EAAE,CAAC,CAAC;IAC3E,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,aAAa,CAAC,QAAgB,EAAE,YAAuF,EAAE,UAAe,EAAE;QAS5I,IAAI;YACA,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;YACpF,MAAM,OAAO,GAAG,QAAQ,CAAC,OAAO,EAAE,CAAC;YACnC,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YAElC,MAAM,OAAO,GAIR,EAAE,CAAC;YAER,QAAQ,YAAY,EAAE;gBAClB,KAAK,kBAAkB;oBACnB,OAAO,MAAM,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;gBACtD,KAAK,WAAW;oBACZ,OAAO,MAAM,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;gBACzD;oBACI,OAAO;wBACH,OAAO,EAAE,KAAK;wBACd,OAAO,EAAE,EAAE;wBACX,KAAK,EAAE,kBAAkB,YAAY,uBAAuB;qBAC/D,CAAC;aACT;SACJ;QAAC,OAAO,KAAK,EAAE;YACZ,OAAO;gBACH,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,EAAE;gBACX,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;aAChE,CAAC;SACL;IACL,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,eAAe,CAAC,KAAe,EAAE,OAI7C;QAQE,MAAM,OAAO,GAA8C,EAAE,CAAC;QAE9D,WAAW;QACX,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE;YAC1B,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC,UAAU,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;gBAC3D,OAAO,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;aAChD;QACL,CAAC,CAAC,CAAC;QAEH,SAAS;QACT,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;YAClB,MAAM,OAAO,GAAG,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;YAClD,MAAM,OAAO,GAAG,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;YAElD,SAAS;YACT,MAAM,UAAU,GAAG,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;YAC3E,MAAM,UAAU,GAAG,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;YAE3E,IAAI,UAAU,IAAI,CAAC,UAAU;gBAAE,OAAO,CAAC,CAAC,CAAC;YACzC,IAAI,CAAC,UAAU,IAAI,UAAU;gBAAE,OAAO,CAAC,CAAC;YAExC,OAAO,OAAO,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;QAC1C,CAAC,CAAC,CAAC;QAEH,OAAO;QACP,OAAO,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE;YAC3B,IAAI,KAAK,GAAG,OAAO,CAAC,MAAM,IAAI,GAAG,CAAC,OAAO,KAAK,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;gBAC3D,OAAO,CAAC,IAAI,CAAC;oBACT,IAAI,EAAE,GAAG,CAAC,IAAI,GAAG,CAAC;oBAClB,UAAU,EAAE,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC;oBAC3B,UAAU,EAAE,GAAG,CAAC,OAAO;iBAC1B,CAAC,CAAC;aACN;QACL,CAAC,CAAC,CAAC;QAEH,OAAO;YACH,OAAO,EAAE,OAAO,CAAC,MAAM,GAAG,CAAC;YAC3B,OAAO;SACV,CAAC;IACN,CAAC;IAED;;OAEG;IACK,iBAAiB,CAAC,UAAkB;QACxC,MAAM,KAAK,GAAG,UAAU,CAAC,KAAK,CAAC,yBAAyB,CAAC,CAAC;QAC1D,OAAO,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACjC,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,kBAAkB,CAAC,KAAe,EAAE,OAIhD;QAQE,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE;YAC1B,cAAc;YACd,MAAM,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,gCAAgC,CAAC,CAAC;YACnE,IAAI,aAAa,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;gBACtC,MAAM,MAAM,GAAG,aAAa,CAAC,CAAC,CAAC,CAAC;gBAChC,IAAI,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;oBACjC,MAAM,WAAW,GAAG,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;wBAC9C,MAAM,OAAO,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC;wBAC7B,IAAI,OAAO,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;4BACnC,OAAO,GAAG,OAAO,OAAO,CAAC;yBAC5B;wBACD,OAAO,OAAO,CAAC;oBACnB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;oBAEd,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CACxB,IAAI,MAAM,GAAG,EACb,IAAI,WAAW,QAAQ,CAC1B,CAAC;oBAEF,OAAO,CAAC,IAAI,CAAC;wBACT,IAAI,EAAE,KAAK,GAAG,CAAC;wBACf,UAAU,EAAE,IAAI;wBAChB,UAAU,EAAE,OAAO;qBACtB,CAAC,CAAC;iBACN;aACJ;QACL,CAAC,CAAC,CAAC;QAEH,OAAO;YACH,OAAO,EAAE,OAAO,CAAC,MAAM,GAAG,CAAC;YAC3B,OAAO;SACV,CAAC;IACN,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,oBAAoB,CAAC,YAAoB,EAAE,QAAgB,EAAE,UAAe,EAAE;QAKhF,MAAM,SAAS,GAA8D;YACzE,iBAAiB,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,sBAAsB,CAAC,IAAI,EAAE,IAAI,CAAC;YAC1E,cAAc,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,mBAAmB,CAAC,IAAI,EAAE,IAAI,CAAC;YACpE,WAAW,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,gBAAgB,CAAC,IAAI,EAAE,IAAI,CAAC;YAC9D,aAAa,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,kBAAkB,CAAC,IAAI,EAAE,IAAI,CAAC;SACrE,CAAC;QAEF,MAAM,SAAS,GAAG,SAAS,CAAC,YAAY,CAAC,CAAC;QAC1C,IAAI,CAAC,SAAS,EAAE;YACZ,OAAO;gBACH,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,EAAE;gBACX,QAAQ,EAAE,EAAE;aACf,CAAC;SACL;QAED,MAAM,OAAO,GAAG,SAAS,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;QAC7C,MAAM,eAAe,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC,CAAC;QAC/D,MAAM,QAAQ,GAAG,eAAe;YAC5B,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,MAAM,EAAE,QAAQ,CAAC;YACjD,CAAC,CAAC,QAAQ,CAAC;QAEf,OAAO;YACH,OAAO,EAAE,IAAI;YACb,OAAO;YACP,QAAQ;SACX,CAAC;IACN,CAAC;IAEO,sBAAsB,CAAC,IAAY,EAAE,OAAY;QACrD,MAAM,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC,CAAC,QAAQ;QAC7D,OAAO;;YAEH,aAAa;;;;QAIjB,aAAa,cAAc,aAAa;;;YAGpC,aAAa;;;;;;iBAMR,aAAa;CAC7B,CAAC;IACE,CAAC;IAEO,mBAAmB,CAAC,IAAY,EAAE,OAAY;QAClD,MAAM,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC;QAClD,OAAO,SAAS,WAAW;;;;;;;;;;;;;;;;;;;iBAmBlB,WAAW;CAC3B,CAAC;IACE,CAAC;IAEO,gBAAgB,CAAC,IAAY,EAAE,OAAY;QAC/C,MAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC;QAC/C,OAAO;;YAEH,QAAQ;;;;;;;;;;;;;;;;;;;CAmBnB,CAAC;IACE,CAAC;IAEO,kBAAkB,CAAC,IAAY,EAAE,OAAY;QACjD,IAAI,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE;YAC3B,OAAO;;;;;;;;;;;;;;;CAelB,CAAC;SACO;QAED,OAAO,6BAA6B,IAAI;;;;CAI/C,CAAC;IACE,CAAC;IAED;;OAEG;IACH,OAAO;QACH,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC;IACjC,CAAC;CACJ;AApgBD,8CAogBC"}