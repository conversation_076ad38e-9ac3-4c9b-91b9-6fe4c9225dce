const BaseAgent = require('./BaseAgent');

/**
 * 规划 Agent
 * 负责项目规划、任务分解和执行路线制定
 */
class PlannerAgent extends BaseAgent {
  constructor(config) {
    super(config);
    this.planTemplates = new Map();
    this.planHistory = [];
    this.methodologies = ['agile', 'waterfall', 'lean', 'design_thinking'];
  }

  /**
   * 初始化规划 Agent
   */
  async onInitialize() {
    this.log('info', '规划 Agent 初始化中...');

    // 加载规划模板
    this.loadPlanTemplates();

    this.log('info', '规划 Agent 初始化完成');
  }

  /**
   * 加载规划模板
   */
  loadPlanTemplates() {
    // Web 应用开发模板
    this.planTemplates.set('web_app', {
      name: 'Web 应用开发',
      phases: [
        {
          name: '需求分析',
          duration: '1-2周',
          tasks: ['用户需求调研', '功能规格定义', '技术选型', '架构设计'],
          deliverables: ['需求文档', '技术方案', '架构图']
        },
        {
          name: '设计阶段',
          duration: '1-2周',
          tasks: ['UI/UX设计', '数据库设计', 'API设计', '原型制作'],
          deliverables: ['设计稿', '数据库模型', 'API文档', '交互原型']
        },
        {
          name: '开发阶段',
          duration: '4-8周',
          tasks: ['前端开发', '后端开发', '数据库实现', '集成测试'],
          deliverables: ['前端代码', '后端代码', '数据库脚本', '测试报告']
        },
        {
          name: '测试部署',
          duration: '1-2周',
          tasks: ['功能测试', '性能测试', '安全测试', '生产部署'],
          deliverables: ['测试报告', '部署文档', '用户手册']
        }
      ]
    });

    // 移动应用开发模板
    this.planTemplates.set('mobile_app', {
      name: '移动应用开发',
      phases: [
        {
          name: '产品规划',
          duration: '1-2周',
          tasks: ['市场调研', '竞品分析', '功能定义', '平台选择'],
          deliverables: ['产品需求文档', '竞品分析报告', '技术选型报告']
        },
        {
          name: '设计开发',
          duration: '3-6周',
          tasks: ['UI设计', '原生开发', '跨平台开发', '功能实现'],
          deliverables: ['设计规范', '应用代码', '功能模块']
        },
        {
          name: '测试发布',
          duration: '2-3周',
          tasks: ['内测', '公测', '应用商店发布', '用户反馈'],
          deliverables: ['测试报告', '发布版本', '用户手册']
        }
      ]
    });

    // API 开发模板
    this.planTemplates.set('api', {
      name: 'API 开发',
      phases: [
        {
          name: '接口设计',
          duration: '1周',
          tasks: ['API规范定义', '数据模型设计', '认证方案', '文档编写'],
          deliverables: ['API文档', '数据模型', '认证设计']
        },
        {
          name: '开发实现',
          duration: '2-4周',
          tasks: ['接口开发', '数据库实现', '业务逻辑', '错误处理'],
          deliverables: ['API代码', '数据库脚本', '业务模块']
        },
        {
          name: '测试部署',
          duration: '1周',
          tasks: ['单元测试', '集成测试', '性能测试', '部署上线'],
          deliverables: ['测试套件', '部署脚本', '监控配置']
        }
      ]
    });
  }

  /**
   * 处理消息 - 使用 LLM 服务
   */
  async onProcessMessage(message, context) {
    try {
      console.log(`[规划 Agent] 收到消息: ${message}`);

      // 如果有 LLM 服务，使用它生成响应
      if (this.llmService) {
        const prompt = `你是一个专业的项目规划 Agent。用户问题：${message}

请以规划 Agent 的身份回答，重点关注：
- 项目计划制定
- 任务分解和优先级
- 时间估算和里程碑
- 风险评估和缓解策略
- 资源分配和进度管理

请用中文回答，提供具体可行的建议。`;

        const response = await this.llmService.generateResponse(prompt, {
          agentType: 'planner',
          maxTokens: 500,
          temperature: 0.7
        });

        return response;
      }

      // 回退到简单响应
      return `📋 **规划 Agent 响应**

您好！我是规划 Agent，我收到了您的消息："${message}"

我可以帮您：
- 📋 项目规划和计划制定
- 🔨 任务分解和优先级排序
- ⏱️ 时间估算和里程碑设定
- ⚠️ 风险评估和缓解策略
- 📊 进度跟踪和项目管理

目前我正在简化模式下运行，可以为您提供基础的规划服务。

请告诉我您的项目需求，我会为您制定合适的计划！`;
    } catch (error) {
      console.error(`[规划 Agent] 错误:`, error);
      return `抱歉，我遇到了一些技术问题。请稍后再试。`;
    }
  }

  /**
   * 处理计划制定请求
   */
  async handlePlanCreationRequest(message, context) {
    const projectType = this.detectProjectType(message, context);
    const complexity = this.assessComplexity(message, context);
    const methodology = this.selectMethodology(message, context);

    try {
      const plan = await this.createProjectPlan(projectType, complexity, methodology, context);
      this.planHistory.push({
        plan,
        context,
        timestamp: new Date().toISOString()
      });

      return this.formatPlanResponse(plan);
    } catch (error) {
      this.log('error', '计划制定失败', { error: error.message });
      return `计划制定失败：${error.message}`;
    }
  }

  /**
   * 处理任务分解请求
   */
  async handleTaskBreakdownRequest(message, context) {
    const feature = this.extractFeatureFromMessage(message);
    const breakdown = this.breakdownFeature(feature, context);

    let response = `📋 **任务分解：${feature}**\n\n`;

    breakdown.forEach((task, index) => {
      response += `${index + 1}. **${task.name}**\n`;
      response += `   - 预估时间：${task.estimate}\n`;
      response += `   - 优先级：${task.priority}\n`;
      response += `   - 依赖：${task.dependencies.join(', ') || '无'}\n\n`;
    });

    return response;
  }

  /**
   * 处理时间估算请求
   */
  async handleTimeEstimationRequest(message, context) {
    const scope = this.extractScopeFromMessage(message);
    const estimation = this.estimateTime(scope, context);

    return (
      `⏱️ **时间估算**\n\n` +
      `**项目范围：** ${scope}\n` +
      `**预估时间：** ${estimation.total}\n` +
      `**分解：**\n` +
      estimation.breakdown.map((item) => `- ${item.phase}: ${item.time}`).join('\n') +
      `\n\n**注意：** 估算基于标准开发流程，实际时间可能因团队经验和项目复杂度而有所差异。`
    );
  }

  /**
   * 处理风险评估请求
   */
  async handleRiskAssessmentRequest(message, context) {
    const risks = this.assessRisks(message, context);

    let response = `⚠️ **风险评估报告**\n\n`;

    risks.forEach((risk) => {
      response += `**${risk.name}** (${risk.level})\n`;
      response += `- 描述：${risk.description}\n`;
      response += `- 影响：${risk.impact}\n`;
      response += `- 缓解措施：${risk.mitigation}\n\n`;
    });

    return response;
  }

  /**
   * 处理一般规划请求
   */
  async handleGeneralPlanningRequest(message, context) {
    return `我是规划 Agent，专门负责项目规划和任务管理。我可以帮您：

📋 **项目规划** - 制定完整的项目开发计划
🔨 **任务分解** - 将大功能分解为可执行的小任务
⏱️ **时间估算** - 评估项目和任务的开发时间
⚠️ **风险评估** - 识别项目风险并提供缓解方案
📊 **进度跟踪** - 监控项目进度和里程碑

请告诉我您的项目类型和需求，例如：
- "为Web应用制定开发计划"
- "分解用户登录功能"
- "估算电商网站开发时间"`;
  }

  /**
   * 执行任务
   */
  async onExecuteTask(action, input, options) {
    this.log('info', '执行规划任务', { action, input: typeof input });

    switch (action) {
      case 'create_project_plan':
        return this.createProjectPlan(input.projectType, input.complexity, input.methodology, input.context);
      case 'breakdown_feature':
        return this.breakdownFeature(input.feature, input.context);
      case 'estimate_time':
        return this.estimateTime(input.scope, input.context);
      case 'assess_risks':
        return this.assessRisks(input.description, input.context);
      default:
        throw new Error(`不支持的规划任务: ${action}`);
    }
  }

  /**
   * 创建项目计划
   */
  async createProjectPlan(projectType, complexity, methodology, context) {
    const template = this.planTemplates.get(projectType) || this.planTemplates.get('web_app');

    const plan = {
      id: this.generatePlanId(),
      name: `${template.name}计划`,
      projectType,
      complexity,
      methodology,
      createdAt: new Date().toISOString(),
      phases: this.adaptPhasesToComplexity(template.phases, complexity),
      totalDuration: this.calculateTotalDuration(template.phases, complexity),
      resources: this.estimateResources(projectType, complexity),
      milestones: this.generateMilestones(template.phases)
    };

    return plan;
  }

  /**
   * 分解功能
   */
  breakdownFeature(feature, context) {
    const tasks = [];

    // 基于功能类型生成任务分解
    if (feature.includes('登录') || feature.includes('login')) {
      tasks.push(
        { name: '设计登录界面', estimate: '1天', priority: '高', dependencies: [] },
        { name: '实现前端表单', estimate: '1天', priority: '高', dependencies: ['设计登录界面'] },
        { name: '后端认证接口', estimate: '2天', priority: '高', dependencies: [] },
        { name: '数据库用户表', estimate: '0.5天', priority: '高', dependencies: [] },
        { name: '集成测试', estimate: '1天', priority: '中', dependencies: ['实现前端表单', '后端认证接口'] }
      );
    } else if (feature.includes('支付') || feature.includes('payment')) {
      tasks.push(
        { name: '支付接口调研', estimate: '1天', priority: '高', dependencies: [] },
        { name: '支付页面设计', estimate: '2天', priority: '高', dependencies: [] },
        { name: '支付流程实现', estimate: '3天', priority: '高', dependencies: ['支付接口调研', '支付页面设计'] },
        { name: '安全测试', estimate: '2天', priority: '高', dependencies: ['支付流程实现'] }
      );
    } else {
      // 通用功能分解
      tasks.push(
        { name: '需求分析', estimate: '0.5天', priority: '高', dependencies: [] },
        { name: '技术设计', estimate: '1天', priority: '高', dependencies: ['需求分析'] },
        { name: '功能实现', estimate: '2-3天', priority: '高', dependencies: ['技术设计'] },
        { name: '测试验证', estimate: '1天', priority: '中', dependencies: ['功能实现'] }
      );
    }

    return tasks;
  }

  /**
   * 估算时间
   */
  estimateTime(scope, context) {
    const baseEstimates = {
      simple: { min: 2, max: 4 },
      medium: { min: 4, max: 8 },
      complex: { min: 8, max: 16 }
    };

    const complexity = this.assessComplexity(scope, context);
    const estimate = baseEstimates[complexity] || baseEstimates['medium'];

    return {
      total: `${estimate.min}-${estimate.max}周`,
      breakdown: [
        { phase: '需求分析', time: `${Math.ceil(estimate.min * 0.2)}周` },
        { phase: '设计开发', time: `${Math.ceil(estimate.min * 0.6)}周` },
        { phase: '测试部署', time: `${Math.ceil(estimate.min * 0.2)}周` }
      ]
    };
  }

  /**
   * 评估风险
   */
  assessRisks(description, context) {
    const risks = [
      {
        name: '技术风险',
        level: '中',
        description: '新技术学习成本和技术难点',
        impact: '可能导致开发延期',
        mitigation: '提前技术调研，制定备选方案'
      },
      {
        name: '需求变更',
        level: '高',
        description: '项目过程中需求可能发生变化',
        impact: '影响进度和成本',
        mitigation: '建立需求变更流程，预留缓冲时间'
      },
      {
        name: '资源风险',
        level: '中',
        description: '人员不足或关键人员离职',
        impact: '项目进度受阻',
        mitigation: '交叉培训，文档化知识'
      }
    ];

    return risks;
  }

  /**
   * 检测项目类型
   */
  detectProjectType(message, context) {
    const lowerMessage = message.toLowerCase();

    if (lowerMessage.includes('web') || lowerMessage.includes('网站')) {
      return 'web_app';
    } else if (lowerMessage.includes('mobile') || lowerMessage.includes('移动') || lowerMessage.includes('app')) {
      return 'mobile_app';
    } else if (lowerMessage.includes('api') || lowerMessage.includes('接口')) {
      return 'api';
    } else {
      return 'web_app'; // 默认
    }
  }

  /**
   * 评估复杂度
   */
  assessComplexity(description, context) {
    const lowerDesc = description.toLowerCase();

    if (lowerDesc.includes('简单') || lowerDesc.includes('basic') || lowerDesc.includes('simple')) {
      return 'simple';
    } else if (lowerDesc.includes('复杂') || lowerDesc.includes('complex') || lowerDesc.includes('enterprise')) {
      return 'complex';
    } else {
      return 'medium';
    }
  }

  /**
   * 选择方法论
   */
  selectMethodology(message, context) {
    const lowerMessage = message.toLowerCase();

    if (lowerMessage.includes('agile') || lowerMessage.includes('敏捷')) {
      return 'agile';
    } else if (lowerMessage.includes('waterfall') || lowerMessage.includes('瀑布')) {
      return 'waterfall';
    } else {
      return 'agile'; // 默认敏捷
    }
  }

  /**
   * 格式化计划响应
   */
  formatPlanResponse(plan) {
    let response = `📋 **${plan.name}**\n\n`;
    response += `**项目信息：**\n`;
    response += `- 类型：${plan.projectType}\n`;
    response += `- 复杂度：${plan.complexity}\n`;
    response += `- 方法论：${plan.methodology}\n`;
    response += `- 总时长：${plan.totalDuration}\n\n`;

    response += `**开发阶段：**\n`;
    plan.phases.forEach((phase, index) => {
      response += `${index + 1}. **${phase.name}** (${phase.duration})\n`;
      response += `   任务：${phase.tasks.join('、')}\n`;
      response += `   交付物：${phase.deliverables.join('、')}\n\n`;
    });

    response += `**里程碑：**\n`;
    plan.milestones.forEach((milestone) => {
      response += `- ${milestone.name}：${milestone.description}\n`;
    });

    return response;
  }

  /**
   * 辅助方法
   */
  generatePlanId() {
    return `plan_${Date.now()}_${Math.random().toString(36).substr(2, 6)}`;
  }

  adaptPhasesToComplexity(phases, complexity) {
    // 根据复杂度调整阶段
    return phases.map((phase) => ({
      ...phase,
      duration: this.adjustDuration(phase.duration, complexity)
    }));
  }

  adjustDuration(duration, complexity) {
    const multipliers = { simple: 0.7, medium: 1.0, complex: 1.5 };
    const multiplier = multipliers[complexity] || 1.0;

    // 简单的时间调整逻辑
    return duration.replace(/(\d+)/g, (match) => {
      return Math.ceil(parseInt(match) * multiplier).toString();
    });
  }

  calculateTotalDuration(phases, complexity) {
    // 简化的总时长计算
    const baseDuration = phases.length * 2; // 假设每阶段平均2周
    const multipliers = { simple: 0.7, medium: 1.0, complex: 1.5 };
    const total = Math.ceil(baseDuration * (multipliers[complexity] || 1.0));
    return `${total}周`;
  }

  estimateResources(projectType, complexity) {
    const baseResources = {
      simple: { developers: 1, designers: 0.5, testers: 0.5 },
      medium: { developers: 2, designers: 1, testers: 1 },
      complex: { developers: 3, designers: 1.5, testers: 1.5 }
    };

    return baseResources[complexity] || baseResources['medium'];
  }

  generateMilestones(phases) {
    return phases.map((phase, index) => ({
      name: `里程碑 ${index + 1}`,
      description: `${phase.name}完成`,
      phase: phase.name
    }));
  }

  extractFeatureFromMessage(message) {
    // 简单的特征提取
    const features = ['登录', '支付', '注册', '搜索', '购物车'];
    for (const feature of features) {
      if (message.includes(feature)) {
        return feature;
      }
    }
    return '通用功能';
  }

  extractScopeFromMessage(message) {
    // 提取项目范围
    return message.replace(/估算|时间|开发/g, '').trim() || '项目开发';
  }
}

module.exports = PlannerAgent;
