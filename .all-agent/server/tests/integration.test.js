const TestSetup = require('./setup');
const AllAgentServer = require('../app');
const axios = require('axios');

/**
 * 集成测试套件
 */
class IntegrationTests {
    constructor() {
        this.setup = new TestSetup();
        this.server = null;
        this.baseURL = 'http://localhost:3001'; // 使用不同端口避免冲突
        this.authToken = null;
    }

    async beforeAll() {
        await this.setup.setupTestEnvironment();
        
        // 设置测试端口
        process.env.PORT = '3001';
        
        // 创建并启动服务器
        this.server = new AllAgentServer();
        
        // 启动服务器但不监听（避免端口冲突）
        await this.server.database.initialize();
        this.server.cache.setConfig({ defaultTTL: 3600, maxMemoryItems: 1000 });
        this.server.authService = new (require('../auth/AuthService'))(this.server.database);
        this.server.projectAnalyzer = new (require('../core/ProjectAnalyzer'))(this.server.cache);
        await this.server.agentManager.initialize();
        await this.server.llmService.initialize();
        await this.server.codeGenerator.initialize();
        
        // 启动HTTP服务器
        await new Promise((resolve) => {
            this.server.server.listen(3001, resolve);
        });

        console.log('✅ 集成测试服务器启动完成');
    }

    async afterAll() {
        if (this.server) {
            await this.server.stop();
        }
        await this.setup.cleanupTestEnvironment();
        console.log('✅ 集成测试环境清理完成');
    }

    /**
     * 健康检查测试
     */
    async testHealthCheck() {
        const response = await axios.get(`${this.baseURL}/health`);
        
        this.setup.assert.equal(response.status, 200, '健康检查应该返回200');
        this.setup.assert.equal(response.data.status, 'healthy', '状态应该为healthy');
        this.setup.assert.truthy(response.data.database, '应该包含数据库状态');
        this.setup.assert.truthy(response.data.cache, '应该包含缓存状态');
    }

    /**
     * 用户注册集成测试
     */
    async testUserRegistrationIntegration() {
        const userData = {
            username: 'integrationuser',
            email: '<EMAIL>',
            password: 'Integration123'
        };

        const response = await axios.post(`${this.baseURL}/auth/register`, userData);
        
        this.setup.assert.equal(response.status, 200, '注册应该成功');
        this.setup.assert.truthy(response.data.success, '响应应该表示成功');
        this.setup.assert.truthy(response.data.data, '应该返回用户数据');
        this.setup.assert.equal(response.data.data.email, userData.email, '邮箱应该匹配');
    }

    /**
     * 用户登录集成测试
     */
    async testUserLoginIntegration() {
        // 先注册用户
        const userData = {
            username: 'loginuser',
            email: '<EMAIL>',
            password: 'Login123456'
        };

        await axios.post(`${this.baseURL}/auth/register`, userData);

        // 登录
        const loginData = {
            username: userData.email,
            password: userData.password
        };

        const response = await axios.post(`${this.baseURL}/auth/login`, loginData);
        
        this.setup.assert.equal(response.status, 200, '登录应该成功');
        this.setup.assert.truthy(response.data.success, '响应应该表示成功');
        this.setup.assert.truthy(response.data.data.token, '应该返回JWT令牌');
        this.setup.assert.truthy(response.data.data.user, '应该返回用户信息');

        // 保存令牌用于后续测试
        this.authToken = response.data.data.token;
    }

    /**
     * 认证保护的API测试
     */
    async testProtectedAPIAccess() {
        // 没有令牌的请求应该失败
        try {
            await axios.post(`${this.baseURL}/api/analyze`, {
                projectPath: '.'
            });
            throw new Error('应该抛出401错误');
        } catch (error) {
            this.setup.assert.equal(error.response.status, 401, '没有令牌应该返回401');
        }

        // 无效令牌的请求应该失败
        try {
            await axios.post(`${this.baseURL}/api/analyze`, {
                projectPath: '.'
            }, {
                headers: {
                    'Authorization': 'Bearer invalid-token'
                }
            });
            throw new Error('应该抛出403错误');
        } catch (error) {
            this.setup.assert.equal(error.response.status, 403, '无效令牌应该返回403');
        }

        // 确保有有效令牌
        if (!this.authToken) {
            await this.testUserLoginIntegration();
        }

        // 有效令牌的请求应该成功
        const response = await axios.post(`${this.baseURL}/api/analyze`, {
            projectPath: '.'
        }, {
            headers: {
                'Authorization': `Bearer ${this.authToken}`
            }
        });

        this.setup.assert.equal(response.status, 200, '有效令牌应该允许访问');
        this.setup.assert.truthy(response.data.success, '分析应该成功');
    }

    /**
     * 项目分析API集成测试
     */
    async testProjectAnalysisIntegration() {
        // 确保有有效令牌
        if (!this.authToken) {
            await this.testUserLoginIntegration();
        }

        // 创建测试项目
        const projectPath = await this.setup.createTestProject();

        try {
            const response = await axios.post(`${this.baseURL}/api/analyze`, {
                projectPath: projectPath,
                options: {
                    maxDepth: 3,
                    includeHidden: false
                }
            }, {
                headers: {
                    'Authorization': `Bearer ${this.authToken}`
                }
            });

            this.setup.assert.equal(response.status, 200, '项目分析应该成功');
            this.setup.assert.truthy(response.data.success, '响应应该表示成功');
            this.setup.assert.truthy(response.data.data, '应该返回分析数据');
            this.setup.assert.truthy(response.data.data.structure, '应该包含项目结构');
            this.setup.assert.truthy(response.data.data.techStack, '应该包含技术栈信息');
            this.setup.assert.truthy(response.data.data.statistics, '应该包含统计信息');

        } finally {
            await this.setup.cleanupTestProject();
        }
    }

    /**
     * Agent状态API测试
     */
    async testAgentStatusAPI() {
        const response = await axios.get(`${this.baseURL}/api/agents`);
        
        this.setup.assert.equal(response.status, 200, 'Agent状态查询应该成功');
        this.setup.assert.truthy(response.data.success, '响应应该表示成功');
        this.setup.assert.truthy(Array.isArray(response.data.data), '应该返回Agent数组');
    }

    /**
     * 任务提交API集成测试
     */
    async testTaskSubmissionIntegration() {
        // 确保有有效令牌
        if (!this.authToken) {
            await this.testUserLoginIntegration();
        }

        const taskData = {
            agentType: 'analyzer',
            action: 'analyze_code',
            input: {
                code: 'function hello() { return "Hello World"; }',
                language: 'javascript'
            }
        };

        const response = await axios.post(`${this.baseURL}/api/tasks`, taskData, {
            headers: {
                'Authorization': `Bearer ${this.authToken}`
            }
        });

        this.setup.assert.equal(response.status, 200, '任务提交应该成功');
        this.setup.assert.truthy(response.data.success, '响应应该表示成功');
        this.setup.assert.truthy(response.data.taskId, '应该返回任务ID');
    }

    /**
     * 用户信息API测试
     */
    async testUserInfoAPI() {
        // 确保有有效令牌
        if (!this.authToken) {
            await this.testUserLoginIntegration();
        }

        const response = await axios.get(`${this.baseURL}/auth/me`, {
            headers: {
                'Authorization': `Bearer ${this.authToken}`
            }
        });

        this.setup.assert.equal(response.status, 200, '获取用户信息应该成功');
        this.setup.assert.truthy(response.data.success, '响应应该表示成功');
        this.setup.assert.truthy(response.data.data, '应该返回用户数据');
        this.setup.assert.equal(response.data.data.email, '<EMAIL>', '邮箱应该匹配');
    }

    /**
     * 登出API测试
     */
    async testLogoutAPI() {
        // 确保有有效令牌
        if (!this.authToken) {
            await this.testUserLoginIntegration();
        }

        const response = await axios.post(`${this.baseURL}/auth/logout`, {}, {
            headers: {
                'Authorization': `Bearer ${this.authToken}`
            }
        });

        this.setup.assert.equal(response.status, 200, '登出应该成功');
        this.setup.assert.truthy(response.data.success, '响应应该表示成功');

        // 登出后令牌应该无效
        try {
            await axios.get(`${this.baseURL}/auth/me`, {
                headers: {
                    'Authorization': `Bearer ${this.authToken}`
                }
            });
            throw new Error('应该抛出403错误');
        } catch (error) {
            this.setup.assert.equal(error.response.status, 403, '登出后令牌应该无效');
        }

        // 清除令牌
        this.authToken = null;
    }

    /**
     * 错误处理测试
     */
    async testErrorHandling() {
        // 测试404错误
        try {
            await axios.get(`${this.baseURL}/nonexistent-endpoint`);
            throw new Error('应该抛出404错误');
        } catch (error) {
            this.setup.assert.equal(error.response.status, 404, '不存在的端点应该返回404');
        }

        // 测试无效JSON
        try {
            await axios.post(`${this.baseURL}/auth/register`, 'invalid json', {
                headers: {
                    'Content-Type': 'application/json'
                }
            });
            throw new Error('应该抛出400错误');
        } catch (error) {
            this.setup.assert.equal(error.response.status, 400, '无效JSON应该返回400');
        }
    }

    /**
     * 速率限制测试
     */
    async testRateLimit() {
        // 快速发送多个注册请求
        const requests = [];
        for (let i = 0; i < 10; i++) {
            requests.push(
                axios.post(`${this.baseURL}/auth/register`, {
                    username: `ratetest${i}`,
                    email: `ratetest${i}@example.com`,
                    password: 'RateTest123'
                }).catch(error => error.response)
            );
        }

        const responses = await Promise.all(requests);
        
        // 应该有一些请求被速率限制
        const rateLimitedResponses = responses.filter(r => r.status === 429);
        this.setup.assert.truthy(
            rateLimitedResponses.length > 0,
            '应该有请求被速率限制'
        );
    }

    /**
     * 运行所有集成测试
     */
    async runAllTests() {
        const tests = {
            '健康检查': () => this.testHealthCheck(),
            '用户注册集成': () => this.testUserRegistrationIntegration(),
            '用户登录集成': () => this.testUserLoginIntegration(),
            '认证保护API': () => this.testProtectedAPIAccess(),
            '项目分析集成': () => this.testProjectAnalysisIntegration(),
            'Agent状态API': () => this.testAgentStatusAPI(),
            '任务提交集成': () => this.testTaskSubmissionIntegration(),
            '用户信息API': () => this.testUserInfoAPI(),
            '登出API': () => this.testLogoutAPI(),
            '错误处理': () => this.testErrorHandling(),
            '速率限制': () => this.testRateLimit()
        };

        await this.beforeAll();
        
        try {
            return await this.setup.runTestSuite('集成测试', tests);
        } finally {
            await this.afterAll();
        }
    }
}

// 如果直接运行此文件，执行测试
if (require.main === module) {
    const tests = new IntegrationTests();
    tests.runAllTests().then(results => {
        process.exit(results.failed > 0 ? 1 : 0);
    }).catch(error => {
        console.error('集成测试运行失败:', error);
        process.exit(1);
    });
}

module.exports = IntegrationTests;
