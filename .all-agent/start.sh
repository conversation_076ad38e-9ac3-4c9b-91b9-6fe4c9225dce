#!/bin/bash

# All-Agent 启动脚本
# 用于启动 All-Agent 服务器和相关服务

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 配置
SERVER_DIR="server"
LOG_DIR="logs"
PID_FILE="$LOG_DIR/all-agent.pid"
LOG_FILE="$LOG_DIR/startup.log"

# 函数定义
print_banner() {
    echo -e "${CYAN}"
    echo "  ╔═══════════════════════════════════════╗"
    echo "  ║            🤖 All-Agent               ║"
    echo "  ║     AI 项目构建与执行系统              ║"
    echo "  ╚═══════════════════════════════════════╝"
    echo -e "${NC}"
}

print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 检查依赖
check_dependencies() {
    print_step "检查系统依赖..."
    
    # 检查 Node.js
    if ! command -v node &> /dev/null; then
        print_error "Node.js 未安装，请先安装 Node.js (版本 >= 16.0.0)"
        exit 1
    fi
    
    NODE_VERSION=$(node --version | cut -d'v' -f2)
    print_status "Node.js 版本: $NODE_VERSION"
    
    # 检查 npm
    if ! command -v npm &> /dev/null; then
        print_error "npm 未安装，请先安装 npm"
        exit 1
    fi
    
    NPM_VERSION=$(npm --version)
    print_status "npm 版本: $NPM_VERSION"
}

# 创建必要目录
create_directories() {
    print_step "创建必要目录..."
    
    mkdir -p "$LOG_DIR"
    mkdir -p "data"
    mkdir -p "uploads"
    mkdir -p "backups"
    
    print_status "目录创建完成"
}

# 安装依赖
install_dependencies() {
    print_step "安装服务器依赖..."
    
    cd "$SERVER_DIR"
    
    if [ ! -f "package.json" ]; then
        print_error "package.json 文件不存在"
        exit 1
    fi
    
    # 检查是否需要安装依赖
    if [ ! -d "node_modules" ] || [ "package.json" -nt "node_modules" ]; then
        print_status "正在安装 npm 依赖..."
        npm install
        print_status "依赖安装完成"
    else
        print_status "依赖已是最新版本"
    fi
    
    cd ..
}

# 检查配置文件
check_config() {
    print_step "检查配置文件..."
    
    if [ ! -f "$SERVER_DIR/.env" ]; then
        if [ -f "$SERVER_DIR/.env.example" ]; then
            print_warning ".env 文件不存在，正在从 .env.example 创建..."
            cp "$SERVER_DIR/.env.example" "$SERVER_DIR/.env"
            print_status "请编辑 $SERVER_DIR/.env 文件配置您的环境变量"
        else
            print_warning "配置文件不存在，将使用默认配置"
        fi
    else
        print_status "配置文件检查完成"
    fi
}

# 检查端口
check_port() {
    local port=${1:-3000}
    
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        print_warning "端口 $port 已被占用"
        
        # 尝试找到占用进程
        local pid=$(lsof -ti:$port)
        if [ ! -z "$pid" ]; then
            local process=$(ps -p $pid -o comm= 2>/dev/null || echo "unknown")
            print_warning "占用进程: $process (PID: $pid)"
            
            read -p "是否要终止占用进程? (y/N): " -n 1 -r
            echo
            if [[ $REPLY =~ ^[Yy]$ ]]; then
                kill -9 $pid
                print_status "进程已终止"
            else
                print_error "无法启动服务器，端口被占用"
                exit 1
            fi
        fi
    fi
}

# 启动服务器
start_server() {
    print_step "启动 All-Agent 服务器..."
    
    cd "$SERVER_DIR"
    
    # 检查端口
    check_port 3000
    
    # 启动服务器
    if [ "$1" = "dev" ]; then
        print_status "以开发模式启动..."
        npm run dev
    else
        print_status "以生产模式启动..."
        
        # 后台启动
        nohup npm start > "../$LOG_FILE" 2>&1 &
        local server_pid=$!
        
        # 保存 PID
        echo $server_pid > "../$PID_FILE"
        
        # 等待服务器启动
        sleep 3
        
        # 检查服务器是否启动成功
        if kill -0 $server_pid 2>/dev/null; then
            print_status "服务器启动成功 (PID: $server_pid)"
            print_status "日志文件: $LOG_FILE"
            
            # 显示访问信息
            echo
            echo -e "${GREEN}🎉 All-Agent 服务器已启动！${NC}"
            echo
            echo -e "${CYAN}访问地址:${NC}"
            echo -e "  📡 HTTP 服务器: ${YELLOW}http://localhost:3000${NC}"
            echo -e "  🔌 WebSocket: ${YELLOW}ws://localhost:3000${NC}"
            echo -e "  🎨 聊天面板: ${YELLOW}http://localhost:3000/ui/chat_panel.html${NC}"
            echo -e "  📊 Agent 追踪: ${YELLOW}http://localhost:3000/ui/agent_trace.html${NC}"
            echo -e "  🗺️ 结构视图: ${YELLOW}http://localhost:3000/ui/structure_view.html${NC}"
            echo
            echo -e "${CYAN}管理命令:${NC}"
            echo -e "  停止服务器: ${YELLOW}./stop.sh${NC}"
            echo -e "  查看日志: ${YELLOW}tail -f $LOG_FILE${NC}"
            echo -e "  健康检查: ${YELLOW}curl http://localhost:3000/health${NC}"
            echo
            
        else
            print_error "服务器启动失败"
            if [ -f "../$LOG_FILE" ]; then
                print_error "错误日志:"
                tail -10 "../$LOG_FILE"
            fi
            exit 1
        fi
    fi
    
    cd ..
}

# 显示帮助信息
show_help() {
    echo "All-Agent 启动脚本"
    echo
    echo "用法: $0 [选项]"
    echo
    echo "选项:"
    echo "  start     启动服务器 (默认)"
    echo "  dev       以开发模式启动"
    echo "  stop      停止服务器"
    echo "  restart   重启服务器"
    echo "  status    查看服务器状态"
    echo "  logs      查看日志"
    echo "  help      显示此帮助信息"
    echo
}

# 停止服务器
stop_server() {
    print_step "停止 All-Agent 服务器..."
    
    if [ -f "$PID_FILE" ]; then
        local pid=$(cat "$PID_FILE")
        
        if kill -0 $pid 2>/dev/null; then
            kill -TERM $pid
            
            # 等待进程结束
            local count=0
            while kill -0 $pid 2>/dev/null && [ $count -lt 10 ]; do
                sleep 1
                count=$((count + 1))
            done
            
            # 如果进程仍在运行，强制终止
            if kill -0 $pid 2>/dev/null; then
                kill -9 $pid
                print_warning "强制终止进程"
            fi
            
            print_status "服务器已停止"
        else
            print_warning "服务器进程不存在"
        fi
        
        rm -f "$PID_FILE"
    else
        print_warning "PID 文件不存在，尝试查找进程..."
        
        # 尝试通过端口查找进程
        local pid=$(lsof -ti:3000 2>/dev/null)
        if [ ! -z "$pid" ]; then
            kill -TERM $pid
            print_status "服务器已停止"
        else
            print_status "没有找到运行中的服务器"
        fi
    fi
}

# 查看服务器状态
check_status() {
    print_step "检查服务器状态..."
    
    if [ -f "$PID_FILE" ]; then
        local pid=$(cat "$PID_FILE")
        
        if kill -0 $pid 2>/dev/null; then
            print_status "服务器正在运行 (PID: $pid)"
            
            # 检查健康状态
            if command -v curl &> /dev/null; then
                if curl -f http://localhost:3000/health >/dev/null 2>&1; then
                    print_status "服务器健康状态: 正常"
                else
                    print_warning "服务器健康状态: 异常"
                fi
            fi
        else
            print_warning "PID 文件存在但进程不在运行"
            rm -f "$PID_FILE"
        fi
    else
        print_status "服务器未运行"
    fi
}

# 查看日志
show_logs() {
    if [ -f "$LOG_FILE" ]; then
        print_status "显示最近的日志 (按 Ctrl+C 退出):"
        tail -f "$LOG_FILE"
    else
        print_warning "日志文件不存在"
    fi
}

# 主函数
main() {
    print_banner
    
    case "${1:-start}" in
        "start")
            check_dependencies
            create_directories
            install_dependencies
            check_config
            start_server
            ;;
        "dev")
            check_dependencies
            create_directories
            install_dependencies
            check_config
            start_server "dev"
            ;;
        "stop")
            stop_server
            ;;
        "restart")
            stop_server
            sleep 2
            check_dependencies
            create_directories
            install_dependencies
            check_config
            start_server
            ;;
        "status")
            check_status
            ;;
        "logs")
            show_logs
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            print_error "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
