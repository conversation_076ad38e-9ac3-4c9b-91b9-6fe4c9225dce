#!/usr/bin/env node

/**
 * 详细的 API 诊断工具
 * 专门诊断 DeepSeek 和 Mistral API 问题
 */

require('dotenv').config({ path: './.env' });

// 调试：打印环境变量
console.log('🔍 调试环境变量:');
console.log(
  'DEEPSEEK_API_KEY:',
  process.env.DEEPSEEK_API_KEY ? process.env.DEEPSEEK_API_KEY.substring(0, 10) + '...' : '未设置'
);
console.log(
  'MISTRAL_API_KEY:',
  process.env.MISTRAL_API_KEY ? process.env.MISTRAL_API_KEY.substring(0, 10) + '...' : '未设置'
);
console.log('');
const fetch = require('node-fetch');
const dns = require('dns').promises;

// 颜色输出
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// 测试 DNS 解析
async function testDNS(hostname) {
  try {
    log(`🔍 测试 DNS 解析: ${hostname}`, 'cyan');
    const addresses = await dns.lookup(hostname);
    log(`✅ DNS 解析成功: ${hostname} -> ${addresses.address}`, 'green');
    return true;
  } catch (error) {
    log(`❌ DNS 解析失败: ${hostname} - ${error.message}`, 'red');
    return false;
  }
}

// 测试网络连接
async function testConnection(url) {
  try {
    log(`🔗 测试网络连接: ${url}`, 'cyan');
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 10000);

    const response = await fetch(url, {
      method: 'HEAD',
      signal: controller.signal,
      timeout: 10000
    });

    clearTimeout(timeoutId);
    log(`✅ 网络连接成功: ${response.status}`, 'green');
    return true;
  } catch (error) {
    log(`❌ 网络连接失败: ${error.message}`, 'red');
    return false;
  }
}

// 测试 DeepSeek API
async function testDeepSeekAPI() {
  const apiKey = process.env.DEEPSEEK_API_KEY;
  const baseUrl = process.env.DEEPSEEK_API_BASE;
  const model = process.env.DEEPSEEK_MODEL;

  log('\n' + '='.repeat(60), 'cyan');
  log('🧪 详细测试 DeepSeek API', 'cyan');
  log('='.repeat(60), 'cyan');

  log(`📋 配置信息:`, 'blue');
  log(`   API Key: ${apiKey ? apiKey.substring(0, 10) + '...' : '未配置'}`, 'blue');
  log(`   Base URL: ${baseUrl}`, 'blue');
  log(`   Model: ${model}`, 'blue');

  if (!apiKey) {
    log('❌ DeepSeek API Key 未配置', 'red');
    return false;
  }

  // 1. 测试 DNS 解析
  const hostname = new URL(baseUrl).hostname;
  const dnsOk = await testDNS(hostname);
  if (!dnsOk) return false;

  // 2. 测试网络连接
  const connOk = await testConnection(baseUrl);
  if (!connOk) return false;

  // 3. 测试 API 调用
  try {
    log(`🚀 测试 API 调用...`, 'cyan');

    const response = await fetch(`${baseUrl}/chat/completions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${apiKey}`
      },
      body: JSON.stringify({
        model: model,
        messages: [{ role: 'user', content: '你好' }],
        max_tokens: 10,
        temperature: 0.1
      }),
      timeout: 30000
    });

    log(`📊 响应状态: ${response.status}`, response.ok ? 'green' : 'red');

    if (!response.ok) {
      const errorText = await response.text();
      log(`❌ API 错误响应:`, 'red');
      log(`   状态码: ${response.status}`, 'red');
      log(`   错误内容: ${errorText}`, 'red');
      return false;
    }

    const data = await response.json();
    log(`✅ DeepSeek API 调用成功!`, 'green');
    log(`📝 响应内容: ${data.choices[0].message.content}`, 'blue');
    return true;
  } catch (error) {
    log(`❌ API 调用异常: ${error.message}`, 'red');
    return false;
  }
}

// 测试 Mistral API
async function testMistralAPI() {
  const apiKey = process.env.MISTRAL_API_KEY;
  const baseUrl = process.env.MISTRAL_API_BASE;
  const model = process.env.MISTRAL_MODEL;

  log('\n' + '='.repeat(60), 'cyan');
  log('🧪 详细测试 Mistral AI API', 'cyan');
  log('='.repeat(60), 'cyan');

  log(`📋 配置信息:`, 'blue');
  log(`   API Key: ${apiKey ? apiKey.substring(0, 10) + '...' : '未配置'}`, 'blue');
  log(`   Base URL: ${baseUrl}`, 'blue');
  log(`   Model: ${model}`, 'blue');

  if (!apiKey) {
    log('❌ Mistral API Key 未配置', 'red');
    return false;
  }

  // 1. 测试 DNS 解析
  const hostname = new URL(baseUrl).hostname;
  const dnsOk = await testDNS(hostname);
  if (!dnsOk) return false;

  // 2. 测试网络连接
  const connOk = await testConnection(baseUrl);
  if (!connOk) return false;

  // 3. 测试 API 调用
  try {
    log(`🚀 测试 API 调用...`, 'cyan');

    const response = await fetch(`${baseUrl}/chat/completions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${apiKey}`
      },
      body: JSON.stringify({
        model: model,
        messages: [{ role: 'user', content: '你好' }],
        max_tokens: 10,
        temperature: 0.1
      }),
      timeout: 30000
    });

    log(`📊 响应状态: ${response.status}`, response.ok ? 'green' : 'red');

    if (!response.ok) {
      const errorText = await response.text();
      log(`❌ API 错误响应:`, 'red');
      log(`   状态码: ${response.status}`, 'red');
      log(`   错误内容: ${errorText}`, 'red');

      // 特殊处理 401 错误
      if (response.status === 401) {
        log(`\n💡 401 错误可能的原因:`, 'yellow');
        log(`   1. API Key 格式错误`, 'yellow');
        log(`   2. API Key 已过期`, 'yellow');
        log(`   3. API Key 权限不足`, 'yellow');
        log(`   4. 账户余额不足`, 'yellow');
        log(`   5. API Key 未激活`, 'yellow');
      }
      return false;
    }

    const data = await response.json();
    log(`✅ Mistral API 调用成功!`, 'green');
    log(`📝 响应内容: ${data.choices[0].message.content}`, 'blue');
    return true;
  } catch (error) {
    log(`❌ API 调用异常: ${error.message}`, 'red');
    return false;
  }
}

// 测试网络环境
async function testNetworkEnvironment() {
  log('\n' + '='.repeat(60), 'cyan');
  log('🌐 测试网络环境', 'cyan');
  log('='.repeat(60), 'cyan');

  // 测试基本网络连接
  const testSites = [
    'https://www.google.com',
    'https://www.baidu.com',
    'https://api.openai.com',
    'https://api.deepseek.ai',
    'https://api.mistral.ai'
  ];

  for (const site of testSites) {
    try {
      const hostname = new URL(site).hostname;
      await testDNS(hostname);
    } catch (error) {
      log(`❌ 无法解析: ${site}`, 'red');
    }
  }
}

// 主函数
async function main() {
  log('🔍 开始详细 API 诊断...', 'cyan');

  // 测试网络环境
  await testNetworkEnvironment();

  // 测试 DeepSeek
  const deepseekOk = await testDeepSeekAPI();

  // 测试 Mistral
  const mistralOk = await testMistralAPI();

  // 总结
  log('\n' + '='.repeat(60), 'cyan');
  log('📊 诊断结果总结', 'cyan');
  log('='.repeat(60), 'cyan');

  log(`DeepSeek API: ${deepseekOk ? '✅ 正常' : '❌ 异常'}`, deepseekOk ? 'green' : 'red');
  log(`Mistral API: ${mistralOk ? '✅ 正常' : '❌ 异常'}`, mistralOk ? 'green' : 'red');

  if (!deepseekOk || !mistralOk) {
    log('\n💡 建议检查:', 'yellow');
    log('1. 网络连接是否正常', 'yellow');
    log('2. 是否有防火墙或代理限制', 'yellow');
    log('3. API Key 是否正确且有效', 'yellow');
    log('4. 账户是否有足够余额', 'yellow');
    log('5. API Key 是否已激活', 'yellow');
  }
}

// 运行诊断
if (require.main === module) {
  main().catch((error) => {
    log(`诊断过程中发生错误: ${error.message}`, 'red');
    process.exit(1);
  });
}

module.exports = { main };
