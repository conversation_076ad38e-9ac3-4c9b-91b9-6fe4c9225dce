apiVersion: v1
kind: Namespace
metadata:
  name: knative-serving
  labels:
    name: knative-serving
    istio-injection: enabled
---
# 文本分析函数
apiVersion: serving.knative.dev/v1
kind: Service
metadata:
  name: text-analyzer
  namespace: knative-serving
  labels:
    app.kubernetes.io/name: text-analyzer
    app.kubernetes.io/component: faas
  annotations:
    autoscaling.knative.dev/minScale: "0"
    autoscaling.knative.dev/maxScale: "10"
    autoscaling.knative.dev/target: "10"
    autoscaling.knative.dev/targetUtilizationPercentage: "70"
spec:
  template:
    metadata:
      labels:
        app.kubernetes.io/name: text-analyzer
        app.kubernetes.io/component: faas
      annotations:
        autoscaling.knative.dev/class: "kpa.autoscaling.knative.dev"
        autoscaling.knative.dev/metric: "concurrency"
    spec:
      containerConcurrency: 10
      timeoutSeconds: 300
      containers:
      - name: text-analyzer
        image: python:3.9-slim
        ports:
        - name: http1
          containerPort: 8080
          protocol: TCP
        env:
        - name: PORT
          value: "8080"
        - name: FUNCTION_NAME
          value: "text-analyzer"
        - name: OPENAI_API_KEY
          valueFrom:
            secretKeyRef:
              name: faas-secrets
              key: openai-api-key
        command:
        - /bin/bash
        - -c
        - |
          pip install flask gunicorn openai nltk textstat
          python -c "
          import os
          import json
          import nltk
          import textstat
          from flask import Flask, request, jsonify
          from datetime import datetime
          import re
          
          # 下载 NLTK 数据
          try:
              nltk.download('punkt', quiet=True)
              nltk.download('stopwords', quiet=True)
              nltk.download('vader_lexicon', quiet=True)
          except:
              pass
          
          app = Flask(__name__)
          
          @app.route('/', methods=['POST'])
          def analyze_text():
              try:
                  data = request.get_json()
                  text = data.get('text', '')
                  
                  if not text:
                      return jsonify({'error': 'No text provided'}), 400
                  
                  # 基础统计
                  word_count = len(text.split())
                  char_count = len(text)
                  sentence_count = len(re.split(r'[.!?]+', text))
                  paragraph_count = len(text.split('\n\n'))
                  
                  # 可读性分析
                  flesch_score = textstat.flesch_reading_ease(text)
                  flesch_grade = textstat.flesch_kincaid_grade(text)
                  
                  # 情感分析（简单版本）
                  try:
                      from nltk.sentiment import SentimentIntensityAnalyzer
                      sia = SentimentIntensityAnalyzer()
                      sentiment = sia.polarity_scores(text)
                  except:
                      sentiment = {'compound': 0, 'pos': 0, 'neu': 1, 'neg': 0}
                  
                  # 关键词提取（简单版本）
                  from nltk.corpus import stopwords
                  from collections import Counter
                  
                  try:
                      stop_words = set(stopwords.words('english'))
                      words = [word.lower() for word in re.findall(r'\b\w+\b', text)]
                      keywords = [word for word in words if word not in stop_words and len(word) > 3]
                      top_keywords = [word for word, count in Counter(keywords).most_common(10)]
                  except:
                      top_keywords = []
                  
                  result = {
                      'statistics': {
                          'word_count': word_count,
                          'character_count': char_count,
                          'sentence_count': sentence_count,
                          'paragraph_count': paragraph_count,
                          'average_words_per_sentence': word_count / max(sentence_count, 1)
                      },
                      'readability': {
                          'flesch_reading_ease': flesch_score,
                          'flesch_kincaid_grade': flesch_grade,
                          'reading_level': 'Easy' if flesch_score > 80 else 'Medium' if flesch_score > 50 else 'Hard'
                      },
                      'sentiment': sentiment,
                      'keywords': top_keywords,
                      'processed_at': datetime.utcnow().isoformat(),
                      'function': 'text-analyzer'
                  }
                  
                  return jsonify(result)
                  
              except Exception as e:
                  return jsonify({'error': str(e)}), 500
          
          @app.route('/health', methods=['GET'])
          def health():
              return jsonify({'status': 'healthy', 'function': 'text-analyzer'})
          
          if __name__ == '__main__':
              port = int(os.environ.get('PORT', 8080))
              app.run(host='0.0.0.0', port=port)
          "
        resources:
          requests:
            cpu: 100m
            memory: 256Mi
          limits:
            cpu: 1000m
            memory: 512Mi
---
# 代码生成函数
apiVersion: serving.knative.dev/v1
kind: Service
metadata:
  name: code-generator
  namespace: knative-serving
  labels:
    app.kubernetes.io/name: code-generator
    app.kubernetes.io/component: faas
  annotations:
    autoscaling.knative.dev/minScale: "0"
    autoscaling.knative.dev/maxScale: "5"
    autoscaling.knative.dev/target: "5"
spec:
  template:
    metadata:
      labels:
        app.kubernetes.io/name: code-generator
        app.kubernetes.io/component: faas
    spec:
      containerConcurrency: 5
      timeoutSeconds: 600
      containers:
      - name: code-generator
        image: python:3.9-slim
        ports:
        - name: http1
          containerPort: 8080
          protocol: TCP
        env:
        - name: PORT
          value: "8080"
        - name: FUNCTION_NAME
          value: "code-generator"
        - name: OPENAI_API_KEY
          valueFrom:
            secretKeyRef:
              name: faas-secrets
              key: openai-api-key
        command:
        - /bin/bash
        - -c
        - |
          pip install flask gunicorn openai
          python -c "
          import os
          import json
          import openai
          from flask import Flask, request, jsonify
          from datetime import datetime
          
          app = Flask(__name__)
          
          # 配置 OpenAI
          openai.api_key = os.environ.get('OPENAI_API_KEY')
          
          @app.route('/', methods=['POST'])
          def generate_code():
              try:
                  data = request.get_json()
                  prompt = data.get('prompt', '')
                  language = data.get('language', 'python')
                  style = data.get('style', 'clean')
                  
                  if not prompt:
                      return jsonify({'error': 'No prompt provided'}), 400
                  
                  # 构建代码生成提示
                  system_prompt = f'''
                  You are an expert {language} programmer. Generate clean, well-documented, and efficient code.
                  Style: {style}
                  Include comments and follow best practices.
                  '''
                  
                  user_prompt = f'''
                  Generate {language} code for the following requirement:
                  {prompt}
                  
                  Please provide:
                  1. Clean, working code
                  2. Comments explaining the logic
                  3. Error handling where appropriate
                  4. Example usage if applicable
                  '''
                  
                  # 调用 OpenAI API
                  if openai.api_key:
                      try:
                          response = openai.ChatCompletion.create(
                              model='gpt-3.5-turbo',
                              messages=[
                                  {'role': 'system', 'content': system_prompt},
                                  {'role': 'user', 'content': user_prompt}
                              ],
                              max_tokens=2000,
                              temperature=0.3
                          )
                          
                          generated_code = response.choices[0].message.content
                      except Exception as e:
                          generated_code = f'# Error calling OpenAI API: {str(e)}\n# Fallback code template\n\ndef main():\n    # TODO: Implement {prompt}\n    pass\n\nif __name__ == \"__main__\":\n    main()'
                  else:
                      # 回退到模板代码
                      generated_code = f'''
# Generated code template for: {prompt}
# Language: {language}
# Style: {style}

def main():
    \"\"\"
    TODO: Implement the following requirement:
    {prompt}
    \"\"\"
    pass

if __name__ == \"__main__\":
    main()
                      '''.strip()
                  
                  result = {
                      'code': generated_code,
                      'language': language,
                      'style': style,
                      'prompt': prompt,
                      'generated_at': datetime.utcnow().isoformat(),
                      'function': 'code-generator'
                  }
                  
                  return jsonify(result)
                  
              except Exception as e:
                  return jsonify({'error': str(e)}), 500
          
          @app.route('/health', methods=['GET'])
          def health():
              return jsonify({'status': 'healthy', 'function': 'code-generator'})
          
          if __name__ == '__main__':
              port = int(os.environ.get('PORT', 8080))
              app.run(host='0.0.0.0', port=port)
          "
        resources:
          requests:
            cpu: 200m
            memory: 512Mi
          limits:
            cpu: 2000m
            memory: 1Gi
---
# 数据处理函数
apiVersion: serving.knative.dev/v1
kind: Service
metadata:
  name: data-processor
  namespace: knative-serving
  labels:
    app.kubernetes.io/name: data-processor
    app.kubernetes.io/component: faas
  annotations:
    autoscaling.knative.dev/minScale: "0"
    autoscaling.knative.dev/maxScale: "20"
    autoscaling.knative.dev/target: "15"
spec:
  template:
    metadata:
      labels:
        app.kubernetes.io/name: data-processor
        app.kubernetes.io/component: faas
    spec:
      containerConcurrency: 15
      timeoutSeconds: 180
      containers:
      - name: data-processor
        image: python:3.9-slim
        ports:
        - name: http1
          containerPort: 8080
          protocol: TCP
        env:
        - name: PORT
          value: "8080"
        - name: FUNCTION_NAME
          value: "data-processor"
        command:
        - /bin/bash
        - -c
        - |
          pip install flask gunicorn pandas numpy
          python -c "
          import os
          import json
          import pandas as pd
          import numpy as np
          from flask import Flask, request, jsonify
          from datetime import datetime
          import io
          import base64
          
          app = Flask(__name__)
          
          @app.route('/', methods=['POST'])
          def process_data():
              try:
                  data = request.get_json()
                  operation = data.get('operation', 'analyze')
                  input_data = data.get('data', [])
                  options = data.get('options', {})
                  
                  if not input_data:
                      return jsonify({'error': 'No data provided'}), 400
                  
                  # 转换为 DataFrame
                  if isinstance(input_data, list):
                      if all(isinstance(item, dict) for item in input_data):
                          df = pd.DataFrame(input_data)
                      else:
                          df = pd.DataFrame({'values': input_data})
                  else:
                      df = pd.DataFrame(input_data)
                  
                  result = {'operation': operation, 'processed_at': datetime.utcnow().isoformat()}
                  
                  if operation == 'analyze':
                      # 数据分析
                      numeric_cols = df.select_dtypes(include=[np.number]).columns
                      
                      result['analysis'] = {
                          'shape': df.shape,
                          'columns': list(df.columns),
                          'dtypes': df.dtypes.to_dict(),
                          'missing_values': df.isnull().sum().to_dict(),
                          'numeric_summary': df[numeric_cols].describe().to_dict() if len(numeric_cols) > 0 else {}
                      }
                      
                  elif operation == 'clean':
                      # 数据清洗
                      original_shape = df.shape
                      
                      # 删除重复行
                      df = df.drop_duplicates()
                      
                      # 处理缺失值
                      if options.get('fill_missing', True):
                          numeric_cols = df.select_dtypes(include=[np.number]).columns
                          df[numeric_cols] = df[numeric_cols].fillna(df[numeric_cols].mean())
                          
                          text_cols = df.select_dtypes(include=['object']).columns
                          df[text_cols] = df[text_cols].fillna('Unknown')
                      
                      result['cleaned_data'] = df.to_dict('records')
                      result['cleaning_summary'] = {
                          'original_shape': original_shape,
                          'cleaned_shape': df.shape,
                          'duplicates_removed': original_shape[0] - df.shape[0]
                      }
                      
                  elif operation == 'transform':
                      # 数据转换
                      transform_type = options.get('type', 'normalize')
                      
                      numeric_cols = df.select_dtypes(include=[np.number]).columns
                      
                      if transform_type == 'normalize':
                          df[numeric_cols] = (df[numeric_cols] - df[numeric_cols].min()) / (df[numeric_cols].max() - df[numeric_cols].min())
                      elif transform_type == 'standardize':
                          df[numeric_cols] = (df[numeric_cols] - df[numeric_cols].mean()) / df[numeric_cols].std()
                      
                      result['transformed_data'] = df.to_dict('records')
                      result['transform_type'] = transform_type
                      
                  elif operation == 'aggregate':
                      # 数据聚合
                      group_by = options.get('group_by', [])
                      agg_func = options.get('function', 'mean')
                      
                      if group_by and all(col in df.columns for col in group_by):
                          numeric_cols = df.select_dtypes(include=[np.number]).columns
                          if len(numeric_cols) > 0:
                              aggregated = df.groupby(group_by)[numeric_cols].agg(agg_func)
                              result['aggregated_data'] = aggregated.reset_index().to_dict('records')
                          else:
                              result['error'] = 'No numeric columns for aggregation'
                      else:
                          # 全局聚合
                          numeric_cols = df.select_dtypes(include=[np.number]).columns
                          if len(numeric_cols) > 0:
                              if agg_func == 'mean':
                                  aggregated = df[numeric_cols].mean()
                              elif agg_func == 'sum':
                                  aggregated = df[numeric_cols].sum()
                              elif agg_func == 'count':
                                  aggregated = df[numeric_cols].count()
                              else:
                                  aggregated = df[numeric_cols].describe()
                              
                              result['aggregated_data'] = aggregated.to_dict()
                  
                  result['function'] = 'data-processor'
                  return jsonify(result)
                  
              except Exception as e:
                  return jsonify({'error': str(e)}), 500
          
          @app.route('/health', methods=['GET'])
          def health():
              return jsonify({'status': 'healthy', 'function': 'data-processor'})
          
          if __name__ == '__main__':
              port = int(os.environ.get('PORT', 8080))
              app.run(host='0.0.0.0', port=port)
          "
        resources:
          requests:
            cpu: 100m
            memory: 256Mi
          limits:
            cpu: 1000m
            memory: 1Gi
---
# FaaS 密钥
apiVersion: v1
kind: Secret
metadata:
  name: faas-secrets
  namespace: knative-serving
type: Opaque
stringData:
  openai-api-key: "your-openai-api-key"
  anthropic-api-key: "your-anthropic-api-key"
---
# Knative 域名映射
apiVersion: serving.knative.dev/v1alpha1
kind: DomainMapping
metadata:
  name: text-analyzer.all-agent.com
  namespace: knative-serving
spec:
  ref:
    name: text-analyzer
    kind: Service
    apiVersion: serving.knative.dev/v1
---
apiVersion: serving.knative.dev/v1alpha1
kind: DomainMapping
metadata:
  name: code-generator.all-agent.com
  namespace: knative-serving
spec:
  ref:
    name: code-generator
    kind: Service
    apiVersion: serving.knative.dev/v1
---
apiVersion: serving.knative.dev/v1alpha1
kind: DomainMapping
metadata:
  name: data-processor.all-agent.com
  namespace: knative-serving
spec:
  ref:
    name: data-processor
    kind: Service
    apiVersion: serving.knative.dev/v1
