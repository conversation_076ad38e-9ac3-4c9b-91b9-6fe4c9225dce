const TestSetup = require('./setup');
const Database = require('../database/Database');

/**
 * 数据库测试套件
 */
class DatabaseTests {
    constructor() {
        this.setup = new TestSetup();
        this.database = null;
    }

    async beforeEach() {
        await this.setup.setupTestEnvironment();
        this.database = new Database({
            dbPath: require('path').join(__dirname, '../data/test.db')
        });
        await this.database.initialize();
    }

    async afterEach() {
        if (this.database) {
            await this.database.close();
        }
        await this.setup.cleanupTestEnvironment();
    }

    /**
     * 数据库连接测试
     */
    async testDatabaseConnection() {
        this.setup.assert.truthy(this.database.isConnected, '数据库应该已连接');
        
        const health = await this.database.healthCheck();
        this.setup.assert.truthy(health.healthy, '数据库健康检查应该通过');
    }

    /**
     * 用户表操作测试
     */
    async testUserOperations() {
        // 插入用户
        const result = await this.database.run(
            'INSERT INTO users (username, email, password_hash, role) VALUES (?, ?, ?, ?)',
            ['testuser', '<EMAIL>', 'hashedpassword', 'user']
        );
        
        this.setup.assert.truthy(result.id, '应该返回插入的用户ID');

        // 查询用户
        const user = await this.database.get(
            'SELECT * FROM users WHERE id = ?',
            [result.id]
        );
        
        this.setup.assert.equal(user.username, 'testuser', '用户名应该匹配');
        this.setup.assert.equal(user.email, '<EMAIL>', '邮箱应该匹配');
        this.setup.assert.equal(user.role, 'user', '角色应该匹配');

        // 更新用户
        await this.database.run(
            'UPDATE users SET username = ? WHERE id = ?',
            ['updateduser', result.id]
        );

        const updatedUser = await this.database.get(
            'SELECT * FROM users WHERE id = ?',
            [result.id]
        );
        
        this.setup.assert.equal(updatedUser.username, 'updateduser', '用户名应该已更新');

        // 删除用户
        await this.database.run('DELETE FROM users WHERE id = ?', [result.id]);
        
        const deletedUser = await this.database.get(
            'SELECT * FROM users WHERE id = ?',
            [result.id]
        );
        
        this.setup.assert.falsy(deletedUser, '用户应该已被删除');
    }

    /**
     * 项目表操作测试
     */
    async testProjectOperations() {
        // 先创建用户
        const userResult = await this.database.run(
            'INSERT INTO users (username, email, password_hash) VALUES (?, ?, ?)',
            ['testuser', '<EMAIL>', 'hashedpassword']
        );

        // 创建项目
        const projectResult = await this.database.run(
            'INSERT INTO projects (user_id, name, description, project_path) VALUES (?, ?, ?, ?)',
            [userResult.id, 'Test Project', 'A test project', '/path/to/project']
        );

        this.setup.assert.truthy(projectResult.id, '应该返回插入的项目ID');

        // 查询项目
        const project = await this.database.get(
            'SELECT * FROM projects WHERE id = ?',
            [projectResult.id]
        );

        this.setup.assert.equal(project.name, 'Test Project', '项目名应该匹配');
        this.setup.assert.equal(project.user_id, userResult.id, '用户ID应该匹配');
    }

    /**
     * 任务表操作测试
     */
    async testTaskOperations() {
        // 创建用户和项目
        const userResult = await this.database.run(
            'INSERT INTO users (username, email, password_hash) VALUES (?, ?, ?)',
            ['testuser', '<EMAIL>', 'hashedpassword']
        );

        const projectResult = await this.database.run(
            'INSERT INTO projects (user_id, name, project_path) VALUES (?, ?, ?)',
            [userResult.id, 'Test Project', '/path/to/project']
        );

        // 创建任务
        const taskResult = await this.database.run(
            'INSERT INTO tasks (task_id, user_id, project_id, agent_type, action, status) VALUES (?, ?, ?, ?, ?, ?)',
            ['task-123', userResult.id, projectResult.id, 'analyzer', 'analyze_project', 'pending']
        );

        this.setup.assert.truthy(taskResult.id, '应该返回插入的任务ID');

        // 查询任务
        const task = await this.database.get(
            'SELECT * FROM tasks WHERE id = ?',
            [taskResult.id]
        );

        this.setup.assert.equal(task.task_id, 'task-123', '任务ID应该匹配');
        this.setup.assert.equal(task.agent_type, 'analyzer', 'Agent类型应该匹配');
        this.setup.assert.equal(task.status, 'pending', '状态应该匹配');

        // 更新任务状态
        await this.database.run(
            'UPDATE tasks SET status = ?, completed_at = datetime("now") WHERE id = ?',
            ['completed', taskResult.id]
        );

        const updatedTask = await this.database.get(
            'SELECT * FROM tasks WHERE id = ?',
            [taskResult.id]
        );

        this.setup.assert.equal(updatedTask.status, 'completed', '状态应该已更新');
        this.setup.assert.truthy(updatedTask.completed_at, '完成时间应该已设置');
    }

    /**
     * 聊天消息表操作测试
     */
    async testChatMessageOperations() {
        // 创建用户
        const userResult = await this.database.run(
            'INSERT INTO users (username, email, password_hash) VALUES (?, ?, ?)',
            ['testuser', '<EMAIL>', 'hashedpassword']
        );

        // 创建聊天消息
        const messageResult = await this.database.run(
            'INSERT INTO chat_messages (user_id, agent_type, message_type, content) VALUES (?, ?, ?, ?)',
            [userResult.id, 'analyzer', 'user', 'Hello, can you analyze my project?']
        );

        this.setup.assert.truthy(messageResult.id, '应该返回插入的消息ID');

        // 查询消息
        const message = await this.database.get(
            'SELECT * FROM chat_messages WHERE id = ?',
            [messageResult.id]
        );

        this.setup.assert.equal(message.agent_type, 'analyzer', 'Agent类型应该匹配');
        this.setup.assert.equal(message.message_type, 'user', '消息类型应该匹配');
        this.setup.assert.equal(message.content, 'Hello, can you analyze my project?', '内容应该匹配');

        // 查询用户的所有消息
        const userMessages = await this.database.all(
            'SELECT * FROM chat_messages WHERE user_id = ? ORDER BY created_at',
            [userResult.id]
        );

        this.setup.assert.equal(userMessages.length, 1, '应该有一条消息');
        this.setup.assert.equal(userMessages[0].id, messageResult.id, '消息ID应该匹配');
    }

    /**
     * 事务测试
     */
    async testTransactions() {
        await this.database.beginTransaction();

        try {
            // 在事务中插入数据
            const userResult = await this.database.run(
                'INSERT INTO users (username, email, password_hash) VALUES (?, ?, ?)',
                ['transactionuser', '<EMAIL>', 'hashedpassword']
            );

            const projectResult = await this.database.run(
                'INSERT INTO projects (user_id, name, project_path) VALUES (?, ?, ?)',
                [userResult.id, 'Transaction Project', '/path/to/project']
            );

            // 提交事务
            await this.database.commit();

            // 验证数据已插入
            const user = await this.database.get(
                'SELECT * FROM users WHERE id = ?',
                [userResult.id]
            );
            
            const project = await this.database.get(
                'SELECT * FROM projects WHERE id = ?',
                [projectResult.id]
            );

            this.setup.assert.truthy(user, '用户应该存在');
            this.setup.assert.truthy(project, '项目应该存在');

        } catch (error) {
            await this.database.rollback();
            throw error;
        }
    }

    /**
     * 事务回滚测试
     */
    async testTransactionRollback() {
        await this.database.beginTransaction();

        try {
            // 插入用户
            const userResult = await this.database.run(
                'INSERT INTO users (username, email, password_hash) VALUES (?, ?, ?)',
                ['rollbackuser', '<EMAIL>', 'hashedpassword']
            );

            // 故意触发错误（违反唯一约束）
            await this.database.run(
                'INSERT INTO users (username, email, password_hash) VALUES (?, ?, ?)',
                ['rollbackuser', '<EMAIL>', 'hashedpassword']
            );

            await this.database.commit();

        } catch (error) {
            await this.database.rollback();

            // 验证数据已回滚
            const user = await this.database.get(
                'SELECT * FROM users WHERE username = ?',
                ['rollbackuser']
            );

            this.setup.assert.falsy(user, '用户应该不存在（已回滚）');
        }
    }

    /**
     * 数据库统计测试
     */
    async testDatabaseStats() {
        // 插入一些测试数据
        await this.database.run(
            'INSERT INTO users (username, email, password_hash) VALUES (?, ?, ?)',
            ['user1', '<EMAIL>', 'hash1']
        );

        await this.database.run(
            'INSERT INTO users (username, email, password_hash) VALUES (?, ?, ?)',
            ['user2', '<EMAIL>', 'hash2']
        );

        // 获取统计信息
        const stats = await this.database.getStats();

        this.setup.assert.truthy(stats, '应该返回统计信息');
        this.setup.assert.equal(stats.users, 2, '用户数量应该为2');
        this.setup.assert.hasProperty(stats, 'projects', '应该包含项目统计');
        this.setup.assert.hasProperty(stats, 'tasks', '应该包含任务统计');
    }

    /**
     * 数据库清理测试
     */
    async testDatabaseCleanup() {
        // 插入过期会话
        await this.database.run(
            'INSERT INTO user_sessions (user_id, session_token, expires_at) VALUES (?, ?, ?)',
            [1, 'expired-token', '2020-01-01 00:00:00']
        );

        // 插入过期缓存
        await this.database.run(
            'INSERT INTO analysis_cache (cache_key, analysis_data, expires_at, project_path) VALUES (?, ?, ?, ?)',
            ['expired-cache', '{}', '2020-01-01 00:00:00', '/test']
        );

        // 执行清理
        await this.database.cleanup();

        // 验证过期数据已清理
        const expiredSession = await this.database.get(
            'SELECT * FROM user_sessions WHERE session_token = ?',
            ['expired-token']
        );

        const expiredCache = await this.database.get(
            'SELECT * FROM analysis_cache WHERE cache_key = ?',
            ['expired-cache']
        );

        this.setup.assert.falsy(expiredSession, '过期会话应该已清理');
        this.setup.assert.falsy(expiredCache, '过期缓存应该已清理');
    }

    /**
     * 运行所有数据库测试
     */
    async runAllTests() {
        const tests = {
            '数据库连接': async () => {
                await this.beforeEach();
                await this.testDatabaseConnection();
                await this.afterEach();
            },
            '用户表操作': async () => {
                await this.beforeEach();
                await this.testUserOperations();
                await this.afterEach();
            },
            '项目表操作': async () => {
                await this.beforeEach();
                await this.testProjectOperations();
                await this.afterEach();
            },
            '任务表操作': async () => {
                await this.beforeEach();
                await this.testTaskOperations();
                await this.afterEach();
            },
            '聊天消息操作': async () => {
                await this.beforeEach();
                await this.testChatMessageOperations();
                await this.afterEach();
            },
            '事务处理': async () => {
                await this.beforeEach();
                await this.testTransactions();
                await this.afterEach();
            },
            '事务回滚': async () => {
                await this.beforeEach();
                await this.testTransactionRollback();
                await this.afterEach();
            },
            '数据库统计': async () => {
                await this.beforeEach();
                await this.testDatabaseStats();
                await this.afterEach();
            },
            '数据库清理': async () => {
                await this.beforeEach();
                await this.testDatabaseCleanup();
                await this.afterEach();
            }
        };

        return await this.setup.runTestSuite('数据库测试', tests);
    }
}

// 如果直接运行此文件，执行测试
if (require.main === module) {
    const tests = new DatabaseTests();
    tests.runAllTests().then(results => {
        process.exit(results.failed > 0 ? 1 : 0);
    }).catch(error => {
        console.error('测试运行失败:', error);
        process.exit(1);
    });
}

module.exports = DatabaseTests;
