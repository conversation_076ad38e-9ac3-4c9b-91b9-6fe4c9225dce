const axios = require('axios');
const { performance } = require('perf_hooks');
const TestSetup = require('./setup');

/**
 * 性能测试套件
 * 包括压力测试、负载测试和性能基准测试
 */
class PerformanceTests {
    constructor() {
        this.setup = new TestSetup();
        this.baseURL = 'http://localhost:3000';
        this.authToken = null;
        this.results = {
            loadTest: {},
            stressTest: {},
            enduranceTest: {},
            concurrencyTest: {},
            memoryTest: {}
        };
    }

    async beforeAll() {
        console.log('🚀 启动性能测试环境...');
        
        // 等待服务器启动
        await this.waitForServer();
        
        // 获取认证令牌
        await this.authenticate();
        
        console.log('✅ 性能测试环境准备完成');
    }

    async afterAll() {
        console.log('🧹 清理性能测试环境...');
    }

    /**
     * 等待服务器启动
     */
    async waitForServer(maxAttempts = 30) {
        for (let i = 0; i < maxAttempts; i++) {
            try {
                await axios.get(`${this.baseURL}/health`);
                return;
            } catch (error) {
                await this.setup.wait(1000);
            }
        }
        throw new Error('服务器启动超时');
    }

    /**
     * 认证获取令牌
     */
    async authenticate() {
        try {
            const response = await axios.post(`${this.baseURL}/auth/login`, {
                username: '<EMAIL>',
                password: 'admin123'
            });
            
            if (response.data.success) {
                this.authToken = response.data.data.token;
            }
        } catch (error) {
            console.warn('认证失败，将使用匿名访问');
        }
    }

    /**
     * 负载测试 - 测试正常负载下的性能
     */
    async runLoadTest() {
        console.log('📊 开始负载测试...');
        
        const testConfig = {
            duration: 60000, // 1分钟
            concurrency: 10,  // 10个并发用户
            rampUpTime: 10000 // 10秒爬坡时间
        };

        const results = await this.executeLoadTest(testConfig);
        this.results.loadTest = results;
        
        console.log('✅ 负载测试完成');
        return results;
    }

    /**
     * 压力测试 - 测试系统极限
     */
    async runStressTest() {
        console.log('🔥 开始压力测试...');
        
        const testConfig = {
            duration: 120000, // 2分钟
            maxConcurrency: 100, // 最大100并发
            stepSize: 10,     // 每次增加10个并发
            stepDuration: 15000 // 每个阶段15秒
        };

        const results = await this.executeStressTest(testConfig);
        this.results.stressTest = results;
        
        console.log('✅ 压力测试完成');
        return results;
    }

    /**
     * 耐久性测试 - 长时间运行测试
     */
    async runEnduranceTest() {
        console.log('⏰ 开始耐久性测试...');
        
        const testConfig = {
            duration: 300000, // 5分钟
            concurrency: 5,   // 5个并发用户
            interval: 1000    // 每秒1个请求
        };

        const results = await this.executeEnduranceTest(testConfig);
        this.results.enduranceTest = results;
        
        console.log('✅ 耐久性测试完成');
        return results;
    }

    /**
     * 并发测试 - 测试并发处理能力
     */
    async runConcurrencyTest() {
        console.log('🔀 开始并发测试...');
        
        const concurrencyLevels = [1, 5, 10, 20, 50, 100];
        const results = {};

        for (const concurrency of concurrencyLevels) {
            console.log(`测试并发级别: ${concurrency}`);
            
            const testResult = await this.executeConcurrencyTest(concurrency);
            results[concurrency] = testResult;
            
            // 等待系统恢复
            await this.setup.wait(5000);
        }

        this.results.concurrencyTest = results;
        console.log('✅ 并发测试完成');
        return results;
    }

    /**
     * 内存泄漏测试
     */
    async runMemoryTest() {
        console.log('🧠 开始内存测试...');
        
        const testConfig = {
            iterations: 1000,
            batchSize: 10,
            interval: 100
        };

        const results = await this.executeMemoryTest(testConfig);
        this.results.memoryTest = results;
        
        console.log('✅ 内存测试完成');
        return results;
    }

    /**
     * 执行负载测试
     */
    async executeLoadTest(config) {
        const startTime = performance.now();
        const endTime = startTime + config.duration;
        const results = {
            totalRequests: 0,
            successfulRequests: 0,
            failedRequests: 0,
            responseTimes: [],
            errors: [],
            throughput: 0,
            averageResponseTime: 0,
            p95ResponseTime: 0,
            p99ResponseTime: 0
        };

        const workers = [];
        
        // 创建并发工作者
        for (let i = 0; i < config.concurrency; i++) {
            workers.push(this.createWorker(endTime, results));
        }

        // 等待所有工作者完成
        await Promise.all(workers);

        // 计算统计数据
        this.calculateStatistics(results, config.duration);
        
        return results;
    }

    /**
     * 执行压力测试
     */
    async executeStressTest(config) {
        const results = {
            phases: [],
            maxThroughput: 0,
            breakingPoint: null
        };

        let currentConcurrency = config.stepSize;
        
        while (currentConcurrency <= config.maxConcurrency) {
            console.log(`压力测试阶段: ${currentConcurrency} 并发`);
            
            const phaseResult = await this.executeLoadTest({
                duration: config.stepDuration,
                concurrency: currentConcurrency
            });

            phaseResult.concurrency = currentConcurrency;
            results.phases.push(phaseResult);

            // 检查是否达到破坏点
            if (phaseResult.failedRequests / phaseResult.totalRequests > 0.05) {
                results.breakingPoint = currentConcurrency;
                console.log(`检测到破坏点: ${currentConcurrency} 并发`);
                break;
            }

            if (phaseResult.throughput > results.maxThroughput) {
                results.maxThroughput = phaseResult.throughput;
            }

            currentConcurrency += config.stepSize;
            
            // 恢复时间
            await this.setup.wait(5000);
        }

        return results;
    }

    /**
     * 执行耐久性测试
     */
    async executeEnduranceTest(config) {
        const startTime = performance.now();
        const endTime = startTime + config.duration;
        const results = {
            totalRequests: 0,
            successfulRequests: 0,
            failedRequests: 0,
            memoryUsage: [],
            responseTimesTrend: [],
            errors: []
        };

        const workers = [];
        
        // 创建工作者
        for (let i = 0; i < config.concurrency; i++) {
            workers.push(this.createEnduranceWorker(endTime, config.interval, results));
        }

        // 内存监控
        const memoryMonitor = this.startMemoryMonitoring(results);

        // 等待完成
        await Promise.all(workers);
        clearInterval(memoryMonitor);

        return results;
    }

    /**
     * 执行并发测试
     */
    async executeConcurrencyTest(concurrency) {
        const requests = [];
        const startTime = performance.now();

        // 同时发起所有请求
        for (let i = 0; i < concurrency; i++) {
            requests.push(this.makeRequest('/health'));
        }

        const responses = await Promise.allSettled(requests);
        const endTime = performance.now();

        const successful = responses.filter(r => r.status === 'fulfilled').length;
        const failed = responses.filter(r => r.status === 'rejected').length;

        return {
            concurrency,
            totalTime: endTime - startTime,
            successful,
            failed,
            successRate: (successful / concurrency) * 100
        };
    }

    /**
     * 执行内存测试
     */
    async executeMemoryTest(config) {
        const results = {
            iterations: config.iterations,
            memoryBefore: process.memoryUsage(),
            memoryAfter: null,
            memoryPeak: 0,
            memoryLeak: false
        };

        for (let i = 0; i < config.iterations; i++) {
            const batch = [];
            
            // 批量请求
            for (let j = 0; j < config.batchSize; j++) {
                batch.push(this.makeRequest('/api/analyze', {
                    projectPath: '.',
                    options: { maxDepth: 2 }
                }));
            }

            await Promise.allSettled(batch);
            
            // 监控内存使用
            const currentMemory = process.memoryUsage().heapUsed;
            if (currentMemory > results.memoryPeak) {
                results.memoryPeak = currentMemory;
            }

            if (i % 100 === 0) {
                // 强制垃圾回收（如果可用）
                if (global.gc) {
                    global.gc();
                }
                
                console.log(`内存测试进度: ${i}/${config.iterations}`);
            }

            await this.setup.wait(config.interval);
        }

        results.memoryAfter = process.memoryUsage();
        
        // 检测内存泄漏
        const memoryIncrease = results.memoryAfter.heapUsed - results.memoryBefore.heapUsed;
        results.memoryLeak = memoryIncrease > 50 * 1024 * 1024; // 50MB阈值

        return results;
    }

    /**
     * 创建工作者
     */
    async createWorker(endTime, results) {
        while (performance.now() < endTime) {
            try {
                const startTime = performance.now();
                await this.makeRequest('/health');
                const responseTime = performance.now() - startTime;
                
                results.totalRequests++;
                results.successfulRequests++;
                results.responseTimes.push(responseTime);
                
            } catch (error) {
                results.totalRequests++;
                results.failedRequests++;
                results.errors.push(error.message);
            }
            
            // 随机延迟，模拟真实用户行为
            await this.setup.wait(Math.random() * 1000);
        }
    }

    /**
     * 创建耐久性工作者
     */
    async createEnduranceWorker(endTime, interval, results) {
        while (performance.now() < endTime) {
            try {
                const startTime = performance.now();
                await this.makeRequest('/health');
                const responseTime = performance.now() - startTime;
                
                results.totalRequests++;
                results.successfulRequests++;
                results.responseTimesTrend.push({
                    timestamp: Date.now(),
                    responseTime
                });
                
            } catch (error) {
                results.totalRequests++;
                results.failedRequests++;
                results.errors.push({
                    timestamp: Date.now(),
                    error: error.message
                });
            }
            
            await this.setup.wait(interval);
        }
    }

    /**
     * 开始内存监控
     */
    startMemoryMonitoring(results) {
        return setInterval(() => {
            const memoryUsage = process.memoryUsage();
            results.memoryUsage.push({
                timestamp: Date.now(),
                ...memoryUsage
            });
        }, 5000);
    }

    /**
     * 发起 HTTP 请求
     */
    async makeRequest(endpoint, data = null) {
        const config = {
            timeout: 30000,
            headers: {}
        };

        if (this.authToken) {
            config.headers.Authorization = `Bearer ${this.authToken}`;
        }

        if (data) {
            return await axios.post(`${this.baseURL}${endpoint}`, data, config);
        } else {
            return await axios.get(`${this.baseURL}${endpoint}`, config);
        }
    }

    /**
     * 计算统计数据
     */
    calculateStatistics(results, duration) {
        if (results.responseTimes.length === 0) {
            return;
        }

        // 排序响应时间
        results.responseTimes.sort((a, b) => a - b);
        
        // 计算平均响应时间
        results.averageResponseTime = results.responseTimes.reduce((a, b) => a + b, 0) / results.responseTimes.length;
        
        // 计算百分位数
        const p95Index = Math.floor(results.responseTimes.length * 0.95);
        const p99Index = Math.floor(results.responseTimes.length * 0.99);
        
        results.p95ResponseTime = results.responseTimes[p95Index];
        results.p99ResponseTime = results.responseTimes[p99Index];
        
        // 计算吞吐量 (请求/秒)
        results.throughput = (results.totalRequests / duration) * 1000;
        
        // 计算错误率
        results.errorRate = (results.failedRequests / results.totalRequests) * 100;
    }

    /**
     * 生成性能报告
     */
    generateReport() {
        const report = {
            timestamp: new Date().toISOString(),
            summary: {
                loadTest: this.summarizeLoadTest(),
                stressTest: this.summarizeStressTest(),
                enduranceTest: this.summarizeEnduranceTest(),
                concurrencyTest: this.summarizeConcurrencyTest(),
                memoryTest: this.summarizeMemoryTest()
            },
            recommendations: this.generateRecommendations(),
            rawResults: this.results
        };

        return report;
    }

    /**
     * 汇总负载测试结果
     */
    summarizeLoadTest() {
        const result = this.results.loadTest;
        return {
            throughput: result.throughput?.toFixed(2) + ' req/s',
            averageResponseTime: result.averageResponseTime?.toFixed(2) + ' ms',
            p95ResponseTime: result.p95ResponseTime?.toFixed(2) + ' ms',
            errorRate: result.errorRate?.toFixed(2) + '%',
            status: result.errorRate < 1 ? 'PASS' : 'FAIL'
        };
    }

    /**
     * 汇总压力测试结果
     */
    summarizeStressTest() {
        const result = this.results.stressTest;
        return {
            maxThroughput: result.maxThroughput?.toFixed(2) + ' req/s',
            breakingPoint: result.breakingPoint ? `${result.breakingPoint} concurrent users` : 'Not reached',
            phases: result.phases?.length || 0,
            status: result.breakingPoint ? 'FOUND_LIMIT' : 'STABLE'
        };
    }

    /**
     * 汇总耐久性测试结果
     */
    summarizeEnduranceTest() {
        const result = this.results.enduranceTest;
        return {
            totalRequests: result.totalRequests,
            successRate: ((result.successfulRequests / result.totalRequests) * 100).toFixed(2) + '%',
            memoryStable: result.memoryUsage?.length > 0,
            status: result.successfulRequests / result.totalRequests > 0.99 ? 'PASS' : 'FAIL'
        };
    }

    /**
     * 汇总并发测试结果
     */
    summarizeConcurrencyTest() {
        const results = this.results.concurrencyTest;
        const maxConcurrency = Math.max(...Object.keys(results).map(Number));
        const maxSuccessRate = Math.max(...Object.values(results).map(r => r.successRate));
        
        return {
            maxConcurrency,
            maxSuccessRate: maxSuccessRate.toFixed(2) + '%',
            status: maxSuccessRate > 95 ? 'PASS' : 'FAIL'
        };
    }

    /**
     * 汇总内存测试结果
     */
    summarizeMemoryTest() {
        const result = this.results.memoryTest;
        return {
            iterations: result.iterations,
            memoryLeak: result.memoryLeak,
            memoryIncrease: ((result.memoryAfter?.heapUsed - result.memoryBefore?.heapUsed) / 1024 / 1024).toFixed(2) + ' MB',
            status: !result.memoryLeak ? 'PASS' : 'FAIL'
        };
    }

    /**
     * 生成优化建议
     */
    generateRecommendations() {
        const recommendations = [];
        
        // 基于负载测试的建议
        if (this.results.loadTest.averageResponseTime > 1000) {
            recommendations.push('平均响应时间较高，建议优化数据库查询和缓存策略');
        }
        
        // 基于压力测试的建议
        if (this.results.stressTest.breakingPoint && this.results.stressTest.breakingPoint < 50) {
            recommendations.push('系统并发处理能力有限，建议增加服务器资源或优化架构');
        }
        
        // 基于内存测试的建议
        if (this.results.memoryTest.memoryLeak) {
            recommendations.push('检测到内存泄漏，建议检查代码中的内存管理');
        }
        
        if (recommendations.length === 0) {
            recommendations.push('系统性能表现良好，无明显优化建议');
        }
        
        return recommendations;
    }

    /**
     * 运行所有性能测试
     */
    async runAllTests() {
        await this.beforeAll();
        
        try {
            await this.runLoadTest();
            await this.runStressTest();
            await this.runEnduranceTest();
            await this.runConcurrencyTest();
            await this.runMemoryTest();
            
            return this.generateReport();
            
        } finally {
            await this.afterAll();
        }
    }
}

// 如果直接运行此文件，执行性能测试
if (require.main === module) {
    const tests = new PerformanceTests();
    tests.runAllTests().then(report => {
        console.log('\n📊 性能测试报告:');
        console.log(JSON.stringify(report.summary, null, 2));
        
        // 保存详细报告
        const fs = require('fs');
        const path = require('path');
        const reportPath = path.join(__dirname, '../reports', `performance-report-${Date.now()}.json`);
        fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
        console.log(`\n📄 详细报告已保存: ${reportPath}`);
        
    }).catch(error => {
        console.error('性能测试失败:', error);
        process.exit(1);
    });
}

module.exports = PerformanceTests;
