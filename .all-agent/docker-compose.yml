version: '3.8'

services:
  # All-Agent 主应用
  all-agent:
    build:
      context: .
      dockerfile: Dockerfile
      target: runtime
    container_name: all-agent-app
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - PORT=3000
      - USE_REDIS=true
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - DATABASE_URL=sqlite:/app/data/all-agent.db
      - JWT_SECRET=${JWT_SECRET:-all-agent-jwt-secret-change-in-production}
      - MONITOR_INTERVAL=30000
      - EMAIL_ALERTS_ENABLED=${EMAIL_ALERTS_ENABLED:-false}
      - EMAIL_SMTP_HOST=${EMAIL_SMTP_HOST}
      - EMAIL_USERNAME=${EMAIL_USERNAME}
      - EMAIL_PASSWORD=${EMAIL_PASSWORD}
      - EMAIL_ALERT_TO=${EMAIL_ALERT_TO}
      - SLACK_ALERTS_ENABLED=${SLACK_ALERTS_ENABLED:-false}
      - SLAC<PERSON>_WEBHOOK_URL=${SLACK_WEBHOOK_URL}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY}
      - GOOGLE_API_KEY=${GOOGLE_API_KEY}
    volumes:
      - all-agent-data:/app/data
      - all-agent-reports:/app/reports
      - all-agent-logs:/app/logs
      - all-agent-backups:/app/backups
    depends_on:
      - redis
      - postgres
    networks:
      - all-agent-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Redis 缓存
  redis:
    image: redis:7-alpine
    container_name: all-agent-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-}
    volumes:
      - redis-data:/data
    networks:
      - all-agent-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 3s
      retries: 3

  # PostgreSQL 数据库 (可选，用于生产环境)
  postgres:
    image: postgres:15-alpine
    container_name: all-agent-postgres
    restart: unless-stopped
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_DB=${POSTGRES_DB:-allagent}
      - POSTGRES_USER=${POSTGRES_USER:-allagent}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-allagent123}
    volumes:
      - postgres-data:/var/lib/postgresql/data
      - ./scripts/init-postgres.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - all-agent-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-allagent}"]
      interval: 30s
      timeout: 5s
      retries: 3

  # Nginx 反向代理
  nginx:
    image: nginx:alpine
    container_name: all-agent-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - nginx-logs:/var/log/nginx
    depends_on:
      - all-agent
    networks:
      - all-agent-network
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost/health"]
      interval: 30s
      timeout: 3s
      retries: 3

  # Prometheus 监控
  prometheus:
    image: prom/prometheus:latest
    container_name: all-agent-prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus-data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - all-agent-network

  # Grafana 可视化
  grafana:
    image: grafana/grafana:latest
    container_name: all-agent-grafana
    restart: unless-stopped
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_USER=${GRAFANA_USER:-admin}
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-admin123}
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana-data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    depends_on:
      - prometheus
    networks:
      - all-agent-network

  # Node Exporter (系统指标)
  node-exporter:
    image: prom/node-exporter:latest
    container_name: all-agent-node-exporter
    restart: unless-stopped
    ports:
      - "9100:9100"
    volumes:
      - /proc:/host/proc:ro
      - /sys:/host/sys:ro
      - /:/rootfs:ro
    command:
      - '--path.procfs=/host/proc'
      - '--path.rootfs=/rootfs'
      - '--path.sysfs=/host/sys'
      - '--collector.filesystem.mount-points-exclude=^/(sys|proc|dev|host|etc)($$|/)'
    networks:
      - all-agent-network

# 网络配置
networks:
  all-agent-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# 数据卷
volumes:
  all-agent-data:
    driver: local
  all-agent-reports:
    driver: local
  all-agent-logs:
    driver: local
  all-agent-backups:
    driver: local
  redis-data:
    driver: local
  postgres-data:
    driver: local
  prometheus-data:
    driver: local
  grafana-data:
    driver: local
  nginx-logs:
    driver: local
