#!/usr/bin/env node

/**
 * All-Agent 核心服务器
 * 提供 WebSocket 通信、Agent 管理、项目分析等核心功能
 */

const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const cors = require('cors');
const path = require('path');
const fs = require('fs').promises;

// 导入核心模块
const AgentManager = require('./core/AgentManager');
const ProjectAnalyzer = require('./core/ProjectAnalyzer');
const LLMService = require('./api/LLMService');
const CodeGenerator = require('./core/CodeGenerator');
const TaskScheduler = require('./core/TaskScheduler');
const Logger = require('./utils/Logger');
const Database = require('./database/DatabaseSimple');
const AuthService = require('./auth/AuthService');
const Cache = require('./utils/Cache');
const RedisCache = require('./utils/RedisCache');
const Monitor = require('./monitoring/Monitor');
const AlertManager = require('./monitoring/AlertManager');
const { setupSwagger } = require('./docs/swagger');
const TracingMiddleware = require('./middleware/tracing-simple');
const MLModelService = require('./services/MLModelService');
const FaaSService = require('./services/FaaSService');
const FrontierTechService = require('./services/FrontierTechServiceSimple');

// 导入中间件
const {
  authenticateToken,
  optionalAuth,
  requireUser,
  requireAdmin,
  rateLimit,
  corsMiddleware,
  errorHandler,
  requestLogger
} = require('./middleware/auth');

class AllAgentServer {
  constructor() {
    this.app = express();
    this.server = http.createServer(this.app);
    this.io = socketIo(this.server, {
      cors: {
        origin: '*',
        methods: ['GET', 'POST']
      }
    });

    this.port = process.env.PORT || 3000;
    this.projectRoot = process.cwd();

    // 初始化核心组件
    this.database = new Database();
    this.cache = null; // 将根据配置选择缓存类型
    this.logger = new Logger();
    this.monitor = new Monitor();
    this.alertManager = new AlertManager();
    this.authService = null; // 将在数据库初始化后创建
    this.agentManager = new AgentManager(this.llmService);
    this.projectAnalyzer = null; // 将在缓存初始化后创建
    this.llmService = new LLMService(this.logger);
    this.codeGenerator = new CodeGenerator();
    this.taskScheduler = new TaskScheduler();
    this.tracing = new TracingMiddleware({
      serviceName: 'all-agent-server',
      serviceVersion: '1.0.0',
      environment: process.env.NODE_ENV || 'development'
    });
    this.mlModelService = new MLModelService();
    this.faasService = new FaaSService();
    this.frontierTechService = new FrontierTechService({
      enableAIOps: true,
      enableEdgeAI: true,
      enableWeb3: process.env.ENABLE_WEB3 === 'true',
      enableQuantum: process.env.ENABLE_QUANTUM === 'true'
    });

    this.setupMiddleware();
    this.setupRoutes();
    this.setupWebSocket();
  }

  setupMiddleware() {
    // CORS 中间件
    this.app.use(corsMiddleware);

    // 分布式追踪中间件
    this.app.use(this.tracing.middleware());

    // 基础中间件
    this.app.use(express.json({ limit: '10mb' }));
    this.app.use(express.urlencoded({ extended: true }));

    // 请求日志中间件
    this.app.use(requestLogger(this.logger));

    // 全局速率限制
    this.app.use(
      rateLimit({
        windowMs: 15 * 60 * 1000, // 15分钟
        maxRequests: 1000, // 每个IP最多1000个请求
        message: '请求过于频繁，请稍后再试'
      })
    );

    // 静态文件服务
    this.app.use('/ui', express.static(path.join(__dirname, '../ui')));

    // Socket.IO 客户端库
    this.app.use('/socket.io', express.static(path.join(__dirname, '../node_modules/socket.io/client-dist')));
  }

  setupRoutes() {
    // 健康检查
    this.app.get('/health', async (req, res) => {
      const dbHealth = await this.database.healthCheck();
      const cacheStats = this.cache.getStats();

      res.json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        version: '1.0.0',
        database: dbHealth,
        cache: cacheStats,
        agents: this.agentManager.getAgentStatus()
      });
    });

    // 认证路由
    this.setupAuthRoutes();

    // API 路由（需要认证）
    this.setupAPIRoutes();

    // 监控路由
    this.setupMonitoringRoutes();

    // 公开路由
    this.setupPublicRoutes();

    // 错误处理中间件
    this.app.use(errorHandler);
  }

  /**
   * 设置认证路由
   */
  setupAuthRoutes() {
    // 用户注册
    this.app.post('/auth/register', rateLimit({ maxRequests: 5 }), async (req, res) => {
      try {
        const user = await this.authService.register(req.body);
        res.json({ success: true, data: user });
      } catch (error) {
        res.status(400).json({ success: false, error: error.message });
      }
    });

    // 用户登录
    this.app.post('/auth/login', rateLimit({ maxRequests: 10 }), async (req, res) => {
      try {
        const result = await this.authService.login(req.body);
        res.json({ success: true, data: result });
      } catch (error) {
        res.status(401).json({ success: false, error: error.message });
      }
    });

    // 刷新令牌
    this.app.post('/auth/refresh', async (req, res) => {
      try {
        const { token } = req.body;
        const result = await this.authService.refreshToken(token);
        res.json({ success: true, data: result });
      } catch (error) {
        res.status(401).json({ success: false, error: error.message });
      }
    });

    // 用户登出
    this.app.post('/auth/logout', authenticateToken(this.authService), async (req, res) => {
      try {
        await this.authService.logout(req.token);
        res.json({ success: true, message: '登出成功' });
      } catch (error) {
        res.status(500).json({ success: false, error: error.message });
      }
    });

    // 修改密码
    this.app.post('/auth/change-password', authenticateToken(this.authService), async (req, res) => {
      try {
        const { oldPassword, newPassword } = req.body;
        await this.authService.changePassword(req.user.id, oldPassword, newPassword);
        res.json({ success: true, message: '密码修改成功' });
      } catch (error) {
        res.status(400).json({ success: false, error: error.message });
      }
    });

    // 获取用户信息
    this.app.get('/auth/me', authenticateToken(this.authService), async (req, res) => {
      res.json({ success: true, data: req.user });
    });
  }

  /**
   * 设置监控路由
   */
  setupMonitoringRoutes() {
    // 监控数据 API（需要管理员权限）
    this.app.get('/api/monitor/metrics', authenticateToken(this.authService), requireAdmin, (req, res) => {
      try {
        const metrics = this.monitor.getMetrics();
        res.json({ success: true, data: metrics });
      } catch (error) {
        res.status(500).json({ success: false, error: error.message });
      }
    });

    // 告警历史 API
    this.app.get('/api/monitor/alerts', authenticateToken(this.authService), requireAdmin, (req, res) => {
      try {
        const limit = parseInt(req.query.limit) || 50;
        const alerts = this.alertManager.getAlertHistory(limit);
        res.json({ success: true, data: alerts });
      } catch (error) {
        res.status(500).json({ success: false, error: error.message });
      }
    });

    // 缓存统计 API
    this.app.get('/api/monitor/cache', authenticateToken(this.authService), requireAdmin, (req, res) => {
      try {
        const stats = this.cache.getStats();
        res.json({ success: true, data: stats });
      } catch (error) {
        res.status(500).json({ success: false, error: error.message });
      }
    });

    // 测试告警 API
    this.app.post('/api/monitor/test-alert', authenticateToken(this.authService), requireAdmin, async (req, res) => {
      try {
        const result = await this.alertManager.testAlert();
        res.json({ success: true, data: result });
      } catch (error) {
        res.status(500).json({ success: false, error: error.message });
      }
    });

    // 监控配置 API
    this.app.get('/api/monitor/config', authenticateToken(this.authService), requireAdmin, (req, res) => {
      try {
        const config = {
          monitor: this.monitor.options,
          alerts: this.alertManager.getConfigStatus()
        };
        res.json({ success: true, data: config });
      } catch (error) {
        res.status(500).json({ success: false, error: error.message });
      }
    });

    // 监控面板（简单的 HTML 页面）
    this.app.get('/monitor', (req, res) => {
      res.send(this.generateMonitoringDashboard());
    });
  }

  /**
   * 设置 API 路由（需要认证）
   */
  setupAPIRoutes() {
    // 项目分析 API
    this.app.post('/api/analyze', authenticateToken(this.authService), async (req, res) => {
      try {
        const { projectPath, options = {} } = req.body;
        const analysis = await this.projectAnalyzer.analyzeProject(projectPath || this.projectRoot, options);

        // 记录分析历史
        await this.recordAnalysis(req.user.id, analysis);

        res.json({ success: true, data: analysis });
      } catch (error) {
        this.logger.error('项目分析失败', error);
        res.status(500).json({
          success: false,
          error: error.message
        });
      }
    });

    // Agent 状态 API
    this.app.get('/api/agents', optionalAuth(this.authService), (req, res) => {
      res.json({
        success: true,
        data: this.agentManager.getAllAgents()
      });
    });

    // 任务提交 API
    this.app.post('/api/tasks', authenticateToken(this.authService), async (req, res) => {
      try {
        const { agentType, action, input, options = {} } = req.body;
        const taskId = await this.taskScheduler.scheduleTask({
          agentType,
          action,
          input,
          options,
          userId: req.user.id
        });

        // 记录任务到数据库
        await this.recordTask(req.user.id, taskId, { agentType, action, input, options });

        res.json({ success: true, taskId });
      } catch (error) {
        this.logger.error('任务调度失败', error);
        res.status(500).json({
          success: false,
          error: error.message
        });
      }
    });

    // 任务状态查询 API
    this.app.get('/api/tasks/:taskId', (req, res) => {
      const { taskId } = req.params;
      const task = this.taskScheduler.getTask(taskId);
      if (task) {
        res.json({ success: true, data: task });
      } else {
        res.status(404).json({
          success: false,
          error: '任务不存在'
        });
      }
    });

    // 代码生成 API
    this.app.post('/api/generate', async (req, res) => {
      try {
        const { template, parameters, context = {} } = req.body;
        const result = await this.codeGenerator.generate(template, parameters, context);
        res.json({ success: true, data: result });
      } catch (error) {
        this.logger.error('代码生成失败', error);
        res.status(500).json({
          success: false,
          error: error.message
        });
      }
    });

    // AI 模型测试接口
    this.app.post('/api/test-ai-model', async (req, res) => {
      try {
        const { action, config, prompt } = req.body;

        switch (action) {
          case 'test_connection':
            const connectionResult = await this.testAIConnection(config);
            res.json(connectionResult);
            break;

          case 'test_prompt':
            const promptResult = await this.testAIPrompt(config, prompt);
            res.json(promptResult);
            break;

          case 'check_environment':
            const envResult = await this.checkAIEnvironment();
            res.json(envResult);
            break;

          default:
            res.status(400).json({
              success: false,
              error: `不支持的测试操作: ${action}`
            });
        }
      } catch (error) {
        this.logger.error('AI 模型测试失败', error);
        res.status(500).json({
          success: false,
          error: error.message
        });
      }
    });

    // 获取项目配置
    this.app.get('/api/config', async (req, res) => {
      try {
        const configPath = path.join(this.projectRoot, '.all-agent/agents.json');
        const config = await fs.readFile(configPath, 'utf8');
        res.json({ success: true, data: JSON.parse(config) });
      } catch (error) {
        res.status(500).json({
          success: false,
          error: '配置文件读取失败'
        });
      }
    });

    // ML 模型管理路由
    this.setupMLRoutes();

    // FaaS 管理路由
    this.setupFaaSRoutes();

    // 前沿技术路由
    this.setupFrontierTechRoutes();

    // 404 处理
    this.app.use('*', (req, res) => {
      res.status(404).json({
        success: false,
        error: '接口不存在'
      });
    });
  }

  /**
   * 设置 ML 模型管理路由
   */
  setupMLRoutes() {
    // 创建实验
    this.app.post('/api/ml/experiments', authenticateToken(this.authService), async (req, res) => {
      try {
        const { name, description, tags } = req.body;
        const experiment = await this.mlModelService.createExperiment(name, description, tags);
        res.json({ success: true, data: experiment });
      } catch (error) {
        res.status(500).json({ success: false, error: error.message });
      }
    });

    // 训练模型
    this.app.post('/api/ml/train', authenticateToken(this.authService), async (req, res) => {
      try {
        const { experimentName, modelConfig } = req.body;
        const result = await this.mlModelService.trainModel(experimentName, modelConfig);
        res.json({ success: true, data: result });
      } catch (error) {
        res.status(500).json({ success: false, error: error.message });
      }
    });

    // 获取模型列表
    this.app.get('/api/ml/models', authenticateToken(this.authService), async (req, res) => {
      try {
        const models = await this.mlModelService.getModels();
        res.json({ success: true, data: models });
      } catch (error) {
        res.status(500).json({ success: false, error: error.message });
      }
    });

    // 部署模型
    this.app.post('/api/ml/deploy', authenticateToken(this.authService), async (req, res) => {
      try {
        const { modelName, version, deploymentName, targetUri } = req.body;
        const deployment = await this.mlModelService.deployModel(modelName, version, deploymentName, targetUri);
        res.json({ success: true, data: deployment });
      } catch (error) {
        res.status(500).json({ success: false, error: error.message });
      }
    });

    // 获取 ML 服务状态
    this.app.get('/api/ml/status', authenticateToken(this.authService), (req, res) => {
      try {
        const status = this.mlModelService.getStatus();
        res.json({ success: true, data: status });
      } catch (error) {
        res.status(500).json({ success: false, error: error.message });
      }
    });
  }

  /**
   * 设置 FaaS 管理路由
   */
  setupFaaSRoutes() {
    // 创建函数
    this.app.post('/api/faas/functions', authenticateToken(this.authService), async (req, res) => {
      try {
        const functionConfig = req.body;
        const func = await this.faasService.createFunction(functionConfig);
        res.json({ success: true, data: func });
      } catch (error) {
        res.status(500).json({ success: false, error: error.message });
      }
    });

    // 调用函数
    this.app.post('/api/faas/invoke/:name', authenticateToken(this.authService), async (req, res) => {
      try {
        const { name } = req.params;
        const { payload, options } = req.body;
        const result = await this.faasService.invokeFunction(name, payload, options);
        res.json(result);
      } catch (error) {
        res.status(500).json({ success: false, error: error.message });
      }
    });

    // 获取函数列表
    this.app.get('/api/faas/functions', authenticateToken(this.authService), (req, res) => {
      try {
        const functions = this.faasService.getFunctions();
        res.json({ success: true, data: functions });
      } catch (error) {
        res.status(500).json({ success: false, error: error.message });
      }
    });

    // 获取函数详情
    this.app.get('/api/faas/functions/:name', authenticateToken(this.authService), (req, res) => {
      try {
        const { name } = req.params;
        const func = this.faasService.getFunction(name);
        if (func) {
          res.json({ success: true, data: func });
        } else {
          res.status(404).json({ success: false, error: 'Function not found' });
        }
      } catch (error) {
        res.status(500).json({ success: false, error: error.message });
      }
    });

    // 更新函数
    this.app.put('/api/faas/functions/:name', authenticateToken(this.authService), async (req, res) => {
      try {
        const { name } = req.params;
        const updates = req.body;
        const func = await this.faasService.updateFunction(name, updates);
        res.json({ success: true, data: func });
      } catch (error) {
        res.status(500).json({ success: false, error: error.message });
      }
    });

    // 删除函数
    this.app.delete('/api/faas/functions/:name', authenticateToken(this.authService), async (req, res) => {
      try {
        const { name } = req.params;
        const result = await this.faasService.deleteFunction(name);
        res.json(result);
      } catch (error) {
        res.status(500).json({ success: false, error: error.message });
      }
    });

    // 获取函数指标
    this.app.get('/api/faas/functions/:name/metrics', authenticateToken(this.authService), async (req, res) => {
      try {
        const { name } = req.params;
        const metrics = await this.faasService.getFunctionMetrics(name);
        res.json({ success: true, data: metrics });
      } catch (error) {
        res.status(500).json({ success: false, error: error.message });
      }
    });

    // 获取 FaaS 服务状态
    this.app.get('/api/faas/status', authenticateToken(this.authService), (req, res) => {
      try {
        const status = this.faasService.getStatus();
        res.json({ success: true, data: status });
      } catch (error) {
        res.status(500).json({ success: false, error: error.message });
      }
    });
  }

  /**
   * 设置前沿技术路由
   */
  setupFrontierTechRoutes() {
    // 获取前沿技术状态
    this.app.get('/api/frontier/status', authenticateToken(this.authService), (req, res) => {
      try {
        const status = this.frontierTechService.getStatus();
        res.json({ success: true, data: status });
      } catch (error) {
        res.status(500).json({ success: false, error: error.message });
      }
    });

    // 获取技术能力矩阵
    this.app.get('/api/frontier/capabilities', authenticateToken(this.authService), (req, res) => {
      try {
        const capabilities = this.frontierTechService.getCapabilityMatrix();
        res.json({ success: true, data: capabilities });
      } catch (error) {
        res.status(500).json({ success: false, error: error.message });
      }
    });

    // 执行集成
    this.app.post('/api/frontier/integrations/:name', authenticateToken(this.authService), async (req, res) => {
      try {
        const { name } = req.params;
        const data = req.body;
        const result = await this.frontierTechService.executeIntegration(name, data);
        res.json({ success: true, data: result });
      } catch (error) {
        res.status(500).json({ success: false, error: error.message });
      }
    });

    // 执行工作流
    this.app.post('/api/frontier/workflows/:name', authenticateToken(this.authService), async (req, res) => {
      try {
        const { name } = req.params;
        const data = req.body;
        const result = await this.frontierTechService.executeWorkflow(name, data);
        res.json({ success: true, data: result });
      } catch (error) {
        res.status(500).json({ success: false, error: error.message });
      }
    });

    // AIOps 相关路由
    this.app.get('/api/frontier/aiops/metrics', authenticateToken(this.authService), async (req, res) => {
      try {
        if (!this.frontierTechService.services.aiops) {
          return res.status(404).json({ success: false, error: 'AIOps service not enabled' });
        }
        const metrics = await this.frontierTechService.services.aiops.collectMetrics();
        res.json({ success: true, data: metrics });
      } catch (error) {
        res.status(500).json({ success: false, error: error.message });
      }
    });

    this.app.post('/api/frontier/aiops/predict', authenticateToken(this.authService), async (req, res) => {
      try {
        if (!this.frontierTechService.services.aiops) {
          return res.status(404).json({ success: false, error: 'AIOps service not enabled' });
        }
        await this.frontierTechService.services.aiops.predictFailures();
        res.json({ success: true, message: 'Failure prediction completed' });
      } catch (error) {
        res.status(500).json({ success: false, error: error.message });
      }
    });

    // 边缘 AI 相关路由
    this.app.post('/api/frontier/edge-ai/optimize', authenticateToken(this.authService), async (req, res) => {
      try {
        if (!this.frontierTechService.services.edgeAI) {
          return res.status(404).json({ success: false, error: 'Edge AI service not enabled' });
        }
        const { modelPath, config } = req.body;
        const result = await this.frontierTechService.services.edgeAI.optimizeModel(modelPath, config);
        res.json({ success: true, data: result });
      } catch (error) {
        res.status(500).json({ success: false, error: error.message });
      }
    });

    this.app.post('/api/frontier/edge-ai/auto-optimize', authenticateToken(this.authService), async (req, res) => {
      try {
        if (!this.frontierTechService.services.edgeAI) {
          return res.status(404).json({ success: false, error: 'Edge AI service not enabled' });
        }
        const { modelPath, constraints } = req.body;
        const result = await this.frontierTechService.services.edgeAI.autoOptimize(modelPath, constraints);
        res.json({ success: true, data: result });
      } catch (error) {
        res.status(500).json({ success: false, error: error.message });
      }
    });

    // Web3 相关路由
    this.app.post('/api/frontier/web3/store-model', authenticateToken(this.authService), async (req, res) => {
      try {
        if (!this.frontierTechService.services.web3) {
          return res.status(404).json({ success: false, error: 'Web3 service not enabled' });
        }
        const { modelData, metadata } = req.body;
        const result = await this.frontierTechService.services.web3.storeAIModel(modelData, metadata);
        res.json({ success: true, data: result });
      } catch (error) {
        res.status(500).json({ success: false, error: error.message });
      }
    });

    this.app.post('/api/frontier/web3/mint-nft', authenticateToken(this.authService), async (req, res) => {
      try {
        if (!this.frontierTechService.services.web3) {
          return res.status(404).json({ success: false, error: 'Web3 service not enabled' });
        }
        const { modelHash, recipient, metadata } = req.body;
        const result = await this.frontierTechService.services.web3.mintAIModelNFT(modelHash, recipient, metadata);
        res.json({ success: true, data: result });
      } catch (error) {
        res.status(500).json({ success: false, error: error.message });
      }
    });

    this.app.post('/api/frontier/web3/create-did', authenticateToken(this.authService), async (req, res) => {
      try {
        if (!this.frontierTechService.services.web3) {
          return res.status(404).json({ success: false, error: 'Web3 service not enabled' });
        }
        const identity = req.body;
        const result = await this.frontierTechService.services.web3.createDID(identity);
        res.json({ success: true, data: result });
      } catch (error) {
        res.status(500).json({ success: false, error: error.message });
      }
    });

    // 量子计算相关路由
    this.app.post('/api/frontier/quantum/grover-search', authenticateToken(this.authService), async (req, res) => {
      try {
        if (!this.frontierTechService.services.quantum) {
          return res.status(404).json({ success: false, error: 'Quantum service not enabled' });
        }
        const { searchSpace, targetItem } = req.body;
        const result = await this.frontierTechService.services.quantum.groverSearch(searchSpace, targetItem);
        res.json({ success: true, data: result });
      } catch (error) {
        res.status(500).json({ success: false, error: error.message });
      }
    });

    this.app.post('/api/frontier/quantum/qaoa', authenticateToken(this.authService), async (req, res) => {
      try {
        if (!this.frontierTechService.services.quantum) {
          return res.status(404).json({ success: false, error: 'Quantum service not enabled' });
        }
        const { costFunction, numLayers } = req.body;
        const result = await this.frontierTechService.services.quantum.quantumApproximateOptimization(
          costFunction,
          numLayers
        );
        res.json({ success: true, data: result });
      } catch (error) {
        res.status(500).json({ success: false, error: error.message });
      }
    });

    this.app.post('/api/frontier/quantum/qml', authenticateToken(this.authService), async (req, res) => {
      try {
        if (!this.frontierTechService.services.quantum) {
          return res.status(404).json({ success: false, error: 'Quantum service not enabled' });
        }
        const { trainingData, labels, algorithm } = req.body;
        const result = await this.frontierTechService.services.quantum.quantumMachineLearning(
          trainingData,
          labels,
          algorithm
        );
        res.json({ success: true, data: result });
      } catch (error) {
        res.status(500).json({ success: false, error: error.message });
      }
    });

    // 获取集成和工作流列表
    this.app.get('/api/frontier/integrations', authenticateToken(this.authService), (req, res) => {
      try {
        const integrations = this.frontierTechService.getIntegrations();
        res.json({ success: true, data: integrations });
      } catch (error) {
        res.status(500).json({ success: false, error: error.message });
      }
    });

    this.app.get('/api/frontier/workflows', authenticateToken(this.authService), (req, res) => {
      try {
        const workflows = this.frontierTechService.getWorkflows();
        res.json({ success: true, data: workflows });
      } catch (error) {
        res.status(500).json({ success: false, error: error.message });
      }
    });
  }

  /**
   * 设置公开路由（无需认证）
   */
  setupPublicRoutes() {
    // 根路径重定向到 UI
    this.app.get('/', (req, res) => {
      res.redirect('/ui/chat_panel.html');
    });

    // API 文档
    this.app.get('/docs', (req, res) => {
      res.redirect('/api-docs');
    });

    // 系统信息
    this.app.get('/info', (req, res) => {
      res.json({
        name: 'All-Agent Server',
        version: '1.0.0',
        description: 'AI 项目构建与执行系统',
        features: [
          'Agent Management',
          'Project Analysis',
          'Code Generation',
          'Task Scheduling',
          'Real-time Communication',
          'AIOps Intelligence',
          'Edge AI Optimization',
          'Web3 Integration',
          'Quantum Computing'
        ],
        endpoints: {
          health: '/health',
          auth: '/auth/*',
          api: '/api/*',
          ui: '/ui/*',
          docs: '/api-docs'
        }
      });
    });

    // 功能演示页面
    this.app.get('/demo', (req, res) => {
      res.send(this.generateDemoPage());
    });
  }

  /**
   * 生成演示页面
   */
  generateDemoPage() {
    return `
        <!DOCTYPE html>
        <html lang="zh-CN">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>All-Agent 功能演示</title>
            <style>
                body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
                .container { max-width: 1200px; margin: 0 auto; }
                .header { text-align: center; margin-bottom: 40px; }
                .feature-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }
                .feature-card { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
                .feature-title { font-size: 18px; font-weight: bold; margin-bottom: 10px; color: #333; }
                .feature-desc { color: #666; margin-bottom: 15px; }
                .feature-link { display: inline-block; padding: 8px 16px; background: #007bff; color: white; text-decoration: none; border-radius: 4px; }
                .feature-link:hover { background: #0056b3; }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>🚀 All-Agent 功能演示</h1>
                    <p>下一代 AI 开发平台 - 集成前沿技术的完整解决方案</p>
                </div>

                <div class="feature-grid">
                    <div class="feature-card">
                        <div class="feature-title">💬 智能聊天面板</div>
                        <div class="feature-desc">与 AI Agent 实时交互，支持多种任务类型</div>
                        <a href="/ui/chat_panel.html" class="feature-link">立即体验</a>
                    </div>

                    <div class="feature-card">
                        <div class="feature-title">🔍 Agent 追踪</div>
                        <div class="feature-desc">实时监控 Agent 执行状态和任务进度</div>
                        <a href="/ui/agent_trace.html" class="feature-link">查看追踪</a>
                    </div>

                    <div class="feature-card">
                        <div class="feature-title">🏗️ 项目结构视图</div>
                        <div class="feature-desc">可视化项目结构和依赖关系</div>
                        <a href="/ui/structure_view.html" class="feature-link">查看结构</a>
                    </div>

                    <div class="feature-card">
                        <div class="feature-title">📊 系统监控</div>
                        <div class="feature-desc">实时系统性能监控和告警</div>
                        <a href="/monitor" class="feature-link">查看监控</a>
                    </div>

                    <div class="feature-card">
                        <div class="feature-title">📚 API 文档</div>
                        <div class="feature-desc">完整的 RESTful API 文档和测试界面</div>
                        <a href="/api-docs" class="feature-link">查看文档</a>
                    </div>

                    <div class="feature-card">
                        <div class="feature-title">🔐 用户认证</div>
                        <div class="feature-desc">安全的用户注册和登录系统</div>
                        <a href="/ui/login.html" class="feature-link">登录系统</a>
                    </div>
                </div>

                <div style="margin-top: 40px; text-align: center; color: #666;">
                    <p>🌟 前沿技术特性：AIOps 智能运维 | 边缘 AI 优化 | Web3 区块链 | 量子计算</p>
                    <p>📖 更多信息请查看 <a href="/info" style="color: #007bff;">系统信息</a></p>
                </div>
            </div>
        </body>
        </html>
        `;
  }

  setupWebSocket() {
    // WebSocket 认证中间件 - 简化版本
    this.io.use(async (socket, next) => {
      try {
        const token = socket.handshake.auth.token;
        if (token && this.authService) {
          try {
            const user = await this.authService.verifyToken(token);
            socket.user = user;
            console.log(`✅ WebSocket 认证成功: ${user.username}`);
          } catch (authError) {
            console.warn('⚠️ WebSocket 认证失败，允许匿名连接:', authError.message);
            socket.user = null;
          }
        } else {
          // 允许匿名连接，但功能受限
          socket.user = null;
          console.log('🔓 WebSocket 匿名连接');
        }
        next();
      } catch (error) {
        console.error('❌ WebSocket 中间件错误:', error);
        // 即使出错也允许连接，但设为匿名用户
        socket.user = null;
        next();
      }
    });

    this.io.on('connection', (socket) => {
      const userInfo = socket.user ? `用户 ${socket.user.username}` : '匿名用户';
      this.logger.info('客户端连接', {
        socketId: socket.id,
        user: userInfo
      });

      // 加入房间
      socket.on('join', (data) => {
        const { room = 'default' } = data;
        socket.join(room);
        socket.emit('joined', { room, socketId: socket.id });
      });

      // 聊天消息处理
      socket.on('chat_message', async (data) => {
        try {
          // 检查是否需要认证
          if (!socket.user) {
            socket.emit('error', { message: '需要登录才能使用聊天功能' });
            return;
          }

          const { agentType, message, context = {} } = data;

          // 记录聊天消息
          await this.recordChatMessage(socket.user.id, agentType, 'user', message, context);

          // 发送消息给对应的 Agent
          const response = await this.agentManager.sendMessage(agentType, message, {
            ...context,
            userId: socket.user.id,
            username: socket.user.username
          });

          // 记录 Agent 响应
          await this.recordChatMessage(socket.user.id, agentType, 'agent', response, context);

          // 返回 Agent 响应
          socket.emit('agent_response', {
            agentType,
            message: response,
            timestamp: new Date().toISOString()
          });
        } catch (error) {
          this.logger.error('聊天消息处理失败', error);
          socket.emit('error', {
            message: '消息处理失败',
            error: error.message
          });
        }
      });

      // 任务状态订阅
      socket.on('subscribe_task', (data) => {
        const { taskId } = data;
        socket.join(`task_${taskId}`);

        // 发送当前任务状态
        const task = this.taskScheduler.getTask(taskId);
        if (task) {
          socket.emit('task_update', task);
        }
      });

      // 项目分析请求
      socket.on('analyze_project', async (data) => {
        try {
          const { projectPath, options = {} } = data;

          // 发送分析开始通知
          socket.emit('analysis_started', {
            message: '开始分析项目...'
          });

          const analysis = await this.projectAnalyzer.analyzeProject(
            projectPath || this.projectRoot,
            options,
            (progress) => {
              // 发送进度更新
              socket.emit('analysis_progress', progress);
            }
          );

          // 发送分析结果
          socket.emit('analysis_complete', analysis);
        } catch (error) {
          this.logger.error('项目分析失败', error);
          socket.emit('analysis_error', {
            error: error.message
          });
        }
      });

      // 断开连接处理
      socket.on('disconnect', () => {
        this.logger.info('客户端断开连接', { socketId: socket.id });
      });
    });

    // 任务状态变更广播
    this.taskScheduler.on('task_update', (task) => {
      this.io.to(`task_${task.id}`).emit('task_update', task);
    });

    // Agent 状态变更广播
    this.agentManager.on('agent_status_change', (agentStatus) => {
      this.io.emit('agent_status_update', agentStatus);
    });
  }

  async start() {
    try {
      console.log('🚀 正在启动 All-Agent 服务器...');

      // 1. 初始化数据库
      console.log('📊 初始化数据库...');
      await this.database.initialize();

      // 2. 初始化缓存系统
      console.log('💾 初始化缓存系统...');
      await this.initializeCache();

      // 3. 初始化认证服务
      console.log('🔐 初始化认证服务...');
      this.authService = new AuthService(this.database);

      // 4. 初始化项目分析器（带缓存支持）
      console.log('🔍 初始化项目分析器...');
      this.projectAnalyzer = new ProjectAnalyzer(this.cache);

      // 5. 初始化其他组件
      console.log('🤖 初始化 Agent 管理器...');
      await this.agentManager.initialize();

      console.log('🧠 初始化 LLM 服务...');
      await this.llmService.initialize();

      console.log('⚡ 初始化代码生成器...');
      await this.codeGenerator.initialize();

      // 6. 创建默认管理员用户（如果不存在）
      await this.createDefaultAdmin();

      // 7. 初始化监控系统
      console.log('📊 初始化监控系统...');
      this.initializeMonitoring();

      // 8. 设置 API 文档
      console.log('📚 设置 API 文档...');
      setupSwagger(this.app);

      // 9. 启动定时任务
      this.startScheduledTasks();

      // 8. 启动服务器
      this.server.listen(this.port, () => {
        this.logger.info(`All-Agent 服务器启动成功`, {
          port: this.port,
          env: process.env.NODE_ENV || 'development'
        });

        console.log(`
🎉 All-Agent Server Started Successfully!

📡 HTTP Server: http://localhost:${this.port}
🔌 WebSocket: ws://localhost:${this.port}
🎨 Chat Panel: http://localhost:${this.port}/ui/chat_panel.html
📊 Agent Trace: http://localhost:${this.port}/ui/agent_trace.html
🗺️ Structure View: http://localhost:${this.port}/ui/structure_view.html

🔐 Authentication Endpoints:
   POST /auth/register - 用户注册
   POST /auth/login - 用户登录
   GET  /auth/me - 获取用户信息

📋 API Endpoints:
   GET  /health - 健康检查
   POST /api/analyze - 项目分析
   POST /api/tasks - 任务提交
   POST /api/generate - 代码生成

💡 Default Admin: <EMAIL> / admin123
                `);
      });
    } catch (error) {
      this.logger.error('服务器启动失败', error);
      console.error('❌ 服务器启动失败:', error.message);
      process.exit(1);
    }
  }

  /**
   * 初始化缓存系统
   */
  async initializeCache() {
    const useRedis = process.env.USE_REDIS === 'true' || process.env.REDIS_HOST;

    if (useRedis) {
      console.log('🔴 使用 Redis 缓存');
      this.cache = new RedisCache({
        host: process.env.REDIS_HOST,
        port: process.env.REDIS_PORT,
        password: process.env.REDIS_PASSWORD,
        db: process.env.REDIS_DB,
        defaultTTL: 3600,
        maxMemoryItems: 1000
      });

      try {
        await this.cache.connect();
      } catch (error) {
        console.warn('⚠️ Redis 连接失败，回退到内存缓存');
        this.cache = new Cache();
      }
    } else {
      console.log('💾 使用内存缓存');
      this.cache = new Cache();
    }

    // 只有内存缓存才有 setConfig 方法
    if (this.cache.setConfig) {
      this.cache.setConfig({
        defaultTTL: 3600,
        maxMemoryItems: 1000
      });
    }
  }

  /**
   * 初始化监控系统
   */
  initializeMonitoring() {
    // 配置监控选项
    const monitorOptions = {
      interval: parseInt(process.env.MONITOR_INTERVAL) || 30000,
      alertThresholds: {
        cpuUsage: parseInt(process.env.CPU_THRESHOLD) || 80,
        memoryUsage: parseInt(process.env.MEMORY_THRESHOLD) || 85,
        diskUsage: parseInt(process.env.DISK_THRESHOLD) || 90,
        errorRate: parseInt(process.env.ERROR_THRESHOLD) || 5,
        responseTime: parseInt(process.env.RESPONSE_THRESHOLD) || 5000
      }
    };

    this.monitor = new Monitor(monitorOptions);

    // 配置告警管理器
    const alertOptions = {
      email: {
        enabled: process.env.EMAIL_ALERTS_ENABLED === 'true',
        smtp: {
          host: process.env.EMAIL_SMTP_HOST,
          port: process.env.EMAIL_SMTP_PORT,
          user: process.env.EMAIL_USERNAME,
          pass: process.env.EMAIL_PASSWORD
        },
        from: process.env.EMAIL_FROM,
        to: process.env.EMAIL_ALERT_TO ? process.env.EMAIL_ALERT_TO.split(',') : []
      },
      slack: {
        enabled: process.env.SLACK_ALERTS_ENABLED === 'true',
        webhookUrl: process.env.SLACK_WEBHOOK_URL,
        channel: process.env.SLACK_CHANNEL || '#alerts'
      },
      webhook: {
        enabled: process.env.WEBHOOK_ALERTS_ENABLED === 'true',
        url: process.env.ALERT_WEBHOOK_URL
      }
    };

    this.alertManager = new AlertManager(alertOptions);

    // 监听告警事件
    this.monitor.on('alert', (alert) => {
      this.alertManager.sendAlert(alert);
    });

    // 启动监控
    this.monitor.start();

    // 添加监控中间件
    this.app.use((req, res, next) => {
      const start = Date.now();

      res.on('finish', () => {
        const responseTime = Date.now() - start;
        this.monitor.recordRequest(req, res, responseTime);
      });

      next();
    });

    console.log('✅ 监控系统初始化完成');
  }

  /**
   * 创建默认管理员用户
   */
  async createDefaultAdmin() {
    try {
      const adminEmail = '<EMAIL>';
      const existingAdmin = await this.database.get('SELECT id FROM users WHERE email = ?', [adminEmail]);

      if (!existingAdmin) {
        await this.authService.register({
          username: 'admin',
          email: adminEmail,
          password: 'admin123',
          role: 'admin'
        });
        console.log('✅ 默认管理员用户已创建');
      }
    } catch (error) {
      console.warn('⚠️ 创建默认管理员失败:', error.message);
    }
  }

  /**
   * 启动定时任务
   */
  startScheduledTasks() {
    // 每小时清理过期数据
    setInterval(async () => {
      try {
        await this.database.cleanup();
        await this.authService.cleanupExpiredSessions();
      } catch (error) {
        this.logger.error('定时清理失败', error);
      }
    }, 3600000); // 1小时

    // 每天备份数据库
    if (process.env.BACKUP_ENABLED === 'true') {
      setInterval(async () => {
        try {
          const backupPath = path.join(
            process.cwd(),
            '.all-agent/backups',
            `backup-${new Date().toISOString().split('T')[0]}.db`
          );
          await this.database.backup(backupPath);
        } catch (error) {
          this.logger.error('数据库备份失败', error);
        }
      }, 86400000); // 24小时
    }
  }

  /**
   * 记录分析历史
   */
  async recordAnalysis(userId, analysis) {
    try {
      // 这里可以记录分析历史到数据库
      // 暂时简化处理
      this.logger.info('项目分析完成', {
        userId,
        projectPath: analysis.projectPath,
        analysisTime: analysis.metadata.analysisTime
      });
    } catch (error) {
      this.logger.error('记录分析历史失败', error);
    }
  }

  /**
   * 记录任务
   */
  async recordTask(userId, taskId, taskData) {
    try {
      await this.database.run(
        `INSERT INTO tasks (task_id, user_id, agent_type, action, input_data, status, created_at)
                 VALUES (?, ?, ?, ?, ?, 'pending', datetime('now'))`,
        [taskId, userId, taskData.agentType, taskData.action, JSON.stringify(taskData.input)]
      );
    } catch (error) {
      this.logger.error('记录任务失败', error);
    }
  }

  /**
   * 记录聊天消息
   */
  async recordChatMessage(userId, agentType, messageType, content, context) {
    try {
      await this.database.run(
        `INSERT INTO chat_messages (user_id, agent_type, message_type, content, context_data, created_at)
                 VALUES (?, ?, ?, ?, ?, datetime('now'))`,
        [userId, agentType, messageType, content, JSON.stringify(context)]
      );
    } catch (error) {
      this.logger.error('记录聊天消息失败', error);
    }
  }

  /**
   * 测试 AI 连接
   */
  async testAIConnection(config) {
    try {
      // 创建临时的 LLM 服务实例
      const tempLLMService = this.createTempLLMService(config);

      // 测试简单的连接
      const testPrompt = 'Hello';
      const response = await tempLLMService.generateResponse(testPrompt, {
        maxTokens: 10,
        temperature: 0.1
      });

      return {
        success: true,
        message: '连接测试成功',
        provider: config.provider,
        model: config.model,
        responseLength: response.length
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        provider: config.provider,
        model: config.model
      };
    }
  }

  /**
   * 测试 AI 提示
   */
  async testAIPrompt(config, prompt) {
    try {
      // 创建临时的 LLM 服务实例
      const tempLLMService = this.createTempLLMService(config);

      // 发送提示并获取响应
      const response = await tempLLMService.generateResponse(prompt, {
        maxTokens: 500,
        temperature: 0.7
      });

      return {
        success: true,
        response: response,
        provider: config.provider,
        model: config.model,
        promptLength: prompt.length,
        responseLength: response.length
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        provider: config.provider,
        model: config.model,
        promptLength: prompt.length
      };
    }
  }

  /**
   * 检查 AI 环境
   */
  async checkAIEnvironment() {
    try {
      const providers = ['openai', 'anthropic', 'azure'];
      const defaultConfig = {
        provider: this.llmService.currentProvider || 'openai',
        model: this.llmService.currentModel || 'gpt-3.5-turbo',
        hasApiKey: !!process.env.OPENAI_API_KEY || !!process.env.ANTHROPIC_API_KEY
      };

      return {
        success: true,
        providers: providers,
        defaultConfig: defaultConfig,
        environment: {
          nodeVersion: process.version,
          platform: process.platform,
          arch: process.arch
        }
      };
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 创建临时 LLM 服务
   */
  createTempLLMService(config) {
    // 简化的 LLM 服务，直接使用配置进行测试
    return {
      generateResponse: async (prompt, options = {}) => {
        if (config.provider === 'openai') {
          return await this.testOpenAI(config, prompt, options);
        } else if (config.provider === 'anthropic') {
          return await this.testAnthropic(config, prompt, options);
        } else {
          throw new Error(`不支持的 AI 提供商: ${config.provider}`);
        }
      }
    };
  }

  /**
   * 测试 OpenAI
   */
  async testOpenAI(config, prompt, options) {
    const fetch = require('node-fetch');

    const baseUrl = config.baseUrl || 'https://api.openai.com/v1';
    const url = `${baseUrl}/chat/completions`;

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${config.apiKey}`
      },
      body: JSON.stringify({
        model: config.model || 'gpt-3.5-turbo',
        messages: [{ role: 'user', content: prompt }],
        max_tokens: options.maxTokens || 100,
        temperature: options.temperature || 0.7
      })
    });

    if (!response.ok) {
      const error = await response.text();
      throw new Error(`OpenAI API 错误: ${response.status} - ${error}`);
    }

    const data = await response.json();
    return data.choices[0].message.content;
  }

  /**
   * 测试 Anthropic
   */
  async testAnthropic(config, prompt, options) {
    const fetch = require('node-fetch');

    const url = 'https://api.anthropic.com/v1/messages';

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': config.apiKey,
        'anthropic-version': '2023-06-01'
      },
      body: JSON.stringify({
        model: config.model || 'claude-3-sonnet-20240229',
        max_tokens: options.maxTokens || 100,
        messages: [{ role: 'user', content: prompt }]
      })
    });

    if (!response.ok) {
      const error = await response.text();
      throw new Error(`Anthropic API 错误: ${response.status} - ${error}`);
    }

    const data = await response.json();
    return data.content[0].text;
  }

  /**
   * 生成监控面板
   */
  generateMonitoringDashboard() {
    return `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>All-Agent 监控面板</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; border-radius: 8px; margin-bottom: 20px; }
        .header h1 { margin: 0; font-size: 28px; }
        .grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }
        .card { background: white; border-radius: 8px; padding: 20px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .card h3 { margin: 0 0 15px 0; color: #333; }
        .metric { display: flex; justify-content: space-between; align-items: center; padding: 10px 0; border-bottom: 1px solid #eee; }
        .metric:last-child { border-bottom: none; }
        .metric-value { font-weight: bold; color: #667eea; }
        .status-good { color: #28a745; }
        .status-warning { color: #ffc107; }
        .status-critical { color: #dc3545; }
        .refresh-btn { background: #667eea; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer; margin: 10px 0; }
        .refresh-btn:hover { background: #5a6fd8; }
        .log { background: #f8f9fa; padding: 15px; border-radius: 4px; font-family: monospace; font-size: 12px; max-height: 200px; overflow-y: auto; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 All-Agent 监控面板</h1>
            <p>实时系统监控和告警管理</p>
        </div>

        <div class="grid">
            <div class="card">
                <h3>🖥️ 系统状态</h3>
                <div id="system-metrics">
                    <div class="metric">
                        <span>CPU 使用率</span>
                        <span class="metric-value" id="cpu-usage">加载中...</span>
                    </div>
                    <div class="metric">
                        <span>内存使用率</span>
                        <span class="metric-value" id="memory-usage">加载中...</span>
                    </div>
                    <div class="metric">
                        <span>系统运行时间</span>
                        <span class="metric-value" id="uptime">加载中...</span>
                    </div>
                </div>
            </div>

            <div class="card">
                <h3>🌐 应用状态</h3>
                <div id="app-metrics">
                    <div class="metric">
                        <span>总请求数</span>
                        <span class="metric-value" id="total-requests">加载中...</span>
                    </div>
                    <div class="metric">
                        <span>错误率</span>
                        <span class="metric-value" id="error-rate">加载中...</span>
                    </div>
                    <div class="metric">
                        <span>活跃连接</span>
                        <span class="metric-value" id="active-connections">加载中...</span>
                    </div>
                </div>
            </div>

            <div class="card">
                <h3>💾 缓存状态</h3>
                <div id="cache-metrics">
                    <div class="metric">
                        <span>命中率</span>
                        <span class="metric-value" id="cache-hit-rate">加载中...</span>
                    </div>
                    <div class="metric">
                        <span>缓存项数</span>
                        <span class="metric-value" id="cache-items">加载中...</span>
                    </div>
                    <div class="metric">
                        <span>缓存类型</span>
                        <span class="metric-value" id="cache-type">加载中...</span>
                    </div>
                </div>
            </div>

            <div class="card">
                <h3>🚨 最近告警</h3>
                <div id="recent-alerts">
                    <div class="log">加载中...</div>
                </div>
                <button class="refresh-btn" onclick="testAlert()">发送测试告警</button>
            </div>
        </div>

        <div style="text-align: center; margin-top: 20px;">
            <button class="refresh-btn" onclick="refreshData()">刷新数据</button>
            <button class="refresh-btn" onclick="window.open('/api-docs', '_blank')">API 文档</button>
        </div>
    </div>

    <script>
        async function fetchMetrics() {
            try {
                const response = await fetch('/api/monitor/metrics');
                const data = await response.json();

                if (data.success) {
                    updateSystemMetrics(data.data.system);
                    updateAppMetrics(data.data.application);
                }
            } catch (error) {
                console.error('获取指标失败:', error);
            }
        }

        async function fetchCacheStats() {
            try {
                const response = await fetch('/api/monitor/cache');
                const data = await response.json();

                if (data.success) {
                    updateCacheMetrics(data.data);
                }
            } catch (error) {
                console.error('获取缓存统计失败:', error);
            }
        }

        async function fetchAlerts() {
            try {
                const response = await fetch('/api/monitor/alerts?limit=10');
                const data = await response.json();

                if (data.success) {
                    updateAlerts(data.data);
                }
            } catch (error) {
                console.error('获取告警失败:', error);
            }
        }

        function updateSystemMetrics(metrics) {
            if (metrics && metrics.length > 0) {
                const latest = metrics[metrics.length - 1];
                document.getElementById('cpu-usage').textContent = latest.cpu?.usage?.toFixed(1) + '%' || 'N/A';
                document.getElementById('memory-usage').textContent = latest.memory?.usage?.toFixed(1) + '%' || 'N/A';
                document.getElementById('uptime').textContent = formatUptime(latest.uptime) || 'N/A';
            }
        }

        function updateAppMetrics(metrics) {
            if (metrics) {
                document.getElementById('total-requests').textContent = metrics.requests?.total || 0;
                document.getElementById('error-rate').textContent = (metrics.errors?.rate || 0).toFixed(1) + '%';
                document.getElementById('active-connections').textContent = metrics.activeConnections || 0;
            }
        }

        function updateCacheMetrics(metrics) {
            if (metrics) {
                document.getElementById('cache-hit-rate').textContent = metrics.hitRate || '0%';
                document.getElementById('cache-items').textContent = metrics.memoryItems || 0;
                document.getElementById('cache-type').textContent = metrics.isConnected ? 'Redis' : '内存';
            }
        }

        function updateAlerts(alerts) {
            const container = document.getElementById('recent-alerts').querySelector('.log');
            if (alerts && alerts.length > 0) {
                container.innerHTML = alerts.map(alert =>
                    \`<div>\${new Date(alert.timestamp).toLocaleString()} - \${alert.level.toUpperCase()}: \${alert.message}</div>\`
                ).join('');
            } else {
                container.innerHTML = '暂无告警';
            }
        }

        function formatUptime(seconds) {
            if (!seconds) return 'N/A';
            const days = Math.floor(seconds / 86400);
            const hours = Math.floor((seconds % 86400) / 3600);
            const minutes = Math.floor((seconds % 3600) / 60);
            return \`\${days}天 \${hours}小时 \${minutes}分钟\`;
        }

        async function testAlert() {
            try {
                const response = await fetch('/api/monitor/test-alert', { method: 'POST' });
                const data = await response.json();

                if (data.success) {
                    alert('测试告警已发送');
                    setTimeout(fetchAlerts, 1000);
                } else {
                    alert('发送测试告警失败: ' + data.error);
                }
            } catch (error) {
                alert('发送测试告警失败: ' + error.message);
            }
        }

        function refreshData() {
            fetchMetrics();
            fetchCacheStats();
            fetchAlerts();
        }

        // 初始加载
        refreshData();

        // 定期刷新
        setInterval(refreshData, 30000); // 30秒刷新一次
    </script>
</body>
</html>`;
  }

  async stop() {
    this.logger.info('正在关闭服务器...');

    // 关闭 WebSocket 连接
    this.io.close();

    // 关闭 HTTP 服务器
    this.server.close();

    // 清理资源
    await this.agentManager.cleanup();
    await this.taskScheduler.cleanup();
    await this.database.close();

    this.logger.info('服务器已关闭');
  }
}

// 启动服务器
if (require.main === module) {
  const server = new AllAgentServer();

  // 优雅关闭
  process.on('SIGINT', async () => {
    console.log('\n收到 SIGINT 信号，正在关闭服务器...');
    await server.stop();
    process.exit(0);
  });

  process.on('SIGTERM', async () => {
    console.log('\n收到 SIGTERM 信号，正在关闭服务器...');
    await server.stop();
    process.exit(0);
  });

  server.start().catch(console.error);
}

module.exports = AllAgentServer;
