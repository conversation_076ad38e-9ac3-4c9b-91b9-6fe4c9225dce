<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>All-Agent 执行轨迹追踪</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f7fa;
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
        }

        .header {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }

        .header h1 {
            color: #2d3748;
            font-size: 28px;
            margin-bottom: 10px;
        }

        .header p {
            color: #718096;
            font-size: 16px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
        }

        .stat-icon {
            font-size: 40px;
            margin-bottom: 15px;
        }

        .stat-value {
            font-size: 32px;
            font-weight: bold;
            color: #2d3748;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #718096;
            font-size: 14px;
        }

        .trace-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .trace-header {
            padding: 25px;
            border-bottom: 1px solid #e2e8f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .trace-title {
            font-size: 20px;
            color: #2d3748;
            font-weight: 600;
        }

        .filter-controls {
            display: flex;
            gap: 10px;
        }

        .filter-btn {
            padding: 8px 16px;
            border: 1px solid #e2e8f0;
            background: white;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
        }

        .filter-btn:hover {
            background: #f7fafc;
        }

        .filter-btn.active {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }

        .trace-timeline {
            padding: 20px;
            max-height: 600px;
            overflow-y: auto;
        }

        .trace-item {
            display: flex;
            margin-bottom: 20px;
            position: relative;
        }

        .trace-item::before {
            content: '';
            position: absolute;
            left: 20px;
            top: 50px;
            bottom: -20px;
            width: 2px;
            background: #e2e8f0;
        }

        .trace-item:last-child::before {
            display: none;
        }

        .trace-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            margin-right: 20px;
            position: relative;
            z-index: 1;
        }

        .trace-icon.analyzer {
            background: #e6fffa;
            color: #319795;
        }

        .trace-icon.planner {
            background: #fef5e7;
            color: #d69e2e;
        }

        .trace-icon.executor {
            background: #f0fff4;
            color: #38a169;
        }

        .trace-icon.error {
            background: #fed7d7;
            color: #e53e3e;
        }

        .trace-content {
            flex: 1;
            background: #f7fafc;
            padding: 20px;
            border-radius: 10px;
            border-left: 4px solid #e2e8f0;
        }

        .trace-content.analyzer {
            border-left-color: #319795;
        }

        .trace-content.planner {
            border-left-color: #d69e2e;
        }

        .trace-content.executor {
            border-left-color: #38a169;
        }

        .trace-content.error {
            border-left-color: #e53e3e;
        }

        .trace-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .trace-agent {
            font-weight: 600;
            color: #2d3748;
        }

        .trace-time {
            font-size: 12px;
            color: #718096;
        }

        .trace-action {
            font-size: 16px;
            color: #4a5568;
            margin-bottom: 10px;
        }

        .trace-details {
            font-size: 14px;
            color: #718096;
            line-height: 1.5;
        }

        .trace-status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
            margin-top: 10px;
        }

        .status-success {
            background: #c6f6d5;
            color: #22543d;
        }

        .status-running {
            background: #bee3f8;
            color: #2a4365;
        }

        .status-error {
            background: #fed7d7;
            color: #742a2a;
        }

        .status-pending {
            background: #faf089;
            color: #744210;
        }

        .progress-bar {
            width: 100%;
            height: 6px;
            background: #e2e8f0;
            border-radius: 3px;
            margin-top: 10px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 3px;
            transition: width 0.3s ease;
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
            
            .trace-header {
                flex-direction: column;
                gap: 15px;
                align-items: flex-start;
            }
            
            .filter-controls {
                width: 100%;
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 Agent 执行轨迹追踪</h1>
            <p>实时监控 All-Agent 系统中各个 Agent 的执行状态和任务进度</p>
        </div>

        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-icon">🤖</div>
                <div class="stat-value">3</div>
                <div class="stat-label">活跃 Agent</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">⚡</div>
                <div class="stat-value">12</div>
                <div class="stat-label">执行中任务</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">✅</div>
                <div class="stat-value">45</div>
                <div class="stat-label">已完成任务</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">⏱️</div>
                <div class="stat-value">2.3s</div>
                <div class="stat-label">平均响应时间</div>
            </div>
        </div>

        <div class="trace-container">
            <div class="trace-header">
                <div class="trace-title">执行轨迹时间线</div>
                <div class="filter-controls">
                    <button class="filter-btn active" data-filter="all">全部</button>
                    <button class="filter-btn" data-filter="analyzer">分析</button>
                    <button class="filter-btn" data-filter="planner">规划</button>
                    <button class="filter-btn" data-filter="executor">执行</button>
                    <button class="filter-btn" data-filter="error">错误</button>
                </div>
            </div>

            <div class="trace-timeline" id="traceTimeline">
                <div class="trace-item" data-type="executor">
                    <div class="trace-icon executor">⚡</div>
                    <div class="trace-content executor">
                        <div class="trace-meta">
                            <div class="trace-agent">执行 Agent</div>
                            <div class="trace-time">2024-12-19 14:32:15</div>
                        </div>
                        <div class="trace-action">正在生成登录页面组件</div>
                        <div class="trace-details">
                            基于用户需求生成 React 登录组件，包含表单验证、样式设计和响应式布局。
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: 75%"></div>
                            </div>
                        </div>
                        <span class="trace-status status-running">执行中</span>
                    </div>
                </div>

                <div class="trace-item" data-type="planner">
                    <div class="trace-icon planner">📋</div>
                    <div class="trace-content planner">
                        <div class="trace-meta">
                            <div class="trace-agent">规划 Agent</div>
                            <div class="trace-time">2024-12-19 14:31:42</div>
                        </div>
                        <div class="trace-action">制定前端开发计划</div>
                        <div class="trace-details">
                            分析用户需求，制定了包含5个阶段的前端开发计划：
                            1. 项目初始化 2. 组件开发 3. 状态管理 4. 路由配置 5. 测试部署
                        </div>
                        <span class="trace-status status-success">已完成</span>
                    </div>
                </div>

                <div class="trace-item" data-type="analyzer">
                    <div class="trace-icon analyzer">🔍</div>
                    <div class="trace-content analyzer">
                        <div class="trace-meta">
                            <div class="trace-agent">分析 Agent</div>
                            <div class="trace-time">2024-12-19 14:30:18</div>
                        </div>
                        <div class="trace-action">分析项目技术栈</div>
                        <div class="trace-details">
                            检测到项目使用 React + TypeScript + Vite 技术栈，
                            识别出现有组件结构和依赖关系，生成了详细的项目分析报告。
                        </div>
                        <span class="trace-status status-success">已完成</span>
                    </div>
                </div>

                <div class="trace-item" data-type="executor">
                    <div class="trace-icon executor">⚡</div>
                    <div class="trace-content executor">
                        <div class="trace-meta">
                            <div class="trace-agent">执行 Agent</div>
                            <div class="trace-time">2024-12-19 14:29:55</div>
                        </div>
                        <div class="trace-action">安装项目依赖</div>
                        <div class="trace-details">
                            成功安装了 React、TypeScript、Vite 等核心依赖包，
                            配置了开发环境和构建脚本。
                        </div>
                        <span class="trace-status status-success">已完成</span>
                    </div>
                </div>

                <div class="trace-item" data-type="error">
                    <div class="trace-icon error">❌</div>
                    <div class="trace-content error">
                        <div class="trace-meta">
                            <div class="trace-agent">执行 Agent</div>
                            <div class="trace-time">2024-12-19 14:28:33</div>
                        </div>
                        <div class="trace-action">代码生成失败</div>
                        <div class="trace-details">
                            尝试生成 API 接口时遇到错误：模板参数不完整。
                            已自动重试，建议检查输入参数的完整性。
                        </div>
                        <span class="trace-status status-error">失败</span>
                    </div>
                </div>

                <div class="trace-item" data-type="planner">
                    <div class="trace-icon planner">📋</div>
                    <div class="trace-content planner">
                        <div class="trace-meta">
                            <div class="trace-agent">规划 Agent</div>
                            <div class="trace-time">2024-12-19 14:27:10</div>
                        </div>
                        <div class="trace-action">项目初始化规划</div>
                        <div class="trace-details">
                            基于用户需求"创建一个现代化的 Web 应用"，
                            制定了详细的项目初始化方案和开发路线图。
                        </div>
                        <span class="trace-status status-success">已完成</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 过滤功能
        const filterBtns = document.querySelectorAll('.filter-btn');
        const traceItems = document.querySelectorAll('.trace-item');

        filterBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                // 更新按钮状态
                filterBtns.forEach(b => b.classList.remove('active'));
                btn.classList.add('active');

                // 过滤轨迹项
                const filter = btn.dataset.filter;
                traceItems.forEach(item => {
                    if (filter === 'all' || item.dataset.type === filter) {
                        item.style.display = 'flex';
                    } else {
                        item.style.display = 'none';
                    }
                });
            });
        });

        // 模拟实时更新
        function simulateRealTimeUpdates() {
            const timeline = document.getElementById('traceTimeline');
            
            setInterval(() => {
                // 随机更新进度条
                const progressBars = document.querySelectorAll('.progress-fill');
                progressBars.forEach(bar => {
                    const currentWidth = parseInt(bar.style.width) || 0;
                    if (currentWidth < 100) {
                        const newWidth = Math.min(100, currentWidth + Math.random() * 10);
                        bar.style.width = newWidth + '%';
                        
                        // 如果完成，更新状态
                        if (newWidth >= 100) {
                            const statusElement = bar.closest('.trace-content').querySelector('.trace-status');
                            statusElement.textContent = '已完成';
                            statusElement.className = 'trace-status status-success';
                        }
                    }
                });

                // 随机添加新的轨迹项（演示用）
                if (Math.random() < 0.1) { // 10% 概率
                    addNewTraceItem();
                }
            }, 2000);
        }

        function addNewTraceItem() {
            const agents = ['analyzer', 'planner', 'executor'];
            const actions = {
                analyzer: ['分析代码结构', '检测依赖关系', '生成文档'],
                planner: ['制定开发计划', '分解任务', '评估风险'],
                executor: ['生成代码', '安装依赖', '运行测试']
            };
            const icons = {
                analyzer: '🔍',
                planner: '📋', 
                executor: '⚡'
            };

            const agentType = agents[Math.floor(Math.random() * agents.length)];
            const action = actions[agentType][Math.floor(Math.random() * actions[agentType].length)];
            const now = new Date();
            
            const newItem = document.createElement('div');
            newItem.className = 'trace-item';
            newItem.dataset.type = agentType;
            newItem.style.opacity = '0';
            newItem.style.transform = 'translateY(-20px)';
            
            newItem.innerHTML = `
                <div class="trace-icon ${agentType}">${icons[agentType]}</div>
                <div class="trace-content ${agentType}">
                    <div class="trace-meta">
                        <div class="trace-agent">${agentType === 'analyzer' ? '分析' : agentType === 'planner' ? '规划' : '执行'} Agent</div>
                        <div class="trace-time">${now.toLocaleString()}</div>
                    </div>
                    <div class="trace-action">${action}</div>
                    <div class="trace-details">正在处理新的任务请求...</div>
                    <span class="trace-status status-running">执行中</span>
                </div>
            `;

            const timeline = document.getElementById('traceTimeline');
            timeline.insertBefore(newItem, timeline.firstChild);

            // 动画效果
            setTimeout(() => {
                newItem.style.transition = 'all 0.5s ease';
                newItem.style.opacity = '1';
                newItem.style.transform = 'translateY(0)';
            }, 100);

            // 限制显示的项目数量
            const items = timeline.querySelectorAll('.trace-item');
            if (items.length > 10) {
                items[items.length - 1].remove();
            }
        }

        // 启动实时更新
        simulateRealTimeUpdates();

        // 更新统计数据
        function updateStats() {
            const runningTasks = document.querySelectorAll('.status-running').length;
            const completedTasks = document.querySelectorAll('.status-success').length;
            
            document.querySelector('.stat-value').textContent = '3'; // 活跃 Agent
            document.querySelectorAll('.stat-value')[1].textContent = runningTasks;
            document.querySelectorAll('.stat-value')[2].textContent = completedTasks;
        }

        setInterval(updateStats, 3000);
    </script>
</body>
</html>
