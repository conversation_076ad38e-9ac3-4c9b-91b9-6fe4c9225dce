import * as vscode from 'vscode';
import { AllAgentClient } from '../client';

export class ChatProvider implements vscode.WebviewViewProvider {
    public static readonly viewType = 'all-agent-chat';
    private _view?: vscode.WebviewView;

    constructor(
        private readonly _context: vscode.ExtensionContext,
        private readonly _client: AllAgentClient
    ) {}

    public resolveWebviewView(
        webviewView: vscode.WebviewView,
        context: vscode.WebviewViewResolveContext,
        _token: vscode.CancellationToken,
    ) {
        this._view = webviewView;

        webviewView.webview.options = {
            enableScripts: true,
            localResourceRoots: [this._context.extensionUri]
        };

        webviewView.webview.html = this._getHtmlForWebview(webviewView.webview);

        // 处理来自 webview 的消息
        webviewView.webview.onDidReceiveMessage(async (data) => {
            switch (data.type) {
                case 'sendMessage':
                    try {
                        const response = await this._client.sendMessage(data.message);
                        webviewView.webview.postMessage({
                            type: 'messageResponse',
                            message: response,
                            timestamp: new Date().toISOString()
                        });
                    } catch (error) {
                        webviewView.webview.postMessage({
                            type: 'error',
                            message: `Error: ${error}`
                        });
                    }
                    break;
                case 'connect':
                    try {
                        await this._client.connect();
                        webviewView.webview.postMessage({
                            type: 'connected'
                        });
                    } catch (error) {
                        webviewView.webview.postMessage({
                            type: 'error',
                            message: `Connection failed: ${error}`
                        });
                    }
                    break;
            }
        });
    }

    private _getHtmlForWebview(webview: vscode.Webview) {
        return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>All-Agent Chat</title>
    <style>
        body {
            font-family: var(--vscode-font-family);
            font-size: var(--vscode-font-size);
            background-color: var(--vscode-editor-background);
            color: var(--vscode-editor-foreground);
            margin: 0;
            padding: 10px;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .chat-container {
            display: flex;
            flex-direction: column;
            height: 100%;
        }

        .messages {
            flex: 1;
            overflow-y: auto;
            padding: 10px 0;
            border-bottom: 1px solid var(--vscode-panel-border);
            margin-bottom: 10px;
        }

        .message {
            margin-bottom: 15px;
            padding: 8px 12px;
            border-radius: 8px;
            max-width: 90%;
            word-wrap: break-word;
        }

        .user-message {
            background-color: var(--vscode-button-background);
            color: var(--vscode-button-foreground);
            margin-left: auto;
            text-align: right;
        }

        .agent-message {
            background-color: var(--vscode-input-background);
            border: 1px solid var(--vscode-input-border);
            margin-right: auto;
        }

        .error-message {
            background-color: var(--vscode-errorForeground);
            color: var(--vscode-editor-background);
            margin-right: auto;
        }

        .input-container {
            display: flex;
            gap: 8px;
            align-items: center;
        }

        .message-input {
            flex: 1;
            padding: 8px 12px;
            background-color: var(--vscode-input-background);
            color: var(--vscode-input-foreground);
            border: 1px solid var(--vscode-input-border);
            border-radius: 4px;
            outline: none;
        }

        .message-input:focus {
            border-color: var(--vscode-focusBorder);
        }

        .send-button {
            padding: 8px 16px;
            background-color: var(--vscode-button-background);
            color: var(--vscode-button-foreground);
            border: none;
            border-radius: 4px;
            cursor: pointer;
            outline: none;
        }

        .send-button:hover {
            background-color: var(--vscode-button-hoverBackground);
        }

        .send-button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .status {
            font-size: 12px;
            color: var(--vscode-descriptionForeground);
            margin-bottom: 10px;
            text-align: center;
        }

        .connected {
            color: var(--vscode-terminal-ansiGreen);
        }

        .disconnected {
            color: var(--vscode-terminal-ansiRed);
        }

        .timestamp {
            font-size: 10px;
            color: var(--vscode-descriptionForeground);
            margin-top: 4px;
        }
    </style>
</head>
<body>
    <div class="chat-container">
        <div class="status" id="status">Disconnected</div>
        <div class="messages" id="messages">
            <div class="message agent-message">
                Welcome to All-Agent! Click "Connect" to start chatting with AI agents.
            </div>
        </div>
        <div class="input-container">
            <input type="text" class="message-input" id="messageInput" placeholder="Type your message..." disabled>
            <button class="send-button" id="sendButton" onclick="sendMessage()" disabled>Send</button>
            <button class="send-button" id="connectButton" onclick="connect()">Connect</button>
        </div>
    </div>

    <script>
        const vscode = acquireVsCodeApi();
        let isConnected = false;

        function connect() {
            vscode.postMessage({ type: 'connect' });
            document.getElementById('connectButton').disabled = true;
            updateStatus('Connecting...', 'connecting');
        }

        function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();
            
            if (!message || !isConnected) return;

            addMessage(message, 'user');
            input.value = '';

            vscode.postMessage({
                type: 'sendMessage',
                message: message
            });
        }

        function addMessage(text, type) {
            const messages = document.getElementById('messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = \`message \${type}-message\`;
            messageDiv.innerHTML = text;
            
            if (type !== 'user') {
                const timestamp = document.createElement('div');
                timestamp.className = 'timestamp';
                timestamp.textContent = new Date().toLocaleTimeString();
                messageDiv.appendChild(timestamp);
            }
            
            messages.appendChild(messageDiv);
            messages.scrollTop = messages.scrollHeight;
        }

        function updateStatus(text, className) {
            const status = document.getElementById('status');
            status.textContent = text;
            status.className = \`status \${className}\`;
        }

        // 监听来自扩展的消息
        window.addEventListener('message', event => {
            const message = event.data;
            
            switch (message.type) {
                case 'connected':
                    isConnected = true;
                    updateStatus('Connected', 'connected');
                    document.getElementById('messageInput').disabled = false;
                    document.getElementById('sendButton').disabled = false;
                    document.getElementById('connectButton').style.display = 'none';
                    break;
                    
                case 'messageResponse':
                    addMessage(message.message, 'agent');
                    break;
                    
                case 'error':
                    addMessage(message.message, 'error');
                    break;
            }
        });

        // 回车发送消息
        document.getElementById('messageInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendMessage();
            }
        });
    </script>
</body>
</html>`;
    }
}
