{"version": 3, "file": "agentProvider.js", "sourceRoot": "", "sources": ["../../src/providers/agentProvider.ts"], "names": [], "mappings": ";;;AAAA,iCAAiC;AAGjC,MAAa,aAAa;IAItB,YAAoB,MAAsB;QAAtB,WAAM,GAAN,MAAM,CAAgB;QAHlC,yBAAoB,GAA6D,IAAI,MAAM,CAAC,YAAY,EAAuC,CAAC;QAC/I,wBAAmB,GAAsD,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC;QAG9G,gBAAgB;QAChB,WAAW,CAAC,GAAG,EAAE;YACb,IAAI,CAAC,OAAO,EAAE,CAAC;QACnB,CAAC,EAAE,IAAI,CAAC,CAAC;IACb,CAAC;IAED,OAAO;QACH,IAAI,CAAC,oBAAoB,CAAC,IAAI,EAAE,CAAC;IACrC,CAAC;IAED,WAAW,CAAC,OAAkB;QAC1B,OAAO,OAAO,CAAC;IACnB,CAAC;IAED,KAAK,CAAC,WAAW,CAAC,OAAmB;QACjC,IAAI,CAAC,OAAO,EAAE;YACV,mBAAmB;YACnB,IAAI;gBACA,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;gBAC7C,OAAO,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI,SAAS,CACpC,KAAK,CAAC,IAAI,IAAI,KAAK,CAAC,IAAI,EACxB,KAAK,CAAC,MAAM,IAAI,SAAS,EACzB,KAAK,CAAC,IAAI,EACV,MAAM,CAAC,wBAAwB,CAAC,SAAS,EACzC,KAAK,CACR,CAAC,CAAC;aACN;YAAC,OAAO,KAAK,EAAE;gBACZ,OAAO,CAAC,IAAI,SAAS,CACjB,uBAAuB,EACvB,OAAO,EACP,OAAO,EACP,MAAM,CAAC,wBAAwB,CAAC,IAAI,CACvC,CAAC,CAAC;aACN;SACJ;aAAM;YACH,aAAa;YACb,MAAM,OAAO,GAAgB,EAAE,CAAC;YAEhC,IAAI,OAAO,CAAC,SAAS,EAAE;gBACnB,MAAM,IAAI,GAAG,OAAO,CAAC,SAAS,CAAC;gBAE/B,OAAO,CAAC,IAAI,CAAC,IAAI,SAAS,CACtB,WAAW,IAAI,CAAC,MAAM,IAAI,SAAS,EAAE,EACrC,MAAM,EACN,QAAQ,EACR,MAAM,CAAC,wBAAwB,CAAC,IAAI,CACvC,CAAC,CAAC;gBAEH,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE;oBACnD,OAAO,CAAC,IAAI,CAAC,IAAI,SAAS,CACtB,iBAAiB,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,EAC/C,MAAM,EACN,cAAc,EACd,MAAM,CAAC,wBAAwB,CAAC,IAAI,CACvC,CAAC,CAAC;iBACN;gBAED,IAAI,IAAI,CAAC,YAAY,KAAK,SAAS,EAAE;oBACjC,OAAO,CAAC,IAAI,CAAC,IAAI,SAAS,CACtB,kBAAkB,IAAI,CAAC,YAAY,EAAE,EACrC,MAAM,EACN,OAAO,EACP,MAAM,CAAC,wBAAwB,CAAC,IAAI,CACvC,CAAC,CAAC;iBACN;gBAED,IAAI,IAAI,CAAC,cAAc,KAAK,SAAS,EAAE;oBACnC,OAAO,CAAC,IAAI,CAAC,IAAI,SAAS,CACtB,oBAAoB,IAAI,CAAC,cAAc,EAAE,EACzC,MAAM,EACN,WAAW,EACX,MAAM,CAAC,wBAAwB,CAAC,IAAI,CACvC,CAAC,CAAC;iBACN;aACJ;YAED,OAAO,OAAO,CAAC;SAClB;IACL,CAAC;CACJ;AApFD,sCAoFC;AAED,MAAM,SAAU,SAAQ,MAAM,CAAC,QAAQ;IACnC,YACoB,KAAa,EACb,MAAc,EACd,IAAY,EACZ,gBAAiD,EACjD,SAAe;QAE/B,KAAK,CAAC,KAAK,EAAE,gBAAgB,CAAC,CAAC;QANf,UAAK,GAAL,KAAK,CAAQ;QACb,WAAM,GAAN,MAAM,CAAQ;QACd,SAAI,GAAJ,IAAI,CAAQ;QACZ,qBAAgB,GAAhB,gBAAgB,CAAiC;QACjD,cAAS,GAAT,SAAS,CAAM;QAI/B,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;QACjC,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;QACzC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;QAC/B,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;IAC/C,CAAC;IAEO,UAAU;QACd,IAAI,IAAI,CAAC,SAAS,EAAE;YAChB,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,aAAa,IAAI,CAAC,SAAS,CAAC,MAAM,WAAW,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;SAC1H;QACD,OAAO,IAAI,CAAC,KAAK,CAAC;IACtB,CAAC;IAEO,cAAc;QAClB,IAAI,IAAI,CAAC,IAAI,KAAK,UAAU;YAAE,OAAO,UAAU,CAAC;QAChD,IAAI,IAAI,CAAC,IAAI,KAAK,SAAS;YAAE,OAAO,UAAU,CAAC;QAC/C,IAAI,IAAI,CAAC,IAAI,KAAK,UAAU;YAAE,OAAO,WAAW,CAAC;QACjD,IAAI,IAAI,CAAC,MAAM,KAAK,SAAS;YAAE,OAAO,IAAI,CAAC;QAC3C,IAAI,IAAI,CAAC,MAAM,KAAK,SAAS;YAAE,OAAO,IAAI,CAAC;QAC3C,IAAI,IAAI,CAAC,MAAM,KAAK,OAAO;YAAE,OAAO,GAAG,CAAC;QACxC,OAAO,EAAE,CAAC;IACd,CAAC;IAEO,OAAO;QACX,QAAQ,IAAI,CAAC,IAAI,EAAE;YACf,KAAK,UAAU;gBACX,OAAO,IAAI,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;YAC1C,KAAK,SAAS;gBACV,OAAO,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC;YAChD,KAAK,UAAU;gBACX,OAAO,IAAI,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;YACxC,KAAK,QAAQ;gBACT,OAAO,IAAI,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;YACzC,KAAK,cAAc;gBACf,OAAO,IAAI,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;YACzC,KAAK,OAAO;gBACR,OAAO,IAAI,MAAM,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;YAC5C,KAAK,WAAW;gBACZ,OAAO,IAAI,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;YACzC;gBACI,IAAI,IAAI,CAAC,MAAM,KAAK,SAAS,EAAE;oBAC3B,OAAO,IAAI,MAAM,CAAC,SAAS,CAAC,eAAe,EAAE,IAAI,MAAM,CAAC,UAAU,CAAC,oBAAoB,CAAC,CAAC,CAAC;iBAC7F;qBAAM,IAAI,IAAI,CAAC,MAAM,KAAK,OAAO,EAAE;oBAChC,OAAO,IAAI,MAAM,CAAC,SAAS,CAAC,OAAO,EAAE,IAAI,MAAM,CAAC,UAAU,CAAC,iBAAiB,CAAC,CAAC,CAAC;iBAClF;qBAAM;oBACH,OAAO,IAAI,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAC;iBACjD;SACR;IACL,CAAC;IAEO,eAAe;QACnB,IAAI,IAAI,CAAC,SAAS,EAAE;YAChB,OAAO,OAAO,CAAC;SAClB;QACD,OAAO,WAAW,CAAC;IACvB,CAAC;CACJ"}