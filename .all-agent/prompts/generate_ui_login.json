{"template_id": "generate_ui_login", "name": "生成 UI 登录页面", "description": "为 Web 应用生成现代化的登录页面，包含 HTML、CSS 和基础 JavaScript", "category": "ui_generation", "version": "1.0.0", "author": "All-Agent System", "tags": ["ui", "login", "authentication", "frontend"], "prompt_template": "你是一个专业的前端开发专家。请为用户生成一个现代化的登录页面。\n\n## 需求分析\n项目类型: {{project_type}}\n设计风格: {{design_style}}\n技术栈: {{tech_stack}}\n特殊要求: {{special_requirements}}\n\n## 生成要求\n1. 创建响应式设计的登录页面\n2. 包含用户名/邮箱和密码输入框\n3. 添加\"记住我\"和\"忘记密码\"选项\n4. 实现基础的表单验证\n5. 使用现代 CSS 样式（支持深色/浅色主题）\n6. 添加适当的无障碍访问支持\n7. 包含基础的 JavaScript 交互逻辑\n\n## 输出格式\n请按以下结构生成代码：\n\n### HTML 结构 (login.html)\n```html\n[生成完整的 HTML 代码]\n```\n\n### CSS 样式 (login.css)\n```css\n[生成完整的 CSS 代码]\n```\n\n### JavaScript 逻辑 (login.js)\n```javascript\n[生成完整的 JavaScript 代码]\n```\n\n## 额外说明\n- 确保代码符合现代 Web 标准\n- 添加适当的注释说明\n- 考虑安全性最佳实践\n- 提供使用说明和集成建议", "parameters": {"project_type": {"type": "string", "description": "项目类型", "default": "web应用", "options": ["web应用", "移动端H5", "桌面应用", "管理后台"]}, "design_style": {"type": "string", "description": "设计风格", "default": "现代简约", "options": ["现代简约", "商务专业", "创意时尚", "极简主义", "科技感"]}, "tech_stack": {"type": "string", "description": "技术栈", "default": "HTML/CSS/JavaScript", "options": ["HTML/CSS/JavaScript", "React", "<PERSON><PERSON>", "Angular", "Bootstrap", "Tailwind CSS"]}, "special_requirements": {"type": "string", "description": "特殊要求", "default": "无", "placeholder": "例如：支持社交登录、双因子认证、企业SSO等"}}, "example_usage": {"input": {"project_type": "web应用", "design_style": "现代简约", "tech_stack": "HTML/CSS/JavaScript", "special_requirements": "支持社交登录（Google、GitHub）"}, "expected_output": "生成包含社交登录按钮的现代化登录页面，具有响应式设计和流畅的用户体验"}, "validation_rules": ["生成的代码必须是有效的 HTML/CSS/JavaScript", "必须包含基础的表单验证", "必须支持响应式设计", "必须包含适当的无障碍访问属性", "代码必须有清晰的注释说明"], "related_templates": ["generate_ui_register", "generate_ui_dashboard", "generate_ui_profile", "setup_authentication_backend"], "metadata": {"created_at": "2024-12-19", "last_updated": "2024-12-19", "usage_count": 0, "success_rate": 0, "estimated_execution_time": "2-5 minutes"}}