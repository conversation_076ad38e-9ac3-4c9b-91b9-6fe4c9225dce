#!/usr/bin/env node

const axios = require('axios');
const { performance } = require('perf_hooks');
const fs = require('fs').promises;
const path = require('path');

/**
 * All-Agent 性能基准测试工具
 * 提供全面的性能测试和基准对比
 */
class BenchmarkTool {
    constructor() {
        this.baseURL = process.env.BENCHMARK_URL || 'http://localhost:3000';
        this.authToken = null;
        this.results = {
            timestamp: new Date().toISOString(),
            environment: {
                nodeVersion: process.version,
                platform: process.platform,
                arch: process.arch,
                cpus: require('os').cpus().length,
                memory: Math.round(require('os').totalmem() / 1024 / 1024 / 1024) + 'GB'
            },
            benchmarks: {}
        };
    }

    /**
     * 运行所有基准测试
     */
    async runAllBenchmarks() {
        console.log('🚀 开始 All-Agent 性能基准测试');
        console.log('='.repeat(60));
        
        try {
            // 初始化
            await this.initialize();
            
            // 运行各项基准测试
            await this.benchmarkAuthentication();
            await this.benchmarkProjectAnalysis();
            await this.benchmarkCachePerformance();
            await this.benchmarkConcurrency();
            await this.benchmarkMemoryUsage();
            await this.benchmarkDatabaseOperations();
            
            // 生成报告
            await this.generateReport();
            
            console.log('\n✅ 所有基准测试完成');
            
        } catch (error) {
            console.error('❌ 基准测试失败:', error.message);
            process.exit(1);
        }
    }

    /**
     * 初始化测试环境
     */
    async initialize() {
        console.log('🔧 初始化测试环境...');
        
        // 等待服务启动
        await this.waitForService();
        
        // 获取认证令牌
        await this.authenticate();
        
        console.log('✅ 初始化完成');
    }

    /**
     * 等待服务启动
     */
    async waitForService(maxAttempts = 30) {
        for (let i = 0; i < maxAttempts; i++) {
            try {
                await axios.get(`${this.baseURL}/health`, { timeout: 5000 });
                return;
            } catch (error) {
                console.log(`等待服务启动... (${i + 1}/${maxAttempts})`);
                await this.sleep(2000);
            }
        }
        throw new Error('服务启动超时');
    }

    /**
     * 认证
     */
    async authenticate() {
        try {
            const response = await axios.post(`${this.baseURL}/auth/login`, {
                username: '<EMAIL>',
                password: 'admin123'
            });
            
            if (response.data.success) {
                this.authToken = response.data.data.token;
            }
        } catch (error) {
            console.warn('认证失败，将使用匿名访问');
        }
    }

    /**
     * 认证性能基准测试
     */
    async benchmarkAuthentication() {
        console.log('\n🔐 认证性能基准测试...');
        
        const iterations = 100;
        const results = {
            login: [],
            verify: [],
            refresh: []
        };

        // 登录性能测试
        console.log('测试登录性能...');
        for (let i = 0; i < iterations; i++) {
            const start = performance.now();
            
            try {
                await axios.post(`${this.baseURL}/auth/login`, {
                    username: '<EMAIL>',
                    password: 'admin123'
                });
                
                results.login.push(performance.now() - start);
            } catch (error) {
                console.warn(`登录测试失败 (${i + 1}):`, error.message);
            }
            
            if (i % 10 === 0) {
                process.stdout.write('.');
            }
        }

        // 令牌验证性能测试
        if (this.authToken) {
            console.log('\n测试令牌验证性能...');
            for (let i = 0; i < iterations; i++) {
                const start = performance.now();
                
                try {
                    await axios.post(`${this.baseURL}/auth/verify`, {
                        token: this.authToken
                    });
                    
                    results.verify.push(performance.now() - start);
                } catch (error) {
                    console.warn(`验证测试失败 (${i + 1}):`, error.message);
                }
                
                if (i % 10 === 0) {
                    process.stdout.write('.');
                }
            }
        }

        this.results.benchmarks.authentication = {
            iterations,
            login: this.calculateStats(results.login),
            verify: this.calculateStats(results.verify),
            refresh: this.calculateStats(results.refresh)
        };

        console.log('\n✅ 认证基准测试完成');
    }

    /**
     * 项目分析性能基准测试
     */
    async benchmarkProjectAnalysis() {
        console.log('\n📊 项目分析性能基准测试...');
        
        if (!this.authToken) {
            console.log('跳过项目分析测试（需要认证）');
            return;
        }

        const testCases = [
            { name: 'small_project', maxDepth: 2, includeHidden: false },
            { name: 'medium_project', maxDepth: 5, includeHidden: false },
            { name: 'large_project', maxDepth: 10, includeHidden: true }
        ];

        const results = {};

        for (const testCase of testCases) {
            console.log(`测试 ${testCase.name}...`);
            const times = [];
            
            for (let i = 0; i < 10; i++) {
                const start = performance.now();
                
                try {
                    await axios.post(`${this.baseURL}/api/analyze`, {
                        projectPath: '.',
                        options: {
                            maxDepth: testCase.maxDepth,
                            includeHidden: testCase.includeHidden,
                            forceRefresh: true
                        }
                    }, {
                        headers: {
                            'Authorization': `Bearer ${this.authToken}`
                        },
                        timeout: 60000
                    });
                    
                    times.push(performance.now() - start);
                } catch (error) {
                    console.warn(`分析测试失败 (${i + 1}):`, error.message);
                }
                
                process.stdout.write('.');
            }
            
            results[testCase.name] = this.calculateStats(times);
        }

        this.results.benchmarks.projectAnalysis = results;
        console.log('\n✅ 项目分析基准测试完成');
    }

    /**
     * 缓存性能基准测试
     */
    async benchmarkCachePerformance() {
        console.log('\n💾 缓存性能基准测试...');
        
        const iterations = 1000;
        const results = {
            hit: [],
            miss: [],
            set: []
        };

        // 缓存命中测试
        console.log('测试缓存命中性能...');
        for (let i = 0; i < iterations; i++) {
            const start = performance.now();
            
            try {
                await axios.get(`${this.baseURL}/health`);
                results.hit.push(performance.now() - start);
            } catch (error) {
                console.warn(`缓存命中测试失败:`, error.message);
            }
            
            if (i % 100 === 0) {
                process.stdout.write('.');
            }
        }

        // 缓存未命中测试（通过添加随机参数）
        console.log('\n测试缓存未命中性能...');
        for (let i = 0; i < 100; i++) {
            const start = performance.now();
            
            try {
                await axios.get(`${this.baseURL}/health?t=${Date.now()}&r=${Math.random()}`);
                results.miss.push(performance.now() - start);
            } catch (error) {
                console.warn(`缓存未命中测试失败:`, error.message);
            }
            
            if (i % 10 === 0) {
                process.stdout.write('.');
            }
        }

        this.results.benchmarks.cache = {
            iterations,
            hit: this.calculateStats(results.hit),
            miss: this.calculateStats(results.miss)
        };

        console.log('\n✅ 缓存基准测试完成');
    }

    /**
     * 并发性能基准测试
     */
    async benchmarkConcurrency() {
        console.log('\n🔀 并发性能基准测试...');
        
        const concurrencyLevels = [1, 5, 10, 20, 50];
        const results = {};

        for (const concurrency of concurrencyLevels) {
            console.log(`测试 ${concurrency} 并发...`);
            
            const promises = [];
            const start = performance.now();
            
            for (let i = 0; i < concurrency; i++) {
                promises.push(
                    axios.get(`${this.baseURL}/health`).catch(error => ({
                        error: error.message
                    }))
                );
            }
            
            const responses = await Promise.all(promises);
            const totalTime = performance.now() - start;
            
            const successful = responses.filter(r => !r.error).length;
            const failed = responses.filter(r => r.error).length;
            
            results[`concurrency_${concurrency}`] = {
                concurrency,
                totalTime,
                successful,
                failed,
                successRate: (successful / concurrency) * 100,
                throughput: (successful / totalTime) * 1000
            };
            
            process.stdout.write('.');
        }

        this.results.benchmarks.concurrency = results;
        console.log('\n✅ 并发基准测试完成');
    }

    /**
     * 内存使用基准测试
     */
    async benchmarkMemoryUsage() {
        console.log('\n🧠 内存使用基准测试...');
        
        const initialMemory = process.memoryUsage();
        const memorySnapshots = [initialMemory];
        
        // 执行一系列操作并监控内存使用
        for (let i = 0; i < 100; i++) {
            try {
                await axios.get(`${this.baseURL}/health`);
                
                if (i % 10 === 0) {
                    memorySnapshots.push(process.memoryUsage());
                    process.stdout.write('.');
                }
            } catch (error) {
                console.warn(`内存测试失败:`, error.message);
            }
        }

        // 强制垃圾回收（如果可用）
        if (global.gc) {
            global.gc();
        }

        const finalMemory = process.memoryUsage();
        
        this.results.benchmarks.memory = {
            initial: initialMemory,
            final: finalMemory,
            snapshots: memorySnapshots,
            heapGrowth: finalMemory.heapUsed - initialMemory.heapUsed,
            rssGrowth: finalMemory.rss - initialMemory.rss
        };

        console.log('\n✅ 内存基准测试完成');
    }

    /**
     * 数据库操作基准测试
     */
    async benchmarkDatabaseOperations() {
        console.log('\n🗄️ 数据库操作基准测试...');
        
        if (!this.authToken) {
            console.log('跳过数据库测试（需要认证）');
            return;
        }

        const operations = {
            read: [],
            write: [],
            update: []
        };

        // 读操作测试
        console.log('测试读操作性能...');
        for (let i = 0; i < 100; i++) {
            const start = performance.now();
            
            try {
                await axios.get(`${this.baseURL}/auth/me`, {
                    headers: {
                        'Authorization': `Bearer ${this.authToken}`
                    }
                });
                
                operations.read.push(performance.now() - start);
            } catch (error) {
                console.warn(`读操作测试失败:`, error.message);
            }
            
            if (i % 10 === 0) {
                process.stdout.write('.');
            }
        }

        this.results.benchmarks.database = {
            read: this.calculateStats(operations.read),
            write: this.calculateStats(operations.write),
            update: this.calculateStats(operations.update)
        };

        console.log('\n✅ 数据库基准测试完成');
    }

    /**
     * 计算统计数据
     */
    calculateStats(times) {
        if (times.length === 0) {
            return {
                count: 0,
                min: 0,
                max: 0,
                mean: 0,
                median: 0,
                p95: 0,
                p99: 0,
                stdDev: 0
            };
        }

        const sorted = times.slice().sort((a, b) => a - b);
        const count = times.length;
        const sum = times.reduce((a, b) => a + b, 0);
        const mean = sum / count;
        
        const variance = times.reduce((acc, time) => acc + Math.pow(time - mean, 2), 0) / count;
        const stdDev = Math.sqrt(variance);
        
        return {
            count,
            min: sorted[0],
            max: sorted[count - 1],
            mean,
            median: sorted[Math.floor(count / 2)],
            p95: sorted[Math.floor(count * 0.95)],
            p99: sorted[Math.floor(count * 0.99)],
            stdDev
        };
    }

    /**
     * 生成基准测试报告
     */
    async generateReport() {
        console.log('\n📊 生成基准测试报告...');
        
        // 计算总体评分
        const score = this.calculateOverallScore();
        
        const report = {
            ...this.results,
            score,
            summary: this.generateSummary(),
            recommendations: this.generateRecommendations()
        };

        // 保存 JSON 报告
        const reportsDir = path.join(__dirname, '../server/reports');
        await fs.mkdir(reportsDir, { recursive: true });
        
        const jsonPath = path.join(reportsDir, `benchmark-${Date.now()}.json`);
        await fs.writeFile(jsonPath, JSON.stringify(report, null, 2));
        
        // 生成 HTML 报告
        const htmlPath = path.join(reportsDir, `benchmark-${Date.now()}.html`);
        await fs.writeFile(htmlPath, this.generateHTMLReport(report));
        
        // 显示摘要
        this.displaySummary(report);
        
        console.log(`\n📄 详细报告已保存:`);
        console.log(`   JSON: ${jsonPath}`);
        console.log(`   HTML: ${htmlPath}`);
    }

    /**
     * 计算总体评分
     */
    calculateOverallScore() {
        let score = 100;
        
        // 基于各项指标扣分
        const auth = this.results.benchmarks.authentication;
        if (auth && auth.login.mean > 1000) score -= 10;
        
        const analysis = this.results.benchmarks.projectAnalysis;
        if (analysis && analysis.small_project && analysis.small_project.mean > 5000) score -= 15;
        
        const cache = this.results.benchmarks.cache;
        if (cache && cache.hit.mean > 100) score -= 10;
        
        const concurrency = this.results.benchmarks.concurrency;
        if (concurrency && concurrency.concurrency_50 && concurrency.concurrency_50.successRate < 95) score -= 20;
        
        const memory = this.results.benchmarks.memory;
        if (memory && memory.heapGrowth > 50 * 1024 * 1024) score -= 15; // 50MB
        
        return Math.max(0, score);
    }

    /**
     * 生成摘要
     */
    generateSummary() {
        const summary = {
            performance: 'Good',
            reliability: 'Good',
            scalability: 'Good'
        };

        // 基于测试结果调整评级
        const score = this.calculateOverallScore();
        
        if (score >= 90) {
            summary.performance = 'Excellent';
        } else if (score >= 70) {
            summary.performance = 'Good';
        } else if (score >= 50) {
            summary.performance = 'Fair';
        } else {
            summary.performance = 'Poor';
        }

        return summary;
    }

    /**
     * 生成优化建议
     */
    generateRecommendations() {
        const recommendations = [];
        
        const auth = this.results.benchmarks.authentication;
        if (auth && auth.login.mean > 1000) {
            recommendations.push('认证响应时间较慢，建议优化数据库查询和密码哈希算法');
        }
        
        const cache = this.results.benchmarks.cache;
        if (cache && cache.hit.mean > 100) {
            recommendations.push('缓存响应时间较慢，建议使用 Redis 或优化缓存策略');
        }
        
        const memory = this.results.benchmarks.memory;
        if (memory && memory.heapGrowth > 50 * 1024 * 1024) {
            recommendations.push('检测到内存增长，建议检查内存泄漏和优化内存使用');
        }
        
        if (recommendations.length === 0) {
            recommendations.push('系统性能表现良好，无明显优化建议');
        }
        
        return recommendations;
    }

    /**
     * 显示摘要
     */
    displaySummary(report) {
        console.log('\n📊 基准测试摘要');
        console.log('='.repeat(60));
        console.log(`总体评分: ${report.score}/100`);
        console.log(`性能评级: ${report.summary.performance}`);
        console.log(`可靠性: ${report.summary.reliability}`);
        console.log(`可扩展性: ${report.summary.scalability}`);
        
        if (report.recommendations.length > 0) {
            console.log('\n💡 优化建议:');
            report.recommendations.forEach((rec, index) => {
                console.log(`   ${index + 1}. ${rec}`);
            });
        }
    }

    /**
     * 生成 HTML 报告
     */
    generateHTMLReport(report) {
        return `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>All-Agent 性能基准测试报告</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; border-radius: 8px 8px 0 0; }
        .content { padding: 30px; }
        .score { font-size: 48px; font-weight: bold; text-align: center; margin: 20px 0; }
        .benchmark { margin: 20px 0; padding: 20px; background: #f8f9fa; border-radius: 6px; }
        .metric { display: flex; justify-content: space-between; margin: 10px 0; }
        .recommendations { background: #e3f2fd; padding: 20px; border-radius: 6px; margin: 20px 0; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 All-Agent 性能基准测试报告</h1>
            <p>生成时间: ${report.timestamp}</p>
        </div>
        <div class="content">
            <div class="score">${report.score}/100</div>
            <div class="benchmark">
                <h3>🔐 认证性能</h3>
                ${report.benchmarks.authentication ? `
                <div class="metric"><span>登录平均时间:</span><span>${report.benchmarks.authentication.login.mean.toFixed(2)}ms</span></div>
                <div class="metric"><span>验证平均时间:</span><span>${report.benchmarks.authentication.verify.mean.toFixed(2)}ms</span></div>
                ` : '<p>未测试</p>'}
            </div>
            <div class="recommendations">
                <h3>💡 优化建议</h3>
                <ul>
                    ${report.recommendations.map(rec => `<li>${rec}</li>`).join('')}
                </ul>
            </div>
        </div>
    </div>
</body>
</html>`;
    }

    /**
     * 工具函数：睡眠
     */
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// 主函数
async function main() {
    const benchmark = new BenchmarkTool();
    await benchmark.runAllBenchmarks();
}

// 如果直接运行此文件
if (require.main === module) {
    main().catch(error => {
        console.error('基准测试失败:', error);
        process.exit(1);
    });
}

module.exports = BenchmarkTool;
