{"template_id": "debug_code_issues", "name": "代码调试诊断", "description": "智能诊断代码问题并提供修复方案", "category": "debugging", "version": "1.0.0", "author": "All-Agent System", "tags": ["debug", "troubleshooting", "error_fixing", "diagnostics"], "prompt_template": "你是一个经验丰富的调试专家。请帮助诊断和修复以下代码问题。\n\n## 问题描述\n错误类型: {{error_type}}\n错误信息: {{error_message}}\n发生环境: {{environment}}\n重现步骤: {{reproduction_steps}}\n\n## 相关代码\n文件路径: {{file_path}}\n编程语言: {{programming_language}}\n\n```{{programming_language}}\n{{problematic_code}}\n```\n\n## 错误日志 (如有)\n```\n{{error_logs}}\n```\n\n## 调试分析\n\n### 🔍 问题诊断\n请按以下步骤进行分析：\n\n1. **错误根因分析**\n   - 直接原因识别\n   - 深层原因挖掘\n   - 相关依赖检查\n\n2. **影响范围评估**\n   - 功能影响程度\n   - 数据安全风险\n   - 用户体验影响\n\n3. **紧急程度判断**\n   - 严重级别: [低/中/高/紧急]\n   - 修复优先级\n   - 临时解决方案需求\n\n### 🛠️ 修复方案\n\n#### 方案一：快速修复 (临时解决)\n```{{programming_language}}\n[提供快速修复代码]\n```\n**说明**: [修复原理和注意事项]\n\n#### 方案二：完整修复 (推荐)\n```{{programming_language}}\n[提供完整修复代码]\n```\n**说明**: [修复原理和长期效果]\n\n#### 方案三：重构修复 (如果需要)\n```{{programming_language}}\n[提供重构后的代码]\n```\n**说明**: [重构必要性和收益]\n\n### 🧪 测试验证\n\n#### 单元测试\n```{{programming_language}}\n[提供测试代码]\n```\n\n#### 集成测试建议\n- [测试场景1]\n- [测试场景2]\n- [边界条件测试]\n\n### 🚫 预防措施\n\n1. **代码改进建议**\n   - [具体改进点]\n\n2. **开发流程优化**\n   - [流程建议]\n\n3. **监控和告警**\n   - [监控指标]\n   - [告警规则]\n\n### 📚 相关文档\n- [相关技术文档链接]\n- [最佳实践参考]\n- [类似问题解决方案]", "parameters": {"error_type": {"type": "string", "description": "错误类型", "required": true, "options": ["语法错误", "运行时错误", "逻辑错误", "性能问题", "内存泄漏", "网络错误", "数据库错误", "其他"]}, "error_message": {"type": "text", "description": "错误信息", "required": true, "placeholder": "粘贴完整的错误信息"}, "environment": {"type": "string", "description": "运行环境", "default": "开发环境", "options": ["开发环境", "测试环境", "预生产环境", "生产环境"]}, "reproduction_steps": {"type": "text", "description": "重现步骤", "placeholder": "描述如何重现这个问题"}, "file_path": {"type": "string", "description": "问题文件路径", "placeholder": "例如: src/components/UserList.js"}, "programming_language": {"type": "string", "description": "编程语言", "default": "javascript", "options": ["javascript", "typescript", "python", "java", "go", "rust", "php", "c#"]}, "problematic_code": {"type": "text", "description": "问题代码", "required": true, "placeholder": "粘贴出现问题的代码片段"}, "error_logs": {"type": "text", "description": "错误日志", "placeholder": "粘贴相关的错误日志（可选）"}}, "example_usage": {"input": {"error_type": "运行时错误", "error_message": "TypeError: Cannot read property 'length' of undefined", "environment": "开发环境", "reproduction_steps": "1. 打开用户列表页面\n2. 点击搜索按钮\n3. 错误出现", "file_path": "src/components/UserList.js", "programming_language": "javascript", "problematic_code": "function filterUsers(users, query) {\n  return users.filter(user => user.name.includes(query));\n}", "error_logs": "TypeError: Cannot read property 'length' of undefined\n    at filterUsers (UserList.js:15:23)"}, "expected_output": "详细的问题诊断、多种修复方案、测试建议和预防措施"}, "validation_rules": ["必须准确识别错误根因", "必须提供至少2种修复方案", "修复代码必须能解决问题", "必须包含测试验证方法", "必须提供预防措施建议"], "related_templates": ["refactor_code", "optimize_performance", "security_audit", "code_review"], "metadata": {"created_at": "2024-12-19", "last_updated": "2024-12-19", "usage_count": 0, "success_rate": 0, "estimated_execution_time": "3-10 minutes"}}