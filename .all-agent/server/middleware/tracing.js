const { NodeSDK } = require('@opentelemetry/sdk-node');
const { Resource } = require('@opentelemetry/resources');
const { SemanticResourceAttributes } = require('@opentelemetry/semantic-conventions');
const { JaegerExporter } = require('@opentelemetry/exporter-jaeger');
const { PrometheusExporter } = require('@opentelemetry/exporter-prometheus');
const { getNodeAutoInstrumentations } = require('@opentelemetry/auto-instrumentations-node');
const { PeriodicExportingMetricReader } = require('@opentelemetry/sdk-metrics');
const opentelemetry = require('@opentelemetry/api');

/**
 * 分布式追踪中间件
 * 集成 OpenTelemetry 和 Jaeger 进行全链路追踪
 */
class TracingMiddleware {
    constructor(options = {}) {
        this.options = {
            serviceName: options.serviceName || 'all-agent',
            serviceVersion: options.serviceVersion || '1.0.0',
            environment: options.environment || process.env.NODE_ENV || 'development',
            jaegerEndpoint: options.jaegerEndpoint || process.env.JAEGER_ENDPOINT || 'http://jaeger-collector:14268/api/traces',
            enableMetrics: options.enableMetrics !== false,
            enableLogs: options.enableLogs !== false,
            sampleRate: options.sampleRate || 1.0,
            ...options
        };

        this.tracer = null;
        this.meter = null;
        this.sdk = null;
        this.initialized = false;
    }

    /**
     * 初始化追踪系统
     */
    async initialize() {
        if (this.initialized) {
            return;
        }

        try {
            // 创建资源
            const resource = new Resource({
                [SemanticResourceAttributes.SERVICE_NAME]: this.options.serviceName,
                [SemanticResourceAttributes.SERVICE_VERSION]: this.options.serviceVersion,
                [SemanticResourceAttributes.DEPLOYMENT_ENVIRONMENT]: this.options.environment,
                [SemanticResourceAttributes.SERVICE_INSTANCE_ID]: process.env.HOSTNAME || 'unknown',
                [SemanticResourceAttributes.PROCESS_PID]: process.pid,
                [SemanticResourceAttributes.HOST_NAME]: require('os').hostname(),
            });

            // 配置 Jaeger 导出器
            const jaegerExporter = new JaegerExporter({
                endpoint: this.options.jaegerEndpoint,
                headers: {
                    'x-service-name': this.options.serviceName,
                },
            });

            // 配置指标导出器
            const prometheusExporter = new PrometheusExporter({
                port: 9464,
                endpoint: '/metrics',
            });

            // 创建 SDK
            this.sdk = new NodeSDK({
                resource,
                traceExporter: jaegerExporter,
                metricReader: new PeriodicExportingMetricReader({
                    exporter: prometheusExporter,
                    exportIntervalMillis: 30000,
                }),
                instrumentations: [
                    getNodeAutoInstrumentations({
                        '@opentelemetry/instrumentation-fs': {
                            enabled: false, // 禁用文件系统追踪以减少噪音
                        },
                        '@opentelemetry/instrumentation-http': {
                            enabled: true,
                            requestHook: this.httpRequestHook.bind(this),
                            responseHook: this.httpResponseHook.bind(this),
                        },
                        '@opentelemetry/instrumentation-express': {
                            enabled: true,
                            requestHook: this.expressRequestHook.bind(this),
                        },
                        '@opentelemetry/instrumentation-redis': {
                            enabled: true,
                        },
                        '@opentelemetry/instrumentation-sqlite3': {
                            enabled: true,
                        },
                    }),
                ],
                sampler: this.createSampler(),
            });

            // 启动 SDK
            await this.sdk.start();

            // 获取追踪器和计量器
            this.tracer = opentelemetry.trace.getTracer(this.options.serviceName, this.options.serviceVersion);
            this.meter = opentelemetry.metrics.getMeter(this.options.serviceName, this.options.serviceVersion);

            // 创建自定义指标
            this.createCustomMetrics();

            this.initialized = true;
            console.log(`✅ 分布式追踪已初始化: ${this.options.serviceName}`);

        } catch (error) {
            console.error('❌ 分布式追踪初始化失败:', error);
            throw error;
        }
    }

    /**
     * 创建采样器
     */
    createSampler() {
        const { TraceIdRatioBasedSampler, ParentBasedSampler } = require('@opentelemetry/sdk-trace-base');
        
        return new ParentBasedSampler({
            root: new TraceIdRatioBasedSampler(this.options.sampleRate),
        });
    }

    /**
     * 创建自定义指标
     */
    createCustomMetrics() {
        // HTTP 请求计数器
        this.httpRequestCounter = this.meter.createCounter('http_requests_total', {
            description: 'Total number of HTTP requests',
        });

        // HTTP 请求持续时间直方图
        this.httpRequestDuration = this.meter.createHistogram('http_request_duration_seconds', {
            description: 'HTTP request duration in seconds',
            boundaries: [0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1, 2, 5, 10],
        });

        // 活跃连接数
        this.activeConnections = this.meter.createUpDownCounter('active_connections', {
            description: 'Number of active connections',
        });

        // 数据库查询计数器
        this.dbQueryCounter = this.meter.createCounter('database_queries_total', {
            description: 'Total number of database queries',
        });

        // 缓存操作计数器
        this.cacheOperationCounter = this.meter.createCounter('cache_operations_total', {
            description: 'Total number of cache operations',
        });

        // 错误计数器
        this.errorCounter = this.meter.createCounter('errors_total', {
            description: 'Total number of errors',
        });
    }

    /**
     * HTTP 请求钩子
     */
    httpRequestHook(span, request) {
        span.setAttributes({
            'http.user_agent': request.getHeader('user-agent'),
            'http.request_id': request.getHeader('x-request-id') || this.generateRequestId(),
            'user.id': request.getHeader('x-user-id'),
            'user.role': request.getHeader('x-user-role'),
        });
    }

    /**
     * HTTP 响应钩子
     */
    httpResponseHook(span, response) {
        span.setAttributes({
            'http.response.size': response.getHeader('content-length'),
            'http.response.content_type': response.getHeader('content-type'),
        });

        // 记录错误
        if (response.statusCode >= 400) {
            span.recordException(new Error(`HTTP ${response.statusCode}`));
            span.setStatus({
                code: opentelemetry.SpanStatusCode.ERROR,
                message: `HTTP ${response.statusCode}`,
            });
        }
    }

    /**
     * Express 请求钩子
     */
    expressRequestHook(span, info) {
        span.setAttributes({
            'express.route': info.route,
            'express.method': info.request.method,
            'express.path': info.request.path,
        });
    }

    /**
     * Express 中间件
     */
    middleware() {
        return (req, res, next) => {
            const startTime = Date.now();
            
            // 生成或获取请求 ID
            const requestId = req.headers['x-request-id'] || this.generateRequestId();
            req.requestId = requestId;
            res.setHeader('x-request-id', requestId);

            // 创建 span
            const span = this.tracer.startSpan(`${req.method} ${req.path}`, {
                kind: opentelemetry.SpanKind.SERVER,
                attributes: {
                    'http.method': req.method,
                    'http.url': req.url,
                    'http.path': req.path,
                    'http.user_agent': req.get('user-agent'),
                    'http.request_id': requestId,
                    'user.id': req.headers['x-user-id'],
                    'user.role': req.headers['x-user-role'],
                },
            });

            // 设置活跃上下文
            const ctx = opentelemetry.trace.setSpan(opentelemetry.context.active(), span);
            
            // 在请求对象上添加追踪信息
            req.span = span;
            req.traceId = span.spanContext().traceId;
            req.spanId = span.spanContext().spanId;

            // 监听响应结束
            res.on('finish', () => {
                const duration = (Date.now() - startTime) / 1000;
                
                // 更新 span 属性
                span.setAttributes({
                    'http.status_code': res.statusCode,
                    'http.response.size': res.get('content-length') || 0,
                    'http.response.content_type': res.get('content-type'),
                });

                // 记录指标
                this.httpRequestCounter.add(1, {
                    method: req.method,
                    status_code: res.statusCode.toString(),
                    route: req.route?.path || req.path,
                });

                this.httpRequestDuration.record(duration, {
                    method: req.method,
                    status_code: res.statusCode.toString(),
                    route: req.route?.path || req.path,
                });

                // 记录错误
                if (res.statusCode >= 400) {
                    span.recordException(new Error(`HTTP ${res.statusCode}`));
                    span.setStatus({
                        code: opentelemetry.SpanStatusCode.ERROR,
                        message: `HTTP ${res.statusCode}`,
                    });

                    this.errorCounter.add(1, {
                        type: 'http_error',
                        status_code: res.statusCode.toString(),
                    });
                }

                // 结束 span
                span.end();
            });

            // 在上下文中执行下一个中间件
            opentelemetry.context.with(ctx, () => {
                next();
            });
        };
    }

    /**
     * 创建子 span
     */
    createSpan(name, options = {}) {
        return this.tracer.startSpan(name, {
            kind: opentelemetry.SpanKind.INTERNAL,
            ...options,
        });
    }

    /**
     * 包装异步函数进行追踪
     */
    traceAsync(name, fn, options = {}) {
        return async (...args) => {
            const span = this.createSpan(name, options);
            
            try {
                const result = await opentelemetry.context.with(
                    opentelemetry.trace.setSpan(opentelemetry.context.active(), span),
                    () => fn(...args)
                );
                
                span.setStatus({ code: opentelemetry.SpanStatusCode.OK });
                return result;
                
            } catch (error) {
                span.recordException(error);
                span.setStatus({
                    code: opentelemetry.SpanStatusCode.ERROR,
                    message: error.message,
                });
                
                this.errorCounter.add(1, {
                    type: 'function_error',
                    function: name,
                });
                
                throw error;
            } finally {
                span.end();
            }
        };
    }

    /**
     * 记录数据库查询
     */
    recordDatabaseQuery(operation, table, duration) {
        this.dbQueryCounter.add(1, {
            operation,
            table,
        });

        const span = opentelemetry.trace.getActiveSpan();
        if (span) {
            span.addEvent('database.query', {
                'db.operation': operation,
                'db.table': table,
                'db.duration': duration,
            });
        }
    }

    /**
     * 记录缓存操作
     */
    recordCacheOperation(operation, hit, duration) {
        this.cacheOperationCounter.add(1, {
            operation,
            hit: hit.toString(),
        });

        const span = opentelemetry.trace.getActiveSpan();
        if (span) {
            span.addEvent('cache.operation', {
                'cache.operation': operation,
                'cache.hit': hit,
                'cache.duration': duration,
            });
        }
    }

    /**
     * 记录自定义事件
     */
    recordEvent(name, attributes = {}) {
        const span = opentelemetry.trace.getActiveSpan();
        if (span) {
            span.addEvent(name, attributes);
        }
    }

    /**
     * 添加 span 属性
     */
    addAttributes(attributes) {
        const span = opentelemetry.trace.getActiveSpan();
        if (span) {
            span.setAttributes(attributes);
        }
    }

    /**
     * 生成请求 ID
     */
    generateRequestId() {
        return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    /**
     * 获取当前追踪上下文
     */
    getCurrentContext() {
        const span = opentelemetry.trace.getActiveSpan();
        if (span) {
            const spanContext = span.spanContext();
            return {
                traceId: spanContext.traceId,
                spanId: spanContext.spanId,
                traceFlags: spanContext.traceFlags,
            };
        }
        return null;
    }

    /**
     * 注入追踪头到 HTTP 请求
     */
    injectHeaders(headers = {}) {
        opentelemetry.propagation.inject(opentelemetry.context.active(), headers);
        return headers;
    }

    /**
     * 从 HTTP 请求提取追踪上下文
     */
    extractContext(headers) {
        return opentelemetry.propagation.extract(opentelemetry.context.active(), headers);
    }

    /**
     * 关闭追踪系统
     */
    async shutdown() {
        if (this.sdk) {
            await this.sdk.shutdown();
            console.log('✅ 分布式追踪已关闭');
        }
    }

    /**
     * 获取追踪统计
     */
    getStats() {
        return {
            serviceName: this.options.serviceName,
            serviceVersion: this.options.serviceVersion,
            environment: this.options.environment,
            initialized: this.initialized,
            jaegerEndpoint: this.options.jaegerEndpoint,
            sampleRate: this.options.sampleRate,
        };
    }
}

module.exports = TracingMiddleware;
