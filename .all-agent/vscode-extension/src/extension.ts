import * as vscode from 'vscode';
import { AllAgentServer } from './server';
import { ChatProvider } from './providers/chatProvider';
import { AgentProvider } from './providers/agentProvider';
import { TaskProvider } from './providers/taskProvider';
import { AllAgentClient } from './client';

let server: AllAgentServer;
let client: AllAgentClient;

export function activate(context: vscode.ExtensionContext) {
    console.log('All-Agent extension is now active!');

    // 初始化服务器和客户端
    server = new AllAgentServer(context);
    client = new AllAgentClient();

    // 设置上下文
    vscode.commands.executeCommand('setContext', 'all-agent:enabled', true);

    // 注册命令
    registerCommands(context);

    // 注册视图提供者
    registerViewProviders(context);

    // 自动启动服务器（如果配置启用）
    const config = vscode.workspace.getConfiguration('all-agent');
    if (config.get('autoStart', true)) {
        vscode.commands.executeCommand('all-agent.startServer');
    }

    // 监听配置变化
    context.subscriptions.push(
        vscode.workspace.onDidChangeConfiguration(e => {
            if (e.affectsConfiguration('all-agent')) {
                handleConfigurationChange();
            }
        })
    );
}

function registerCommands(context: vscode.ExtensionContext) {
    // 启动服务器
    context.subscriptions.push(
        vscode.commands.registerCommand('all-agent.startServer', async () => {
            try {
                await server.start();
                vscode.window.showInformationMessage('All-Agent server started successfully!');
            } catch (error) {
                vscode.window.showErrorMessage(`Failed to start All-Agent server: ${error instanceof Error ? error.message : String(error)}`);
            }
        })
    );

    // 停止服务器
    context.subscriptions.push(
        vscode.commands.registerCommand('all-agent.stopServer', async () => {
            try {
                await server.stop();
                vscode.window.showInformationMessage('All-Agent server stopped.');
            } catch (error) {
                vscode.window.showErrorMessage(`Failed to stop All-Agent server: ${error instanceof Error ? error.message : String(error)}`);
            }
        })
    );

    // 打开聊天面板
    context.subscriptions.push(
        vscode.commands.registerCommand('all-agent.openChat', () => {
            const panel = vscode.window.createWebviewPanel(
                'all-agent-chat',
                'All-Agent Chat',
                vscode.ViewColumn.Beside,
                {
                    enableScripts: true,
                    retainContextWhenHidden: true
                }
            );

            panel.webview.html = getChatWebviewContent();

            // 处理来自 webview 的消息
            panel.webview.onDidReceiveMessage(
                async message => {
                    switch (message.command) {
                        case 'sendMessage':
                            try {
                                const response = await client.sendMessage(message.text);
                                panel.webview.postMessage({
                                    command: 'messageResponse',
                                    response: response
                                });
                            } catch (error) {
                                panel.webview.postMessage({
                                    command: 'error',
                                    error: error instanceof Error ? error.message : String(error)
                                });
                            }
                            break;
                    }
                }
            );
        })
    );

    // 分析项目
    context.subscriptions.push(
        vscode.commands.registerCommand('all-agent.analyzeProject', async (uri?: vscode.Uri) => {
            const workspaceFolder = uri ? vscode.workspace.getWorkspaceFolder(uri) : vscode.workspace.workspaceFolders?.[0];

            if (!workspaceFolder) {
                vscode.window.showErrorMessage('No workspace folder found');
                return;
            }

            try {
                vscode.window.withProgress({
                    location: vscode.ProgressLocation.Notification,
                    title: 'Analyzing project...',
                    cancellable: false
                }, async () => {
                    const analysis = await client.analyzeProject(workspaceFolder.uri.fsPath);

                    // 显示分析结果
                    const panel = vscode.window.createWebviewPanel(
                        'all-agent-analysis',
                        'Project Analysis',
                        vscode.ViewColumn.Active,
                        { enableScripts: true }
                    );

                    panel.webview.html = getAnalysisWebviewContent(analysis);
                });
            } catch (error) {
                vscode.window.showErrorMessage(`Analysis failed: ${error instanceof Error ? error.message : String(error)}`);
            }
        })
    );

    // 生成代码
    context.subscriptions.push(
        vscode.commands.registerCommand('all-agent.generateCode', async () => {
            const editor = vscode.window.activeTextEditor;
            if (!editor) {
                vscode.window.showErrorMessage('No active editor');
                return;
            }

            const selection = editor.selection;
            const selectedText = editor.document.getText(selection);

            if (!selectedText) {
                vscode.window.showErrorMessage('No text selected');
                return;
            }

            const prompt = await vscode.window.showInputBox({
                prompt: 'What would you like to do with the selected code?',
                placeHolder: 'e.g., "Add error handling", "Optimize performance", "Add comments"'
            });

            if (!prompt) return;

            try {
                vscode.window.withProgress({
                    location: vscode.ProgressLocation.Notification,
                    title: 'Generating code...',
                    cancellable: false
                }, async () => {
                    const result = await client.generateCode(selectedText, prompt);

                    // 替换选中的文本
                    await editor.edit(editBuilder => {
                        editBuilder.replace(selection, result);
                    });
                });
            } catch (error) {
                vscode.window.showErrorMessage(`Code generation failed: ${error instanceof Error ? error.message : String(error)}`);
            }
        })
    );

    // 打开设置
    context.subscriptions.push(
        vscode.commands.registerCommand('all-agent.openSettings', () => {
            vscode.commands.executeCommand('workbench.action.openSettings', 'all-agent');
        })
    );
}

function registerViewProviders(context: vscode.ExtensionContext) {
    // 聊天提供者
    const chatProvider = new ChatProvider(context, client);
    context.subscriptions.push(
        vscode.window.registerWebviewViewProvider('all-agent-chat', chatProvider)
    );

    // Agent 提供者
    const agentProvider = new AgentProvider(client);
    context.subscriptions.push(
        vscode.window.registerTreeDataProvider('all-agent-agents', agentProvider)
    );

    // 任务提供者
    const taskProvider = new TaskProvider(client);
    context.subscriptions.push(
        vscode.window.registerTreeDataProvider('all-agent-tasks', taskProvider)
    );
}

function handleConfigurationChange() {
    // 重新配置服务器
    if (server) {
        server.updateConfiguration();
    }
}

function getChatWebviewContent(): string {
    return `
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>All-Agent Chat</title>
        <style>
            body {
                font-family: var(--vscode-font-family);
                padding: 10px;
                background: var(--vscode-editor-background);
                color: var(--vscode-editor-foreground);
            }
            .chat-container {
                display: flex;
                flex-direction: column;
                height: 100vh;
            }
            .messages {
                flex: 1;
                overflow-y: auto;
                padding: 10px;
                border: 1px solid var(--vscode-panel-border);
                margin-bottom: 10px;
            }
            .message {
                margin-bottom: 10px;
                padding: 8px;
                border-radius: 4px;
            }
            .user-message {
                background: var(--vscode-button-background);
                color: var(--vscode-button-foreground);
                text-align: right;
            }
            .agent-message {
                background: var(--vscode-input-background);
                border: 1px solid var(--vscode-input-border);
            }
            .input-container {
                display: flex;
                gap: 10px;
            }
            input {
                flex: 1;
                padding: 8px;
                background: var(--vscode-input-background);
                color: var(--vscode-input-foreground);
                border: 1px solid var(--vscode-input-border);
            }
            button {
                padding: 8px 16px;
                background: var(--vscode-button-background);
                color: var(--vscode-button-foreground);
                border: none;
                cursor: pointer;
            }
        </style>
    </head>
    <body>
        <div class="chat-container">
            <div class="messages" id="messages"></div>
            <div class="input-container">
                <input type="text" id="messageInput" placeholder="Type your message...">
                <button onclick="sendMessage()">Send</button>
            </div>
        </div>

        <script>
            const vscode = acquireVsCodeApi();

            function sendMessage() {
                const input = document.getElementById('messageInput');
                const message = input.value.trim();
                if (!message) return;

                addMessage(message, 'user');
                input.value = '';

                vscode.postMessage({
                    command: 'sendMessage',
                    text: message
                });
            }

            function addMessage(text, sender) {
                const messages = document.getElementById('messages');
                const messageDiv = document.createElement('div');
                messageDiv.className = \`message \${sender}-message\`;
                messageDiv.textContent = text;
                messages.appendChild(messageDiv);
                messages.scrollTop = messages.scrollHeight;
            }

            window.addEventListener('message', event => {
                const message = event.data;
                switch (message.command) {
                    case 'messageResponse':
                        addMessage(message.response, 'agent');
                        break;
                    case 'error':
                        addMessage('Error: ' + message.error, 'agent');
                        break;
                }
            });

            document.getElementById('messageInput').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    sendMessage();
                }
            });
        </script>
    </body>
    </html>`;
}

function getAnalysisWebviewContent(analysis: any): string {
    return `
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <title>Project Analysis</title>
        <style>
            body {
                font-family: var(--vscode-font-family);
                padding: 20px;
                background: var(--vscode-editor-background);
                color: var(--vscode-editor-foreground);
            }
            .section {
                margin-bottom: 20px;
                padding: 15px;
                border: 1px solid var(--vscode-panel-border);
                border-radius: 4px;
            }
            h2 { color: var(--vscode-textLink-foreground); }
            pre {
                background: var(--vscode-textCodeBlock-background);
                padding: 10px;
                border-radius: 4px;
                overflow-x: auto;
            }
        </style>
    </head>
    <body>
        <h1>Project Analysis Results</h1>
        <div class="section">
            <h2>Summary</h2>
            <p>${analysis.summary || 'Analysis completed successfully'}</p>
        </div>
        <div class="section">
            <h2>Details</h2>
            <pre>${JSON.stringify(analysis, null, 2)}</pre>
        </div>
    </body>
    </html>`;
}

export function deactivate() {
    if (server) {
        server.stop();
    }
}
