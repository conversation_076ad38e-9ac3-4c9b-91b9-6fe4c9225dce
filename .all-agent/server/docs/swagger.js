const swaggerJsdoc = require('swagger-jsdoc');
const swaggerUi = require('swagger-ui-express');

/**
 * Swagger API 文档配置
 */
const swaggerOptions = {
    definition: {
        openapi: '3.0.0',
        info: {
            title: 'All-Agent API',
            version: '1.0.0',
            description: 'All-Agent AI 项目构建与执行系统 API 文档',
            contact: {
                name: 'All-Agent Team',
                email: '<EMAIL>'
            },
            license: {
                name: 'MIT',
                url: 'https://opensource.org/licenses/MIT'
            }
        },
        servers: [
            {
                url: 'http://localhost:3000',
                description: '开发服务器'
            },
            {
                url: 'https://api.all-agent.com',
                description: '生产服务器'
            }
        ],
        components: {
            securitySchemes: {
                bearerAuth: {
                    type: 'http',
                    scheme: 'bearer',
                    bearerFormat: 'JWT',
                    description: 'JWT 认证令牌'
                }
            },
            schemas: {
                User: {
                    type: 'object',
                    properties: {
                        id: {
                            type: 'integer',
                            description: '用户ID'
                        },
                        username: {
                            type: 'string',
                            description: '用户名'
                        },
                        email: {
                            type: 'string',
                            format: 'email',
                            description: '邮箱地址'
                        },
                        role: {
                            type: 'string',
                            enum: ['user', 'admin'],
                            description: '用户角色'
                        },
                        avatar_url: {
                            type: 'string',
                            format: 'uri',
                            description: '头像URL'
                        },
                        created_at: {
                            type: 'string',
                            format: 'date-time',
                            description: '创建时间'
                        },
                        last_login: {
                            type: 'string',
                            format: 'date-time',
                            description: '最后登录时间'
                        }
                    }
                },
                Project: {
                    type: 'object',
                    properties: {
                        id: {
                            type: 'integer',
                            description: '项目ID'
                        },
                        name: {
                            type: 'string',
                            description: '项目名称'
                        },
                        description: {
                            type: 'string',
                            description: '项目描述'
                        },
                        project_path: {
                            type: 'string',
                            description: '项目路径'
                        },
                        tech_stack: {
                            type: 'object',
                            description: '技术栈信息'
                        },
                        status: {
                            type: 'string',
                            enum: ['active', 'inactive', 'archived'],
                            description: '项目状态'
                        },
                        created_at: {
                            type: 'string',
                            format: 'date-time',
                            description: '创建时间'
                        }
                    }
                },
                Task: {
                    type: 'object',
                    properties: {
                        id: {
                            type: 'integer',
                            description: '任务ID'
                        },
                        task_id: {
                            type: 'string',
                            description: '任务唯一标识'
                        },
                        agent_type: {
                            type: 'string',
                            enum: ['analyzer', 'planner', 'executor'],
                            description: 'Agent 类型'
                        },
                        action: {
                            type: 'string',
                            description: '执行动作'
                        },
                        status: {
                            type: 'string',
                            enum: ['pending', 'running', 'completed', 'failed'],
                            description: '任务状态'
                        },
                        priority: {
                            type: 'string',
                            enum: ['low', 'normal', 'high', 'urgent'],
                            description: '任务优先级'
                        },
                        created_at: {
                            type: 'string',
                            format: 'date-time',
                            description: '创建时间'
                        },
                        completed_at: {
                            type: 'string',
                            format: 'date-time',
                            description: '完成时间'
                        }
                    }
                },
                ProjectAnalysis: {
                    type: 'object',
                    properties: {
                        projectPath: {
                            type: 'string',
                            description: '项目路径'
                        },
                        timestamp: {
                            type: 'string',
                            format: 'date-time',
                            description: '分析时间'
                        },
                        structure: {
                            type: 'object',
                            description: '项目结构信息'
                        },
                        techStack: {
                            type: 'object',
                            description: '技术栈信息'
                        },
                        dependencies: {
                            type: 'object',
                            description: '依赖关系'
                        },
                        statistics: {
                            type: 'object',
                            description: '统计信息'
                        },
                        recommendations: {
                            type: 'array',
                            items: {
                                type: 'string'
                            },
                            description: '建议列表'
                        }
                    }
                },
                Agent: {
                    type: 'object',
                    properties: {
                        id: {
                            type: 'string',
                            description: 'Agent ID'
                        },
                        type: {
                            type: 'string',
                            enum: ['analyzer', 'planner', 'executor'],
                            description: 'Agent 类型'
                        },
                        name: {
                            type: 'string',
                            description: 'Agent 名称'
                        },
                        status: {
                            type: 'string',
                            enum: ['idle', 'busy', 'error'],
                            description: 'Agent 状态'
                        },
                        capabilities: {
                            type: 'array',
                            items: {
                                type: 'string'
                            },
                            description: '能力列表'
                        },
                        lastActivity: {
                            type: 'string',
                            format: 'date-time',
                            description: '最后活动时间'
                        }
                    }
                },
                Error: {
                    type: 'object',
                    properties: {
                        success: {
                            type: 'boolean',
                            example: false
                        },
                        error: {
                            type: 'string',
                            description: '错误信息'
                        },
                        code: {
                            type: 'string',
                            description: '错误代码'
                        }
                    }
                },
                Success: {
                    type: 'object',
                    properties: {
                        success: {
                            type: 'boolean',
                            example: true
                        },
                        data: {
                            type: 'object',
                            description: '返回数据'
                        },
                        message: {
                            type: 'string',
                            description: '成功信息'
                        }
                    }
                }
            }
        },
        security: [
            {
                bearerAuth: []
            }
        ]
    },
    apis: [
        './routes/*.js',
        './app.js',
        './docs/api-docs.js'
    ]
};

/**
 * 生成 Swagger 规范
 */
const specs = swaggerJsdoc(swaggerOptions);

/**
 * 设置 Swagger UI
 */
function setupSwagger(app) {
    // Swagger UI 配置
    const swaggerUiOptions = {
        explorer: true,
        customCss: `
            .swagger-ui .topbar { display: none; }
            .swagger-ui .info .title { color: #667eea; }
            .swagger-ui .scheme-container { background: #f8f9fa; padding: 20px; border-radius: 8px; }
        `,
        customSiteTitle: 'All-Agent API 文档',
        customfavIcon: '/favicon.ico'
    };

    // 设置 API 文档路由
    app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(specs, swaggerUiOptions));
    
    // 提供 JSON 格式的 API 规范
    app.get('/api-docs.json', (req, res) => {
        res.setHeader('Content-Type', 'application/json');
        res.send(specs);
    });

    console.log('📚 Swagger API 文档已设置:');
    console.log('   - UI 界面: http://localhost:3000/api-docs');
    console.log('   - JSON 规范: http://localhost:3000/api-docs.json');
}

/**
 * 生成 API 文档数据
 */
function generateApiDocs() {
    return {
        info: specs.info,
        servers: specs.servers,
        paths: Object.keys(specs.paths || {}),
        components: Object.keys(specs.components?.schemas || {}),
        security: specs.security
    };
}

/**
 * 验证 API 规范
 */
function validateApiSpec() {
    const errors = [];
    
    if (!specs.info) {
        errors.push('缺少 API 信息');
    }
    
    if (!specs.paths || Object.keys(specs.paths).length === 0) {
        errors.push('没有定义 API 路径');
    }
    
    if (!specs.components?.schemas) {
        errors.push('没有定义数据模型');
    }
    
    return {
        valid: errors.length === 0,
        errors
    };
}

module.exports = {
    setupSwagger,
    generateApiDocs,
    validateApiSpec,
    specs
};
