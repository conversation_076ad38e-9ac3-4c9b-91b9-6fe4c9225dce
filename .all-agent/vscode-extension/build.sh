#!/bin/bash

# All-Agent VSCode Extension Build Script

echo "🚀 Building All-Agent VSCode Extension..."

# 检查 Node.js
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js first."
    exit 1
fi

# 检查 npm
if ! command -v npm &> /dev/null; then
    echo "❌ npm is not installed. Please install npm first."
    exit 1
fi

# 进入扩展目录
cd "$(dirname "$0")"

echo "📦 Installing dependencies..."
npm install

if [ $? -ne 0 ]; then
    echo "❌ Failed to install dependencies"
    exit 1
fi

echo "🔨 Compiling TypeScript..."
npm run compile

if [ $? -ne 0 ]; then
    echo "❌ Failed to compile TypeScript"
    exit 1
fi

# 检查是否安装了 vsce
if ! command -v vsce &> /dev/null; then
    echo "📦 Installing vsce (Visual Studio Code Extension manager)..."
    npm install -g vsce
fi

echo "📦 Packaging extension..."
vsce package

if [ $? -eq 0 ]; then
    echo "✅ Extension built successfully!"
    echo ""
    echo "📋 Installation Instructions:"
    echo "1. Open VSCode or Cursor"
    echo "2. Press Ctrl+Shift+P (or Cmd+Shift+P on Mac)"
    echo "3. Type 'Extensions: Install from VSIX'"
    echo "4. Select the generated .vsix file"
    echo ""
    echo "🔧 Configuration:"
    echo "1. Open VSCode Settings (Ctrl+, or Cmd+,)"
    echo "2. Search for 'All-Agent'"
    echo "3. Configure your API keys:"
    echo "   - DeepSeek API Key: ***********************************"
    echo "   - Mistral API Key: 1cPOCn740EGNqIcoKnWQqSky9Aqm6SSs"
    echo ""
    echo "🎉 Ready to use All-Agent in VSCode!"
else
    echo "❌ Failed to package extension"
    exit 1
fi
