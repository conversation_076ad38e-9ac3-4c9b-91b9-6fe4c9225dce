/**
 * 统一错误处理中心
 */
class ErrorHandler {
  constructor(logger) {
    this.logger = logger;
    this.errorCounts = new Map();
    this.errorPatterns = new Map();
  }

  /**
   * 自定义错误类
   */
  static createErrorClasses() {
    class AppError extends Error {
      constructor(message, statusCode = 500, code = 'INTERNAL_ERROR', details = {}) {
        super(message);
        this.name = this.constructor.name;
        this.statusCode = statusCode;
        this.code = code;
        this.details = details;
        this.timestamp = new Date().toISOString();
        this.isOperational = true;

        Error.captureStackTrace(this, this.constructor);
      }
    }

    class ValidationError extends AppError {
      constructor(message, details = {}) {
        super(message, 400, 'VALIDATION_ERROR', details);
      }
    }

    class AuthenticationError extends AppError {
      constructor(message = 'Authentication failed', details = {}) {
        super(message, 401, 'AUTHENTICATION_ERROR', details);
      }
    }

    class AuthorizationError extends AppError {
      constructor(message = 'Access denied', details = {}) {
        super(message, 403, 'AUTHORIZATION_ERROR', details);
      }
    }

    class NotFoundError extends AppError {
      constructor(message = 'Resource not found', details = {}) {
        super(message, 404, 'NOT_FOUND_ERROR', details);
      }
    }

    class ConflictError extends AppError {
      constructor(message = 'Resource conflict', details = {}) {
        super(message, 409, 'CONFLICT_ERROR', details);
      }
    }

    class RateLimitError extends AppError {
      constructor(message = 'Rate limit exceeded', details = {}) {
        super(message, 429, 'RATE_LIMIT_ERROR', details);
      }
    }

    class ExternalServiceError extends AppError {
      constructor(message = 'External service error', details = {}) {
        super(message, 502, 'EXTERNAL_SERVICE_ERROR', details);
      }
    }

    return {
      AppError,
      ValidationError,
      AuthenticationError,
      AuthorizationError,
      NotFoundError,
      ConflictError,
      RateLimitError,
      ExternalServiceError
    };
  }

  /**
   * 全局错误处理中间件
   */
  globalErrorHandler() {
    return (error, req, res, next) => {
      // 记录错误
      this.logError(error, req);

      // 更新错误统计
      this.updateErrorStats(error);

      // 检查是否是操作性错误
      if (error.isOperational) {
        return this.handleOperationalError(error, req, res);
      }

      // 处理程序错误
      return this.handleProgrammingError(error, req, res);
    };
  }

  /**
   * 处理操作性错误
   */
  handleOperationalError(error, req, res) {
    const response = {
      success: false,
      error: {
        code: error.code,
        message: error.message,
        timestamp: error.timestamp
      }
    };

    // 在开发环境中包含更多详细信息
    if (process.env.NODE_ENV === 'development') {
      response.error.details = error.details;
      response.error.stack = error.stack;
    }

    return res.status(error.statusCode).json(response);
  }

  /**
   * 处理程序错误
   */
  handleProgrammingError(error, req, res) {
    // 记录严重错误
    this.logger.error('Programming Error:', {
      error: error.message,
      stack: error.stack,
      url: req.url,
      method: req.method,
      ip: req.ip,
      userAgent: req.get('User-Agent')
    });

    // 返回通用错误响应
    const response = {
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'An internal server error occurred',
        timestamp: new Date().toISOString()
      }
    };

    // 在开发环境中包含错误详情
    if (process.env.NODE_ENV === 'development') {
      response.error.message = error.message;
      response.error.stack = error.stack;
    }

    return res.status(500).json(response);
  }

  /**
   * 记录错误
   */
  logError(error, req = null) {
    const errorInfo = {
      message: error.message,
      code: error.code || 'UNKNOWN_ERROR',
      statusCode: error.statusCode || 500,
      timestamp: new Date().toISOString(),
      stack: error.stack
    };

    if (req) {
      errorInfo.request = {
        url: req.url,
        method: req.method,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        body: this.sanitizeRequestBody(req.body)
      };
    }

    // 根据错误严重程度选择日志级别
    if (error.statusCode >= 500) {
      this.logger.error('Server Error:', errorInfo);
    } else if (error.statusCode >= 400) {
      this.logger.warn('Client Error:', errorInfo);
    } else {
      this.logger.info('Error:', errorInfo);
    }
  }

  /**
   * 清理请求体中的敏感信息
   */
  sanitizeRequestBody(body) {
    if (!body || typeof body !== 'object') {
      return body;
    }

    const sanitized = { ...body };
    const sensitiveFields = ['password', 'token', 'secret', 'key', 'auth'];

    for (const field of sensitiveFields) {
      if (sanitized[field]) {
        sanitized[field] = '[REDACTED]';
      }
    }

    return sanitized;
  }

  /**
   * 更新错误统计
   */
  updateErrorStats(error) {
    const errorKey = error.code || error.name || 'UNKNOWN_ERROR';
    const currentCount = this.errorCounts.get(errorKey) || 0;
    this.errorCounts.set(errorKey, currentCount + 1);

    // 检查错误模式
    this.detectErrorPatterns(error);
  }

  /**
   * 检测错误模式
   */
  detectErrorPatterns(error) {
    const now = Date.now();
    const timeWindow = 5 * 60 * 1000; // 5分钟窗口
    const errorKey = error.code || error.name || 'UNKNOWN_ERROR';

    if (!this.errorPatterns.has(errorKey)) {
      this.errorPatterns.set(errorKey, []);
    }

    const pattern = this.errorPatterns.get(errorKey);
    pattern.push(now);

    // 清理旧的错误记录
    const cutoff = now - timeWindow;
    this.errorPatterns.set(errorKey, pattern.filter(timestamp => timestamp > cutoff));

    // 检查是否有异常模式
    if (pattern.length > 10) { // 5分钟内超过10次相同错误
      this.logger.warn(`Error pattern detected: ${errorKey} occurred ${pattern.length} times in 5 minutes`);
      
      // 这里可以触发告警或自动恢复机制
      this.triggerErrorAlert(errorKey, pattern.length);
    }
  }

  /**
   * 触发错误告警
   */
  triggerErrorAlert(errorCode, count) {
    const alert = {
      type: 'error_pattern',
      errorCode,
      count,
      timestamp: new Date().toISOString(),
      severity: count > 20 ? 'critical' : 'warning'
    };

    this.logger.error('Error Alert:', alert);
    
    // 这里可以集成到告警系统
    // 例如：发送邮件、Slack通知、触发自动恢复等
  }

  /**
   * 异步错误处理
   */
  handleAsyncError(asyncFn) {
    return (req, res, next) => {
      Promise.resolve(asyncFn(req, res, next)).catch(next);
    };
  }

  /**
   * 进程级错误处理
   */
  setupProcessErrorHandlers() {
    // 未捕获的异常
    process.on('uncaughtException', (error) => {
      this.logger.error('Uncaught Exception:', {
        error: error.message,
        stack: error.stack
      });

      // 优雅关闭
      this.gracefulShutdown('uncaughtException');
    });

    // 未处理的 Promise 拒绝
    process.on('unhandledRejection', (reason, promise) => {
      this.logger.error('Unhandled Rejection:', {
        reason: reason instanceof Error ? reason.message : reason,
        stack: reason instanceof Error ? reason.stack : undefined,
        promise: promise.toString()
      });

      // 优雅关闭
      this.gracefulShutdown('unhandledRejection');
    });

    // 进程信号处理
    process.on('SIGTERM', () => {
      this.logger.info('SIGTERM received, shutting down gracefully');
      this.gracefulShutdown('SIGTERM');
    });

    process.on('SIGINT', () => {
      this.logger.info('SIGINT received, shutting down gracefully');
      this.gracefulShutdown('SIGINT');
    });
  }

  /**
   * 优雅关闭
   */
  gracefulShutdown(reason) {
    this.logger.info(`Graceful shutdown initiated: ${reason}`);
    
    // 给应用30秒时间清理资源
    setTimeout(() => {
      this.logger.error('Forced shutdown after timeout');
      process.exit(1);
    }, 30000);

    // 触发应用清理
    process.emit('shutdown', reason);
  }

  /**
   * 获取错误统计
   */
  getErrorStats() {
    return {
      errorCounts: Object.fromEntries(this.errorCounts),
      totalErrors: Array.from(this.errorCounts.values()).reduce((sum, count) => sum + count, 0),
      timestamp: new Date().toISOString()
    };
  }

  /**
   * 重置错误统计
   */
  resetErrorStats() {
    this.errorCounts.clear();
    this.errorPatterns.clear();
  }
}

// 导出错误类和处理器
const ErrorClasses = ErrorHandler.createErrorClasses();

module.exports = {
  ErrorHandler,
  ...ErrorClasses
};
