import * as vscode from 'vscode';
import axios from 'axios';
import * as WebSocket from 'ws';
import { TerminalService } from './services/terminalService';
import { BrowserService } from './services/browserService';
import { ContextService } from './services/contextService';
import { AutomationService } from './services/automationService';
import { FileSystemService } from './services/fileSystemService';
import { PerformanceService } from './services/performanceService';

export class AllAgentClient {
    private baseUrl: string;
    private ws: WebSocket | null = null;
    private authToken: string | null = null;
    private terminalService: TerminalService;
    private browserService: BrowserService;
    private contextService: ContextService;
    private automationService: AutomationService;
    private fileSystemService: FileSystemService;
    private performanceService: PerformanceService;

    constructor() {
        const config = vscode.workspace.getConfiguration('all-agent');
        const port = config.get('serverPort', 3000);
        this.baseUrl = `http://localhost:${port}`;

        // 初始化服务
        this.performanceService = new PerformanceService();
        this.terminalService = new TerminalService();
        this.browserService = new BrowserService(this.terminalService);
        this.contextService = new ContextService();
        this.fileSystemService = new FileSystemService();
        this.automationService = new AutomationService(
            this.terminalService,
            this.browserService,
            this.contextService
        );
    }

    async connect(): Promise<void> {
        try {
            // 首先尝试登录获取认证令牌
            await this.authenticate();

            // 建立 WebSocket 连接
            const wsUrl = this.baseUrl.replace('http', 'ws');
            this.ws = new WebSocket(wsUrl);

            return new Promise((resolve, reject) => {
                if (!this.ws) {
                    reject(new Error('WebSocket not initialized'));
                    return;
                }

                this.ws.on('open', () => {
                    console.log('Connected to All-Agent server');

                    // 发送认证信息
                    if (this.ws && this.authToken) {
                        this.ws.send(JSON.stringify({
                            type: 'auth',
                            token: this.authToken
                        }));
                    }

                    resolve();
                });

                this.ws.on('error', (error: Error) => {
                    console.error('WebSocket error:', error);
                    reject(error);
                });

                this.ws.on('close', () => {
                    console.log('Disconnected from All-Agent server');
                    this.ws = null;
                });
            });
        } catch (error) {
            throw new Error(`Failed to connect to All-Agent server: ${error}`);
        }
    }

    async disconnect(): Promise<void> {
        if (this.ws) {
            this.ws.close();
            this.ws = null;
        }
    }

    async sendMessage(message: string): Promise<string> {
        const timer = this.performanceService.startTimer('sendMessage');

        try {
            // 检查缓存
            const cacheKey = `message:${message.substring(0, 100)}`;
            const cached = this.performanceService.getCache(cacheKey);
            if (cached) {
                timer(true, { cached: true });
                return cached;
            }

            const response = await axios.post(`${this.baseUrl}/api/chat`, {
                message: message,
                agentType: 'analyzer' // 默认使用分析 Agent
            }, {
                headers: {
                    'Authorization': `Bearer ${this.authToken}`,
                    'Content-Type': 'application/json'
                },
                timeout: 30000 // 30秒超时
            });

            const result = response.data.response || 'No response received';

            // 缓存响应（5分钟）
            this.performanceService.setCache(cacheKey, result, 300000);

            timer(true, { responseLength: result.length });
            return result;
        } catch (error) {
            timer(false, { error: error instanceof Error ? error.message : String(error) });
            throw new Error(`Failed to send message: ${error}`);
        }
    }

    async analyzeProject(projectPath: string): Promise<any> {
        try {
            const response = await axios.post(`${this.baseUrl}/api/analyze`, {
                path: projectPath,
                options: {
                    includeFiles: true,
                    includeDependencies: true,
                    includeStructure: true
                }
            }, {
                headers: {
                    'Authorization': `Bearer ${this.authToken}`,
                    'Content-Type': 'application/json'
                }
            });

            return response.data;
        } catch (error) {
            throw new Error(`Failed to analyze project: ${error}`);
        }
    }

    async generateCode(context: string, prompt: string): Promise<string> {
        try {
            const response = await axios.post(`${this.baseUrl}/api/generate`, {
                context: context,
                prompt: prompt,
                type: 'code'
            }, {
                headers: {
                    'Authorization': `Bearer ${this.authToken}`,
                    'Content-Type': 'application/json'
                }
            });

            return response.data.code || response.data.result || 'No code generated';
        } catch (error) {
            throw new Error(`Failed to generate code: ${error}`);
        }
    }

    async getAgents(): Promise<any[]> {
        try {
            const response = await axios.get(`${this.baseUrl}/api/agents`, {
                headers: {
                    'Authorization': `Bearer ${this.authToken}`
                }
            });

            return response.data.agents || [];
        } catch (error) {
            console.error('Failed to get agents:', error);
            return [];
        }
    }

    async getTasks(): Promise<any[]> {
        try {
            const response = await axios.get(`${this.baseUrl}/api/tasks`, {
                headers: {
                    'Authorization': `Bearer ${this.authToken}`
                }
            });

            return response.data.tasks || [];
        } catch (error) {
            console.error('Failed to get tasks:', error);
            return [];
        }
    }

    private async authenticate(): Promise<void> {
        try {
            // 尝试使用默认管理员账户登录
            const response = await axios.post(`${this.baseUrl}/auth/login`, {
                email: '<EMAIL>',
                password: 'Cjb65691910'
            });

            this.authToken = response.data.token;
        } catch (error) {
            // 如果管理员登录失败，尝试创建临时用户
            try {
                const registerResponse = await axios.post(`${this.baseUrl}/auth/register`, {
                    username: 'vscode-extension',
                    email: '<EMAIL>',
                    password: 'vscode123456'
                });

                if (registerResponse.data.token) {
                    this.authToken = registerResponse.data.token;
                } else {
                    // 注册成功后登录
                    const loginResponse = await axios.post(`${this.baseUrl}/auth/login`, {
                        username: 'vscode-extension',
                        password: 'vscode123456'
                    });
                    this.authToken = loginResponse.data.token;
                }
            } catch (authError) {
                throw new Error(`Authentication failed: ${authError}`);
            }
        }
    }

    isConnected(): boolean {
        return this.ws !== null && this.ws.readyState === WebSocket.OPEN;
    }

    /**
     * 处理终端请求
     */
    async handleTerminalRequest(message: string): Promise<string> {
        try {
            // 使用智能命令解析
            const result = await this.terminalService.executeSmartCommand(message, {
                showOutput: true,
                terminalName: 'All-Agent'
            });

            if (result.success) {
                let response = `✅ **Command executed successfully**\n\n`;

                if (result.output) {
                    response += `**Output:**\n\`\`\`\n${result.output}\n\`\`\``;
                } else {
                    response += `Command completed without output.`;
                }

                return response;
            } else {
                let response = `❌ **Command failed**\n\n`;

                if (result.error) {
                    response += `**Error:**\n\`\`\`\n${result.error}\n\`\`\`\n\n`;
                }

                if (result.suggestion) {
                    response += `**Suggestion:** ${result.suggestion}`;
                }

                return response;
            }
        } catch (error) {
            return `❌ Terminal error: ${error instanceof Error ? error.message : String(error)}`;
        }
    }

    /**
     * 处理文件请求
     */
    async handleFileRequest(message: string, context: any): Promise<string> {
        try {
            const lowerMessage = message.toLowerCase();

            // 文件搜索请求
            if (lowerMessage.includes('search') || lowerMessage.includes('find')) {
                const searchQuery = this.extractSearchQuery(message);
                if (searchQuery) {
                    const results = await this.fileSystemService.smartFileSearch(searchQuery, {
                        maxResults: 10
                    });

                    let response = `🔍 **File Search Results for "${searchQuery}"**\n\n`;

                    if (results.length === 0) {
                        response += `No files found matching "${searchQuery}".`;
                    } else {
                        response += `Found ${results.length} files:\n\n`;
                        results.forEach((result, index) => {
                            const fileName = result.file.split('/').pop();
                            response += `${index + 1}. **${fileName}** (Score: ${result.score})\n`;
                            response += `   Path: ${result.file}\n`;
                            if (result.matches.length > 0) {
                                response += `   Matches: ${result.matches.length} lines\n`;
                                result.matches.slice(0, 2).forEach(match => {
                                    response += `   Line ${match.line}: ${match.content}\n`;
                                });
                            }
                            response += '\n';
                        });
                    }

                    return response;
                }
            }

            // 文件创建请求
            if (lowerMessage.includes('create') || lowerMessage.includes('generate')) {
                const templateType = this.detectTemplateType(message);
                const fileName = this.extractFileName(message);

                if (templateType && fileName) {
                    const template = await this.fileSystemService.generateFileTemplate(
                        templateType,
                        fileName
                    );

                    if (template.success) {
                        return `✅ **File Template Generated**\n\n` +
                               `**File:** ${fileName}\n` +
                               `**Type:** ${templateType}\n\n` +
                               `**Preview:**\n\`\`\`\n${template.content.substring(0, 500)}...\n\`\`\`\n\n` +
                               `Would you like me to create this file?`;
                    }
                }
            }

            // 重构请求
            if (lowerMessage.includes('refactor') || lowerMessage.includes('optimize')) {
                const currentFile = context.editor?.fileName;
                if (currentFile) {
                    const refactorType = this.detectRefactorType(message);
                    const result = await this.fileSystemService.smartRefactor(
                        currentFile,
                        refactorType
                    );

                    let response = `🔧 **Code Refactoring Results**\n\n`;

                    if (result.success) {
                        response += `**Changes made:** ${result.changes.length}\n\n`;
                        result.changes.slice(0, 5).forEach((change, index) => {
                            response += `${index + 1}. Line ${change.line}:\n`;
                            response += `   Before: \`${change.oldContent}\`\n`;
                            response += `   After:  \`${change.newContent}\`\n\n`;
                        });
                    } else {
                        response += `No changes made. ${result.error || 'No optimizations found.'}`;
                    }

                    return response;
                }
            }

            // 默认处理
            const contextualMessage = `File operation request in ${context.workspace?.name || 'unknown project'}:
Current file: ${context.editor?.fileName || 'none'}
Selected text: ${context.editor?.selection?.text ? 'yes' : 'no'}
Request: ${message}`;

            return await this.sendMessage(contextualMessage);
        } catch (error) {
            return `❌ File operation error: ${error instanceof Error ? error.message : String(error)}`;
        }
    }

    private extractSearchQuery(message: string): string | null {
        const patterns = [
            /search\s+(?:for\s+)?["']([^"']+)["']/i,
            /find\s+(?:files?\s+)?["']([^"']+)["']/i,
            /search\s+(?:for\s+)?(\w+)/i,
            /find\s+(\w+)/i
        ];

        for (const pattern of patterns) {
            const match = message.match(pattern);
            if (match) {
                return match[1];
            }
        }

        return null;
    }

    private detectTemplateType(message: string): string | null {
        const lowerMessage = message.toLowerCase();

        if (lowerMessage.includes('react') || lowerMessage.includes('component')) {
            return 'react-component';
        }
        if (lowerMessage.includes('service') || lowerMessage.includes('class')) {
            return 'node-service';
        }
        if (lowerMessage.includes('test')) {
            return 'test-file';
        }
        if (lowerMessage.includes('config')) {
            return 'config-file';
        }

        return null;
    }

    private extractFileName(message: string): string | null {
        const patterns = [
            /create\s+(?:file\s+)?["']([^"']+)["']/i,
            /generate\s+(?:file\s+)?["']([^"']+)["']/i,
            /create\s+(\w+\.\w+)/i,
            /generate\s+(\w+\.\w+)/i
        ];

        for (const pattern of patterns) {
            const match = message.match(pattern);
            if (match) {
                return match[1];
            }
        }

        return null;
    }

    private detectRefactorType(message: string): 'extract-function' | 'rename-variable' | 'add-types' | 'optimize-imports' {
        const lowerMessage = message.toLowerCase();

        if (lowerMessage.includes('import')) {
            return 'optimize-imports';
        }
        if (lowerMessage.includes('type')) {
            return 'add-types';
        }
        if (lowerMessage.includes('extract')) {
            return 'extract-function';
        }

        return 'optimize-imports';
    }

    /**
     * 处理分析请求
     */
    async handleAnalysisRequest(message: string, context: any): Promise<string> {
        try {
            const projectInfo = context.project || {};
            const analysisContext = `Project analysis request:
Project: ${context.workspace?.name || 'unknown'}
Type: ${projectInfo.type || 'unknown'}
Technologies: ${projectInfo.technologies?.join(', ') || 'none detected'}
Has tests: ${projectInfo.hasTests ? 'yes' : 'no'}
Recent errors: ${context.errors?.length || 0}
Request: ${message}`;

            return await this.sendMessage(analysisContext);
        } catch (error) {
            return `❌ Analysis error: ${error instanceof Error ? error.message : String(error)}`;
        }
    }

    /**
     * 处理浏览器请求
     */
    async handleBrowserRequest(message: string): Promise<string> {
        try {
            // 检测本地服务器
            const servers = await this.browserService.detectLocalServers();

            let response = `🌐 Browser request: ${message}\n\n`;

            if (servers.length > 0) {
                response += `**Detected local servers:**\n`;
                servers.forEach(server => {
                    response += `• ${server.url} (${server.service || 'Unknown service'})\n`;
                });
                response += '\n';
            } else {
                response += `No local servers detected on common ports.\n\n`;
            }

            // 如果消息包含 URL，检查页面状态
            const urlMatch = message.match(/(https?:\/\/[^\s]+)/);
            if (urlMatch) {
                const url = urlMatch[1];
                const status = await this.browserService.checkPageStatus(url);
                response += `**Page status for ${url}:**\n`;
                response += `• Status: ${status.status}\n`;
                response += `• Accessible: ${status.accessible ? 'Yes' : 'No'}\n`;
                response += `• Response time: ${status.responseTime}ms\n`;
            }

            return response;
        } catch (error) {
            return `❌ Browser error: ${error instanceof Error ? error.message : String(error)}`;
        }
    }

    /**
     * 处理调试请求
     */
    async handleDebugRequest(message: string, context: any): Promise<string> {
        try {
            const errors = context.errors || [];
            let response = `🔧 **Debug Analysis**\n\n`;

            // 检查是否是自动修复请求
            if (message.toLowerCase().includes('fix') || message.toLowerCase().includes('auto')) {
                const fixResult = await this.automationService.autoFixProject();

                response += `**Auto-fix Results:**\n`;
                response += `• Fixed: ${fixResult.fixes.filter(f => f.result === 'success').length} issues\n`;
                response += `• Failed: ${fixResult.fixes.filter(f => f.result === 'failed').length} issues\n`;
                response += `• Skipped: ${fixResult.fixes.filter(f => f.result === 'skipped').length} issues\n\n`;

                if (fixResult.fixes.length > 0) {
                    response += `**Fix Details:**\n`;
                    fixResult.fixes.forEach((fix, index) => {
                        const icon = fix.result === 'success' ? '✅' : fix.result === 'failed' ? '❌' : '⏭️';
                        response += `${icon} ${fix.issue}\n`;
                        response += `   Action: ${fix.action}\n`;
                        if (fix.details) {
                            response += `   Details: ${fix.details}\n`;
                        }
                        response += '\n';
                    });
                }

                return response;
            }

            // 常规调试分析
            if (errors.length > 0) {
                response += `**Current Errors (${errors.length}):**\n`;
                errors.slice(0, 5).forEach((error: any, index: number) => {
                    response += `${index + 1}. **${error.file}:${error.line}**\n`;
                    response += `   ${error.message}\n`;
                    if (error.source) {
                        response += `   Source: ${error.source}\n`;
                    }
                    response += '\n';
                });

                response += `💡 **Quick Actions:**\n`;
                response += `• Type "fix errors" for automatic fixes\n`;
                response += `• Type "analyze project" for detailed analysis\n\n`;
            } else {
                response += `✅ **No current errors detected**\n\n`;
            }

            response += `**Project Health:**\n`;
            response += `• Type: ${context.project?.type || 'unknown'}\n`;
            response += `• Technologies: ${context.project?.technologies?.join(', ') || 'none'}\n`;
            response += `• Has tests: ${context.project?.hasTests ? 'yes' : 'no'}\n`;
            response += `• Health score: ${context.project?.health?.score || 'unknown'}\n`;

            if (context.project?.health?.issues?.length > 0) {
                response += `\n**Health Issues:**\n`;
                context.project.health.issues.forEach((issue: string) => {
                    response += `• ${issue}\n`;
                });
            }

            return response;
        } catch (error) {
            return `❌ Debug error: ${error instanceof Error ? error.message : String(error)}`;
        }
    }

    /**
     * 获取上下文服务
     */
    getContextService(): ContextService {
        return this.contextService;
    }

    /**
     * 获取终端服务
     */
    getTerminalService(): TerminalService {
        return this.terminalService;
    }

    /**
     * 获取浏览器服务
     */
    getBrowserService(): BrowserService {
        return this.browserService;
    }

    /**
     * 获取性能服务
     */
    getPerformanceService(): PerformanceService {
        return this.performanceService;
    }

    /**
     * 获取文件系统服务
     */
    getFileSystemService(): FileSystemService {
        return this.fileSystemService;
    }

    updateConfiguration(): void {
        const config = vscode.workspace.getConfiguration('all-agent');
        const port = config.get('serverPort', 3000);
        this.baseUrl = `http://localhost:${port}`;

        // 如果已连接，重新连接以使用新配置
        if (this.isConnected()) {
            this.disconnect().then(() => {
                this.connect().catch(error => {
                    console.error('Failed to reconnect:', error);
                });
            });
        }
    }

    /**
     * 清理资源
     */
    dispose(): void {
        this.disconnect();
        this.terminalService.dispose();
        this.browserService.dispose();
        this.contextService.dispose();
    }
}
