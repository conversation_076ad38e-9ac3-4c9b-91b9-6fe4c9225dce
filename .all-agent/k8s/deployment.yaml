apiVersion: apps/v1
kind: Deployment
metadata:
  name: all-agent-app
  namespace: all-agent
  labels:
    app.kubernetes.io/name: all-agent
    app.kubernetes.io/component: app
    app.kubernetes.io/version: "1.0.0"
spec:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: all-agent
      app.kubernetes.io/component: app
  template:
    metadata:
      labels:
        app.kubernetes.io/name: all-agent
        app.kubernetes.io/component: app
        app.kubernetes.io/version: "1.0.0"
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "3000"
        prometheus.io/path: "/metrics"
    spec:
      serviceAccountName: all-agent-sa
      securityContext:
        runAsNonRoot: true
        runAsUser: 1001
        runAsGroup: 1001
        fsGroup: 1001
      containers:
      - name: all-agent
        image: all-agent:latest
        imagePullPolicy: IfNotPresent
        ports:
        - name: http
          containerPort: 3000
          protocol: TCP
        env:
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: all-agent-secrets
              key: jwt-secret
        - name: REDIS_PASSWORD
          valueFrom:
            secretKeyRef:
              name: all-agent-secrets
              key: redis-password
              optional: true
        - name: OPENAI_API_KEY
          valueFrom:
            secretKeyRef:
              name: all-agent-secrets
              key: openai-api-key
              optional: true
        - name: ANTHROPIC_API_KEY
          valueFrom:
            secretKeyRef:
              name: all-agent-secrets
              key: anthropic-api-key
              optional: true
        envFrom:
        - configMapRef:
            name: all-agent-config
        volumeMounts:
        - name: data
          mountPath: /app/data
        - name: reports
          mountPath: /app/reports
        - name: logs
          mountPath: /app/logs
        resources:
          requests:
            cpu: 200m
            memory: 256Mi
          limits:
            cpu: 1000m
            memory: 1Gi
        livenessProbe:
          httpGet:
            path: /health
            port: http
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /health
            port: http
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: false
          capabilities:
            drop:
            - ALL
      volumes:
      - name: data
        persistentVolumeClaim:
          claimName: all-agent-data-pvc
      - name: reports
        persistentVolumeClaim:
          claimName: all-agent-reports-pvc
      - name: logs
        emptyDir: {}
      restartPolicy: Always
      terminationGracePeriodSeconds: 30
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: all-agent-redis
  namespace: all-agent
  labels:
    app.kubernetes.io/name: all-agent
    app.kubernetes.io/component: redis
spec:
  replicas: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: all-agent
      app.kubernetes.io/component: redis
  template:
    metadata:
      labels:
        app.kubernetes.io/name: all-agent
        app.kubernetes.io/component: redis
    spec:
      containers:
      - name: redis
        image: redis:7-alpine
        ports:
        - name: redis
          containerPort: 6379
          protocol: TCP
        command:
        - redis-server
        - --appendonly
        - "yes"
        - --requirepass
        - $(REDIS_PASSWORD)
        env:
        - name: REDIS_PASSWORD
          valueFrom:
            secretKeyRef:
              name: all-agent-secrets
              key: redis-password
              optional: true
        volumeMounts:
        - name: redis-data
          mountPath: /data
        resources:
          requests:
            cpu: 100m
            memory: 128Mi
          limits:
            cpu: 500m
            memory: 512Mi
        livenessProbe:
          exec:
            command:
            - redis-cli
            - ping
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          exec:
            command:
            - redis-cli
            - ping
          initialDelaySeconds: 5
          periodSeconds: 5
      volumes:
      - name: redis-data
        persistentVolumeClaim:
          claimName: all-agent-redis-pvc
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: all-agent-nginx
  namespace: all-agent
  labels:
    app.kubernetes.io/name: all-agent
    app.kubernetes.io/component: nginx
spec:
  replicas: 2
  selector:
    matchLabels:
      app.kubernetes.io/name: all-agent
      app.kubernetes.io/component: nginx
  template:
    metadata:
      labels:
        app.kubernetes.io/name: all-agent
        app.kubernetes.io/component: nginx
    spec:
      containers:
      - name: nginx
        image: nginx:alpine
        ports:
        - name: http
          containerPort: 80
          protocol: TCP
        volumeMounts:
        - name: nginx-config
          mountPath: /etc/nginx/nginx.conf
          subPath: nginx.conf
        resources:
          requests:
            cpu: 50m
            memory: 64Mi
          limits:
            cpu: 200m
            memory: 256Mi
        livenessProbe:
          httpGet:
            path: /health
            port: http
          initialDelaySeconds: 10
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: http
          initialDelaySeconds: 5
          periodSeconds: 5
      volumes:
      - name: nginx-config
        configMap:
          name: nginx-config
