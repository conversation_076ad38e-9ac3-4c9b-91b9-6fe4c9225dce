#!/usr/bin/env node

/**
 * All-Agent Server - 重构后的简化启动文件
 * 一个强大的多Agent AI系统，支持代码分析、规划和执行
 */

const ServerCore = require('./core/ServerCore');
const { ErrorHandler } = require('./utils/ErrorHandler');
const { loadEnvironmentVariables } = require('./utils/Environment');
const Logger = require('./utils/Logger');

// 创建核心实例
const logger = new Logger();
const errorHandler = new ErrorHandler(logger);

// 设置进程级错误处理
errorHandler.setupProcessErrorHandlers();

// 加载环境变量
loadEnvironmentVariables();

// 创建服务器实例
const server = new ServerCore({
  port: process.env.PORT || 3000,
  projectRoot: process.cwd()
});

// 优雅关闭处理
process.on('shutdown', async (reason) => {
  logger.info(`Shutdown signal received: ${reason}`);
  
  try {
    await server.stop();
    logger.info('Server stopped successfully');
    process.exit(0);
  } catch (error) {
    logger.error('Error during shutdown:', error);
    process.exit(1);
  }
});

// 启动服务器
async function startServer() {
  try {
    logger.info('🚀 Starting All-Agent Server...');
    
    await server.start();
    
    // 显示启动信息
    const stats = server.getStats();
    logger.info('📊 Server Statistics:', stats);
    
    // 显示配置摘要
    const { getConfigSummary } = require('./utils/Environment');
    const configSummary = getConfigSummary();
    logger.info('⚙️ Configuration Summary:', configSummary);
    
  } catch (error) {
    logger.error('❌ Failed to start server:', error);
    process.exit(1);
  }
}

// 如果直接运行此文件，启动服务器
if (require.main === module) {
  startServer();
}

// 导出服务器实例供测试使用
module.exports = { server, logger, errorHandler };
