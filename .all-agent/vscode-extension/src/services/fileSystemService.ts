import * as vscode from 'vscode';
import * as path from 'path';

/**
 * 文件系统服务 - 提供智能文件操作和批量处理能力
 */
export class FileSystemService {
    private outputChannel: vscode.OutputChannel;

    constructor() {
        this.outputChannel = vscode.window.createOutputChannel('All-Agent File Operations');
    }

    /**
     * 智能文件搜索
     */
    async smartFileSearch(query: string, options: {
        includeContent?: boolean;
        fileTypes?: string[];
        excludePatterns?: string[];
        maxResults?: number;
    } = {}): Promise<Array<{
        file: string;
        matches: Array<{
            line: number;
            content: string;
            context?: string;
        }>;
        score: number;
    }>> {
        const {
            includeContent = true,
            fileTypes = ['js', 'ts', 'jsx', 'tsx', 'py', 'java', 'cpp', 'c', 'cs'],
            excludePatterns = ['**/node_modules/**', '**/dist/**', '**/build/**'],
            maxResults = 50
        } = options;

        const results: Array<{
            file: string;
            matches: Array<{
                line: number;
                content: string;
                context?: string;
            }>;
            score: number;
        }> = [];

        try {
            // 构建搜索模式
            const searchPattern = fileTypes.length > 0 
                ? `**/*.{${fileTypes.join(',')}}` 
                : '**/*';

            const files = await vscode.workspace.findFiles(
                searchPattern,
                `{${excludePatterns.join(',')}}`,
                maxResults * 2
            );

            for (const file of files) {
                try {
                    const document = await vscode.workspace.openTextDocument(file);
                    const content = document.getText();
                    const lines = content.split('\n');
                    
                    const matches: Array<{
                        line: number;
                        content: string;
                        context?: string;
                    }> = [];

                    let score = 0;

                    // 文件名匹配
                    const fileName = path.basename(file.fsPath).toLowerCase();
                    if (fileName.includes(query.toLowerCase())) {
                        score += 10;
                    }

                    // 内容匹配
                    if (includeContent) {
                        lines.forEach((line, index) => {
                            if (line.toLowerCase().includes(query.toLowerCase())) {
                                matches.push({
                                    line: index + 1,
                                    content: line.trim(),
                                    context: this.getLineContext(lines, index)
                                });
                                score += 1;
                            }
                        });
                    }

                    if (score > 0) {
                        results.push({
                            file: file.fsPath,
                            matches,
                            score
                        });
                    }
                } catch (error) {
                    // 忽略无法读取的文件
                }
            }

            // 按分数排序
            results.sort((a, b) => b.score - a.score);
            return results.slice(0, maxResults);
        } catch (error) {
            this.outputChannel.appendLine(`Search error: ${error}`);
            return [];
        }
    }

    /**
     * 获取行上下文
     */
    private getLineContext(lines: string[], lineIndex: number): string {
        const start = Math.max(0, lineIndex - 2);
        const end = Math.min(lines.length, lineIndex + 3);
        return lines.slice(start, end).join('\n');
    }

    /**
     * 批量文件操作
     */
    async batchFileOperation(operation: 'create' | 'delete' | 'rename' | 'move', files: Array<{
        source: string;
        target?: string;
        content?: string;
    }>): Promise<{
        success: boolean;
        results: Array<{
            file: string;
            success: boolean;
            error?: string;
        }>;
    }> {
        const results: Array<{
            file: string;
            success: boolean;
            error?: string;
        }> = [];

        for (const fileOp of files) {
            try {
                switch (operation) {
                    case 'create':
                        await this.createFile(fileOp.source, fileOp.content || '');
                        results.push({ file: fileOp.source, success: true });
                        break;
                    case 'delete':
                        await this.deleteFile(fileOp.source);
                        results.push({ file: fileOp.source, success: true });
                        break;
                    case 'rename':
                        if (fileOp.target) {
                            await this.renameFile(fileOp.source, fileOp.target);
                            results.push({ file: fileOp.source, success: true });
                        }
                        break;
                    case 'move':
                        if (fileOp.target) {
                            await this.moveFile(fileOp.source, fileOp.target);
                            results.push({ file: fileOp.source, success: true });
                        }
                        break;
                }
            } catch (error) {
                results.push({
                    file: fileOp.source,
                    success: false,
                    error: error instanceof Error ? error.message : String(error)
                });
            }
        }

        const successCount = results.filter(r => r.success).length;
        return {
            success: successCount > 0,
            results
        };
    }

    /**
     * 创建文件
     */
    private async createFile(filePath: string, content: string): Promise<void> {
        const uri = vscode.Uri.file(filePath);
        const encoder = new TextEncoder();
        await vscode.workspace.fs.writeFile(uri, encoder.encode(content));
        this.outputChannel.appendLine(`Created: ${filePath}`);
    }

    /**
     * 删除文件
     */
    private async deleteFile(filePath: string): Promise<void> {
        const uri = vscode.Uri.file(filePath);
        await vscode.workspace.fs.delete(uri);
        this.outputChannel.appendLine(`Deleted: ${filePath}`);
    }

    /**
     * 重命名文件
     */
    private async renameFile(oldPath: string, newPath: string): Promise<void> {
        const oldUri = vscode.Uri.file(oldPath);
        const newUri = vscode.Uri.file(newPath);
        await vscode.workspace.fs.rename(oldUri, newUri);
        this.outputChannel.appendLine(`Renamed: ${oldPath} -> ${newPath}`);
    }

    /**
     * 移动文件
     */
    private async moveFile(sourcePath: string, targetPath: string): Promise<void> {
        const sourceUri = vscode.Uri.file(sourcePath);
        const targetUri = vscode.Uri.file(targetPath);
        await vscode.workspace.fs.rename(sourceUri, targetUri);
        this.outputChannel.appendLine(`Moved: ${sourcePath} -> ${targetPath}`);
    }

    /**
     * 智能代码重构
     */
    async smartRefactor(filePath: string, refactorType: 'extract-function' | 'rename-variable' | 'add-types' | 'optimize-imports', options: any = {}): Promise<{
        success: boolean;
        changes: Array<{
            line: number;
            oldContent: string;
            newContent: string;
        }>;
        error?: string;
    }> {
        try {
            const document = await vscode.workspace.openTextDocument(vscode.Uri.file(filePath));
            const content = document.getText();
            const lines = content.split('\n');
            
            const changes: Array<{
                line: number;
                oldContent: string;
                newContent: string;
            }> = [];

            switch (refactorType) {
                case 'optimize-imports':
                    return await this.optimizeImports(lines, changes);
                case 'add-types':
                    return await this.addTypeAnnotations(lines, changes);
                default:
                    return {
                        success: false,
                        changes: [],
                        error: `Refactor type '${refactorType}' not implemented yet`
                    };
            }
        } catch (error) {
            return {
                success: false,
                changes: [],
                error: error instanceof Error ? error.message : String(error)
            };
        }
    }

    /**
     * 优化导入语句
     */
    private async optimizeImports(lines: string[], changes: Array<{
        line: number;
        oldContent: string;
        newContent: string;
    }>): Promise<{
        success: boolean;
        changes: Array<{
            line: number;
            oldContent: string;
            newContent: string;
        }>;
    }> {
        const imports: Array<{ line: number; content: string; }> = [];
        
        // 收集所有导入语句
        lines.forEach((line, index) => {
            if (line.trim().startsWith('import ') && !line.includes('//')) {
                imports.push({ line: index, content: line });
            }
        });

        // 按模块名排序
        imports.sort((a, b) => {
            const moduleA = this.extractModuleName(a.content);
            const moduleB = this.extractModuleName(b.content);
            
            // 内置模块优先
            const isBuiltinA = !moduleA.startsWith('./') && !moduleA.startsWith('../');
            const isBuiltinB = !moduleB.startsWith('./') && !moduleB.startsWith('../');
            
            if (isBuiltinA && !isBuiltinB) return -1;
            if (!isBuiltinA && isBuiltinB) return 1;
            
            return moduleA.localeCompare(moduleB);
        });

        // 生成变更
        imports.forEach((imp, index) => {
            if (index < imports.length && imp.content !== lines[imp.line]) {
                changes.push({
                    line: imp.line + 1,
                    oldContent: lines[imp.line],
                    newContent: imp.content
                });
            }
        });

        return {
            success: changes.length > 0,
            changes
        };
    }

    /**
     * 提取模块名
     */
    private extractModuleName(importLine: string): string {
        const match = importLine.match(/from\s+['"]([^'"]+)['"]/);
        return match ? match[1] : '';
    }

    /**
     * 添加类型注解
     */
    private async addTypeAnnotations(lines: string[], changes: Array<{
        line: number;
        oldContent: string;
        newContent: string;
    }>): Promise<{
        success: boolean;
        changes: Array<{
            line: number;
            oldContent: string;
            newContent: string;
        }>;
    }> {
        lines.forEach((line, index) => {
            // 简单的函数参数类型推断
            const functionMatch = line.match(/function\s+(\w+)\s*\(([^)]*)\)/);
            if (functionMatch && !line.includes(':')) {
                const params = functionMatch[2];
                if (params && !params.includes(':')) {
                    const typedParams = params.split(',').map(param => {
                        const trimmed = param.trim();
                        if (trimmed && !trimmed.includes(':')) {
                            return `${trimmed}: any`;
                        }
                        return trimmed;
                    }).join(', ');
                    
                    const newLine = line.replace(
                        `(${params})`,
                        `(${typedParams}): any`
                    );
                    
                    changes.push({
                        line: index + 1,
                        oldContent: line,
                        newContent: newLine
                    });
                }
            }
        });

        return {
            success: changes.length > 0,
            changes
        };
    }

    /**
     * 生成文件模板
     */
    async generateFileTemplate(templateType: string, fileName: string, options: any = {}): Promise<{
        success: boolean;
        content: string;
        filePath: string;
    }> {
        const templates: { [key: string]: (name: string, options: any) => string } = {
            'react-component': (name, opts) => this.generateReactComponent(name, opts),
            'node-service': (name, opts) => this.generateNodeService(name, opts),
            'test-file': (name, opts) => this.generateTestFile(name, opts),
            'config-file': (name, opts) => this.generateConfigFile(name, opts)
        };

        const generator = templates[templateType];
        if (!generator) {
            return {
                success: false,
                content: '',
                filePath: ''
            };
        }

        const content = generator(fileName, options);
        const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
        const filePath = workspaceFolder 
            ? path.join(workspaceFolder.uri.fsPath, fileName)
            : fileName;

        return {
            success: true,
            content,
            filePath
        };
    }

    private generateReactComponent(name: string, options: any): string {
        const componentName = name.replace(/\.[^/.]+$/, ""); // 移除扩展名
        return `import React from 'react';

interface ${componentName}Props {
  // Add your props here
}

const ${componentName}: React.FC<${componentName}Props> = (props) => {
  return (
    <div>
      <h1>${componentName}</h1>
      {/* Add your component content here */}
    </div>
  );
};

export default ${componentName};
`;
    }

    private generateNodeService(name: string, options: any): string {
        const serviceName = name.replace(/\.[^/.]+$/, "");
        return `class ${serviceName} {
  constructor() {
    // Initialize service
  }

  async initialize(): Promise<void> {
    // Service initialization logic
  }

  async process(data: any): Promise<any> {
    // Service processing logic
    return data;
  }

  async cleanup(): Promise<void> {
    // Cleanup resources
  }
}

export default ${serviceName};
`;
    }

    private generateTestFile(name: string, options: any): string {
        const testName = name.replace(/\.[^/.]+$/, "");
        return `import { describe, it, expect, beforeEach, afterEach } from '@jest/globals';

describe('${testName}', () => {
  beforeEach(() => {
    // Setup before each test
  });

  afterEach(() => {
    // Cleanup after each test
  });

  it('should work correctly', () => {
    // Test implementation
    expect(true).toBe(true);
  });

  it('should handle edge cases', () => {
    // Edge case testing
    expect(true).toBe(true);
  });
});
`;
    }

    private generateConfigFile(name: string, options: any): string {
        if (name.includes('tsconfig')) {
            return `{
  "compilerOptions": {
    "target": "ES2020",
    "module": "commonjs",
    "lib": ["ES2020"],
    "outDir": "./dist",
    "rootDir": "./src",
    "strict": true,
    "esModuleInterop": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true
  },
  "include": ["src/**/*"],
  "exclude": ["node_modules", "dist"]
}
`;
        }
        
        return `// Configuration file for ${name}
module.exports = {
  // Add your configuration here
};
`;
    }

    /**
     * 清理资源
     */
    dispose(): void {
        this.outputChannel.dispose();
    }
}
