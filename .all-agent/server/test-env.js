#!/usr/bin/env node

// 清除所有环境变量
delete process.env.MISTRAL_API_KEY;
delete process.env.DEEPSEEK_API_KEY;

console.log('🔍 测试环境变量加载...');
console.log('加载前:');
console.log('MISTRAL_API_KEY:', process.env.MISTRAL_API_KEY);
console.log('DEEPSEEK_API_KEY:', process.env.DEEPSEEK_API_KEY);

// 重新加载 .env
require('dotenv').config({ path: './.env' });

console.log('\n加载后:');
console.log('MISTRAL_API_KEY:', process.env.MISTRAL_API_KEY);
console.log('DEEPSEEK_API_KEY:', process.env.DEEPSEEK_API_KEY);

// 直接测试 Mistral API
async function testMistralDirect() {
    const fetch = require('node-fetch');
    
    console.log('\n🧪 直接测试 Mistral API...');
    
    try {
        const response = await fetch('https://api.mistral.ai/v1/chat/completions', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${process.env.MISTRAL_API_KEY}`
            },
            body: JSON.stringify({
                model: 'mistral-large-latest',
                messages: [{ role: 'user', content: '你好' }],
                max_tokens: 10
            })
        });

        console.log('响应状态:', response.status);
        
        if (response.ok) {
            const data = await response.json();
            console.log('✅ Mistral API 成功!');
            console.log('响应:', data.choices[0].message.content);
        } else {
            const error = await response.text();
            console.log('❌ Mistral API 失败:', error);
        }
    } catch (error) {
        console.log('❌ 请求异常:', error.message);
    }
}

testMistralDirect();
