{"source": {"include": ["./", "./README.md"], "includePattern": "\\.(js|jsx)$", "exclude": ["node_modules/", "tests/", "coverage/", "dist/", "build/", "logs/", "data/", "backups/", "uploads/"]}, "opts": {"destination": "./docs/generated/", "recurse": true, "readme": "./README.md"}, "plugins": ["plugins/markdown"], "templates": {"cleverLinks": false, "monospaceLinks": false}, "tags": {"allowUnknownTags": true, "dictionaries": ["jsdoc", "closure"]}, "markdown": {"parser": "gfm", "hardwrap": false}}