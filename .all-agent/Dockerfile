# All-Agent 多阶段构建 Dockerfile
# 基于 Node.js 18 Alpine 镜像，体积小且安全

# 构建阶段
FROM node:18-alpine AS builder

# 设置工作目录
WORKDIR /app

# 安装构建依赖
RUN apk add --no-cache \
    python3 \
    make \
    g++ \
    sqlite \
    sqlite-dev

# 复制 package 文件
COPY server/package*.json ./

# 安装依赖
RUN npm ci --only=production && npm cache clean --force

# 复制源代码
COPY server/ ./

# 创建必要的目录
RUN mkdir -p data reports logs backups

# 运行阶段
FROM node:18-alpine AS runtime

# 创建非 root 用户
RUN addgroup -g 1001 -S nodejs && \
    adduser -S allagent -u 1001

# 安装运行时依赖
RUN apk add --no-cache \
    sqlite \
    curl \
    dumb-init

# 设置工作目录
WORKDIR /app

# 从构建阶段复制文件
COPY --from=builder --chown=allagent:nodejs /app ./

# 创建数据目录并设置权限
RUN mkdir -p /app/data /app/reports /app/logs /app/backups && \
    chown -R allagent:nodejs /app

# 切换到非 root 用户
USER allagent

# 暴露端口
EXPOSE 3000

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:3000/health || exit 1

# 使用 dumb-init 作为 PID 1
ENTRYPOINT ["dumb-init", "--"]

# 启动应用
CMD ["node", "app.js"]

# 元数据标签
LABEL maintainer="All-Agent Team <<EMAIL>>"
LABEL version="1.0.0"
LABEL description="All-Agent AI 项目构建与执行系统"
LABEL org.opencontainers.image.source="https://github.com/all-agent/all-agent"
LABEL org.opencontainers.image.documentation="https://docs.all-agent.com"
LABEL org.opencontainers.image.licenses="MIT"
