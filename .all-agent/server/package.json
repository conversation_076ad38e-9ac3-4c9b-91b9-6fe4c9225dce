{"name": "all-agent-server", "version": "1.0.0", "description": "All-Agent 核心服务器 - 为无代码用户提供 AI 项目构建与执行系统", "main": "app.js", "scripts": {"start": "node app.js", "dev": "nodemon app.js", "test": "node tests/runner.js", "test:db": "node tests/runner.js database", "test:auth": "node tests/runner.js auth", "test:integration": "node tests/runner.js integration", "test:watch": "nodemon tests/runner.js", "test:jest": "jest", "test:jest:watch": "jest --watch", "lint": "eslint .", "lint:fix": "eslint . --fix", "docs": "jsdoc -c jsdoc.conf.json", "clean": "rm -rf node_modules package-lock.json", "setup": "npm install && npm run build", "build": "echo 'Build completed'", "health": "curl -f http://localhost:3000/health || exit 1", "test:performance": "node tests/performance.test.js", "benchmark": "node ../scripts/benchmark.js", "docker:build": "docker build -t all-agent:latest ..", "docker:run": "docker run -p 3000:3000 all-agent:latest", "docker:compose": "docker-compose -f ../docker-compose.yml up -d", "docker:compose:down": "docker-compose -f ../docker-compose.yml down", "k8s:deploy": "kubectl apply -f ../k8s/", "k8s:delete": "kubectl delete -f ../k8s/", "deploy": "../scripts/deploy.sh", "deploy:docker": "../scripts/deploy.sh docker-compose", "deploy:k8s": "../scripts/deploy.sh kubernetes"}, "keywords": ["ai", "agent", "automation", "code-generation", "no-code", "project-management", "llm", "websocket", "api"], "author": "All-Agent Team", "license": "MIT", "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "dependencies": {"express": "^4.18.2", "socket.io": "^4.7.4", "cors": "^2.8.5", "handlebars": "^4.7.8", "dotenv": "^16.3.1", "axios": "^1.6.2", "ws": "^8.14.2", "uuid": "^9.0.1", "lodash": "^4.17.21", "moment": "^2.29.4", "chalk": "^4.1.2", "commander": "^11.1.0", "inquirer": "^8.2.6", "ora": "^5.4.1", "boxen": "^5.1.2", "figlet": "^1.7.0", "sqlite3": "^5.1.6", "bcrypt": "^5.1.1", "jsonwebtoken": "^9.0.2", "joi": "^17.11.0", "helmet": "^7.1.0", "compression": "^1.7.4", "express-rate-limit": "^7.1.5", "multer": "^1.4.5-lts.1", "sharp": "^0.32.6", "node-cron": "^3.0.3", "redis": "^4.6.10", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.0", "nodemailer": "^6.9.7"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3", "eslint": "^8.55.0", "eslint-config-standard": "^17.1.0", "eslint-plugin-import": "^2.29.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^6.1.1", "jsdoc": "^4.0.2", "@types/node": "^20.10.4"}, "repository": {"type": "git", "url": "https://github.com/all-agent/all-agent.git"}, "bugs": {"url": "https://github.com/all-agent/all-agent/issues"}, "homepage": "https://github.com/all-agent/all-agent#readme", "config": {"port": 3000, "logLevel": "info"}, "jest": {"testEnvironment": "node", "collectCoverage": true, "coverageDirectory": "coverage", "coverageReporters": ["text", "lcov", "html"], "testMatch": ["**/__tests__/**/*.js", "**/?(*.)+(spec|test).js"], "collectCoverageFrom": ["**/*.js", "!**/node_modules/**", "!**/coverage/**", "!jest.config.js"]}, "eslintConfig": {"extends": ["standard"], "env": {"node": true, "es2021": true, "jest": true}, "rules": {"no-console": "off", "no-unused-vars": ["error", {"argsIgnorePattern": "^_"}]}}, "nodemonConfig": {"watch": ["**/*.js"], "ignore": ["node_modules/**", "logs/**", "coverage/**"], "ext": "js,json", "env": {"NODE_ENV": "development"}}}