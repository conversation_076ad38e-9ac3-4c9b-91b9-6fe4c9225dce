const path = require('path');
const fs = require('fs');

/**
 * 环境变量管理模块
 */
class Environment {
  constructor() {
    this.loaded = false;
    this.config = {};
  }

  /**
   * 加载环境变量
   */
  loadEnvironmentVariables() {
    if (this.loaded) {
      return this.config;
    }

    const possibleEnvPaths = [
      path.join(__dirname, '../.env'),
      path.join(__dirname, '../../.env'),
      path.join(process.cwd(), '.all-agent/.env'),
      path.join(process.cwd(), '.all-agent/server/.env'),
      path.join(process.cwd(), '.env')
    ];

    for (const envPath of possibleEnvPaths) {
      if (fs.existsSync(envPath)) {
        require('dotenv').config({ path: envPath });
        console.log(`✅ Environment variables loaded from ${envPath}`);
        break;
      }
    }

    this.config = this.validateAndNormalizeConfig();
    this.loaded = true;
    return this.config;
  }

  /**
   * 验证和规范化配置
   */
  validateAndNormalizeConfig() {
    const config = {
      // 服务器配置
      PORT: this.getNumber('PORT', 3000),
      NODE_ENV: this.getString('NODE_ENV', 'development'),
      
      // 数据库配置
      DB_PATH: this.getString('DB_PATH', './data/all-agent.db'),
      
      // 认证配置
      JWT_SECRET: this.getRequiredString('JWT_SECRET', 'all-agent-jwt-secret-change-in-production'),
      JWT_EXPIRES_IN: this.getString('JWT_EXPIRES_IN', '7d'),
      
      // LLM 配置
      DEFAULT_LLM_PROVIDER: this.getString('DEFAULT_LLM_PROVIDER', 'deepseek'),
      DEEPSEEK_API_KEY: this.getString('DEEPSEEK_API_KEY'),
      MISTRAL_API_KEY: this.getString('MISTRAL_API_KEY'),
      OPENAI_API_KEY: this.getString('OPENAI_API_KEY'),
      ANTHROPIC_API_KEY: this.getString('ANTHROPIC_API_KEY'),
      
      // 缓存配置
      CACHE_TYPE: this.getString('CACHE_TYPE', 'memory'),
      REDIS_URL: this.getString('REDIS_URL', 'redis://localhost:6379'),
      
      // 监控配置
      ENABLE_MONITORING: this.getBoolean('ENABLE_MONITORING', true),
      METRICS_PORT: this.getNumber('METRICS_PORT', 9090),
      
      // 安全配置
      CORS_ORIGIN: this.getString('CORS_ORIGIN', '*'),
      RATE_LIMIT_WINDOW: this.getNumber('RATE_LIMIT_WINDOW', 15 * 60 * 1000),
      RATE_LIMIT_MAX: this.getNumber('RATE_LIMIT_MAX', 1000),
      
      // 日志配置
      LOG_LEVEL: this.getString('LOG_LEVEL', 'info'),
      LOG_FILE: this.getString('LOG_FILE', './logs/all-agent.log'),
      
      // 功能开关
      ENABLE_WEB3: this.getBoolean('ENABLE_WEB3', false),
      ENABLE_QUANTUM: this.getBoolean('ENABLE_QUANTUM', false),
      ENABLE_EDGE_AI: this.getBoolean('ENABLE_EDGE_AI', true),
      ENABLE_AIOPS: this.getBoolean('ENABLE_AIOPS', true)
    };

    // 验证必需的配置
    this.validateRequiredConfig(config);
    
    // 在开发环境中显示警告
    if (config.NODE_ENV === 'development') {
      this.showDevelopmentWarnings(config);
    }

    return config;
  }

  /**
   * 获取字符串配置
   */
  getString(key, defaultValue = '') {
    return process.env[key] || defaultValue;
  }

  /**
   * 获取必需的字符串配置
   */
  getRequiredString(key, fallback = null) {
    const value = process.env[key];
    if (!value && !fallback) {
      throw new Error(`Required environment variable ${key} is not set`);
    }
    return value || fallback;
  }

  /**
   * 获取数字配置
   */
  getNumber(key, defaultValue = 0) {
    const value = process.env[key];
    if (!value) return defaultValue;
    
    const parsed = parseInt(value, 10);
    if (isNaN(parsed)) {
      console.warn(`Warning: ${key} is not a valid number, using default: ${defaultValue}`);
      return defaultValue;
    }
    return parsed;
  }

  /**
   * 获取布尔配置
   */
  getBoolean(key, defaultValue = false) {
    const value = process.env[key];
    if (!value) return defaultValue;
    
    return ['true', '1', 'yes', 'on'].includes(value.toLowerCase());
  }

  /**
   * 获取数组配置
   */
  getArray(key, defaultValue = [], separator = ',') {
    const value = process.env[key];
    if (!value) return defaultValue;
    
    return value.split(separator).map(item => item.trim()).filter(Boolean);
  }

  /**
   * 验证必需的配置
   */
  validateRequiredConfig(config) {
    const errors = [];

    // 检查 JWT 密钥
    if (config.JWT_SECRET === 'all-agent-jwt-secret-change-in-production' && config.NODE_ENV === 'production') {
      errors.push('JWT_SECRET must be changed in production environment');
    }

    // 检查至少有一个 LLM API Key
    const llmKeys = [config.DEEPSEEK_API_KEY, config.MISTRAL_API_KEY, config.OPENAI_API_KEY, config.ANTHROPIC_API_KEY];
    if (!llmKeys.some(key => key && key.length > 0)) {
      console.warn('Warning: No LLM API keys configured. Some features may not work.');
    }

    if (errors.length > 0) {
      throw new Error(`Configuration validation failed:\n${errors.join('\n')}`);
    }
  }

  /**
   * 显示开发环境警告
   */
  showDevelopmentWarnings(config) {
    const warnings = [];

    if (config.JWT_SECRET === 'all-agent-jwt-secret-change-in-production') {
      warnings.push('Using default JWT secret (change for production)');
    }

    if (config.CORS_ORIGIN === '*') {
      warnings.push('CORS is set to allow all origins (restrict for production)');
    }

    if (!config.DEEPSEEK_API_KEY && !config.MISTRAL_API_KEY && !config.OPENAI_API_KEY) {
      warnings.push('No LLM API keys configured (some features will use mock responses)');
    }

    if (warnings.length > 0) {
      console.log('\n⚠️  Development Environment Warnings:');
      warnings.forEach(warning => console.log(`   • ${warning}`));
      console.log('');
    }
  }

  /**
   * 获取配置摘要
   */
  getConfigSummary() {
    if (!this.loaded) {
      this.loadEnvironmentVariables();
    }

    return {
      environment: this.config.NODE_ENV,
      port: this.config.PORT,
      database: this.config.DB_PATH,
      llmProvider: this.config.DEFAULT_LLM_PROVIDER,
      cacheType: this.config.CACHE_TYPE,
      monitoring: this.config.ENABLE_MONITORING,
      features: {
        web3: this.config.ENABLE_WEB3,
        quantum: this.config.ENABLE_QUANTUM,
        edgeAI: this.config.ENABLE_EDGE_AI,
        aiOps: this.config.ENABLE_AIOPS
      }
    };
  }

  /**
   * 检查配置是否为生产环境就绪
   */
  isProductionReady() {
    if (!this.loaded) {
      this.loadEnvironmentVariables();
    }

    const issues = [];

    if (this.config.JWT_SECRET === 'all-agent-jwt-secret-change-in-production') {
      issues.push('Default JWT secret is being used');
    }

    if (this.config.CORS_ORIGIN === '*') {
      issues.push('CORS allows all origins');
    }

    if (this.config.LOG_LEVEL === 'debug') {
      issues.push('Debug logging is enabled');
    }

    return {
      ready: issues.length === 0,
      issues
    };
  }
}

// 单例实例
const environment = new Environment();

// 导出函数
function loadEnvironmentVariables() {
  return environment.loadEnvironmentVariables();
}

function getConfig() {
  return environment.config;
}

function getConfigSummary() {
  return environment.getConfigSummary();
}

function isProductionReady() {
  return environment.isProductionReady();
}

module.exports = {
  Environment,
  loadEnvironmentVariables,
  getConfig,
  getConfigSummary,
  isProductionReady
};
