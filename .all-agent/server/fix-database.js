#!/usr/bin/env node

/**
 * 修复数据库表结构
 */

// 加载环境变量
const path = require('path');
const fs = require('fs');

function loadEnvironmentVariables() {
  const possibleEnvPaths = [
    path.join(__dirname, '.env'),
    path.join(__dirname, '../.env'),
    path.join(process.cwd(), '.all-agent/.env'),
    path.join(process.cwd(), '.all-agent/server/.env')
  ];

  for (const envPath of possibleEnvPaths) {
    if (fs.existsSync(envPath)) {
      require('dotenv').config({ path: envPath });
      console.log(`✅ 环境变量已从 ${envPath} 加载`);
      break;
    }
  }
}

loadEnvironmentVariables();

const Database = require('./database/Database');

async function fixDatabase() {
  console.log('🔧 开始修复数据库...');
  
  try {
    const db = new Database();
    await db.connect();
    
    console.log('📊 检查当前表结构...');
    
    // 检查 users 表结构
    const tableInfo = await db.all("PRAGMA table_info(users)");
    console.log('当前 users 表字段:', tableInfo.map(col => col.name));
    
    // 检查是否有 last_login 字段
    const hasLastLogin = tableInfo.some(col => col.name === 'last_login');
    
    if (!hasLastLogin) {
      console.log('❌ 缺少 last_login 字段，正在添加...');
      await db.run("ALTER TABLE users ADD COLUMN last_login DATETIME");
      console.log('✅ 已添加 last_login 字段');
    } else {
      console.log('✅ last_login 字段已存在');
    }
    
    // 重新检查表结构
    const updatedTableInfo = await db.all("PRAGMA table_info(users)");
    console.log('更新后的 users 表字段:', updatedTableInfo.map(col => col.name));
    
    // 检查现有用户
    const users = await db.all("SELECT id, username, email, role, is_active FROM users");
    console.log(`📋 当前用户数量: ${users.length}`);
    
    if (users.length > 0) {
      console.log('现有用户:');
      users.forEach(user => {
        console.log(`  - ${user.username} (${user.email}) - ${user.role} - ${user.is_active ? '激活' : '禁用'}`);
      });
    }
    
    // 创建测试用户（如果不存在）
    const testUser = await db.get("SELECT * FROM users WHERE username = ?", ['testuser']);
    if (!testUser) {
      console.log('🔧 创建测试用户...');
      const bcrypt = require('bcrypt');
      const hashedPassword = await bcrypt.hash('Test123456', 12);
      
      await db.run(`
        INSERT INTO users (username, email, password_hash, role, is_active)
        VALUES (?, ?, ?, ?, ?)
      `, ['testuser', '<EMAIL>', hashedPassword, 'user', 1]);
      
      console.log('✅ 测试用户创建成功');
    } else {
      console.log('✅ 测试用户已存在');
    }
    
    // 创建管理员用户（如果不存在）
    const adminUser = await db.get("SELECT * FROM users WHERE email = ?", ['<EMAIL>']);
    if (!adminUser) {
      console.log('🔧 创建管理员用户...');
      const bcrypt = require('bcrypt');
      const hashedPassword = await bcrypt.hash('Admin123456', 12);
      
      await db.run(`
        INSERT INTO users (username, email, password_hash, role, is_active)
        VALUES (?, ?, ?, ?, ?)
      `, ['admin', '<EMAIL>', hashedPassword, 'admin', 1]);
      
      console.log('✅ 管理员用户创建成功');
      console.log('📋 管理员登录信息:');
      console.log('   邮箱: <EMAIL>');
      console.log('   密码: Admin123456');
    } else {
      console.log('✅ 管理员用户已存在');
    }
    
    await db.close();
    console.log('🎉 数据库修复完成！');
    
  } catch (error) {
    console.error('❌ 数据库修复失败:', error.message);
    console.error('错误详情:', error);
    process.exit(1);
  }
}

// 运行修复
if (require.main === module) {
  fixDatabase().catch(error => {
    console.error('修复过程中发生错误:', error);
    process.exit(1);
  });
}

module.exports = { fixDatabase };
