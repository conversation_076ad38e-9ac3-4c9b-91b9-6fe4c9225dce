import * as vscode from 'vscode';

/**
 * 性能监控服务 - 提供性能优化和监控能力
 */
export class PerformanceService {
    private metrics: Map<string, Array<{
        timestamp: number;
        duration: number;
        success: boolean;
        metadata?: any;
    }>> = new Map();

    private cache: Map<string, {
        data: any;
        timestamp: number;
        ttl: number;
    }> = new Map();

    private readonly CACHE_CLEANUP_INTERVAL = 60000; // 1分钟
    private cleanupTimer?: NodeJS.Timeout;

    constructor() {
        this.startCacheCleanup();
    }

    /**
     * 性能计时器
     */
    startTimer(operation: string): (success?: boolean, metadata?: any) => void {
        const startTime = Date.now();

        return (success: boolean = true, metadata?: any) => {
            const duration = Date.now() - startTime;
            this.recordMetric(operation, duration, success, metadata);
        };
    }

    /**
     * 记录性能指标
     */
    private recordMetric(operation: string, duration: number, success: boolean, metadata?: any): void {
        if (!this.metrics.has(operation)) {
            this.metrics.set(operation, []);
        }

        const metrics = this.metrics.get(operation)!;
        metrics.push({
            timestamp: Date.now(),
            duration,
            success,
            metadata
        });

        // 保留最近100条记录
        if (metrics.length > 100) {
            metrics.splice(0, metrics.length - 100);
        }
    }

    /**
     * 获取性能统计
     */
    getPerformanceStats(operation?: string): {
        [key: string]: {
            totalCalls: number;
            successRate: number;
            averageDuration: number;
            minDuration: number;
            maxDuration: number;
            recentTrend: 'improving' | 'degrading' | 'stable';
        };
    } {
        const stats: any = {};

        const operations = operation ? [operation] : Array.from(this.metrics.keys());

        for (const op of operations) {
            const metrics = this.metrics.get(op) || [];
            if (metrics.length === 0) continue;

            const successfulCalls = metrics.filter(m => m.success);
            const durations = metrics.map(m => m.duration);

            // 计算趋势
            const recentMetrics = metrics.slice(-10);
            const olderMetrics = metrics.slice(-20, -10);
            const recentAvg = recentMetrics.reduce((sum, m) => sum + m.duration, 0) / recentMetrics.length;
            const olderAvg = olderMetrics.length > 0
                ? olderMetrics.reduce((sum, m) => sum + m.duration, 0) / olderMetrics.length
                : recentAvg;

            let trend: 'improving' | 'degrading' | 'stable' = 'stable';
            if (recentAvg < olderAvg * 0.9) trend = 'improving';
            else if (recentAvg > olderAvg * 1.1) trend = 'degrading';

            stats[op] = {
                totalCalls: metrics.length,
                successRate: (successfulCalls.length / metrics.length) * 100,
                averageDuration: durations.reduce((sum, d) => sum + d, 0) / durations.length,
                minDuration: Math.min(...durations),
                maxDuration: Math.max(...durations),
                recentTrend: trend
            };
        }

        return stats;
    }

    /**
     * 缓存管理
     */
    setCache(key: string, data: any, ttlMs: number = 300000): void { // 默认5分钟
        this.cache.set(key, {
            data,
            timestamp: Date.now(),
            ttl: ttlMs
        });
    }

    getCache(key: string): any | null {
        const cached = this.cache.get(key);
        if (!cached) return null;

        if (Date.now() - cached.timestamp > cached.ttl) {
            this.cache.delete(key);
            return null;
        }

        return cached.data;
    }

    clearCache(pattern?: string): void {
        if (!pattern) {
            this.cache.clear();
            return;
        }

        const regex = new RegExp(pattern);
        for (const [key] of this.cache) {
            if (regex.test(key)) {
                this.cache.delete(key);
            }
        }
    }

    /**
     * 内存使用监控
     */
    getMemoryUsage(): {
        used: number;
        total: number;
        percentage: number;
        cacheSize: number;
        metricsSize: number;
    } {
        const memUsage = process.memoryUsage();

        return {
            used: memUsage.heapUsed,
            total: memUsage.heapTotal,
            percentage: (memUsage.heapUsed / memUsage.heapTotal) * 100,
            cacheSize: this.cache.size,
            metricsSize: this.metrics.size
        };
    }

    /**
     * 性能建议
     */
    getPerformanceRecommendations(): Array<{
        type: 'warning' | 'info' | 'critical';
        message: string;
        action?: string;
    }> {
        const recommendations: Array<{
            type: 'warning' | 'info' | 'critical';
            message: string;
            action?: string;
        }> = [];

        const memUsage = this.getMemoryUsage();
        const stats = this.getPerformanceStats();

        // 内存使用检查
        if (memUsage.percentage > 80) {
            recommendations.push({
                type: 'critical',
                message: `High memory usage: ${memUsage.percentage.toFixed(1)}%`,
                action: 'Consider clearing cache or restarting extension'
            });
        } else if (memUsage.percentage > 60) {
            recommendations.push({
                type: 'warning',
                message: `Moderate memory usage: ${memUsage.percentage.toFixed(1)}%`,
                action: 'Monitor memory usage'
            });
        }

        // 缓存大小检查
        if (memUsage.cacheSize > 1000) {
            recommendations.push({
                type: 'warning',
                message: `Large cache size: ${memUsage.cacheSize} items`,
                action: 'Consider clearing old cache entries'
            });
        }

        // 性能趋势检查
        for (const [operation, stat] of Object.entries(stats)) {
            if (stat.recentTrend === 'degrading') {
                recommendations.push({
                    type: 'warning',
                    message: `Performance degrading for ${operation}`,
                    action: 'Check for resource leaks or optimize operation'
                });
            }

            if (stat.successRate < 90) {
                recommendations.push({
                    type: 'warning',
                    message: `Low success rate for ${operation}: ${stat.successRate.toFixed(1)}%`,
                    action: 'Investigate and fix errors'
                });
            }

            if (stat.averageDuration > 5000) {
                recommendations.push({
                    type: 'info',
                    message: `Slow operation ${operation}: ${stat.averageDuration.toFixed(0)}ms average`,
                    action: 'Consider optimization or caching'
                });
            }
        }

        return recommendations;
    }

    /**
     * 批量操作优化
     */
    async batchOperation<T, R>(
        items: T[],
        operation: (item: T) => Promise<R>,
        options: {
            batchSize?: number;
            delayMs?: number;
            maxConcurrency?: number;
        } = {}
    ): Promise<R[]> {
        const {
            batchSize = 10,
            delayMs = 100,
            maxConcurrency = 3
        } = options;

        const results: R[] = [];
        const batches: T[][] = [];

        // 分批处理
        for (let i = 0; i < items.length; i += batchSize) {
            batches.push(items.slice(i, i + batchSize));
        }

        // 限制并发
        const semaphore = new Array(maxConcurrency).fill(null);
        let batchIndex = 0;

        const processBatch = async (): Promise<void> => {
            while (batchIndex < batches.length) {
                const currentBatch = batches[batchIndex++];
                if (!currentBatch) break;

                const batchResults = await Promise.all(
                    currentBatch.map(item => operation(item))
                );

                results.push(...batchResults);

                if (delayMs > 0 && batchIndex < batches.length) {
                    await new Promise(resolve => setTimeout(resolve, delayMs));
                }
            }
        };

        await Promise.all(semaphore.map(() => processBatch()));

        return results;
    }

    /**
     * 防抖函数
     */
    debounce<T extends (...args: any[]) => any>(
        func: T,
        waitMs: number
    ): (...args: Parameters<T>) => void {
        let timeoutId: NodeJS.Timeout;

        return (...args: Parameters<T>) => {
            clearTimeout(timeoutId);
            timeoutId = setTimeout(() => func(...args), waitMs);
        };
    }

    /**
     * 节流函数
     */
    throttle<T extends (...args: any[]) => any>(
        func: T,
        limitMs: number
    ): (...args: Parameters<T>) => void {
        let lastCall = 0;

        return (...args: Parameters<T>) => {
            const now = Date.now();
            if (now - lastCall >= limitMs) {
                lastCall = now;
                func(...args);
            }
        };
    }

    /**
     * 启动缓存清理
     */
    private startCacheCleanup(): void {
        this.cleanupTimer = setInterval(() => {
            const now = Date.now();
            for (const [key, cached] of this.cache) {
                if (now - cached.timestamp > cached.ttl) {
                    this.cache.delete(key);
                }
            }
        }, this.CACHE_CLEANUP_INTERVAL);
    }

    /**
     * 清理资源
     */
    dispose(): void {
        if (this.cleanupTimer) {
            clearInterval(this.cleanupTimer);
        }
        this.cache.clear();
        this.metrics.clear();
    }
}
