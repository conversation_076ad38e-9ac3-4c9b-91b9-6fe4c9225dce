# All-Agent .gitignore

# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# Production builds
dist/
build/
*.min.js
*.min.css

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.env.frontier

# Database files
data/
*.db
*.sqlite
*.sqlite3

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov
.nyc_output/

# Test results
test-results/
junit.xml

# Cache directories
.cache/
.parcel-cache/
.eslintcache
.prettiercache
.stylelintcache

# Temporary folders
tmp/
temp/
.tmp/

# Uploads and user data
uploads/
backups/
reports/

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~
.project
.classpath
.settings/

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
desktop.ini

# Package manager files
package-lock.json
yarn.lock
pnpm-lock.yaml

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache
.cache
.parcel-cache

# Next.js build output
.next

# Nuxt.js build / generate output
.nuxt

# Gatsby files
.cache/
public

# Storybook build outputs
.out
.storybook-out

# Generated documentation
docs/generated/
api-docs/

# ML models and data
models/
*.h5
*.pkl
*.joblib
*.model

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
Pipfile.lock

# PEP 582
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# Translations
*.mo
*.pot

# Django stuff:
local_settings.py
db.sqlite3

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# celery beat schedule file
celerybeat-schedule

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# Docker
.dockerignore

# Kubernetes
*.kubeconfig

# Terraform
*.tfstate
*.tfstate.*
.terraform/
.terraform.lock.hcl

# Ansible
*.retry

# Vagrant
.vagrant/

# Local configuration
config/local.json
config/development.json
config/production.json

# SSL certificates
*.pem
*.key
*.crt
*.csr
certs/

# Blockchain and Web3
.secret
wallet.json
keystore/

# Quantum computing
quantum_circuits/
quantum_results/

# AI/ML specific
checkpoints/
tensorboard_logs/
mlruns/
artifacts/
experiments/

# Edge AI
edge_models/
optimized_models/

# Monitoring and observability
prometheus_data/
grafana_data/
jaeger_data/

# Backup files
*.bak
*.backup
*.old

# Archive files
*.zip
*.tar.gz
*.rar
*.7z

# Large files
*.iso
*.dmg
*.img

# Video files
*.mp4
*.avi
*.mov
*.wmv

# Audio files
*.mp3
*.wav
*.flac

# Image files (large)
*.psd
*.ai
*.eps

# Compiled source
*.com
*.class
*.dll
*.exe
*.o
*.so

# Packages
*.7z
*.dmg
*.gz
*.iso
*.jar
*.rar
*.tar
*.zip
