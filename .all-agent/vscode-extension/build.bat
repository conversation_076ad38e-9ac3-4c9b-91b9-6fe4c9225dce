@echo off
echo 🚀 Building All-Agent VSCode Extension...

REM 检查 Node.js
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js is not installed. Please install Node.js first.
    pause
    exit /b 1
)

REM 检查 npm
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ npm is not installed. Please install npm first.
    pause
    exit /b 1
)

REM 进入扩展目录
cd /d "%~dp0"

echo 📦 Installing dependencies...
npm install

if %errorlevel% neq 0 (
    echo ❌ Failed to install dependencies
    pause
    exit /b 1
)

echo 🔨 Compiling TypeScript...
npm run compile

if %errorlevel% neq 0 (
    echo ❌ Failed to compile TypeScript
    pause
    exit /b 1
)

REM 检查是否安装了 vsce
vsce --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 📦 Installing vsce (Visual Studio Code Extension manager)...
    npm install -g vsce
)

echo 📦 Packaging extension...
vsce package

if %errorlevel% equ 0 (
    echo ✅ Extension built successfully!
    echo.
    echo 📋 Installation Instructions:
    echo 1. Open VSCode or Cursor
    echo 2. Press Ctrl+Shift+P (or Cmd+Shift+P on Mac)
    echo 3. Type "Extensions: Install from VSIX"
    echo 4. Select the generated .vsix file
    echo.
    echo 🔧 Configuration:
    echo 1. Open VSCode Settings (Ctrl+, or Cmd+,)
    echo 2. Search for "All-Agent"
    echo 3. Configure your API keys:
    echo    - DeepSeek API Key: ***********************************
    echo    - Mistral API Key: 1cPOCn740EGNqIcoKnWQqSky9Aqm6SSs
    echo.
    echo 🎉 Ready to use All-Agent in VSCode!
) else (
    echo ❌ Failed to package extension
)

pause
