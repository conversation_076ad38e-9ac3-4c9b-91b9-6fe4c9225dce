const tf = require('@tensorflow/tfjs-node');
const fs = require('fs').promises;
const path = require('path');

/**
 * 边缘 AI 优化引擎
 * 专门针对边缘设备的 AI 模型优化和推理加速
 */
class EdgeAIOptimizer {
    constructor(options = {}) {
        this.options = {
            modelsPath: options.modelsPath || './edge-ai/models',
            optimizedModelsPath: options.optimizedModelsPath || './edge-ai/optimized',
            deviceProfile: options.deviceProfile || 'edge-standard',
            maxMemoryMB: options.maxMemoryMB || 512,
            maxLatencyMs: options.maxLatencyMs || 100,
            targetAccuracy: options.targetAccuracy || 0.85,
            quantizationBits: options.quantizationBits || 8,
            enablePruning: options.enablePruning !== false,
            enableDistillation: options.enableDistillation !== false,
            ...options
        };

        this.deviceProfiles = {
            'edge-minimal': { memory: 128, cpu: 'arm-cortex-a7', gpu: false },
            'edge-standard': { memory: 512, cpu: 'arm-cortex-a53', gpu: false },
            'edge-enhanced': { memory: 1024, cpu: 'arm-cortex-a72', gpu: 'mali-g71' },
            'edge-powerful': { memory: 2048, cpu: 'arm-cortex-a78', gpu: 'adreno-640' }
        };

        this.optimizedModels = new Map();
        this.performanceMetrics = new Map();
        this.optimizationHistory = [];

        this.initializeOptimizer();
    }

    /**
     * 初始化优化器
     */
    async initializeOptimizer() {
        try {
            await fs.mkdir(this.options.modelsPath, { recursive: true });
            await fs.mkdir(this.options.optimizedModelsPath, { recursive: true });
            
            console.log('🔧 边缘 AI 优化器初始化完成');
            console.log(`📱 设备配置: ${this.options.deviceProfile}`);
            console.log(`💾 内存限制: ${this.options.maxMemoryMB}MB`);
            console.log(`⚡ 延迟目标: ${this.options.maxLatencyMs}ms`);
        } catch (error) {
            console.error('边缘 AI 优化器初始化失败:', error);
            throw error;
        }
    }

    /**
     * 优化模型
     */
    async optimizeModel(modelPath, optimizationConfig = {}) {
        try {
            console.log(`🚀 开始优化模型: ${modelPath}`);
            
            // 加载原始模型
            const originalModel = await tf.loadLayersModel(`file://${modelPath}`);
            const originalSize = await this.calculateModelSize(originalModel);
            
            console.log(`📊 原始模型大小: ${(originalSize / 1024 / 1024).toFixed(2)}MB`);

            let optimizedModel = originalModel;
            const optimizationSteps = [];

            // 1. 模型剪枝
            if (this.options.enablePruning && optimizationConfig.pruning !== false) {
                console.log('✂️ 执行模型剪枝...');
                optimizedModel = await this.pruneModel(optimizedModel, optimizationConfig.pruningRatio || 0.3);
                optimizationSteps.push('pruning');
            }

            // 2. 量化
            if (optimizationConfig.quantization !== false) {
                console.log('🔢 执行模型量化...');
                optimizedModel = await this.quantizeModel(optimizedModel, this.options.quantizationBits);
                optimizationSteps.push('quantization');
            }

            // 3. 知识蒸馏
            if (this.options.enableDistillation && optimizationConfig.distillation !== false) {
                console.log('🧠 执行知识蒸馏...');
                optimizedModel = await this.distillModel(originalModel, optimizationConfig.studentConfig);
                optimizationSteps.push('distillation');
            }

            // 4. 层融合
            if (optimizationConfig.layerFusion !== false) {
                console.log('🔗 执行层融合优化...');
                optimizedModel = await this.fuseLayersOptimization(optimizedModel);
                optimizationSteps.push('layer_fusion');
            }

            // 5. 图优化
            if (optimizationConfig.graphOptimization !== false) {
                console.log('📈 执行计算图优化...');
                optimizedModel = await this.optimizeComputationGraph(optimizedModel);
                optimizationSteps.push('graph_optimization');
            }

            // 计算优化后的模型大小
            const optimizedSize = await this.calculateModelSize(optimizedModel);
            const compressionRatio = originalSize / optimizedSize;

            // 性能测试
            const performanceMetrics = await this.benchmarkModel(optimizedModel);

            // 保存优化后的模型
            const optimizedModelPath = path.join(
                this.options.optimizedModelsPath,
                `optimized_${path.basename(modelPath)}`
            );
            await optimizedModel.save(`file://${optimizedModelPath}`);

            const optimizationResult = {
                originalPath: modelPath,
                optimizedPath: optimizedModelPath,
                originalSize,
                optimizedSize,
                compressionRatio,
                optimizationSteps,
                performanceMetrics,
                deviceProfile: this.options.deviceProfile,
                timestamp: new Date().toISOString()
            };

            this.optimizedModels.set(path.basename(modelPath), optimizationResult);
            this.optimizationHistory.push(optimizationResult);

            console.log(`✅ 模型优化完成:`);
            console.log(`   压缩比: ${compressionRatio.toFixed(2)}x`);
            console.log(`   推理延迟: ${performanceMetrics.averageLatency.toFixed(2)}ms`);
            console.log(`   内存使用: ${(performanceMetrics.memoryUsage / 1024 / 1024).toFixed(2)}MB`);

            return optimizationResult;

        } catch (error) {
            console.error('模型优化失败:', error);
            throw error;
        }
    }

    /**
     * 模型剪枝
     */
    async pruneModel(model, pruningRatio = 0.3) {
        try {
            // 创建剪枝后的模型
            const prunedModel = tf.sequential();
            
            for (let i = 0; i < model.layers.length; i++) {
                const layer = model.layers[i];
                
                if (layer.getWeights().length > 0) {
                    // 对有权重的层进行剪枝
                    const weights = layer.getWeights();
                    const prunedWeights = [];
                    
                    for (const weight of weights) {
                        const prunedWeight = await this.pruneWeights(weight, pruningRatio);
                        prunedWeights.push(prunedWeight);
                    }
                    
                    // 创建新层并设置剪枝后的权重
                    const newLayer = this.cloneLayer(layer);
                    prunedModel.add(newLayer);
                    newLayer.setWeights(prunedWeights);
                } else {
                    // 无权重的层直接复制
                    prunedModel.add(this.cloneLayer(layer));
                }
            }

            return prunedModel;
        } catch (error) {
            console.error('模型剪枝失败:', error);
            return model;
        }
    }

    /**
     * 权重剪枝
     */
    async pruneWeights(weights, pruningRatio) {
        return tf.tidy(() => {
            const flatWeights = weights.flatten();
            const absWeights = tf.abs(flatWeights);
            
            // 计算阈值
            const sortedWeights = tf.topk(absWeights, Math.floor(absWeights.size * (1 - pruningRatio)));
            const threshold = tf.min(sortedWeights.values);
            
            // 创建掩码
            const mask = tf.greater(absWeights, threshold);
            const maskedWeights = tf.mul(flatWeights, tf.cast(mask, 'float32'));
            
            return maskedWeights.reshape(weights.shape);
        });
    }

    /**
     * 模型量化
     */
    async quantizeModel(model, bits = 8) {
        try {
            // TensorFlow.js 的量化实现
            const quantizedModel = tf.sequential();
            
            for (const layer of model.layers) {
                if (layer.getWeights().length > 0) {
                    const weights = layer.getWeights();
                    const quantizedWeights = [];
                    
                    for (const weight of weights) {
                        const quantizedWeight = await this.quantizeWeights(weight, bits);
                        quantizedWeights.push(quantizedWeight);
                    }
                    
                    const newLayer = this.cloneLayer(layer);
                    quantizedModel.add(newLayer);
                    newLayer.setWeights(quantizedWeights);
                } else {
                    quantizedModel.add(this.cloneLayer(layer));
                }
            }

            return quantizedModel;
        } catch (error) {
            console.error('模型量化失败:', error);
            return model;
        }
    }

    /**
     * 权重量化
     */
    async quantizeWeights(weights, bits) {
        return tf.tidy(() => {
            const maxVal = tf.max(tf.abs(weights));
            const scale = tf.div(tf.scalar(Math.pow(2, bits - 1) - 1), maxVal);
            
            const quantized = tf.round(tf.mul(weights, scale));
            const dequantized = tf.div(quantized, scale);
            
            return dequantized;
        });
    }

    /**
     * 知识蒸馏
     */
    async distillModel(teacherModel, studentConfig = {}) {
        try {
            // 创建学生模型（更小的网络）
            const studentModel = this.createStudentModel(teacherModel, studentConfig);
            
            // 准备蒸馏数据
            const distillationData = await this.generateDistillationData(teacherModel);
            
            // 训练学生模型
            await this.trainStudentModel(studentModel, teacherModel, distillationData);
            
            return studentModel;
        } catch (error) {
            console.error('知识蒸馏失败:', error);
            return teacherModel;
        }
    }

    /**
     * 创建学生模型
     */
    createStudentModel(teacherModel, config) {
        const reductionFactor = config.reductionFactor || 0.5;
        const studentModel = tf.sequential();
        
        for (const layer of teacherModel.layers) {
            if (layer.units) {
                // 减少神经元数量
                const reducedUnits = Math.max(1, Math.floor(layer.units * reductionFactor));
                const newLayer = tf.layers.dense({
                    units: reducedUnits,
                    activation: layer.activation,
                    inputShape: layer.inputShape
                });
                studentModel.add(newLayer);
            } else {
                // 其他层保持不变
                studentModel.add(this.cloneLayer(layer));
            }
        }
        
        return studentModel;
    }

    /**
     * 层融合优化
     */
    async fuseLayersOptimization(model) {
        try {
            // 简化的层融合实现
            const fusedModel = tf.sequential();
            let skipNext = false;
            
            for (let i = 0; i < model.layers.length; i++) {
                if (skipNext) {
                    skipNext = false;
                    continue;
                }
                
                const currentLayer = model.layers[i];
                const nextLayer = model.layers[i + 1];
                
                // 检查是否可以融合（例如 Dense + Activation）
                if (this.canFuseLayers(currentLayer, nextLayer)) {
                    const fusedLayer = this.fuseLayers(currentLayer, nextLayer);
                    fusedModel.add(fusedLayer);
                    skipNext = true;
                } else {
                    fusedModel.add(this.cloneLayer(currentLayer));
                }
            }
            
            return fusedModel;
        } catch (error) {
            console.error('层融合优化失败:', error);
            return model;
        }
    }

    /**
     * 计算图优化
     */
    async optimizeComputationGraph(model) {
        try {
            // 简化的计算图优化
            // 在实际实现中，这里会进行更复杂的图优化
            return model;
        } catch (error) {
            console.error('计算图优化失败:', error);
            return model;
        }
    }

    /**
     * 模型性能基准测试
     */
    async benchmarkModel(model) {
        try {
            const inputShape = model.inputs[0].shape.slice(1); // 去掉 batch 维度
            const testInput = tf.randomNormal([1, ...inputShape]);
            
            // 预热
            for (let i = 0; i < 5; i++) {
                const prediction = model.predict(testInput);
                prediction.dispose();
            }
            
            // 性能测试
            const iterations = 100;
            const latencies = [];
            
            for (let i = 0; i < iterations; i++) {
                const startTime = performance.now();
                const prediction = model.predict(testInput);
                await prediction.data(); // 确保计算完成
                const endTime = performance.now();
                
                latencies.push(endTime - startTime);
                prediction.dispose();
            }
            
            testInput.dispose();
            
            // 计算统计信息
            const averageLatency = latencies.reduce((sum, lat) => sum + lat, 0) / latencies.length;
            const minLatency = Math.min(...latencies);
            const maxLatency = Math.max(...latencies);
            const p95Latency = latencies.sort((a, b) => a - b)[Math.floor(latencies.length * 0.95)];
            
            // 估算内存使用
            const memoryUsage = await this.estimateMemoryUsage(model);
            
            return {
                averageLatency,
                minLatency,
                maxLatency,
                p95Latency,
                memoryUsage,
                throughput: 1000 / averageLatency // 每秒推理次数
            };
        } catch (error) {
            console.error('性能基准测试失败:', error);
            return {
                averageLatency: 0,
                minLatency: 0,
                maxLatency: 0,
                p95Latency: 0,
                memoryUsage: 0,
                throughput: 0
            };
        }
    }

    /**
     * 计算模型大小
     */
    async calculateModelSize(model) {
        let totalSize = 0;
        
        for (const layer of model.layers) {
            const weights = layer.getWeights();
            for (const weight of weights) {
                totalSize += weight.size * 4; // 假设 float32，每个参数 4 字节
            }
        }
        
        return totalSize;
    }

    /**
     * 估算内存使用
     */
    async estimateMemoryUsage(model) {
        const modelSize = await this.calculateModelSize(model);
        const activationMemory = this.estimateActivationMemory(model);
        return modelSize + activationMemory;
    }

    /**
     * 估算激活内存
     */
    estimateActivationMemory(model) {
        let maxActivationSize = 0;
        
        for (const layer of model.layers) {
            if (layer.outputShape) {
                const layerSize = layer.outputShape.reduce((prod, dim) => prod * (dim || 1), 1) * 4;
                maxActivationSize = Math.max(maxActivationSize, layerSize);
            }
        }
        
        return maxActivationSize * 2; // 考虑前向和反向传播
    }

    /**
     * 克隆层
     */
    cloneLayer(layer) {
        const config = layer.getConfig();
        return tf.layers[layer.getClassName()](config);
    }

    /**
     * 检查是否可以融合层
     */
    canFuseLayers(layer1, layer2) {
        if (!layer1 || !layer2) return false;
        
        // 简单的融合规则：Dense + Activation
        return layer1.getClassName() === 'Dense' && 
               layer2.getClassName() === 'Activation';
    }

    /**
     * 融合层
     */
    fuseLayers(layer1, layer2) {
        const config1 = layer1.getConfig();
        const config2 = layer2.getConfig();
        
        // 将激活函数融合到 Dense 层中
        return tf.layers.dense({
            ...config1,
            activation: config2.activation
        });
    }

    /**
     * 自动优化模型
     */
    async autoOptimize(modelPath, targetConstraints = {}) {
        const constraints = {
            maxMemoryMB: targetConstraints.maxMemoryMB || this.options.maxMemoryMB,
            maxLatencyMs: targetConstraints.maxLatencyMs || this.options.maxLatencyMs,
            minAccuracy: targetConstraints.minAccuracy || this.options.targetAccuracy,
            ...targetConstraints
        };

        console.log('🤖 开始自动优化...');
        console.log('约束条件:', constraints);

        let bestModel = null;
        let bestResult = null;
        let bestScore = 0;

        // 尝试不同的优化策略
        const strategies = [
            { pruning: true, quantization: true, distillation: false },
            { pruning: true, quantization: false, distillation: true },
            { pruning: false, quantization: true, distillation: true },
            { pruning: true, quantization: true, distillation: true }
        ];

        for (const strategy of strategies) {
            try {
                const result = await this.optimizeModel(modelPath, strategy);
                const score = this.calculateOptimizationScore(result, constraints);
                
                if (score > bestScore) {
                    bestScore = score;
                    bestResult = result;
                }
            } catch (error) {
                console.warn(`优化策略失败:`, strategy, error.message);
            }
        }

        if (bestResult) {
            console.log(`✅ 自动优化完成，最佳评分: ${bestScore.toFixed(2)}`);
            return bestResult;
        } else {
            throw new Error('所有优化策略都失败了');
        }
    }

    /**
     * 计算优化评分
     */
    calculateOptimizationScore(result, constraints) {
        let score = 0;
        
        // 内存约束评分
        const memoryScore = Math.max(0, 1 - (result.performanceMetrics.memoryUsage / 1024 / 1024) / constraints.maxMemoryMB);
        score += memoryScore * 0.4;
        
        // 延迟约束评分
        const latencyScore = Math.max(0, 1 - result.performanceMetrics.averageLatency / constraints.maxLatencyMs);
        score += latencyScore * 0.4;
        
        // 压缩比评分
        const compressionScore = Math.min(1, result.compressionRatio / 10);
        score += compressionScore * 0.2;
        
        return score;
    }

    /**
     * 获取优化器状态
     */
    getStatus() {
        return {
            deviceProfile: this.options.deviceProfile,
            optimizedModels: this.optimizedModels.size,
            totalOptimizations: this.optimizationHistory.length,
            averageCompressionRatio: this.calculateAverageCompressionRatio(),
            memoryConstraint: this.options.maxMemoryMB,
            latencyConstraint: this.options.maxLatencyMs,
            status: 'ready'
        };
    }

    /**
     * 计算平均压缩比
     */
    calculateAverageCompressionRatio() {
        if (this.optimizationHistory.length === 0) return 0;
        
        const totalRatio = this.optimizationHistory.reduce(
            (sum, result) => sum + result.compressionRatio, 0
        );
        
        return totalRatio / this.optimizationHistory.length;
    }

    /**
     * 获取优化建议
     */
    getOptimizationRecommendations(modelPath) {
        const recommendations = [];
        
        // 基于设备配置给出建议
        const profile = this.deviceProfiles[this.options.deviceProfile];
        
        if (profile.memory < 512) {
            recommendations.push({
                type: 'aggressive_pruning',
                description: '建议使用激进的模型剪枝以适应低内存设备',
                priority: 'high'
            });
        }
        
        if (!profile.gpu) {
            recommendations.push({
                type: 'cpu_optimization',
                description: '建议优化 CPU 推理性能，避免使用 GPU 特定操作',
                priority: 'medium'
            });
        }
        
        if (this.options.maxLatencyMs < 50) {
            recommendations.push({
                type: 'ultra_low_latency',
                description: '建议使用知识蒸馏和量化以实现超低延迟',
                priority: 'high'
            });
        }
        
        return recommendations;
    }
}

module.exports = EdgeAIOptimizer;
