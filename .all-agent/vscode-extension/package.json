{"name": "all-agent", "displayName": "All-Agent", "description": "AI-powered multi-agent development assistant for code analysis, planning, and execution", "version": "1.0.0", "publisher": "all-agent", "engines": {"vscode": "^1.74.0"}, "categories": ["Other", "Machine Learning", "Snippets", "Debuggers"], "keywords": ["ai", "agent", "assistant", "code-analysis", "planning", "automation"], "activationEvents": ["onStartupFinished"], "main": "./out/extension.js", "contributes": {"commands": [{"command": "all-agent.startServer", "title": "Start All-Agent Server", "category": "All-Agent"}, {"command": "all-agent.stopServer", "title": "Stop All-Agent Server", "category": "All-Agent"}, {"command": "all-agent.openChat", "title": "Open Chat Panel", "category": "All-Agent"}, {"command": "all-agent.analyzeProject", "title": "Analyze Current Project", "category": "All-Agent"}, {"command": "all-agent.generateCode", "title": "Generate Code", "category": "All-Agent"}, {"command": "all-agent.openSettings", "title": "Open Settings", "category": "All-Agent"}], "views": {"all-agent": [{"id": "all-agent-chat", "name": "Cha<PERSON>", "type": "webview"}, {"id": "all-agent-agents", "name": "Agents", "type": "tree"}, {"id": "all-agent-tasks", "name": "Tasks", "type": "tree"}]}, "viewsContainers": {"activitybar": [{"id": "all-agent", "title": "All-Agent", "icon": "$(robot)"}]}, "configuration": {"title": "All-Agent", "properties": {"all-agent.serverPort": {"type": "number", "default": 3000, "description": "Port for All-Agent server"}, "all-agent.autoStart": {"type": "boolean", "default": true, "description": "Automatically start All-Agent server when VSCode opens"}, "all-agent.llmProvider": {"type": "string", "enum": ["deepseek", "mistral", "openai", "anthropic", "google"], "default": "deepseek", "description": "Default LLM provider"}, "all-agent.deepseekApiKey": {"type": "string", "default": "", "description": "DeepSeek API Key"}, "all-agent.mistralApiKey": {"type": "string", "default": "", "description": "Mistral AI API Key"}, "all-agent.openaiApiKey": {"type": "string", "default": "", "description": "OpenAI API Key"}}}, "menus": {"explorer/context": [{"command": "all-agent.analyzeProject", "when": "explorerResourceIsFolder", "group": "all-agent"}], "editor/context": [{"command": "all-agent.generateCode", "when": "editorHasSelection", "group": "all-agent"}]}}, "scripts": {"vscode:prepublish": "npm run compile", "compile": "tsc -p ./", "watch": "tsc -watch -p ./"}, "devDependencies": {"@types/vscode": "^1.74.0", "@types/node": "16.x", "typescript": "^4.9.4"}, "dependencies": {"axios": "^1.6.0", "ws": "^8.14.0"}}