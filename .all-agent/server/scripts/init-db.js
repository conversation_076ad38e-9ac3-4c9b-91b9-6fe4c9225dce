#!/usr/bin/env node

/**
 * All-Agent 数据库初始化脚本
 * 创建必要的数据库表和初始数据
 */

const Database = require('better-sqlite3');
const path = require('path');
const fs = require('fs');

// 配置
const DB_PATH = process.env.DATABASE_PATH || './data/all-agent.db';
const DATA_DIR = path.dirname(DB_PATH);

// 确保数据目录存在
if (!fs.existsSync(DATA_DIR)) {
  fs.mkdirSync(DATA_DIR, { recursive: true });
  console.log(`✅ 创建数据目录: ${DATA_DIR}`);
}

// 连接数据库
const db = new sqlite3.Database(DB_PATH, (err) => {
  if (err) {
    console.error('❌ 数据库连接失败:', err.message);
    process.exit(1);
  }
  console.log(`🔗 连接到数据库: ${DB_PATH}`);
});

// 数据库表结构
const tables = {
  // 用户表
  users: `
        CREATE TABLE IF NOT EXISTS users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username VARCHAR(50) UNIQUE NOT NULL,
            email VARCHAR(100) UNIQUE NOT NULL,
            password_hash VARCHAR(255) NOT NULL,
            role VARCHAR(20) DEFAULT 'user',
            avatar_url VARCHAR(255),
            preferences TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            last_login DATETIME,
            is_active BOOLEAN DEFAULT 1
        )
    `,

  // 会话表
  sessions: `
        CREATE TABLE IF NOT EXISTS sessions (
            id VARCHAR(255) PRIMARY KEY,
            user_id INTEGER NOT NULL,
            data TEXT,
            expires_at DATETIME NOT NULL,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
        )
    `,

  // 项目表
  projects: `
        CREATE TABLE IF NOT EXISTS projects (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name VARCHAR(100) NOT NULL,
            description TEXT,
            path VARCHAR(500) NOT NULL,
            user_id INTEGER NOT NULL,
            status VARCHAR(20) DEFAULT 'active',
            metadata TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
        )
    `,

  // 任务表
  tasks: `
        CREATE TABLE IF NOT EXISTS tasks (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            task_id VARCHAR(100) UNIQUE NOT NULL,
            user_id INTEGER NOT NULL,
            project_id INTEGER,
            agent_type VARCHAR(50) NOT NULL,
            action VARCHAR(100) NOT NULL,
            input_data TEXT,
            output_data TEXT,
            status VARCHAR(20) DEFAULT 'pending',
            priority INTEGER DEFAULT 5,
            progress INTEGER DEFAULT 0,
            error_message TEXT,
            started_at DATETIME,
            completed_at DATETIME,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE,
            FOREIGN KEY (project_id) REFERENCES projects (id) ON DELETE SET NULL
        )
    `,

  // 聊天消息表
  chat_messages: `
        CREATE TABLE IF NOT EXISTS chat_messages (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            agent_type VARCHAR(50) NOT NULL,
            message_type VARCHAR(20) NOT NULL,
            content TEXT NOT NULL,
            context_data TEXT,
            metadata TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
        )
    `,

  // Agent 状态表
  agent_states: `
        CREATE TABLE IF NOT EXISTS agent_states (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            agent_type VARCHAR(50) NOT NULL,
            agent_id VARCHAR(100) NOT NULL,
            status VARCHAR(20) NOT NULL,
            current_task VARCHAR(100),
            metadata TEXT,
            last_heartbeat DATETIME DEFAULT CURRENT_TIMESTAMP,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            UNIQUE(agent_type, agent_id)
        )
    `,

  // 分析历史表
  analysis_history: `
        CREATE TABLE IF NOT EXISTS analysis_history (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            project_id INTEGER,
            analysis_type VARCHAR(50) NOT NULL,
            input_data TEXT,
            result_data TEXT,
            execution_time INTEGER,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE,
            FOREIGN KEY (project_id) REFERENCES projects (id) ON DELETE SET NULL
        )
    `,

  // 系统配置表
  system_config: `
        CREATE TABLE IF NOT EXISTS system_config (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            config_key VARCHAR(100) UNIQUE NOT NULL,
            config_value TEXT,
            config_type VARCHAR(20) DEFAULT 'string',
            description TEXT,
            is_public BOOLEAN DEFAULT 0,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
    `,

  // 审计日志表
  audit_logs: `
        CREATE TABLE IF NOT EXISTS audit_logs (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER,
            action VARCHAR(100) NOT NULL,
            resource_type VARCHAR(50),
            resource_id VARCHAR(100),
            details TEXT,
            ip_address VARCHAR(45),
            user_agent TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE SET NULL
        )
    `,

  // 前沿技术记录表
  frontier_tech_records: `
        CREATE TABLE IF NOT EXISTS frontier_tech_records (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            tech_type VARCHAR(50) NOT NULL,
            operation VARCHAR(100) NOT NULL,
            input_data TEXT,
            output_data TEXT,
            execution_time INTEGER,
            status VARCHAR(20) DEFAULT 'completed',
            error_message TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
        )
    `
};

// 索引定义
const indexes = [
  'CREATE INDEX IF NOT EXISTS idx_users_username ON users(username)',
  'CREATE INDEX IF NOT EXISTS idx_users_email ON users(email)',
  'CREATE INDEX IF NOT EXISTS idx_sessions_user_id ON sessions(user_id)',
  'CREATE INDEX IF NOT EXISTS idx_sessions_expires_at ON sessions(expires_at)',
  'CREATE INDEX IF NOT EXISTS idx_projects_user_id ON projects(user_id)',
  'CREATE INDEX IF NOT EXISTS idx_tasks_user_id ON tasks(user_id)',
  'CREATE INDEX IF NOT EXISTS idx_tasks_status ON tasks(status)',
  'CREATE INDEX IF NOT EXISTS idx_tasks_agent_type ON tasks(agent_type)',
  'CREATE INDEX IF NOT EXISTS idx_chat_messages_user_id ON chat_messages(user_id)',
  'CREATE INDEX IF NOT EXISTS idx_chat_messages_agent_type ON chat_messages(agent_type)',
  'CREATE INDEX IF NOT EXISTS idx_agent_states_agent_type ON agent_states(agent_type)',
  'CREATE INDEX IF NOT EXISTS idx_analysis_history_user_id ON analysis_history(user_id)',
  'CREATE INDEX IF NOT EXISTS idx_audit_logs_user_id ON audit_logs(user_id)',
  'CREATE INDEX IF NOT EXISTS idx_audit_logs_action ON audit_logs(action)',
  'CREATE INDEX IF NOT EXISTS idx_frontier_tech_records_user_id ON frontier_tech_records(user_id)',
  'CREATE INDEX IF NOT EXISTS idx_frontier_tech_records_tech_type ON frontier_tech_records(tech_type)'
];

// 初始数据
const initialData = {
  system_config: [
    {
      config_key: 'system_version',
      config_value: '1.0.0',
      description: '系统版本号',
      is_public: 1
    },
    {
      config_key: 'max_concurrent_tasks',
      config_value: '10',
      config_type: 'integer',
      description: '最大并发任务数'
    },
    {
      config_key: 'default_agent_timeout',
      config_value: '300000',
      config_type: 'integer',
      description: '默认 Agent 超时时间（毫秒）'
    },
    {
      config_key: 'enable_audit_logging',
      config_value: 'true',
      config_type: 'boolean',
      description: '是否启用审计日志'
    },
    {
      config_key: 'aiops_enabled',
      config_value: 'true',
      config_type: 'boolean',
      description: '是否启用 AIOps 功能'
    },
    {
      config_key: 'edge_ai_enabled',
      config_value: 'true',
      config_type: 'boolean',
      description: '是否启用边缘 AI 功能'
    },
    {
      config_key: 'web3_enabled',
      config_value: 'false',
      config_type: 'boolean',
      description: '是否启用 Web3 功能'
    },
    {
      config_key: 'quantum_enabled',
      config_value: 'false',
      config_type: 'boolean',
      description: '是否启用量子计算功能'
    }
  ]
};

// 创建表
async function createTables() {
  console.log('📋 开始创建数据库表...');

  for (const [tableName, sql] of Object.entries(tables)) {
    try {
      await new Promise((resolve, reject) => {
        db.run(sql, (err) => {
          if (err) reject(err);
          else resolve();
        });
      });
      console.log(`✅ 创建表: ${tableName}`);
    } catch (error) {
      console.error(`❌ 创建表 ${tableName} 失败:`, error.message);
      throw error;
    }
  }
}

// 创建索引
async function createIndexes() {
  console.log('🔍 开始创建数据库索引...');

  for (const sql of indexes) {
    try {
      await new Promise((resolve, reject) => {
        db.run(sql, (err) => {
          if (err) reject(err);
          else resolve();
        });
      });
    } catch (error) {
      console.error('❌ 创建索引失败:', error.message);
      throw error;
    }
  }
  console.log('✅ 索引创建完成');
}

// 插入初始数据
async function insertInitialData() {
  console.log('📝 开始插入初始数据...');

  for (const [tableName, records] of Object.entries(initialData)) {
    for (const record of records) {
      const columns = Object.keys(record).join(', ');
      const placeholders = Object.keys(record)
        .map(() => '?')
        .join(', ');
      const values = Object.values(record);

      const sql = `INSERT OR IGNORE INTO ${tableName} (${columns}) VALUES (${placeholders})`;

      try {
        await new Promise((resolve, reject) => {
          db.run(sql, values, (err) => {
            if (err) reject(err);
            else resolve();
          });
        });
      } catch (error) {
        console.error(`❌ 插入数据到 ${tableName} 失败:`, error.message);
        throw error;
      }
    }
    console.log(`✅ 初始化表: ${tableName}`);
  }
}

// 验证数据库
async function validateDatabase() {
  console.log('🔍 验证数据库结构...');

  const tableNames = Object.keys(tables);

  for (const tableName of tableNames) {
    try {
      const result = await new Promise((resolve, reject) => {
        db.get(`SELECT name FROM sqlite_master WHERE type='table' AND name=?`, [tableName], (err, row) => {
          if (err) reject(err);
          else resolve(row);
        });
      });

      if (result) {
        console.log(`✅ 验证表: ${tableName}`);
      } else {
        throw new Error(`表 ${tableName} 不存在`);
      }
    } catch (error) {
      console.error(`❌ 验证表 ${tableName} 失败:`, error.message);
      throw error;
    }
  }
}

// 主函数
async function main() {
  try {
    console.log('🚀 开始初始化 All-Agent 数据库...');

    await createTables();
    await createIndexes();
    await insertInitialData();
    await validateDatabase();

    console.log('🎉 数据库初始化完成！');
    console.log(`📍 数据库位置: ${path.resolve(DB_PATH)}`);

    // 显示统计信息
    const stats = await new Promise((resolve, reject) => {
      db.get(`SELECT COUNT(*) as table_count FROM sqlite_master WHERE type='table'`, (err, row) => {
        if (err) reject(err);
        else resolve(row);
      });
    });

    console.log(`📊 数据库统计: ${stats.table_count} 个表`);
  } catch (error) {
    console.error('❌ 数据库初始化失败:', error.message);
    process.exit(1);
  } finally {
    db.close((err) => {
      if (err) {
        console.error('❌ 关闭数据库连接失败:', err.message);
      } else {
        console.log('🔒 数据库连接已关闭');
      }
    });
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = { main };
