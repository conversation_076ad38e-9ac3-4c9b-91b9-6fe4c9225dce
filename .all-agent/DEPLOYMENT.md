# All-Agent 部署指南

## 🚀 快速开始

All-Agent 支持多种部署方式，从本地开发到生产环境，满足不同场景需求。

### 部署方式对比

| 部署方式       | 适用场景 | 复杂度 | 可扩展性 | 推荐指数 |
| -------------- | -------- | ------ | -------- | -------- |
| 本地部署       | 开发测试 | ⭐      | ⭐        | ⭐⭐⭐      |
| Docker Compose | 小型生产 | ⭐⭐     | ⭐⭐       | ⭐⭐⭐⭐     |
| Kubernetes     | 大型生产 | ⭐⭐⭐    | ⭐⭐⭐⭐⭐    | ⭐⭐⭐⭐⭐    |
| 多云部署       | 全球化   | ⭐⭐⭐⭐   | ⭐⭐⭐⭐⭐    | ⭐⭐⭐⭐⭐    |
| 边缘计算       | 低延迟   | ⭐⭐     | ⭐⭐⭐      | ⭐⭐⭐⭐     |
| 服务网格       | 企业级   | ⭐⭐⭐⭐⭐  | ⭐⭐⭐⭐⭐    | ⭐⭐⭐⭐⭐    |

## 📋 环境要求

### 基础要求
- **Node.js**: >= 18.0.0
- **npm**: >= 8.0.0
- **内存**: >= 2GB
- **磁盘**: >= 10GB

### 容器化要求
- **Docker**: >= 20.10
- **Docker Compose**: >= 2.0

### Kubernetes 要求
- **Kubernetes**: >= 1.20
- **kubectl**: >= 1.20
- **Helm**: >= 3.0 (可选)

## 🔧 环境配置

### 1. 克隆项目
```bash
git clone https://github.com/all-agent/all-agent.git
cd all-agent
```

### 2. 环境变量配置
```bash
# 复制环境配置模板
cp server/.env.example .env

# 编辑配置文件
vim .env
```

### 3. 必要配置项
```bash
# 基础配置
NODE_ENV=production
PORT=3000
JWT_SECRET=your-super-secret-jwt-key

# 数据库配置
DATABASE_URL=sqlite:./data/all-agent.db

# Redis 配置 (可选)
USE_REDIS=true
REDIS_HOST=localhost
REDIS_PORT=6379

# LLM API 配置
OPENAI_API_KEY=your-openai-api-key
ANTHROPIC_API_KEY=your-anthropic-api-key

# 监控配置
MONITOR_INTERVAL=30000
CPU_THRESHOLD=80
MEMORY_THRESHOLD=85

# 告警配置 (可选)
EMAIL_ALERTS_ENABLED=true
EMAIL_SMTP_HOST=smtp.gmail.com
EMAIL_USERNAME=<EMAIL>
EMAIL_PASSWORD=your-app-password
EMAIL_ALERT_TO=<EMAIL>

SLACK_ALERTS_ENABLED=true
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/YOUR/WEBHOOK
```

## 🏠 本地部署

### 快速启动
```bash
# 使用部署脚本
./scripts/deploy.sh local development

# 或手动启动
cd server
npm install
npm start
```

### 验证部署
```bash
# 健康检查
curl http://localhost:3000/health

# 访问应用
open http://localhost:3000
```

### 本地开发
```bash
# 开发模式启动
npm run dev

# 运行测试
npm test

# 性能测试
npm run test:performance
```

## 🐳 Docker 部署

### 单容器部署
```bash
# 构建镜像
docker build -t all-agent:latest .

# 运行容器
docker run -d \
  --name all-agent \
  -p 3000:3000 \
  -v $(pwd)/data:/app/data \
  -e NODE_ENV=production \
  all-agent:latest
```

### Docker Compose 部署 (推荐)
```bash
# 启动完整服务栈
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f

# 停止服务
docker-compose down
```

### Docker Compose 服务说明
```yaml
services:
  all-agent:     # 主应用
  redis:         # 缓存服务
  postgres:      # 数据库 (可选)
  nginx:         # 反向代理
  prometheus:    # 监控收集
  grafana:       # 监控可视化
  node-exporter: # 系统指标
```

### 验证 Docker 部署
```bash
# 健康检查
curl http://localhost:3000/health

# 查看监控
open http://localhost:3001  # Grafana
open http://localhost:9090  # Prometheus
```

## ☸️ Kubernetes 部署

### 前置条件
```bash
# 确保 kubectl 已配置
kubectl cluster-info

# 确保有足够资源
kubectl top nodes
```

### 快速部署
```bash
# 使用部署脚本
./scripts/deploy.sh kubernetes production

# 或手动部署
kubectl apply -f k8s/
```

### 手动部署步骤
```bash
# 1. 创建命名空间
kubectl apply -f k8s/namespace.yaml

# 2. 创建配置和密钥
kubectl apply -f k8s/configmap.yaml
kubectl apply -f k8s/secrets.yaml

# 3. 创建存储
kubectl apply -f k8s/pvc.yaml

# 4. 部署应用
kubectl apply -f k8s/deployment.yaml

# 5. 创建服务
kubectl apply -f k8s/service.yaml

# 6. 配置入口 (可选)
kubectl apply -f k8s/ingress.yaml
```

### 验证 Kubernetes 部署
```bash
# 查看 Pod 状态
kubectl get pods -n all-agent

# 查看服务状态
kubectl get svc -n all-agent

# 端口转发访问
kubectl port-forward svc/all-agent-app 3000:3000 -n all-agent

# 健康检查
curl http://localhost:3000/health
```

### Kubernetes 管理命令
```bash
# 查看日志
kubectl logs -l app.kubernetes.io/name=all-agent -n all-agent

# 扩容
kubectl scale deployment all-agent-app --replicas=5 -n all-agent

# 滚动更新
kubectl set image deployment/all-agent-app all-agent=all-agent:v2.0.0 -n all-agent

# 回滚
kubectl rollout undo deployment/all-agent-app -n all-agent

# 删除部署
kubectl delete namespace all-agent
```

## 🔧 高级配置

### 生产环境优化
```bash
# 环境变量
NODE_ENV=production
LOG_LEVEL=warn
USE_REDIS=true
REDIS_HOST=redis-cluster.example.com

# 数据库配置
DATABASE_URL=postgresql://user:<EMAIL>/allagent

# 安全配置
JWT_SECRET=super-secure-secret-key
CORS_ORIGIN=https://app.company.com

# 监控配置
EMAIL_ALERTS_ENABLED=true
SLACK_ALERTS_ENABLED=true
```

### 负载均衡配置
```nginx
# Nginx 配置示例
upstream all-agent-backend {
    server all-agent-1:3000;
    server all-agent-2:3000;
    server all-agent-3:3000;
}

server {
    listen 80;
    server_name app.company.com;

    location / {
        proxy_pass http://all-agent-backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

### SSL/TLS 配置
```yaml
# Kubernetes Ingress with TLS
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: all-agent-ingress
spec:
  tls:
  - hosts:
    - app.company.com
    secretName: all-agent-tls
  rules:
  - host: app.company.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: all-agent-app
            port:
              number: 3000
```

## 📊 监控和日志

### 监控访问
```bash
# Grafana 监控面板
http://localhost:3001
# 默认账户: admin/admin123

# Prometheus 指标
http://localhost:9090

# 应用监控面板
http://localhost:3000/monitor
```

### 日志查看
```bash
# Docker Compose
docker-compose logs -f all-agent

# Kubernetes
kubectl logs -f deployment/all-agent-app -n all-agent

# 本地部署
tail -f server/logs/app.log
```

### 性能监控
```bash
# 运行性能测试
npm run test:performance

# 运行基准测试
npm run benchmark

# 查看性能报告
open server/reports/performance-*.html
```

## 🔒 安全配置

### 基础安全
```bash
# 强密码配置
JWT_SECRET=$(openssl rand -base64 32)

# 限制 CORS
CORS_ORIGIN=https://your-domain.com

# 启用 HTTPS
SSL_CERT_PATH=/path/to/cert.pem
SSL_KEY_PATH=/path/to/key.pem
```

### 容器安全
```dockerfile
# 使用非 root 用户
USER allagent

# 只读根文件系统
--read-only

# 限制能力
--cap-drop=ALL
```

### Kubernetes 安全
```yaml
# 安全上下文
securityContext:
  runAsNonRoot: true
  runAsUser: 1001
  readOnlyRootFilesystem: true
  allowPrivilegeEscalation: false
```

## 🚨 故障排除

### 常见问题

#### 1. 服务启动失败
```bash
# 检查端口占用
lsof -i :3000

# 检查环境变量
env | grep NODE_ENV

# 查看详细日志
npm start 2>&1 | tee debug.log
```

#### 2. 数据库连接失败
```bash
# 检查数据库文件权限
ls -la data/

# 检查 SQLite 版本
sqlite3 --version

# 手动测试连接
sqlite3 data/all-agent.db ".tables"
```

#### 3. Redis 连接失败
```bash
# 检查 Redis 服务
redis-cli ping

# 检查网络连接
telnet redis-host 6379

# 查看 Redis 日志
docker logs redis-container
```

#### 4. 容器启动失败
```bash
# 查看容器日志
docker logs all-agent

# 检查镜像
docker images | grep all-agent

# 进入容器调试
docker exec -it all-agent sh
```

#### 5. Kubernetes 部署失败
```bash
# 查看 Pod 状态
kubectl describe pod -n all-agent

# 查看事件
kubectl get events -n all-agent

# 检查资源限制
kubectl top pods -n all-agent
```

### 性能问题排查
```bash
# 内存使用
free -h
docker stats

# CPU 使用
top
htop

# 磁盘使用
df -h
du -sh data/

# 网络连接
netstat -tulpn | grep 3000
```

## 📚 更多资源

### 文档链接
- [API 文档](http://localhost:3000/api-docs)
- [监控面板](http://localhost:3000/monitor)
- [项目 README](./README.md)
- [开发指南](./DEVELOPMENT.md)

### 社区支持
- [GitHub Issues](https://github.com/all-agent/all-agent/issues)
- [讨论区](https://github.com/all-agent/all-agent/discussions)
- [Wiki](https://github.com/all-agent/all-agent/wiki)

### 联系方式
- **邮箱**: <EMAIL>
- **Slack**: [All-Agent Community](https://all-agent.slack.com)
- **Twitter**: [@AllAgentAI](https://twitter.com/AllAgentAI)

---

*最后更新: 2024-12-19*
*版本: 1.0.0*
