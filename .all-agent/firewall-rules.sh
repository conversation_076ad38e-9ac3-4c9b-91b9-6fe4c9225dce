#!/bin/bash

# All-Agent 防火墙配置
# 仅允许必要的端口访问

# 清除现有规则
iptables -F
iptables -X
iptables -t nat -F
iptables -t nat -X

# 设置默认策略
iptables -P INPUT DROP
iptables -P FORWARD DROP
iptables -P OUTPUT ACCEPT

# 允许本地回环
iptables -A INPUT -i lo -j ACCEPT
iptables -A OUTPUT -o lo -j ACCEPT

# 允许已建立的连接
iptables -A INPUT -m state --state ESTABLISHED,RELATED -j ACCEPT

# 允许 SSH (端口 22)
iptables -A INPUT -p tcp --dport 22 -j ACCEPT

# 允许 HTTP (端口 80)
iptables -A INPUT -p tcp --dport 80 -j ACCEPT

# 允许 HTTPS (端口 443)
iptables -A INPUT -p tcp --dport 443 -j ACCEPT

# 允许 All-Agent 服务端口 (端口 3000)
iptables -A INPUT -p tcp --dport 3000 -j ACCEPT

# 允许 ping
iptables -A INPUT -p icmp --icmp-type echo-request -j ACCEPT

# 记录被拒绝的连接
iptables -A INPUT -j LOG --log-prefix "DROPPED: "
iptables -A INPUT -j DROP

# 保存规则
iptables-save > /etc/iptables/rules.v4

echo "防火墙规则已配置"
