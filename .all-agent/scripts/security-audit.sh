#!/bin/bash

# All-Agent 安全审计脚本
# 运行多种安全扫描工具检查漏洞

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查结果统计
TOTAL_CHECKS=0
PASSED_CHECKS=0
FAILED_CHECKS=0
WARNING_CHECKS=0

# 执行检查并记录结果
run_check() {
    local check_name="$1"
    local check_command="$2"
    local is_critical="${3:-true}"
    
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    log_info "检查: $check_name"
    
    if eval "$check_command"; then
        log_success "✅ $check_name - 通过"
        PASSED_CHECKS=$((PASSED_CHECKS + 1))
        return 0
    else
        if [ "$is_critical" = "true" ]; then
            log_error "❌ $check_name - 失败"
            FAILED_CHECKS=$((FAILED_CHECKS + 1))
            return 1
        else
            log_warning "⚠️ $check_name - 警告"
            WARNING_CHECKS=$((WARNING_CHECKS + 1))
            return 0
        fi
    fi
}

# 1. npm 安全审计
npm_security_audit() {
    log_info "=== npm 安全审计 ==="
    
    cd server
    
    # 检查已知漏洞
    log_info "运行 npm audit..."
    if npm audit --audit-level=moderate; then
        log_success "npm audit 通过 - 无中高危漏洞"
        PASSED_CHECKS=$((PASSED_CHECKS + 1))
    else
        local audit_result=$?
        if [ $audit_result -eq 1 ]; then
            log_warning "发现低危漏洞，建议修复"
            WARNING_CHECKS=$((WARNING_CHECKS + 1))
        else
            log_error "发现中高危漏洞，需要立即修复"
            FAILED_CHECKS=$((FAILED_CHECKS + 1))
        fi
    fi
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    
    # 检查过时依赖
    log_info "检查过时依赖..."
    if npm outdated --depth=0; then
        log_warning "发现过时依赖，建议更新"
        WARNING_CHECKS=$((WARNING_CHECKS + 1))
    else
        log_success "所有依赖都是最新的"
        PASSED_CHECKS=$((PASSED_CHECKS + 1))
    fi
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    
    cd ..
}

# 2. 代码安全扫描
code_security_scan() {
    log_info "=== 代码安全扫描 ==="
    
    # 检查硬编码密钥
    log_info "扫描硬编码密钥和敏感信息..."
    
    # 常见的敏感信息模式
    local sensitive_patterns=(
        "password\s*=\s*['\"][^'\"]{3,}['\"]"
        "api[_-]?key\s*=\s*['\"][^'\"]{10,}['\"]"
        "secret\s*=\s*['\"][^'\"]{10,}['\"]"
        "token\s*=\s*['\"][^'\"]{20,}['\"]"
        "private[_-]?key"
        "-----BEGIN.*PRIVATE.*KEY-----"
    )
    
    local found_issues=0
    for pattern in "${sensitive_patterns[@]}"; do
        if grep -r -i -E "$pattern" server/ --exclude-dir=node_modules --exclude-dir=data --exclude="*.log" 2>/dev/null; then
            found_issues=$((found_issues + 1))
        fi
    done
    
    if [ $found_issues -eq 0 ]; then
        log_success "未发现硬编码敏感信息"
        PASSED_CHECKS=$((PASSED_CHECKS + 1))
    else
        log_error "发现 $found_issues 个潜在的硬编码敏感信息"
        FAILED_CHECKS=$((FAILED_CHECKS + 1))
    fi
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    
    # 检查不安全的函数调用
    log_info "扫描不安全的函数调用..."
    local unsafe_patterns=(
        "eval\s*\("
        "Function\s*\("
        "setTimeout\s*\(\s*['\"]"
        "setInterval\s*\(\s*['\"]"
        "innerHTML\s*="
        "document\.write"
    )
    
    local unsafe_found=0
    for pattern in "${unsafe_patterns[@]}"; do
        if grep -r -E "$pattern" server/ --exclude-dir=node_modules --exclude-dir=data 2>/dev/null; then
            unsafe_found=$((unsafe_found + 1))
        fi
    done
    
    if [ $unsafe_found -eq 0 ]; then
        log_success "未发现不安全的函数调用"
        PASSED_CHECKS=$((PASSED_CHECKS + 1))
    else
        log_warning "发现 $unsafe_found 个潜在不安全的函数调用"
        WARNING_CHECKS=$((WARNING_CHECKS + 1))
    fi
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
}

# 3. 配置安全检查
config_security_check() {
    log_info "=== 配置安全检查 ==="
    
    cd server
    
    # 检查环境变量配置
    run_check "JWT 密钥强度" "test \${#JWT_SECRET} -ge 32" "false"
    
    # 检查文件权限
    run_check "敏感文件权限" "find . -name '*.env*' -perm +044 | wc -l | grep -q '^0$'" "false"
    
    # 检查默认配置
    if [ -f ".env" ]; then
        if grep -q "change-in-production" .env; then
            log_warning "发现默认配置，生产环境需要更改"
            WARNING_CHECKS=$((WARNING_CHECKS + 1))
        else
            log_success "配置已自定义"
            PASSED_CHECKS=$((PASSED_CHECKS + 1))
        fi
    else
        log_warning "未找到 .env 文件"
        WARNING_CHECKS=$((WARNING_CHECKS + 1))
    fi
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    
    cd ..
}

# 4. 网络安全检查
network_security_check() {
    log_info "=== 网络安全检查 ==="
    
    # 启动测试服务器
    cd server
    export NODE_ENV=test
    export PORT=3002
    export JWT_SECRET=test-jwt-secret-for-security-audit
    
    log_info "启动测试服务器进行安全扫描..."
    node app-new.js &
    SERVER_PID=$!
    sleep 5
    
    # 检查 HTTPS 重定向
    log_info "检查安全头..."
    local security_headers=$(curl -s -I http://localhost:3002/health)
    
    if echo "$security_headers" | grep -q "X-Content-Type-Options"; then
        log_success "X-Content-Type-Options 头存在"
        PASSED_CHECKS=$((PASSED_CHECKS + 1))
    else
        log_warning "缺少 X-Content-Type-Options 头"
        WARNING_CHECKS=$((WARNING_CHECKS + 1))
    fi
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    
    if echo "$security_headers" | grep -q "X-Frame-Options"; then
        log_success "X-Frame-Options 头存在"
        PASSED_CHECKS=$((PASSED_CHECKS + 1))
    else
        log_warning "缺少 X-Frame-Options 头"
        WARNING_CHECKS=$((WARNING_CHECKS + 1))
    fi
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    
    # 检查 SQL 注入防护
    log_info "测试 SQL 注入防护..."
    local sql_injection_response=$(curl -s -X POST http://localhost:3002/auth/login \
        -H "Content-Type: application/json" \
        -d '{"username":"admin'\''OR 1=1--","password":"test"}')
    
    if echo "$sql_injection_response" | grep -q "error"; then
        log_success "SQL 注入防护正常"
        PASSED_CHECKS=$((PASSED_CHECKS + 1))
    else
        log_error "SQL 注入防护可能存在问题"
        FAILED_CHECKS=$((FAILED_CHECKS + 1))
    fi
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    
    # 检查 XSS 防护
    log_info "测试 XSS 防护..."
    local xss_response=$(curl -s -X POST http://localhost:3002/api/test-ai-model \
        -H "Content-Type: application/json" \
        -d '{"action":"test_prompt","prompt":"<script>alert(1)</script>"}')
    
    if echo "$xss_response" | grep -q "script"; then
        log_warning "XSS 防护可能不足"
        WARNING_CHECKS=$((WARNING_CHECKS + 1))
    else
        log_success "XSS 防护正常"
        PASSED_CHECKS=$((PASSED_CHECKS + 1))
    fi
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    
    # 停止测试服务器
    kill $SERVER_PID 2>/dev/null || true
    sleep 2
    
    cd ..
}

# 5. 依赖安全分析
dependency_security_analysis() {
    log_info "=== 依赖安全分析 ==="
    
    cd server
    
    # 检查依赖许可证
    log_info "检查依赖许可证..."
    if command -v license-checker >/dev/null 2>&1; then
        license-checker --summary > /tmp/license-summary.txt 2>/dev/null || true
        if [ -f /tmp/license-summary.txt ]; then
            log_success "依赖许可证检查完成"
            PASSED_CHECKS=$((PASSED_CHECKS + 1))
        else
            log_warning "无法检查依赖许可证"
            WARNING_CHECKS=$((WARNING_CHECKS + 1))
        fi
    else
        log_warning "license-checker 未安装，跳过许可证检查"
        WARNING_CHECKS=$((WARNING_CHECKS + 1))
    fi
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    
    # 检查依赖数量
    local dep_count=$(npm list --depth=0 2>/dev/null | grep -c "├──\|└──" || echo "0")
    log_info "项目依赖数量: $dep_count"
    
    if [ "$dep_count" -lt 100 ]; then
        log_success "依赖数量合理 ($dep_count < 100)"
        PASSED_CHECKS=$((PASSED_CHECKS + 1))
    else
        log_warning "依赖数量较多 ($dep_count >= 100)，建议审查"
        WARNING_CHECKS=$((WARNING_CHECKS + 1))
    fi
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    
    cd ..
}

# 6. 文件系统安全检查
filesystem_security_check() {
    log_info "=== 文件系统安全检查 ==="
    
    # 检查敏感文件
    local sensitive_files=(
        ".env"
        "*.key"
        "*.pem"
        "*.p12"
        "*.pfx"
        "id_rsa"
        "id_dsa"
    )
    
    local found_sensitive=0
    for pattern in "${sensitive_files[@]}"; do
        if find . -name "$pattern" -not -path "./server/node_modules/*" 2>/dev/null | grep -q .; then
            found_sensitive=$((found_sensitive + 1))
        fi
    done
    
    if [ $found_sensitive -eq 0 ]; then
        log_success "未发现暴露的敏感文件"
        PASSED_CHECKS=$((PASSED_CHECKS + 1))
    else
        log_warning "发现 $found_sensitive 个敏感文件，请检查权限"
        WARNING_CHECKS=$((WARNING_CHECKS + 1))
    fi
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    
    # 检查备份文件
    local backup_patterns=(
        "*.bak"
        "*.backup"
        "*.old"
        "*.orig"
        "*~"
    )
    
    local found_backups=0
    for pattern in "${backup_patterns[@]}"; do
        if find . -name "$pattern" 2>/dev/null | grep -q .; then
            found_backups=$((found_backups + 1))
        fi
    done
    
    if [ $found_backups -eq 0 ]; then
        log_success "未发现备份文件"
        PASSED_CHECKS=$((PASSED_CHECKS + 1))
    else
        log_warning "发现 $found_backups 个备份文件，建议清理"
        WARNING_CHECKS=$((WARNING_CHECKS + 1))
    fi
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
}

# 主函数
main() {
    log_info "开始 All-Agent 安全审计..."
    log_info "审计时间: $(date)"
    echo
    
    # 执行所有安全检查
    npm_security_audit || true
    code_security_scan || true
    config_security_check || true
    network_security_check || true
    dependency_security_analysis || true
    filesystem_security_check || true
    
    # 输出审计结果
    echo
    log_info "=== 安全审计结果汇总 ==="
    echo "总检查项: $TOTAL_CHECKS"
    echo -e "通过: ${GREEN}$PASSED_CHECKS${NC}"
    echo -e "警告: ${YELLOW}$WARNING_CHECKS${NC}"
    echo -e "失败: ${RED}$FAILED_CHECKS${NC}"
    
    # 计算安全评分
    local security_score=$(( (PASSED_CHECKS * 100) / TOTAL_CHECKS ))
    echo "安全评分: $security_score%"
    
    # 生成安全报告
    cat > security-report.txt << EOF
All-Agent 安全审计报告
=====================

审计时间: $(date)
总检查项: $TOTAL_CHECKS
通过: $PASSED_CHECKS
警告: $WARNING_CHECKS
失败: $FAILED_CHECKS
安全评分: $security_score%

建议:
1. 修复所有失败项
2. 处理警告项
3. 在生产环境中使用强密钥
4. 定期更新依赖
5. 启用 HTTPS
EOF
    
    log_success "安全报告已生成: security-report.txt"
    
    if [ $FAILED_CHECKS -eq 0 ]; then
        log_success "🎉 安全审计通过！系统安全性良好。"
        exit 0
    else
        log_error "❌ 安全审计发现问题，请修复后重新审计。"
        exit 1
    fi
}

# 执行主函数
main "$@"
