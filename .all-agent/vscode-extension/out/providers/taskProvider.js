"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TaskProvider = void 0;
const vscode = require("vscode");
class TaskProvider {
    constructor(client) {
        this.client = client;
        this._onDidChangeTreeData = new vscode.EventEmitter();
        this.onDidChangeTreeData = this._onDidChangeTreeData.event;
        // 定期刷新任务状态
        setInterval(() => {
            this.refresh();
        }, 3000);
    }
    refresh() {
        this._onDidChangeTreeData.fire();
    }
    getTreeItem(element) {
        return element;
    }
    async getChildren(element) {
        if (!element) {
            // 根级别 - 返回所有任务
            try {
                const tasks = await this.client.getTasks();
                if (tasks.length === 0) {
                    return [new TaskItem('No active tasks', 'idle', 'none', vscode.TreeItemCollapsibleState.None)];
                }
                return tasks.map(task => new TaskItem(task.title || task.id || 'Unnamed Task', task.status || 'unknown', task.type || 'general', vscode.TreeItemCollapsibleState.Collapsed, task));
            }
            catch (error) {
                return [new TaskItem('Failed to load tasks', 'error', 'error', vscode.TreeItemCollapsibleState.None)];
            }
        }
        else {
            // 任务详细信息
            const details = [];
            if (element.taskData) {
                const data = element.taskData;
                if (data.agent) {
                    details.push(new TaskItem(`Agent: ${data.agent}`, 'info', 'agent', vscode.TreeItemCollapsibleState.None));
                }
                if (data.status) {
                    details.push(new TaskItem(`Status: ${data.status}`, 'info', 'status', vscode.TreeItemCollapsibleState.None));
                }
                if (data.progress !== undefined) {
                    details.push(new TaskItem(`Progress: ${data.progress}%`, 'info', 'progress', vscode.TreeItemCollapsibleState.None));
                }
                if (data.startTime) {
                    const startTime = new Date(data.startTime).toLocaleString();
                    details.push(new TaskItem(`Started: ${startTime}`, 'info', 'time', vscode.TreeItemCollapsibleState.None));
                }
                if (data.description) {
                    details.push(new TaskItem(`Description: ${data.description}`, 'info', 'description', vscode.TreeItemCollapsibleState.None));
                }
            }
            return details;
        }
    }
}
exports.TaskProvider = TaskProvider;
class TaskItem extends vscode.TreeItem {
    constructor(label, status, type, collapsibleState, taskData) {
        super(label, collapsibleState);
        this.label = label;
        this.status = status;
        this.type = type;
        this.collapsibleState = collapsibleState;
        this.taskData = taskData;
        this.tooltip = this.getTooltip();
        this.description = this.getDescription();
        this.iconPath = this.getIcon();
        this.contextValue = this.getContextValue();
    }
    getTooltip() {
        if (this.taskData) {
            let tooltip = `Task: ${this.taskData.title || this.taskData.id}\n`;
            tooltip += `Status: ${this.taskData.status}\n`;
            tooltip += `Type: ${this.taskData.type}`;
            if (this.taskData.agent) {
                tooltip += `\nAgent: ${this.taskData.agent}`;
            }
            if (this.taskData.description) {
                tooltip += `\nDescription: ${this.taskData.description}`;
            }
            return tooltip;
        }
        return this.label;
    }
    getDescription() {
        if (this.taskData) {
            if (this.taskData.progress !== undefined) {
                return `${this.taskData.progress}%`;
            }
            return this.status;
        }
        return '';
    }
    getIcon() {
        switch (this.type) {
            case 'analysis':
                return new vscode.ThemeIcon('search');
            case 'planning':
                return new vscode.ThemeIcon('list-ordered');
            case 'execution':
                return new vscode.ThemeIcon('play');
            case 'generation':
                return new vscode.ThemeIcon('file-code');
            case 'agent':
                return new vscode.ThemeIcon('person');
            case 'status':
                return new vscode.ThemeIcon('pulse');
            case 'progress':
                return new vscode.ThemeIcon('graph');
            case 'time':
                return new vscode.ThemeIcon('clock');
            case 'description':
                return new vscode.ThemeIcon('note');
            case 'none':
                return new vscode.ThemeIcon('circle-slash');
            default:
                switch (this.status) {
                    case 'running':
                        return new vscode.ThemeIcon('loading~spin', new vscode.ThemeColor('terminal.ansiYellow'));
                    case 'completed':
                        return new vscode.ThemeIcon('check', new vscode.ThemeColor('terminal.ansiGreen'));
                    case 'failed':
                        return new vscode.ThemeIcon('error', new vscode.ThemeColor('errorForeground'));
                    case 'pending':
                        return new vscode.ThemeIcon('clock', new vscode.ThemeColor('terminal.ansiBlue'));
                    case 'idle':
                        return new vscode.ThemeIcon('circle-outline');
                    default:
                        return new vscode.ThemeIcon('question');
                }
        }
    }
    getContextValue() {
        if (this.taskData) {
            return 'task';
        }
        return 'taskInfo';
    }
}
//# sourceMappingURL=taskProvider.js.map