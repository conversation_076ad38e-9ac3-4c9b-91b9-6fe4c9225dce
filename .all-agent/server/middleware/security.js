/**
 * 生产环境安全中间件
 * 实施多层安全防护
 */

const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
const slowDown = require('express-slow-down');
const cors = require('cors');
const compression = require('compression');

class SecurityMiddleware {
    constructor(app, options = {}) {
        this.app = app;
        this.options = {
            rateLimitWindow: options.rateLimitWindow || 15,
            rateLimitMax: options.rateLimitMax || 100,
            corsOrigin: options.corsOrigin || false,
            ...options
        };
        this.setupMiddleware();
    }

    setupMiddleware() {
        // 1. 安全头配置
        this.app.use(helmet({
            contentSecurityPolicy: {
                directives: {
                    defaultSrc: ["'self'"],
                    styleSrc: ["'self'", "'unsafe-inline'"],
                    scriptSrc: ["'self'"],
                    imgSrc: ["'self'", "data:", "https:"],
                    connectSrc: ["'self'"],
                    fontSrc: ["'self'"],
                    objectSrc: ["'none'"],
                    mediaSrc: ["'self'"],
                    frameSrc: ["'none'"],
                }
            },
            crossOriginEmbedderPolicy: false
        }));

        // 2. CORS 配置
        this.app.use(cors({
            origin: this.options.corsOrigin,
            credentials: true,
            methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
            allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
        }));

        // 3. 压缩
        this.app.use(compression());

        // 4. 速率限制
        const limiter = rateLimit({
            windowMs: this.options.rateLimitWindow * 60 * 1000,
            max: this.options.rateLimitMax,
            message: {
                error: 'Too many requests',
                retryAfter: this.options.rateLimitWindow * 60
            },
            standardHeaders: true,
            legacyHeaders: false,
        });
        this.app.use(limiter);

        // 5. 慢速攻击防护
        const speedLimiter = slowDown({
            windowMs: 15 * 60 * 1000, // 15分钟
            delayAfter: 50, // 50个请求后开始延迟
            delayMs: 500 // 每个请求延迟500ms
        });
        this.app.use(speedLimiter);

        // 6. 请求大小限制
        this.app.use(express.json({ limit: '10mb' }));
        this.app.use(express.urlencoded({ extended: true, limit: '10mb' }));

        // 7. 隐藏技术栈信息
        this.app.disable('x-powered-by');

        // 8. 安全日志
        this.app.use(this.securityLogger);
    }

    securityLogger(req, res, next) {
        const startTime = Date.now();
        
        res.on('finish', () => {
            const duration = Date.now() - startTime;
            const logData = {
                timestamp: new Date().toISOString(),
                method: req.method,
                url: req.url,
                ip: req.ip,
                userAgent: req.get('User-Agent'),
                statusCode: res.statusCode,
                duration,
                contentLength: res.get('Content-Length') || 0
            };

            // 记录可疑活动
            if (res.statusCode >= 400 || duration > 5000) {
                console.warn('[SECURITY] 可疑请求:', logData);
            }
        });

        next();
    }

    // API 密钥验证中间件
    static apiKeyAuth(req, res, next) {
        const apiKey = req.header('X-API-Key');
        const validApiKey = process.env.API_KEY;

        if (!apiKey || apiKey !== validApiKey) {
            return res.status(401).json({
                error: 'Invalid API key',
                code: 'UNAUTHORIZED'
            });
        }

        next();
    }

    // JWT 验证中间件
    static jwtAuth(req, res, next) {
        const token = req.header('Authorization')?.replace('Bearer ', '');
        
        if (!token) {
            return res.status(401).json({
                error: 'Access token required',
                code: 'TOKEN_REQUIRED'
            });
        }

        try {
            const jwt = require('jsonwebtoken');
            const decoded = jwt.verify(token, process.env.JWT_SECRET);
            req.user = decoded;
            next();
        } catch (error) {
            return res.status(401).json({
                error: 'Invalid token',
                code: 'TOKEN_INVALID'
            });
        }
    }

    // 输入验证中间件
    static validateInput(schema) {
        return (req, res, next) => {
            const { error } = schema.validate(req.body);
            if (error) {
                return res.status(400).json({
                    error: 'Validation failed',
                    details: error.details.map(d => d.message)
                });
            }
            next();
        };
    }
}

module.exports = SecurityMiddleware;
