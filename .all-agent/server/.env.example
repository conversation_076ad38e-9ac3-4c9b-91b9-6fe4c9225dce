# All-Agent 服务器环境配置示例
# 复制此文件为 .env 并填入实际配置值

# 服务器配置
NODE_ENV=development
PORT=3000
HOST=localhost

# 日志配置
LOG_LEVEL=info
LOG_DIR=../logs
ENABLE_FILE_LOGGING=true
ENABLE_CONSOLE_LOGGING=true

# LLM API 配置
# Anthropic Claude
ANTHROPIC_API_KEY=your_anthropic_api_key_here
ANTHROPIC_BASE_URL=https://api.anthropic.com/v1

# OpenAI GPT
OPENAI_API_KEY=********************************************************************************************************************************************************************
OPENAI_BASE_URL=https://api.openai.com/v1

# Google Gemini
GOOGLE_API_KEY=AIzaSyDxwc3MxJLkk9rtErYHM5PjayF6EhJZL4s
GOOGLE_BASE_URL=https://generativelanguage.googleapis.com/v1

# mistral
MISTRAL_API_KEY=vaz7dC9tYaY4ONoltjBGC4oEcXxwtNRR
MISTRAL_BASE_URL=https://api.mistral.ai/v1

# DeepSeek
DEEPSEEK_API_KEY=***********************************
DEEPSEEK_BASE_URL=https://api.deepseek.ai/v1

# 本地 LLM (如 Ollama)
LOCAL_LLM_URL=http://localhost:11434
LOCAL_LLM_MODEL=llama2

# 默认 LLM 提供商
DEFAULT_LLM_PROVIDER=anthropic

# 数据库配置
DATABASE_URL=sqlite:../data/all-agent.db
DATABASE_TYPE=sqlite
DATABASE_BACKUP_ENABLED=true
DATABASE_BACKUP_INTERVAL=86400000

# Redis 配置 (可选，用于缓存)
# Redis 配置（云端 Redis）
REDIS_URL=redis://default:<EMAIL>:12904
REDIS_PASSWORD=xxJmjhQJ9FZ5lD9rFIYL51AwiaZY3RFR
REDIS_DB=0


# 安全配置
JWT_SECRET=b4a96469d6656fe39312cd4dae8480df036211a28ce0532f383542183be02f81bb1f94b102ef38bd10d3ab20e0a3e9c081ac8aa31125f7592ced0b0944e563af
CORS_ORIGIN=*
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# 文件上传配置
MAX_FILE_SIZE=10485760
UPLOAD_DIR=../uploads
ALLOWED_FILE_TYPES=.js,.ts,.py,.java,.go,.rs,.php,.rb,.cs,.cpp,.c,.h,.vue,.html,.css,.scss,.less,.json,.xml,.yaml,.yml,.md,.sql,.txt

# 项目分析配置
MAX_SCAN_DEPTH=10
EXCLUDE_PATTERNS=node_modules,.git,.svn,dist,build,target,__pycache__,.pytest_cache,coverage,.nyc_output,logs,*.log,.DS_Store,Thumbs.db
SUPPORTED_LANGUAGES=javascript,typescript,python,java,go,rust,php,ruby,csharp,cpp,c,vue,html,css,scss,less,json,xml,yaml,markdown,sql

# Agent 配置
MAX_CONCURRENT_TASKS=10
TASK_TIMEOUT_MS=300000
RETRY_ATTEMPTS=3
RETRY_DELAY_MS=1000

# 代码生成配置
TEMPLATE_CACHE_TTL=3600000
OUTPUT_HISTORY_LIMIT=100
ENABLE_CODE_VALIDATION=true

# WebSocket 配置
WS_HEARTBEAT_INTERVAL=30000
WS_CONNECTION_TIMEOUT=60000
WS_MAX_CONNECTIONS=100

# 监控配置
ENABLE_METRICS=true
METRICS_PORT=9090
HEALTH_CHECK_INTERVAL=30000

# 开发配置
ENABLE_DEBUG=false
ENABLE_PROFILING=false
ENABLE_HOT_RELOAD=true

# 生产配置
CLUSTER_MODE=false
WORKER_PROCESSES=auto
GRACEFUL_SHUTDOWN_TIMEOUT=10000

# 外部服务配置
GITHUB_TOKEN=vaz7dC9tYaY4ONoltjBGC4oEcXxwtNRR
DOCKER_REGISTRY_URL=
DEPLOYMENT_WEBHOOK_URL=

# 通知配置
SLACK_WEBHOOK_URL=
EMAIL_SMTP_HOST=
EMAIL_SMTP_PORT=587
EMAIL_USERNAME=
EMAIL_PASSWORD=

# 备份配置
BACKUP_ENABLED=false
BACKUP_INTERVAL=86400000
BACKUP_RETENTION_DAYS=30
BACKUP_STORAGE_PATH=../backups
