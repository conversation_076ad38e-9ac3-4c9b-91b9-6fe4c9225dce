const request = require('supertest');
const { server } = require('../../app-new');

describe('All-Agent Server Integration Tests', () => {
  let app;
  let authToken;

  beforeAll(async () => {
    app = server.app;
    await server.initialize();
  });

  afterAll(async () => {
    await server.stop();
  });

  describe('Health Check', () => {
    test('GET /health should return server status', async () => {
      const response = await request(app)
        .get('/health')
        .expect(200);

      expect(response.body).toHaveProperty('status', 'healthy');
      expect(response.body).toHaveProperty('timestamp');
      expect(response.body).toHaveProperty('version');
    });
  });

  describe('Authentication', () => {
    test('POST /auth/register should create new user', async () => {
      const userData = {
        username: 'testuser',
        email: '<EMAIL>',
        password: 'TestPass123!'
      };

      const response = await request(app)
        .post('/auth/register')
        .send(userData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('id');
      expect(response.body.data.email).toBe(userData.email);
    });

    test('POST /auth/login should authenticate user', async () => {
      const loginData = {
        email: '<EMAIL>',
        password: 'TestPass123!'
      };

      const response = await request(app)
        .post('/auth/login')
        .send(loginData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('token');
      authToken = response.body.data.token;
    });

    test('GET /auth/me should return user info with valid token', async () => {
      const response = await request(app)
        .get('/auth/me')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('email', '<EMAIL>');
    });
  });

  describe('API Endpoints', () => {
    test('GET /api/agents should return agent status', async () => {
      const response = await request(app)
        .get('/api/agents')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toBeInstanceOf(Array);
    });

    test('POST /api/analyze should analyze project', async () => {
      const analysisData = {
        projectPath: process.cwd(),
        options: { includeTests: true }
      };

      const response = await request(app)
        .post('/api/analyze')
        .set('Authorization', `Bearer ${authToken}`)
        .send(analysisData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('summary');
    });
  });

  describe('Error Handling', () => {
    test('should handle invalid routes', async () => {
      const response = await request(app)
        .get('/invalid-route')
        .expect(404);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('not found');
    });

    test('should handle malformed JSON', async () => {
      const response = await request(app)
        .post('/auth/login')
        .send('invalid json')
        .expect(400);

      expect(response.body.success).toBe(false);
    });
  });

  describe('Security', () => {
    test('should reject requests without authentication', async () => {
      const response = await request(app)
        .post('/api/analyze')
        .send({ projectPath: '/test' })
        .expect(401);

      expect(response.body.success).toBe(false);
    });

    test('should enforce rate limiting', async () => {
      const requests = Array(15).fill().map(() => 
        request(app).get('/health')
      );

      const responses = await Promise.all(requests);
      const rateLimited = responses.some(res => res.status === 429);
      
      // 在高频请求下应该触发速率限制
      // 注意：这个测试可能需要调整，取决于具体的速率限制配置
    });
  });

  describe('Performance', () => {
    test('health check should respond quickly', async () => {
      const start = Date.now();
      
      await request(app)
        .get('/health')
        .expect(200);
      
      const duration = Date.now() - start;
      expect(duration).toBeLessThan(1000); // 应该在1秒内响应
    });

    test('should handle concurrent requests', async () => {
      const concurrentRequests = 10;
      const requests = Array(concurrentRequests).fill().map(() => 
        request(app).get('/health')
      );

      const start = Date.now();
      const responses = await Promise.all(requests);
      const duration = Date.now() - start;

      // 所有请求都应该成功
      responses.forEach(response => {
        expect(response.status).toBe(200);
      });

      // 并发处理应该相对高效
      expect(duration).toBeLessThan(5000);
    });
  });
});
