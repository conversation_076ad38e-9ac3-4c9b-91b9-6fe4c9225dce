#!/usr/bin/env node

/**
 * All-Agent 核心服务器
 * 提供 WebSocket 通信、Agent 管理、项目分析等核心功能
 */

const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const cors = require('cors');
const path = require('path');
const fs = require('fs').promises;

// 导入核心模块
const AgentManager = require('./core/AgentManager');
const ProjectAnalyzer = require('./core/ProjectAnalyzer');
const LLMService = require('./api/LLMService');
const CodeGenerator = require('./core/CodeGenerator');
const TaskScheduler = require('./core/TaskScheduler');
const Logger = require('./utils/Logger');

class AllAgentServer {
    constructor() {
        this.app = express();
        this.server = http.createServer(this.app);
        this.io = socketIo(this.server, {
            cors: {
                origin: "*",
                methods: ["GET", "POST"]
            }
        });
        
        this.port = process.env.PORT || 3000;
        this.projectRoot = process.cwd();
        
        // 初始化核心组件
        this.agentManager = new AgentManager();
        this.projectAnalyzer = new ProjectAnalyzer();
        this.llmService = new LLMService();
        this.codeGenerator = new CodeGenerator();
        this.taskScheduler = new TaskScheduler();
        this.logger = new Logger();
        
        this.setupMiddleware();
        this.setupRoutes();
        this.setupWebSocket();
    }

    setupMiddleware() {
        // 基础中间件
        this.app.use(cors());
        this.app.use(express.json({ limit: '10mb' }));
        this.app.use(express.urlencoded({ extended: true }));
        
        // 静态文件服务
        this.app.use('/ui', express.static(path.join(__dirname, '../ui')));
        
        // 请求日志
        this.app.use((req, res, next) => {
            this.logger.info(`${req.method} ${req.path}`, { 
                ip: req.ip, 
                userAgent: req.get('User-Agent') 
            });
            next();
        });
    }

    setupRoutes() {
        // 健康检查
        this.app.get('/health', (req, res) => {
            res.json({
                status: 'healthy',
                timestamp: new Date().toISOString(),
                version: '1.0.0',
                agents: this.agentManager.getAgentStatus()
            });
        });

        // 项目分析 API
        this.app.post('/api/analyze', async (req, res) => {
            try {
                const { projectPath, options = {} } = req.body;
                const analysis = await this.projectAnalyzer.analyzeProject(
                    projectPath || this.projectRoot, 
                    options
                );
                res.json({ success: true, data: analysis });
            } catch (error) {
                this.logger.error('项目分析失败', error);
                res.status(500).json({ 
                    success: false, 
                    error: error.message 
                });
            }
        });

        // Agent 状态 API
        this.app.get('/api/agents', (req, res) => {
            res.json({
                success: true,
                data: this.agentManager.getAllAgents()
            });
        });

        // 任务提交 API
        this.app.post('/api/tasks', async (req, res) => {
            try {
                const { agentType, action, input, options = {} } = req.body;
                const taskId = await this.taskScheduler.scheduleTask({
                    agentType,
                    action,
                    input,
                    options
                });
                res.json({ success: true, taskId });
            } catch (error) {
                this.logger.error('任务调度失败', error);
                res.status(500).json({ 
                    success: false, 
                    error: error.message 
                });
            }
        });

        // 任务状态查询 API
        this.app.get('/api/tasks/:taskId', (req, res) => {
            const { taskId } = req.params;
            const task = this.taskScheduler.getTask(taskId);
            if (task) {
                res.json({ success: true, data: task });
            } else {
                res.status(404).json({ 
                    success: false, 
                    error: '任务不存在' 
                });
            }
        });

        // 代码生成 API
        this.app.post('/api/generate', async (req, res) => {
            try {
                const { template, parameters, context = {} } = req.body;
                const result = await this.codeGenerator.generate(
                    template, 
                    parameters, 
                    context
                );
                res.json({ success: true, data: result });
            } catch (error) {
                this.logger.error('代码生成失败', error);
                res.status(500).json({ 
                    success: false, 
                    error: error.message 
                });
            }
        });

        // 获取项目配置
        this.app.get('/api/config', async (req, res) => {
            try {
                const configPath = path.join(this.projectRoot, '.all-agent/agents.json');
                const config = await fs.readFile(configPath, 'utf8');
                res.json({ success: true, data: JSON.parse(config) });
            } catch (error) {
                res.status(500).json({ 
                    success: false, 
                    error: '配置文件读取失败' 
                });
            }
        });

        // 404 处理
        this.app.use('*', (req, res) => {
            res.status(404).json({ 
                success: false, 
                error: '接口不存在' 
            });
        });
    }

    setupWebSocket() {
        this.io.on('connection', (socket) => {
            this.logger.info('客户端连接', { socketId: socket.id });

            // 加入房间
            socket.on('join', (data) => {
                const { room = 'default' } = data;
                socket.join(room);
                socket.emit('joined', { room, socketId: socket.id });
            });

            // 聊天消息处理
            socket.on('chat_message', async (data) => {
                try {
                    const { agentType, message, context = {} } = data;
                    
                    // 发送消息给对应的 Agent
                    const response = await this.agentManager.sendMessage(
                        agentType, 
                        message, 
                        context
                    );
                    
                    // 返回 Agent 响应
                    socket.emit('agent_response', {
                        agentType,
                        message: response,
                        timestamp: new Date().toISOString()
                    });
                    
                } catch (error) {
                    this.logger.error('聊天消息处理失败', error);
                    socket.emit('error', { 
                        message: '消息处理失败', 
                        error: error.message 
                    });
                }
            });

            // 任务状态订阅
            socket.on('subscribe_task', (data) => {
                const { taskId } = data;
                socket.join(`task_${taskId}`);
                
                // 发送当前任务状态
                const task = this.taskScheduler.getTask(taskId);
                if (task) {
                    socket.emit('task_update', task);
                }
            });

            // 项目分析请求
            socket.on('analyze_project', async (data) => {
                try {
                    const { projectPath, options = {} } = data;
                    
                    // 发送分析开始通知
                    socket.emit('analysis_started', { 
                        message: '开始分析项目...' 
                    });
                    
                    const analysis = await this.projectAnalyzer.analyzeProject(
                        projectPath || this.projectRoot, 
                        options,
                        (progress) => {
                            // 发送进度更新
                            socket.emit('analysis_progress', progress);
                        }
                    );
                    
                    // 发送分析结果
                    socket.emit('analysis_complete', analysis);
                    
                } catch (error) {
                    this.logger.error('项目分析失败', error);
                    socket.emit('analysis_error', { 
                        error: error.message 
                    });
                }
            });

            // 断开连接处理
            socket.on('disconnect', () => {
                this.logger.info('客户端断开连接', { socketId: socket.id });
            });
        });

        // 任务状态变更广播
        this.taskScheduler.on('task_update', (task) => {
            this.io.to(`task_${task.id}`).emit('task_update', task);
        });

        // Agent 状态变更广播
        this.agentManager.on('agent_status_change', (agentStatus) => {
            this.io.emit('agent_status_update', agentStatus);
        });
    }

    async start() {
        try {
            // 初始化组件
            await this.agentManager.initialize();
            await this.llmService.initialize();
            
            // 启动服务器
            this.server.listen(this.port, () => {
                this.logger.info(`All-Agent 服务器启动成功`, {
                    port: this.port,
                    env: process.env.NODE_ENV || 'development'
                });
                
                console.log(`
🤖 All-Agent Server Started!
📡 HTTP Server: http://localhost:${this.port}
🔌 WebSocket: ws://localhost:${this.port}
🎨 UI Panel: http://localhost:${this.port}/ui/chat_panel.html
📊 Agent Trace: http://localhost:${this.port}/ui/agent_trace.html
🗺️ Structure View: http://localhost:${this.port}/ui/structure_view.html
                `);
            });
            
        } catch (error) {
            this.logger.error('服务器启动失败', error);
            process.exit(1);
        }
    }

    async stop() {
        this.logger.info('正在关闭服务器...');
        
        // 关闭 WebSocket 连接
        this.io.close();
        
        // 关闭 HTTP 服务器
        this.server.close();
        
        // 清理资源
        await this.agentManager.cleanup();
        await this.taskScheduler.cleanup();
        
        this.logger.info('服务器已关闭');
    }
}

// 启动服务器
if (require.main === module) {
    const server = new AllAgentServer();
    
    // 优雅关闭
    process.on('SIGINT', async () => {
        console.log('\n收到 SIGINT 信号，正在关闭服务器...');
        await server.stop();
        process.exit(0);
    });
    
    process.on('SIGTERM', async () => {
        console.log('\n收到 SIGTERM 信号，正在关闭服务器...');
        await server.stop();
        process.exit(0);
    });
    
    server.start().catch(console.error);
}

module.exports = AllAgentServer;
