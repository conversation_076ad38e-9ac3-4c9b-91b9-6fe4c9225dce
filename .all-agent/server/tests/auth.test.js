const TestSetup = require('./setup');
const Database = require('../database/Database');
const AuthService = require('../auth/AuthService');

/**
 * 认证服务测试套件
 */
class AuthTests {
    constructor() {
        this.setup = new TestSetup();
        this.database = null;
        this.authService = null;
    }

    async beforeEach() {
        await this.setup.setupTestEnvironment();
        this.database = new Database({
            dbPath: require('path').join(__dirname, '../data/test.db')
        });
        await this.database.initialize();
        this.authService = new AuthService(this.database);
    }

    async afterEach() {
        if (this.database) {
            await this.database.close();
        }
        await this.setup.cleanupTestEnvironment();
    }

    /**
     * 用户注册测试
     */
    async testUserRegistration() {
        const userData = {
            username: 'testuser',
            email: '<EMAIL>',
            password: 'Test123456',
            role: 'user'
        };

        const user = await this.authService.register(userData);

        this.setup.assert.truthy(user, '应该返回用户对象');
        this.setup.assert.equal(user.username, 'testuser', '用户名应该匹配');
        this.setup.assert.equal(user.email, '<EMAIL>', '邮箱应该匹配');
        this.setup.assert.equal(user.role, 'user', '角色应该匹配');
        this.setup.assert.falsy(user.password_hash, '不应该返回密码哈希');

        // 验证用户已存储在数据库中
        const dbUser = await this.database.get(
            'SELECT * FROM users WHERE email = ?',
            ['<EMAIL>']
        );

        this.setup.assert.truthy(dbUser, '用户应该存储在数据库中');
        this.setup.assert.truthy(dbUser.password_hash, '密码应该已加密存储');
    }

    /**
     * 重复注册测试
     */
    async testDuplicateRegistration() {
        const userData = {
            username: 'testuser',
            email: '<EMAIL>',
            password: 'Test123456'
        };

        // 第一次注册
        await this.authService.register(userData);

        // 第二次注册应该失败
        await this.setup.assert.throws(
            () => this.authService.register(userData),
            '重复注册应该抛出错误'
        );

        // 测试重复用户名
        await this.setup.assert.throws(
            () => this.authService.register({
                username: 'testuser',
                email: '<EMAIL>',
                password: 'Test123456'
            }),
            '重复用户名应该抛出错误'
        );

        // 测试重复邮箱
        await this.setup.assert.throws(
            () => this.authService.register({
                username: 'differentuser',
                email: '<EMAIL>',
                password: 'Test123456'
            }),
            '重复邮箱应该抛出错误'
        );
    }

    /**
     * 输入验证测试
     */
    async testInputValidation() {
        // 测试无效用户名
        await this.setup.assert.throws(
            () => this.authService.register({
                username: 'ab', // 太短
                email: '<EMAIL>',
                password: 'Test123456'
            }),
            '用户名太短应该抛出错误'
        );

        // 测试无效邮箱
        await this.setup.assert.throws(
            () => this.authService.register({
                username: 'testuser',
                email: 'invalid-email', // 无效格式
                password: 'Test123456'
            }),
            '无效邮箱应该抛出错误'
        );

        // 测试弱密码
        await this.setup.assert.throws(
            () => this.authService.register({
                username: 'testuser',
                email: '<EMAIL>',
                password: '123' // 太短且简单
            }),
            '弱密码应该抛出错误'
        );
    }

    /**
     * 用户登录测试
     */
    async testUserLogin() {
        // 先注册用户
        await this.authService.register({
            username: 'testuser',
            email: '<EMAIL>',
            password: 'Test123456'
        });

        // 使用邮箱登录
        const loginResult = await this.authService.login({
            username: '<EMAIL>',
            password: 'Test123456'
        });

        this.setup.assert.truthy(loginResult, '应该返回登录结果');
        this.setup.assert.truthy(loginResult.token, '应该返回JWT令牌');
        this.setup.assert.truthy(loginResult.user, '应该返回用户信息');
        this.setup.assert.equal(loginResult.user.email, '<EMAIL>', '用户邮箱应该匹配');

        // 使用用户名登录
        const loginResult2 = await this.authService.login({
            username: 'testuser',
            password: 'Test123456'
        });

        this.setup.assert.truthy(loginResult2.token, '使用用户名也应该能登录');
    }

    /**
     * 登录失败测试
     */
    async testLoginFailure() {
        // 先注册用户
        await this.authService.register({
            username: 'testuser',
            email: '<EMAIL>',
            password: 'Test123456'
        });

        // 错误密码
        await this.setup.assert.throws(
            () => this.authService.login({
                username: '<EMAIL>',
                password: 'WrongPassword'
            }),
            '错误密码应该登录失败'
        );

        // 不存在的用户
        await this.setup.assert.throws(
            () => this.authService.login({
                username: '<EMAIL>',
                password: 'Test123456'
            }),
            '不存在的用户应该登录失败'
        );
    }

    /**
     * JWT令牌验证测试
     */
    async testTokenVerification() {
        // 注册并登录用户
        await this.authService.register({
            username: 'testuser',
            email: '<EMAIL>',
            password: 'Test123456'
        });

        const loginResult = await this.authService.login({
            username: '<EMAIL>',
            password: 'Test123456'
        });

        // 验证有效令牌
        const user = await this.authService.verifyToken(loginResult.token);
        this.setup.assert.truthy(user, '应该返回用户信息');
        this.setup.assert.equal(user.email, '<EMAIL>', '用户邮箱应该匹配');

        // 验证无效令牌
        await this.setup.assert.throws(
            () => this.authService.verifyToken('invalid-token'),
            '无效令牌应该抛出错误'
        );
    }

    /**
     * 令牌刷新测试
     */
    async testTokenRefresh() {
        // 注册并登录用户
        await this.authService.register({
            username: 'testuser',
            email: '<EMAIL>',
            password: 'Test123456'
        });

        const loginResult = await this.authService.login({
            username: '<EMAIL>',
            password: 'Test123456'
        });

        // 刷新令牌
        const refreshResult = await this.authService.refreshToken(loginResult.token);
        
        this.setup.assert.truthy(refreshResult, '应该返回刷新结果');
        this.setup.assert.truthy(refreshResult.token, '应该返回新令牌');
        this.setup.assert.truthy(refreshResult.user, '应该返回用户信息');
        
        // 新令牌应该不同于原令牌
        this.setup.assert.truthy(
            refreshResult.token !== loginResult.token,
            '新令牌应该不同于原令牌'
        );

        // 新令牌应该有效
        const user = await this.authService.verifyToken(refreshResult.token);
        this.setup.assert.equal(user.email, '<EMAIL>', '新令牌应该有效');
    }

    /**
     * 用户登出测试
     */
    async testUserLogout() {
        // 注册并登录用户
        await this.authService.register({
            username: 'testuser',
            email: '<EMAIL>',
            password: 'Test123456'
        });

        const loginResult = await this.authService.login({
            username: '<EMAIL>',
            password: 'Test123456'
        });

        // 登出
        const logoutResult = await this.authService.logout(loginResult.token);
        this.setup.assert.truthy(logoutResult.success, '登出应该成功');

        // 登出后令牌应该无效
        await this.setup.assert.throws(
            () => this.authService.verifyToken(loginResult.token),
            '登出后令牌应该无效'
        );
    }

    /**
     * 密码修改测试
     */
    async testPasswordChange() {
        // 注册用户
        const user = await this.authService.register({
            username: 'testuser',
            email: '<EMAIL>',
            password: 'OldPassword123'
        });

        // 修改密码
        await this.authService.changePassword(
            user.id,
            'OldPassword123',
            'NewPassword456'
        );

        // 使用新密码登录
        const loginResult = await this.authService.login({
            username: '<EMAIL>',
            password: 'NewPassword456'
        });

        this.setup.assert.truthy(loginResult.token, '使用新密码应该能登录');

        // 使用旧密码登录应该失败
        await this.setup.assert.throws(
            () => this.authService.login({
                username: '<EMAIL>',
                password: 'OldPassword123'
            }),
            '使用旧密码应该登录失败'
        );
    }

    /**
     * 密码修改失败测试
     */
    async testPasswordChangeFailure() {
        // 注册用户
        const user = await this.authService.register({
            username: 'testuser',
            email: '<EMAIL>',
            password: 'OldPassword123'
        });

        // 使用错误的旧密码
        await this.setup.assert.throws(
            () => this.authService.changePassword(
                user.id,
                'WrongOldPassword',
                'NewPassword456'
            ),
            '错误的旧密码应该修改失败'
        );

        // 使用无效的新密码
        await this.setup.assert.throws(
            () => this.authService.changePassword(
                user.id,
                'OldPassword123',
                '123' // 太弱
            ),
            '无效的新密码应该修改失败'
        );
    }

    /**
     * 用户信息更新测试
     */
    async testUserUpdate() {
        // 注册用户
        const user = await this.authService.register({
            username: 'testuser',
            email: '<EMAIL>',
            password: 'Test123456'
        });

        // 更新用户信息
        const updatedUser = await this.authService.updateUser(user.id, {
            username: 'updateduser',
            avatar_url: 'https://example.com/avatar.jpg'
        });

        this.setup.assert.equal(updatedUser.username, 'updateduser', '用户名应该已更新');
        this.setup.assert.equal(updatedUser.avatar_url, 'https://example.com/avatar.jpg', '头像URL应该已更新');
        this.setup.assert.equal(updatedUser.email, '<EMAIL>', '邮箱应该保持不变');
    }

    /**
     * 会话清理测试
     */
    async testSessionCleanup() {
        // 注册并登录用户
        await this.authService.register({
            username: 'testuser',
            email: '<EMAIL>',
            password: 'Test123456'
        });

        const loginResult = await this.authService.login({
            username: '<EMAIL>',
            password: 'Test123456'
        });

        // 手动插入过期会话
        await this.database.run(
            'INSERT INTO user_sessions (user_id, session_token, expires_at) VALUES (?, ?, ?)',
            [loginResult.user.id, 'expired-token', '2020-01-01 00:00:00']
        );

        // 清理过期会话
        const cleanedCount = await this.authService.cleanupExpiredSessions();
        
        this.setup.assert.truthy(cleanedCount >= 1, '应该清理至少一个过期会话');

        // 验证过期会话已清理
        const expiredSession = await this.database.get(
            'SELECT * FROM user_sessions WHERE session_token = ?',
            ['expired-token']
        );

        this.setup.assert.falsy(expiredSession, '过期会话应该已清理');
    }

    /**
     * 运行所有认证测试
     */
    async runAllTests() {
        const tests = {
            '用户注册': async () => {
                await this.beforeEach();
                await this.testUserRegistration();
                await this.afterEach();
            },
            '重复注册': async () => {
                await this.beforeEach();
                await this.testDuplicateRegistration();
                await this.afterEach();
            },
            '输入验证': async () => {
                await this.beforeEach();
                await this.testInputValidation();
                await this.afterEach();
            },
            '用户登录': async () => {
                await this.beforeEach();
                await this.testUserLogin();
                await this.afterEach();
            },
            '登录失败': async () => {
                await this.beforeEach();
                await this.testLoginFailure();
                await this.afterEach();
            },
            'JWT令牌验证': async () => {
                await this.beforeEach();
                await this.testTokenVerification();
                await this.afterEach();
            },
            '令牌刷新': async () => {
                await this.beforeEach();
                await this.testTokenRefresh();
                await this.afterEach();
            },
            '用户登出': async () => {
                await this.beforeEach();
                await this.testUserLogout();
                await this.afterEach();
            },
            '密码修改': async () => {
                await this.beforeEach();
                await this.testPasswordChange();
                await this.afterEach();
            },
            '密码修改失败': async () => {
                await this.beforeEach();
                await this.testPasswordChangeFailure();
                await this.afterEach();
            },
            '用户信息更新': async () => {
                await this.beforeEach();
                await this.testUserUpdate();
                await this.afterEach();
            },
            '会话清理': async () => {
                await this.beforeEach();
                await this.testSessionCleanup();
                await this.afterEach();
            }
        };

        return await this.setup.runTestSuite('认证服务测试', tests);
    }
}

// 如果直接运行此文件，执行测试
if (require.main === module) {
    const tests = new AuthTests();
    tests.runAllTests().then(results => {
        process.exit(results.failed > 0 ? 1 : 0);
    }).catch(error => {
        console.error('测试运行失败:', error);
        process.exit(1);
    });
}

module.exports = AuthTests;
