/**
 * 安全日志记录器
 * 记录安全相关事件和告警
 */

const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

class SecureLogger {
    constructor(options = {}) {
        this.logDir = options.logDir || './logs';
        this.maxFileSize = options.maxFileSize || 100 * 1024 * 1024; // 100MB
        this.maxFiles = options.maxFiles || 10;
        this.encryptLogs = options.encryptLogs || false;
        this.encryptionKey = options.encryptionKey || process.env.ENCRYPTION_KEY;
        
        this.ensureLogDir();
    }

    ensureLogDir() {
        if (!fs.existsSync(this.logDir)) {
            fs.mkdirSync(this.logDir, { recursive: true });
        }
    }

    log(level, message, metadata = {}) {
        const logEntry = {
            timestamp: new Date().toISOString(),
            level: level.toUpperCase(),
            message,
            metadata,
            pid: process.pid,
            hostname: require('os').hostname()
        };

        const logLine = JSON.stringify(logEntry) + '\n';
        const fileName = `security-${new Date().toISOString().split('T')[0]}.log`;
        const filePath = path.join(this.logDir, fileName);

        if (this.encryptLogs && this.encryptionKey) {
            this.writeEncrypted(filePath, logLine);
        } else {
            fs.appendFileSync(filePath, logLine);
        }

        // 控制台输出
        console.log(`[${logEntry.level}] ${logEntry.message}`);
    }

    writeEncrypted(filePath, data) {
        const cipher = crypto.createCipher('aes-256-cbc', this.encryptionKey);
        let encrypted = cipher.update(data, 'utf8', 'hex');
        encrypted += cipher.final('hex');
        fs.appendFileSync(filePath, encrypted + '\n');
    }

    // 安全事件记录方法
    logSecurityEvent(event, details = {}) {
        this.log('security', `Security event: ${event}`, details);
    }

    logAuthFailure(username, ip, reason) {
        this.logSecurityEvent('AUTH_FAILURE', {
            username,
            ip,
            reason,
            timestamp: Date.now()
        });
    }

    logSuspiciousActivity(activity, details) {
        this.logSecurityEvent('SUSPICIOUS_ACTIVITY', {
            activity,
            details,
            timestamp: Date.now()
        });
    }

    logDataAccess(user, resource, action) {
        this.logSecurityEvent('DATA_ACCESS', {
            user,
            resource,
            action,
            timestamp: Date.now()
        });
    }

    logConfigChange(user, setting, oldValue, newValue) {
        this.logSecurityEvent('CONFIG_CHANGE', {
            user,
            setting,
            oldValue: oldValue ? '[REDACTED]' : null,
            newValue: newValue ? '[REDACTED]' : null,
            timestamp: Date.now()
        });
    }
}

module.exports = SecureLogger;
