const path = require('path');
const fs = require('fs').promises;

/**
 * 测试环境设置
 */
class TestSetup {
  constructor() {
    this.testDbPath = path.join(__dirname, '../data/test.db');
    this.originalEnv = { ...process.env };
  }

  /**
   * 设置测试环境
   */
  async setupTestEnvironment() {
    // 设置测试环境变量
    process.env.NODE_ENV = 'test';
    process.env.DATABASE_URL = `sqlite:${this.testDbPath}`;
    process.env.JWT_SECRET = process.env.JWT_SECRET || require('crypto').randomBytes(32).toString('hex');
    process.env.LOG_LEVEL = 'error'; // 减少测试时的日志输出

    // 清理测试数据库
    await this.cleanupTestDatabase();

    console.log('✅ 测试环境设置完成');
  }

  /**
   * 清理测试环境
   */
  async cleanupTestEnvironment() {
    // 恢复原始环境变量
    process.env = { ...this.originalEnv };

    // 删除测试数据库
    await this.cleanupTestDatabase();

    console.log('✅ 测试环境清理完成');
  }

  /**
   * 清理测试数据库
   */
  async cleanupTestDatabase() {
    try {
      await fs.unlink(this.testDbPath);
    } catch (error) {
      // 文件不存在，忽略错误
    }
  }

  /**
   * 创建测试用户
   */
  async createTestUser(authService, userData = {}) {
    const defaultUser = {
      username: 'testuser',
      email: '<EMAIL>',
      password: 'test123456',
      role: 'user'
    };

    const user = { ...defaultUser, ...userData };
    return await authService.register(user);
  }

  /**
   * 创建测试管理员
   */
  async createTestAdmin(authService) {
    return await this.createTestUser(authService, {
      username: 'testadmin',
      email: '<EMAIL>',
      role: 'admin'
    });
  }

  /**
   * 登录测试用户
   */
  async loginTestUser(authService, credentials = {}) {
    const defaultCredentials = {
      username: '<EMAIL>',
      password: 'test123456'
    };

    const loginData = { ...defaultCredentials, ...credentials };
    return await authService.login(loginData);
  }

  /**
   * 创建测试项目数据
   */
  createTestProjectData() {
    return {
      projectPath: path.join(__dirname, '../test-project'),
      options: {
        maxDepth: 3,
        includeHidden: false
      }
    };
  }

  /**
   * 创建测试项目目录结构
   */
  async createTestProject() {
    const projectPath = path.join(__dirname, '../test-project');

    // 创建目录结构
    await fs.mkdir(projectPath, { recursive: true });
    await fs.mkdir(path.join(projectPath, 'src'), { recursive: true });
    await fs.mkdir(path.join(projectPath, 'tests'), { recursive: true });

    // 创建测试文件
    await fs.writeFile(
      path.join(projectPath, 'package.json'),
      JSON.stringify(
        {
          name: 'test-project',
          version: '1.0.0',
          dependencies: {
            express: '^4.18.0',
            react: '^18.0.0'
          }
        },
        null,
        2
      )
    );

    await fs.writeFile(
      path.join(projectPath, 'src/index.js'),
      `const express = require('express');
const app = express();

app.get('/', (req, res) => {
    res.json({ message: 'Hello World' });
});

module.exports = app;`
    );

    await fs.writeFile(
      path.join(projectPath, 'src/utils.js'),
      `function add(a, b) {
    return a + b;
}

function multiply(a, b) {
    return a * b;
}

module.exports = { add, multiply };`
    );

    return projectPath;
  }

  /**
   * 清理测试项目
   */
  async cleanupTestProject() {
    const projectPath = path.join(__dirname, '../test-project');
    try {
      await fs.rm(projectPath, { recursive: true, force: true });
    } catch (error) {
      // 忽略清理错误
    }
  }

  /**
   * 等待异步操作
   */
  async wait(ms = 100) {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  /**
   * 模拟 HTTP 请求
   */
  createMockRequest(data = {}) {
    return {
      body: data.body || {},
      params: data.params || {},
      query: data.query || {},
      headers: data.headers || {},
      user: data.user || null,
      ip: data.ip || '127.0.0.1',
      method: data.method || 'GET',
      url: data.url || '/',
      ...data
    };
  }

  /**
   * 模拟 HTTP 响应
   */
  createMockResponse() {
    const res = {
      statusCode: 200,
      headers: {},
      data: null,
      status: function (code) {
        this.statusCode = code;
        return this;
      },
      json: function (data) {
        this.data = data;
        return this;
      },
      send: function (data) {
        this.data = data;
        return this;
      },
      header: function (name, value) {
        this.headers[name] = value;
        return this;
      },
      end: function () {
        return this;
      }
    };
    return res;
  }

  /**
   * 断言助手
   */
  assert = {
    equal: (actual, expected, message) => {
      if (actual !== expected) {
        throw new Error(message || `Expected ${expected}, got ${actual}`);
      }
    },

    deepEqual: (actual, expected, message) => {
      if (JSON.stringify(actual) !== JSON.stringify(expected)) {
        throw new Error(message || `Objects are not equal`);
      }
    },

    truthy: (value, message) => {
      if (!value) {
        throw new Error(message || `Expected truthy value, got ${value}`);
      }
    },

    falsy: (value, message) => {
      if (value) {
        throw new Error(message || `Expected falsy value, got ${value}`);
      }
    },

    throws: async (fn, message) => {
      try {
        await fn();
        throw new Error(message || 'Expected function to throw');
      } catch (error) {
        if (error.message === (message || 'Expected function to throw')) {
          throw error;
        }
        // 函数确实抛出了错误，测试通过
      }
    },

    contains: (array, item, message) => {
      if (!array.includes(item)) {
        throw new Error(message || `Array does not contain ${item}`);
      }
    },

    hasProperty: (obj, prop, message) => {
      if (!(prop in obj)) {
        throw new Error(message || `Object does not have property ${prop}`);
      }
    }
  };

  /**
   * 测试运行器
   */
  async runTest(testName, testFn) {
    try {
      console.log(`🧪 运行测试: ${testName}`);
      await testFn();
      console.log(`✅ 测试通过: ${testName}`);
      return { success: true, name: testName };
    } catch (error) {
      console.error(`❌ 测试失败: ${testName}`);
      console.error(`   错误: ${error.message}`);
      return { success: false, name: testName, error: error.message };
    }
  }

  /**
   * 测试套件运行器
   */
  async runTestSuite(suiteName, tests) {
    console.log(`\n🧪 开始测试套件: ${suiteName}`);
    console.log('='.repeat(50));

    const results = [];
    let passed = 0;
    let failed = 0;

    for (const [testName, testFn] of Object.entries(tests)) {
      const result = await this.runTest(testName, testFn);
      results.push(result);

      if (result.success) {
        passed++;
      } else {
        failed++;
      }
    }

    console.log('\n📊 测试结果:');
    console.log(`   通过: ${passed}`);
    console.log(`   失败: ${failed}`);
    console.log(`   总计: ${passed + failed}`);

    if (failed > 0) {
      console.log('\n❌ 失败的测试:');
      results
        .filter((r) => !r.success)
        .forEach((r) => {
          console.log(`   - ${r.name}: ${r.error}`);
        });
    }

    return {
      suiteName,
      total: passed + failed,
      passed,
      failed,
      results
    };
  }
}

module.exports = TestSetup;
