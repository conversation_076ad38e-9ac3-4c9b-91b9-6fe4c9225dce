/**
 * HTTPS 服务器配置
 * 用于生产环境的安全连接
 */

const https = require('https');
const fs = require('fs');
const path = require('path');

class HTTPSServer {
    constructor(app, options = {}) {
        this.app = app;
        this.options = {
            port: options.port || 443,
            certPath: options.certPath || './ssl/cert.pem',
            keyPath: options.keyPath || './ssl/key.pem',
            ...options
        };
    }

    start() {
        try {
            const httpsOptions = {
                cert: fs.readFileSync(this.options.certPath),
                key: fs.readFileSync(this.options.keyPath)
            };

            const server = https.createServer(httpsOptions, this.app);
            
            server.listen(this.options.port, () => {
                console.log(`🔒 HTTPS Server running on port ${this.options.port}`);
            });

            return server;
        } catch (error) {
            console.error('HTTPS 服务器启动失败:', error);
            throw error;
        }
    }
}

module.exports = HTTPSServer;
