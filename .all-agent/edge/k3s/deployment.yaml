apiVersion: v1
kind: Namespace
metadata:
  name: all-agent-edge
  labels:
    name: all-agent-edge
    edge.all-agent.com/zone: "edge"
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: all-agent-edge-config
  namespace: all-agent-edge
  labels:
    app.kubernetes.io/name: all-agent
    app.kubernetes.io/component: edge-config
data:
  NODE_ENV: "production"
  PORT: "3000"
  EDGE_MODE: "true"
  EDGE_ZONE: "default"
  CENTRAL_HUB_URL: "https://central.all-agent.com"
  SYNC_INTERVAL: "30000"
  CACHE_TTL: "300"
  MAX_MEMORY_USAGE: "512"
  MAX_CPU_USAGE: "80"
  OFFLINE_MODE: "true"
  DATA_RETENTION_DAYS: "7"
  LOG_LEVEL: "info"
  METRICS_ENABLED: "true"
  HEALTH_CHECK_INTERVAL: "10000"
---
apiVersion: v1
kind: Secret
metadata:
  name: all-agent-edge-secrets
  namespace: all-agent-edge
type: Opaque
data:
  jwt-secret: YWxsLWFnZW50LWVkZ2Utand0LXNlY3JldA== # base64: all-agent-edge-jwt-secret
  sync-token: ZWRnZS1zeW5jLXRva2VuLTEyMzQ1Ng== # base64: edge-sync-token-123456
  encryption-key: ZWRnZS1lbmNyeXB0aW9uLWtleS1hYmNkZWY= # base64: edge-encryption-key-abcdef
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: all-agent-edge
  namespace: all-agent-edge
  labels:
    app.kubernetes.io/name: all-agent
    app.kubernetes.io/component: edge
    app.kubernetes.io/version: "1.0.0"
spec:
  replicas: 1
  strategy:
    type: Recreate
  selector:
    matchLabels:
      app.kubernetes.io/name: all-agent
      app.kubernetes.io/component: edge
  template:
    metadata:
      labels:
        app.kubernetes.io/name: all-agent
        app.kubernetes.io/component: edge
        app.kubernetes.io/version: "1.0.0"
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "3000"
        prometheus.io/path: "/metrics"
    spec:
      serviceAccountName: all-agent-edge-sa
      securityContext:
        runAsNonRoot: true
        runAsUser: 1001
        runAsGroup: 1001
        fsGroup: 1001
      containers:
      - name: all-agent-edge
        image: all-agent:edge-latest
        imagePullPolicy: IfNotPresent
        ports:
        - name: http
          containerPort: 3000
          protocol: TCP
        env:
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: all-agent-edge-secrets
              key: jwt-secret
        - name: SYNC_TOKEN
          valueFrom:
            secretKeyRef:
              name: all-agent-edge-secrets
              key: sync-token
        - name: ENCRYPTION_KEY
          valueFrom:
            secretKeyRef:
              name: all-agent-edge-secrets
              key: encryption-key
        - name: NODE_NAME
          valueFrom:
            fieldRef:
              fieldPath: spec.nodeName
        - name: POD_NAME
          valueFrom:
            fieldRef:
              fieldPath: metadata.name
        - name: POD_NAMESPACE
          valueFrom:
            fieldRef:
              fieldPath: metadata.namespace
        - name: POD_IP
          valueFrom:
            fieldRef:
              fieldPath: status.podIP
        envFrom:
        - configMapRef:
            name: all-agent-edge-config
        volumeMounts:
        - name: data
          mountPath: /app/data
        - name: cache
          mountPath: /app/cache
        - name: logs
          mountPath: /app/logs
        - name: tmp
          mountPath: /tmp
        resources:
          requests:
            cpu: 100m
            memory: 256Mi
          limits:
            cpu: 500m
            memory: 512Mi
        livenessProbe:
          httpGet:
            path: /health
            port: http
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /ready
            port: http
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          capabilities:
            drop:
            - ALL
      volumes:
      - name: data
        persistentVolumeClaim:
          claimName: all-agent-edge-data-pvc
      - name: cache
        emptyDir:
          sizeLimit: 1Gi
      - name: logs
        emptyDir:
          sizeLimit: 500Mi
      - name: tmp
        emptyDir:
          sizeLimit: 100Mi
      restartPolicy: Always
      terminationGracePeriodSeconds: 30
      nodeSelector:
        edge.all-agent.com/zone: "edge"
      tolerations:
      - key: "edge.all-agent.com/node"
        operator: "Equal"
        value: "true"
        effect: "NoSchedule"
---
apiVersion: v1
kind: Service
metadata:
  name: all-agent-edge
  namespace: all-agent-edge
  labels:
    app.kubernetes.io/name: all-agent
    app.kubernetes.io/component: edge
spec:
  type: NodePort
  ports:
  - port: 3000
    targetPort: http
    protocol: TCP
    name: http
    nodePort: 30000
  selector:
    app.kubernetes.io/name: all-agent
    app.kubernetes.io/component: edge
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: all-agent-edge-data-pvc
  namespace: all-agent-edge
  labels:
    app.kubernetes.io/name: all-agent
    app.kubernetes.io/component: edge-storage
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 10Gi
  storageClassName: local-path
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: all-agent-edge-sa
  namespace: all-agent-edge
  labels:
    app.kubernetes.io/name: all-agent
    app.kubernetes.io/component: edge-serviceaccount
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: all-agent-edge-role
  labels:
    app.kubernetes.io/name: all-agent
    app.kubernetes.io/component: edge-role
rules:
- apiGroups: [""]
  resources: ["nodes", "pods", "services", "endpoints"]
  verbs: ["get", "list", "watch"]
- apiGroups: ["apps"]
  resources: ["deployments", "replicasets"]
  verbs: ["get", "list", "watch"]
- apiGroups: ["metrics.k8s.io"]
  resources: ["nodes", "pods"]
  verbs: ["get", "list"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: all-agent-edge-binding
  labels:
    app.kubernetes.io/name: all-agent
    app.kubernetes.io/component: edge-binding
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: all-agent-edge-role
subjects:
- kind: ServiceAccount
  name: all-agent-edge-sa
  namespace: all-agent-edge
---
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: all-agent-edge-netpol
  namespace: all-agent-edge
  labels:
    app.kubernetes.io/name: all-agent
    app.kubernetes.io/component: edge-network-policy
spec:
  podSelector:
    matchLabels:
      app.kubernetes.io/name: all-agent
      app.kubernetes.io/component: edge
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          name: monitoring
    - podSelector: {}
    ports:
    - protocol: TCP
      port: 3000
  egress:
  - to: []
    ports:
    - protocol: TCP
      port: 53
    - protocol: UDP
      port: 53
  - to: []
    ports:
    - protocol: TCP
      port: 443
  - to: []
    ports:
    - protocol: TCP
      port: 80
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: edge-sync-script
  namespace: all-agent-edge
  labels:
    app.kubernetes.io/name: all-agent
    app.kubernetes.io/component: edge-sync
data:
  sync.sh: |
    #!/bin/bash
    set -e
    
    # 边缘节点同步脚本
    CENTRAL_HUB_URL=${CENTRAL_HUB_URL:-"https://central.all-agent.com"}
    SYNC_TOKEN=${SYNC_TOKEN}
    EDGE_ZONE=${EDGE_ZONE:-"default"}
    
    echo "开始同步边缘节点数据..."
    echo "中央节点: $CENTRAL_HUB_URL"
    echo "边缘区域: $EDGE_ZONE"
    
    # 上传本地数据到中央节点
    upload_data() {
        echo "上传本地数据..."
        curl -X POST "$CENTRAL_HUB_URL/api/edge/sync/upload" \
            -H "Authorization: Bearer $SYNC_TOKEN" \
            -H "Content-Type: application/json" \
            -H "X-Edge-Zone: $EDGE_ZONE" \
            -d @/app/data/sync_data.json
    }
    
    # 从中央节点下载数据
    download_data() {
        echo "下载中央数据..."
        curl -X GET "$CENTRAL_HUB_URL/api/edge/sync/download" \
            -H "Authorization: Bearer $SYNC_TOKEN" \
            -H "X-Edge-Zone: $EDGE_ZONE" \
            -o /app/data/central_data.json
    }
    
    # 健康检查
    health_check() {
        echo "执行健康检查..."
        curl -f "$CENTRAL_HUB_URL/health" || {
            echo "中央节点不可达，启用离线模式"
            export OFFLINE_MODE=true
        }
    }
    
    # 主同步逻辑
    main() {
        health_check
        
        if [ "$OFFLINE_MODE" != "true" ]; then
            upload_data
            download_data
            echo "同步完成"
        else
            echo "离线模式，跳过同步"
        fi
    }
    
    main "$@"
---
apiVersion: batch/v1
kind: CronJob
metadata:
  name: edge-sync-job
  namespace: all-agent-edge
  labels:
    app.kubernetes.io/name: all-agent
    app.kubernetes.io/component: edge-sync-job
spec:
  schedule: "*/5 * * * *"  # 每5分钟同步一次
  jobTemplate:
    spec:
      template:
        spec:
          serviceAccountName: all-agent-edge-sa
          containers:
          - name: sync
            image: curlimages/curl:latest
            command:
            - /bin/sh
            - /scripts/sync.sh
            env:
            - name: CENTRAL_HUB_URL
              valueFrom:
                configMapKeyRef:
                  name: all-agent-edge-config
                  key: CENTRAL_HUB_URL
            - name: SYNC_TOKEN
              valueFrom:
                secretKeyRef:
                  name: all-agent-edge-secrets
                  key: sync-token
            - name: EDGE_ZONE
              valueFrom:
                configMapKeyRef:
                  name: all-agent-edge-config
                  key: EDGE_ZONE
            volumeMounts:
            - name: sync-script
              mountPath: /scripts
            - name: data
              mountPath: /app/data
          volumes:
          - name: sync-script
            configMap:
              name: edge-sync-script
              defaultMode: 0755
          - name: data
            persistentVolumeClaim:
              claimName: all-agent-edge-data-pvc
          restartPolicy: OnFailure
