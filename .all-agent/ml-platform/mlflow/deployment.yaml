apiVersion: v1
kind: Namespace
metadata:
  name: ml-platform
  labels:
    name: ml-platform
    istio-injection: enabled
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: mlflow-config
  namespace: ml-platform
  labels:
    app.kubernetes.io/name: mlflow
    app.kubernetes.io/component: config
data:
  MLFLOW_BACKEND_STORE_URI: "*******************************************/mlflow"
  MLFLOW_DEFAULT_ARTIFACT_ROOT: "s3://all-agent-mlflow-artifacts"
  MLFLOW_S3_ENDPOINT_URL: "http://minio:9000"
  MLFLOW_TRACKING_URI: "http://mlflow-server:5000"
  AWS_ACCESS_KEY_ID: "minioadmin"
  AWS_SECRET_ACCESS_KEY: "minioadmin"
  PYTHONPATH: "/opt/mlflow"
---
apiVersion: v1
kind: Secret
metadata:
  name: mlflow-secrets
  namespace: ml-platform
type: Opaque
stringData:
  postgres-password: "mlflow123"
  minio-access-key: "minioadmin"
  minio-secret-key: "minioadmin"
  mlflow-auth-token: "all-agent-mlflow-token-123456"
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: mlflow-server
  namespace: ml-platform
  labels:
    app.kubernetes.io/name: mlflow
    app.kubernetes.io/component: server
spec:
  replicas: 2
  selector:
    matchLabels:
      app.kubernetes.io/name: mlflow
      app.kubernetes.io/component: server
  template:
    metadata:
      labels:
        app.kubernetes.io/name: mlflow
        app.kubernetes.io/component: server
        version: v1
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "5000"
        prometheus.io/path: "/metrics"
    spec:
      serviceAccountName: mlflow-sa
      containers:
      - name: mlflow
        image: python:3.9-slim
        ports:
        - name: http
          containerPort: 5000
          protocol: TCP
        env:
        - name: MLFLOW_BACKEND_STORE_URI
          valueFrom:
            configMapKeyRef:
              name: mlflow-config
              key: MLFLOW_BACKEND_STORE_URI
        - name: MLFLOW_DEFAULT_ARTIFACT_ROOT
          valueFrom:
            configMapKeyRef:
              name: mlflow-config
              key: MLFLOW_DEFAULT_ARTIFACT_ROOT
        - name: MLFLOW_S3_ENDPOINT_URL
          valueFrom:
            configMapKeyRef:
              name: mlflow-config
              key: MLFLOW_S3_ENDPOINT_URL
        - name: AWS_ACCESS_KEY_ID
          valueFrom:
            secretKeyRef:
              name: mlflow-secrets
              key: minio-access-key
        - name: AWS_SECRET_ACCESS_KEY
          valueFrom:
            secretKeyRef:
              name: mlflow-secrets
              key: minio-secret-key
        command:
        - /bin/bash
        - -c
        - |
          pip install mlflow[extras]==2.8.1 psycopg2-binary boto3 prometheus-client
          mlflow server \
            --backend-store-uri $MLFLOW_BACKEND_STORE_URI \
            --default-artifact-root $MLFLOW_DEFAULT_ARTIFACT_ROOT \
            --host 0.0.0.0 \
            --port 5000 \
            --serve-artifacts
        resources:
          requests:
            cpu: 200m
            memory: 512Mi
          limits:
            cpu: 1000m
            memory: 1Gi
        livenessProbe:
          httpGet:
            path: /health
            port: http
          initialDelaySeconds: 60
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /health
            port: http
          initialDelaySeconds: 30
          periodSeconds: 10
        volumeMounts:
        - name: mlflow-data
          mountPath: /opt/mlflow
      volumes:
      - name: mlflow-data
        persistentVolumeClaim:
          claimName: mlflow-data-pvc
---
apiVersion: v1
kind: Service
metadata:
  name: mlflow-server
  namespace: ml-platform
  labels:
    app.kubernetes.io/name: mlflow
    app.kubernetes.io/component: server
spec:
  type: ClusterIP
  ports:
  - port: 5000
    targetPort: http
    protocol: TCP
    name: http
  selector:
    app.kubernetes.io/name: mlflow
    app.kubernetes.io/component: server
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: postgres
  namespace: ml-platform
  labels:
    app.kubernetes.io/name: postgres
    app.kubernetes.io/component: database
spec:
  replicas: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: postgres
      app.kubernetes.io/component: database
  template:
    metadata:
      labels:
        app.kubernetes.io/name: postgres
        app.kubernetes.io/component: database
    spec:
      containers:
      - name: postgres
        image: postgres:15-alpine
        ports:
        - name: postgres
          containerPort: 5432
          protocol: TCP
        env:
        - name: POSTGRES_DB
          value: "mlflow"
        - name: POSTGRES_USER
          value: "mlflow"
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: mlflow-secrets
              key: postgres-password
        - name: PGDATA
          value: "/var/lib/postgresql/data/pgdata"
        volumeMounts:
        - name: postgres-data
          mountPath: /var/lib/postgresql/data
        resources:
          requests:
            cpu: 100m
            memory: 256Mi
          limits:
            cpu: 500m
            memory: 512Mi
      volumes:
      - name: postgres-data
        persistentVolumeClaim:
          claimName: postgres-data-pvc
---
apiVersion: v1
kind: Service
metadata:
  name: postgres
  namespace: ml-platform
  labels:
    app.kubernetes.io/name: postgres
    app.kubernetes.io/component: database
spec:
  type: ClusterIP
  ports:
  - port: 5432
    targetPort: postgres
    protocol: TCP
    name: postgres
  selector:
    app.kubernetes.io/name: postgres
    app.kubernetes.io/component: database
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: minio
  namespace: ml-platform
  labels:
    app.kubernetes.io/name: minio
    app.kubernetes.io/component: storage
spec:
  replicas: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: minio
      app.kubernetes.io/component: storage
  template:
    metadata:
      labels:
        app.kubernetes.io/name: minio
        app.kubernetes.io/component: storage
    spec:
      containers:
      - name: minio
        image: minio/minio:latest
        ports:
        - name: api
          containerPort: 9000
          protocol: TCP
        - name: console
          containerPort: 9001
          protocol: TCP
        env:
        - name: MINIO_ACCESS_KEY
          valueFrom:
            secretKeyRef:
              name: mlflow-secrets
              key: minio-access-key
        - name: MINIO_SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: mlflow-secrets
              key: minio-secret-key
        command:
        - /bin/bash
        - -c
        - |
          mkdir -p /data/all-agent-mlflow-artifacts
          minio server /data --console-address ":9001"
        volumeMounts:
        - name: minio-data
          mountPath: /data
        resources:
          requests:
            cpu: 100m
            memory: 256Mi
          limits:
            cpu: 500m
            memory: 512Mi
      volumes:
      - name: minio-data
        persistentVolumeClaim:
          claimName: minio-data-pvc
---
apiVersion: v1
kind: Service
metadata:
  name: minio
  namespace: ml-platform
  labels:
    app.kubernetes.io/name: minio
    app.kubernetes.io/component: storage
spec:
  type: ClusterIP
  ports:
  - port: 9000
    targetPort: api
    protocol: TCP
    name: api
  - port: 9001
    targetPort: console
    protocol: TCP
    name: console
  selector:
    app.kubernetes.io/name: minio
    app.kubernetes.io/component: storage
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: mlflow-data-pvc
  namespace: ml-platform
  labels:
    app.kubernetes.io/name: mlflow
    app.kubernetes.io/component: storage
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 10Gi
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: postgres-data-pvc
  namespace: ml-platform
  labels:
    app.kubernetes.io/name: postgres
    app.kubernetes.io/component: storage
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 20Gi
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: minio-data-pvc
  namespace: ml-platform
  labels:
    app.kubernetes.io/name: minio
    app.kubernetes.io/component: storage
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 50Gi
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: mlflow-sa
  namespace: ml-platform
  labels:
    app.kubernetes.io/name: mlflow
    app.kubernetes.io/component: serviceaccount
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: mlflow-role
  labels:
    app.kubernetes.io/name: mlflow
    app.kubernetes.io/component: role
rules:
- apiGroups: [""]
  resources: ["pods", "services", "endpoints"]
  verbs: ["get", "list", "watch"]
- apiGroups: ["apps"]
  resources: ["deployments", "replicasets"]
  verbs: ["get", "list", "watch"]
- apiGroups: ["batch"]
  resources: ["jobs"]
  verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: mlflow-binding
  labels:
    app.kubernetes.io/name: mlflow
    app.kubernetes.io/component: binding
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: mlflow-role
subjects:
- kind: ServiceAccount
  name: mlflow-sa
  namespace: ml-platform
