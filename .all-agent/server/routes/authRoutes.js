const express = require('express');
const { body, validationResult } = require('express-validator');
const rateLimit = require('express-rate-limit');

/**
 * 认证路由模块
 */
class AuthRoutes {
  constructor(authService) {
    this.authService = authService;
    this.router = express.Router();
    this.setupRoutes();
  }

  setupRoutes() {
    // 注册限流
    const registerLimiter = rateLimit({
      windowMs: 15 * 60 * 1000, // 15分钟
      max: 5, // 限制每个IP 5次注册尝试
      message: {
        error: 'Too many registration attempts, please try again later.',
        retryAfter: '15 minutes'
      }
    });

    // 登录限流
    const loginLimiter = rateLimit({
      windowMs: 15 * 60 * 1000, // 15分钟
      max: 10, // 限制每个IP 10次登录尝试
      message: {
        error: 'Too many login attempts, please try again later.',
        retryAfter: '15 minutes'
      }
    });

    // 用户注册
    this.router.post('/register', 
      registerLimiter,
      [
        body('username').isLength({ min: 3, max: 50 }).trim().escape(),
        body('email').isEmail().normalizeEmail(),
        body('password').isLength({ min: 8 }).matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
      ],
      this.register.bind(this)
    );

    // 用户登录
    this.router.post('/login',
      loginLimiter,
      [
        body('username').trim().escape(),
        body('password').isLength({ min: 1 })
      ],
      this.login.bind(this)
    );

    // 刷新令牌
    this.router.post('/refresh',
      [body('token').isJWT()],
      this.refreshToken.bind(this)
    );

    // 用户登出
    this.router.post('/logout', this.logout.bind(this));

    // 修改密码
    this.router.post('/change-password',
      [
        body('oldPassword').isLength({ min: 1 }),
        body('newPassword').isLength({ min: 8 }).matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
      ],
      this.changePassword.bind(this)
    );

    // 获取用户信息
    this.router.get('/me', this.getCurrentUser.bind(this));

    // 密码重置请求
    this.router.post('/reset-password',
      [body('email').isEmail().normalizeEmail()],
      this.resetPassword.bind(this)
    );
  }

  /**
   * 用户注册
   */
  async register(req, res) {
    try {
      // 验证输入
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          error: 'Validation failed',
          details: errors.array()
        });
      }

      const user = await this.authService.register(req.body);
      res.status(201).json({
        success: true,
        data: user,
        message: 'User registered successfully'
      });
    } catch (error) {
      res.status(400).json({
        success: false,
        error: error.message
      });
    }
  }

  /**
   * 用户登录
   */
  async login(req, res) {
    try {
      // 验证输入
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          error: 'Validation failed',
          details: errors.array()
        });
      }

      const result = await this.authService.login(req.body);
      res.json({
        success: true,
        data: result,
        message: 'Login successful'
      });
    } catch (error) {
      res.status(401).json({
        success: false,
        error: error.message
      });
    }
  }

  /**
   * 刷新令牌
   */
  async refreshToken(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          error: 'Invalid token format'
        });
      }

      const { token } = req.body;
      const result = await this.authService.refreshToken(token);
      res.json({
        success: true,
        data: result,
        message: 'Token refreshed successfully'
      });
    } catch (error) {
      res.status(401).json({
        success: false,
        error: error.message
      });
    }
  }

  /**
   * 用户登出
   */
  async logout(req, res) {
    try {
      const token = this.extractToken(req);
      if (!token) {
        return res.status(400).json({
          success: false,
          error: 'No token provided'
        });
      }

      await this.authService.logout(token);
      res.json({
        success: true,
        message: 'Logout successful'
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        error: error.message
      });
    }
  }

  /**
   * 修改密码
   */
  async changePassword(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          error: 'Validation failed',
          details: errors.array()
        });
      }

      const token = this.extractToken(req);
      if (!token) {
        return res.status(401).json({
          success: false,
          error: 'Authentication required'
        });
      }

      const user = await this.authService.verifyToken(token);
      const { oldPassword, newPassword } = req.body;
      
      await this.authService.changePassword(user.id, oldPassword, newPassword);
      res.json({
        success: true,
        message: 'Password changed successfully'
      });
    } catch (error) {
      res.status(400).json({
        success: false,
        error: error.message
      });
    }
  }

  /**
   * 获取当前用户信息
   */
  async getCurrentUser(req, res) {
    try {
      const token = this.extractToken(req);
      if (!token) {
        return res.status(401).json({
          success: false,
          error: 'Authentication required'
        });
      }

      const user = await this.authService.verifyToken(token);
      res.json({
        success: true,
        data: user
      });
    } catch (error) {
      res.status(401).json({
        success: false,
        error: error.message
      });
    }
  }

  /**
   * 密码重置
   */
  async resetPassword(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          error: 'Invalid email format'
        });
      }

      const { email } = req.body;
      const result = await this.authService.resetPassword(email);
      res.json({
        success: true,
        data: result,
        message: 'Password reset email sent'
      });
    } catch (error) {
      res.status(400).json({
        success: false,
        error: error.message
      });
    }
  }

  /**
   * 从请求中提取 JWT 令牌
   */
  extractToken(req) {
    const authHeader = req.headers.authorization;
    if (authHeader && authHeader.startsWith('Bearer ')) {
      return authHeader.substring(7);
    }
    return null;
  }

  /**
   * 获取路由器实例
   */
  getRouter() {
    return this.router;
  }
}

module.exports = AuthRoutes;
