# 🤝 贡献指南

感谢您对 All-Agent 项目的关注！我们欢迎所有形式的贡献，包括但不限于：

- 🐛 Bug 报告
- 💡 功能建议
- 📝 文档改进
- 🔧 代码贡献
- 🧪 测试用例
- 🌐 翻译工作

## 📋 贡献流程

### 1. 准备工作

```bash
# Fork 项目到您的 GitHub 账户
# 克隆您的 Fork
git clone https://github.com/YOUR_USERNAME/all-agent.git
cd all-agent/.all-agent

# 添加上游仓库
git remote add upstream https://github.com/ORIGINAL_OWNER/all-agent.git

# 安装依赖
npm install
pip3 install -r requirements.txt
```

### 2. 创建分支

```bash
# 从 main 分支创建新分支
git checkout -b feature/your-feature-name

# 或者修复 bug
git checkout -b fix/bug-description
```

### 3. 开发

```bash
# 启动开发服务器
npm run dev

# 运行测试
npm test

# 代码格式化
npm run format

# 代码检查
npm run lint
```

### 4. 提交代码

```bash
# 添加更改
git add .

# 提交（请遵循提交信息规范）
git commit -m "feat: add new feature description"

# 推送到您的 Fork
git push origin feature/your-feature-name
```

### 5. 创建 Pull Request

1. 访问您的 Fork 页面
2. 点击 "New Pull Request"
3. 填写 PR 描述
4. 等待代码审查

## 📝 提交信息规范

我们使用 [Conventional Commits](https://www.conventionalcommits.org/) 规范：

```
<type>[optional scope]: <description>

[optional body]

[optional footer(s)]
```

### 类型 (type)

- `feat`: 新功能
- `fix`: Bug 修复
- `docs`: 文档更新
- `style`: 代码格式化（不影响功能）
- `refactor`: 代码重构
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动
- `perf`: 性能优化
- `ci`: CI/CD 相关

### 示例

```bash
feat: add quantum computing interface
fix: resolve memory leak in edge AI optimizer
docs: update API documentation
test: add unit tests for Web3 integration
```

## 🧪 测试指南

### 运行测试

```bash
# 运行所有测试
npm test

# 运行单元测试
npm run test:unit

# 运行集成测试
npm run test:integration

# 生成覆盖率报告
npm run test:coverage
```

### 编写测试

```javascript
// 单元测试示例
describe('AIOpsEngine', () => {
  test('should detect anomalies correctly', async () => {
    const engine = new AIOpsEngine();
    const result = await engine.detectAnomalies();
    expect(result).toBeDefined();
  });
});

// 集成测试示例
describe('API Integration', () => {
  test('POST /api/frontier/quantum/grover-search', async () => {
    const response = await request(app)
      .post('/api/frontier/quantum/grover-search')
      .send({ searchSpace: [1,2,3,4], targetItem: 3 })
      .expect(200);
    
    expect(response.body.success).toBe(true);
  });
});
```

## 📚 代码规范

### JavaScript/Node.js

- 使用 ES6+ 语法
- 遵循 ESLint 配置
- 使用 Prettier 格式化
- 添加 JSDoc 注释

```javascript
/**
 * 计算量子搜索的最优迭代次数
 * @param {number} searchSpaceSize - 搜索空间大小
 * @returns {number} 最优迭代次数
 */
function calculateOptimalIterations(searchSpaceSize) {
  return Math.floor(Math.PI / 4 * Math.sqrt(searchSpaceSize));
}
```

### Python

- 遵循 PEP 8 规范
- 使用类型提示
- 添加 docstring

```python
def optimize_model(model_path: str, config: Dict[str, Any]) -> OptimizationResult:
    """
    优化 AI 模型用于边缘部署
    
    Args:
        model_path: 模型文件路径
        config: 优化配置
        
    Returns:
        优化结果
    """
    pass
```

## 🏗️ 架构指南

### 目录结构

```
.all-agent/
├── server/                 # 后端服务
│   ├── core/              # 核心模块
│   ├── services/          # 业务服务
│   ├── middleware/        # 中间件
│   ├── utils/             # 工具函数
│   └── tests/             # 测试文件
├── ui/                    # 前端界面
├── aiops/                 # AIOps 智能运维
├── edge-ai/               # 边缘 AI 优化
├── web3/                  # Web3 区块链
├── quantum/               # 量子计算
└── docs/                  # 文档
```

### 设计原则

1. **模块化**: 每个功能模块独立，低耦合
2. **可扩展**: 易于添加新功能和技术
3. **可测试**: 代码易于单元测试和集成测试
4. **可维护**: 代码清晰，文档完整
5. **性能优先**: 考虑性能影响

## 🔧 开发环境

### 必需工具

- Node.js >= 18.0
- Python >= 3.9
- Git
- Docker (可选)

### 推荐工具

- VSCode + 扩展包
- Postman (API 测试)
- Docker Desktop
- Kubernetes (本地开发)

### 环境配置

```bash
# 复制环境配置
cp .env.example .env

# 编辑配置文件
vim .env

# 初始化数据库
npm run init-db
```

## 🐛 Bug 报告

### 报告模板

```markdown
**Bug 描述**
简要描述遇到的问题

**复现步骤**
1. 执行 '...'
2. 点击 '....'
3. 滚动到 '....'
4. 看到错误

**期望行为**
描述您期望发生的情况

**实际行为**
描述实际发生的情况

**环境信息**
- OS: [e.g. macOS 14.0]
- Node.js: [e.g. 18.17.0]
- Python: [e.g. 3.9.7]
- 浏览器: [e.g. Chrome 119]

**附加信息**
添加任何其他相关信息、截图或日志
```

## 💡 功能建议

### 建议模板

```markdown
**功能描述**
简要描述建议的功能

**问题背景**
描述这个功能要解决的问题

**解决方案**
描述您希望的解决方案

**替代方案**
描述您考虑过的其他解决方案

**附加信息**
添加任何其他相关信息
```

## 📖 文档贡献

### 文档类型

- API 文档
- 用户指南
- 开发文档
- 架构设计
- 最佳实践

### 文档规范

- 使用 Markdown 格式
- 添加代码示例
- 包含截图（如适用）
- 保持更新

## 🌐 国际化

我们欢迎翻译贡献：

- 中文（简体）- 主要语言
- 英文 - 国际化
- 其他语言 - 欢迎贡献

## 🎯 优先级

### 高优先级

- 🐛 Critical Bug 修复
- 🔒 安全漏洞修复
- 📚 文档完善
- 🧪 测试覆盖率提升

### 中优先级

- ✨ 新功能开发
- ⚡ 性能优化
- 🎨 UI/UX 改进
- 🔧 开发工具改进

### 低优先级

- 🧹 代码清理
- 📝 注释完善
- 🎯 代码重构
- 🌐 国际化

## 📞 联系方式

- GitHub Issues: 技术问题和 Bug 报告
- GitHub Discussions: 功能讨论和问答
- Email: <EMAIL>
- Discord: https://discord.gg/all-agent

## 📄 许可证

通过贡献代码，您同意您的贡献将在 MIT 许可证下授权。

## 🙏 致谢

感谢所有贡献者的努力！您的贡献让 All-Agent 变得更好。

---

**Happy Coding!** 🚀
